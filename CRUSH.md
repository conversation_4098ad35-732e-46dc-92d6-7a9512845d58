# Water Management System - CRUSH Configuration

## Build/Commands
- `composer test` - Run all tests (Pest)
- `./vendor/bin/pest --filter="TestName"` - Run specific test
- `composer run dev` - Start development server with queue and Vite
- `composer run build` - Build frontend assets
- `vendor/bin/pint` - Code formatting (<PERSON><PERSON> Pint)
- `vendor/bin/rector` - Code refactoring

## Code Style
- **PHP**: PSR-12 with strict types (`declare(strict_types=1)`)
- **Imports**: Use PSR-4 autoloading, group related imports
- **Type-hint**: All parameters and return types
- **Naming**: kebab-case for Livewire components, PascalCase for classes
- **Error Handling**: Use try-catch for external services, throw exceptions for validation
- **Livewire**: Single responsibility, use traits like `WithFormValidation`
- **Services**: Injectable business logic classes
- **Models**: Use Eloquent relationships, type-hint properties
- **Testing**: Pest PHP with Feature/Unit separation

## Standards
- Use Laravel's validation system
- Follow repository pattern for data access
- Use Spatie permissions for authorization
- Implement proper exception handling
- Use dependency injection for services