<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware): void {
        $middleware->validateCsrfTokens(except: [
            '/api/webhooks/whatsapp',
            '/api/exports*',
        ]);

        $middleware->alias([
            'estate.access' => \App\Http\Middleware\EstateAccessMiddleware::class,
            'house.access' => \App\Http\Middleware\HouseAccessMiddleware::class,
            'whatsapp.webhook' => \App\Http\Middleware\WhatsAppWebhookMiddleware::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions): void {
        //
    })->create();
