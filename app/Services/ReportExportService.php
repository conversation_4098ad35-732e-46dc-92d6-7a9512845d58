<?php

namespace App\Services;

use App\Models\Contact;
use App\Models\Estate;
use App\Models\House;
use App\Models\Invoice;
use App\Models\MeterReading;
use App\Models\WaterRate;
use Carbon\Carbon;
use Illuminate\Support\Collection;

class ReportExportService
{
    public function __construct(protected CalculationHelper $calculationHelper, protected ExportHelper $exportHelper) {}

    public function generateManagementReport(array $filters = []): array
    {
        $dateRange = $filters['date_range'] ?? 30;
        $selectedEstate = $filters['estate_id'] ?? null;
        $startDate = Carbon::now()->subDays($dateRange);

        return [
            'summary' => $this->getSummaryData($startDate, $selectedEstate),
            'estate_analytics' => $this->getEstateAnalytics($startDate, $selectedEstate),
            'consumption_trends' => $this->getConsumptionTrends($startDate, $selectedEstate),
            'revenue_analysis' => $this->getRevenueAnalysis($startDate, $selectedEstate),
            'top_consumers' => $this->getTopConsumers($startDate, $selectedEstate),
            'overdue_accounts' => $this->getOverdueAccounts($selectedEstate),
            'date_range' => $dateRange,
            'generated_at' => Carbon::now(),
        ];
    }

    public function generateEstateReport(Estate $estate, array $filters = []): array
    {
        $dateRange = $filters['date_range'] ?? 30;
        $startDate = Carbon::now()->subDays($dateRange);

        return [
            'estate_info' => [
                'name' => $estate->name,
                'location' => $estate->location,
                'total_houses' => $estate->houses->count(),
                'occupied_houses' => $estate->houses->where('status', 'occupied')->count(),
                'occupancy_rate' => ($estate->houses->where('status', 'occupied')->count() / max(1, $estate->houses->count())) * 100,
            ],
            'consumption_summary' => $this->getEstateConsumptionSummary($estate, $startDate),
            'house_analytics' => $this->getHouseAnalytics($estate, $startDate),
            'billing_summary' => $this->getEstateBillingSummary($estate, $startDate),
            'maintenance_alerts' => $this->getMaintenanceAlerts($estate),
            'date_range' => $dateRange,
            'generated_at' => Carbon::now(),
        ];
    }

    private function getSummaryData(Carbon $startDate, ?int $estateId): array
    {
        $baseQuery = function ($query) use ($startDate, $estateId): void {
            $query->where('created_at', '>=', $startDate);

            if ($estateId) {
                $query->whereHas('house', function ($q) use ($estateId): void {
                    $q->where('estate_id', $estateId);
                });
            }
        };

        $houseCount = $estateId
            ? House::where('estate_id', $estateId)->count()
            : House::count();

        return [
            'total_houses' => $houseCount,
            'total_estates' => Estate::count(),
            'total_consumption' => MeterReading::where($baseQuery)->sum('consumption'),
            'total_revenue' => Invoice::where($baseQuery)->sum('amount'),
            'avg_daily_consumption' => $this->calculationHelper->calculateAverageDailyConsumption(
                MeterReading::where($baseQuery)->get()
            ),
            'pending_readings' => MeterReading::where('status', 'pending')
                ->when($estateId, function ($query) use ($estateId): void {
                    $query->whereHas('house', function ($q) use ($estateId): void {
                        $q->where('estate_id', $estateId);
                    });
                })
                ->count(),
            'overdue_invoices' => Invoice::where('status', 'overdue')
                ->when($estateId, function ($query) use ($estateId): void {
                    $query->whereHas('house', function ($q) use ($estateId): void {
                        $q->where('estate_id', $estateId);
                    });
                })
                ->count(),
        ];
    }

    private function getEstateAnalytics(Carbon $startDate, ?int $estateId): Collection
    {
        $query = Estate::with(['houses.meterReadings' => function ($query) use ($startDate): void {
            $query->where('created_at', '>=', $startDate);
        }, 'houses.invoices' => function ($query) use ($startDate): void {
            $query->where('created_at', '>=', $startDate);
        }]);

        if ($estateId) {
            $query->where('id', $estateId);
        }

        return $query->get()->map(function ($estate) {
            $totalConsumption = $estate->houses->flatMap->meterReadings->sum('consumption');
            $totalRevenue = $estate->houses->flatMap->invoices->sum('amount');
            $totalHouses = $estate->houses->count();

            return [
                'estate' => $estate->name,
                'total_houses' => $totalHouses,
                'total_consumption' => $totalConsumption,
                'total_revenue' => $totalRevenue,
                'avg_consumption_per_house' => $totalHouses > 0 ? $totalConsumption / $totalHouses : 0,
                'revenue_per_house' => $totalHouses > 0 ? $totalRevenue / $totalHouses : 0,
                'occupancy_rate' => $totalHouses > 0
                    ? ($estate->houses->where('status', 'occupied')->count() / $totalHouses) * 100
                    : 0,
            ];
        })->sortByDesc('total_consumption');
    }

    private function getConsumptionTrends(Carbon $startDate, ?int $estateId): Collection
    {
        $query = MeterReading::selectRaw('DATE(created_at) as date, SUM(consumption) as total_consumption, COUNT(*) as reading_count')
            ->where('created_at', '>=', $startDate)
            ->groupBy('date')
            ->orderBy('date');

        if ($estateId) {
            $query->whereHas('house', function ($q) use ($estateId): void {
                $q->where('estate_id', $estateId);
            });
        }

        return $query->get()->map(fn ($item) => [
            'date' => $item->date,
            'total_consumption' => $item->total_consumption,
            'reading_count' => $item->reading_count,
            'avg_consumption' => $this->calculationHelper->calculateAverage($item->total_consumption, $item->reading_count),
        ]);
    }

    private function getRevenueAnalysis(Carbon $startDate, ?int $estateId): array
    {
        $query = Invoice::where('created_at', '>=', $startDate);

        if ($estateId) {
            $query->whereHas('house', function ($q) use ($estateId): void {
                $q->where('estate_id', $estateId);
            });
        }

        $revenueByStatus = $query->selectRaw('status, SUM(amount) as total, COUNT(*) as count')
            ->groupBy('status')
            ->get()
            ->keyBy('status');

        $revenueByDate = $query->selectRaw('DATE(created_at) as date, SUM(amount) as total, COUNT(*) as count')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        return [
            'by_status' => $revenueByStatus,
            'by_date' => $revenueByDate,
            'total_revenue' => $revenueByStatus->sum('total'),
            'total_invoices' => $revenueByStatus->sum('count'),
            'collection_rate' => $this->calculationHelper->calculateCollectionRate(
                $revenueByStatus->map(fn ($item) => ['amount' => $item->total])
            ),
        ];
    }

    private function getTopConsumers(Carbon $startDate, ?int $estateId): Collection
    {
        $query = House::with(['meterReadings' => function ($query) use ($startDate): void {
            $query->where('created_at', '>=', $startDate);
        }, 'estate'])
            ->when($estateId, function ($query) use ($estateId): void {
                $query->where('estate_id', $estateId);
            });

        return $query->get()->map(function ($house) {
            $totalConsumption = $house->meterReadings->sum('consumption');
            $readingCount = $house->meterReadings->count();

            return [
                'house' => $house->house_number,
                'estate' => $house->estate->name,
                'total_consumption' => $totalConsumption,
                'reading_count' => $readingCount,
                'avg_daily_consumption' => $this->calculationHelper->calculateAverageDailyConsumption(
                    $house->meterReadings
                ),
                'status' => $house->status,
            ];
        })->sortByDesc('total_consumption')->take(20);
    }

    private function getOverdueAccounts(?int $estateId): Collection
    {
        return Invoice::with(['house.estate', 'house.contacts'])
            ->where('status', 'overdue')
            ->when($estateId, function ($query) use ($estateId): void {
                $query->whereHas('house', function ($q) use ($estateId): void {
                    $q->where('estate_id', $estateId);
                });
            })
            ->get()
            ->map(fn ($invoice) => [
                'house' => $invoice->house->house_number,
                'estate' => $invoice->house->estate->name,
                'amount' => $invoice->amount,
                'due_date' => $invoice->due_date,
                'days_overdue' => Carbon::parse($invoice->due_date)->diffInDays(Carbon::now()),
                'contact' => $invoice->house->contacts->first()?->name ?? 'N/A',
            ])
            ->sortByDesc('days_overdue');
    }

    private function getEstateConsumptionSummary(Estate $estate, Carbon $startDate): array
    {
        $readings = $estate->houses->flatMap->meterReadings
            ->where('created_at', '>=', $startDate);

        return [
            'total_consumption' => $readings->sum('consumption'),
            'reading_count' => $readings->count(),
            'avg_daily_consumption' => $this->calculationHelper->calculateAverageDailyConsumption($readings),
            'max_consumption' => $readings->max('consumption') ?? 0,
            'min_consumption' => $readings->min('consumption') ?? 0,
            'houses_with_readings' => $readings->groupBy('house_id')->count(),
        ];
    }

    private function getHouseAnalytics(Estate $estate, Carbon $startDate): Collection
    {
        return $estate->houses->map(function ($house) use ($startDate) {
            $readings = $house->meterReadings->where('created_at', '>=', $startDate);
            $invoices = $house->invoices->where('created_at', '>=', $startDate);

            return [
                'house_number' => $house->house_number,
                'status' => $house->status,
                'total_consumption' => $readings->sum('consumption'),
                'reading_count' => $readings->count(),
                'total_billed' => $invoices->sum('amount'),
                'total_paid' => $invoices->where('status', 'paid')->sum('amount'),
                'outstanding_balance' => $invoices->where('status', '!=', 'paid')->sum('amount'),
                'last_reading_date' => $readings->max('created_at'),
            ];
        })->sortByDesc('total_consumption');
    }

    private function getEstateBillingSummary(Estate $estate, Carbon $startDate): array
    {
        $invoices = $estate->houses->flatMap->invoices
            ->where('created_at', '>=', $startDate);

        return [
            'total_invoiced' => $invoices->sum('amount'),
            'total_paid' => $invoices->where('status', 'paid')->sum('amount'),
            'total_outstanding' => $invoices->where('status', '!=', 'paid')->sum('amount'),
            'collection_rate' => $this->calculationHelper->calculateCollectionRate(
                $invoices->groupBy('status')
            ),
            'invoice_count' => $invoices->count(),
            'paid_invoices' => $invoices->where('status', 'paid')->count(),
            'overdue_invoices' => $invoices->where('status', 'overdue')->count(),
        ];
    }

    private function getMaintenanceAlerts(Estate $estate): Collection
    {
        return $estate->houses->filter(function ($house) {
            // Check for unusual consumption patterns
            $recentReadings = $house->meterReadings->take(3);
            if ($recentReadings->count() < 3) {
                return false;
            }

            $avgConsumption = $recentReadings->avg('consumption');
            $lastReading = $recentReadings->first();

            return $lastReading && $lastReading->consumption > $avgConsumption * 2;
        })->map(fn ($house) => [
            'house' => $house->house_number,
            'alert_type' => 'High Consumption',
            'message' => 'Unusual water consumption detected',
            'severity' => 'warning',
        ]);
    }

    // This method is now handled by CalculationHelper
    // private function calculateCollectionRate(Collection $revenueByStatus): float
    // {
    //     $total = $revenueByStatus->sum('total');
    //     $paid = $revenueByStatus->get('paid')?->total ?? 0;
    //     return $total > 0 ? ($paid / $total) * 100 : 0;
    // }

    // Export Data Methods
    public function getEstatesExportData(array $filters = []): Collection
    {
        $query = Estate::query();

        if (! empty($filters['search'])) {
            $query->where(function ($q) use ($filters): void {
                $q->where('name', 'like', '%'.$filters['search'].'%')
                    ->orWhere('location', 'like', '%'.$filters['search'].'%');
            });
        }

        if (! empty($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        return $query->withCount(['houses', 'houses as occupied_houses' => function ($q): void {
            $q->where('status', 'occupied');
        }])->get()->map(fn ($estate) => [
            'ID' => $estate->id,
            'Name' => $estate->name,
            'Location' => $estate->location,
            'Status' => $estate->status,
            'Total Houses' => $estate->houses_count,
            'Occupied Houses' => $estate->occupied_houses,
            'Occupancy Rate' => $this->calculationHelper->calculatePercentage(
                $estate->occupied_houses,
                $estate->houses_count
            ),
            'Created At' => $estate->created_at->format('Y-m-d H:i:s'),
            'Updated At' => $estate->updated_at->format('Y-m-d H:i:s'),
        ]);
    }

    public function getHousesExportData(array $filters = []): Collection
    {
        $builder = House::with('estate');

        if (! empty($filters['estate_id'])) {
            $builder->where('estate_id', $filters['estate_id']);
        }

        if (! empty($filters['search'])) {
            $builder->where(function ($q) use ($filters): void {
                $q->where('house_number', 'like', '%'.$filters['search'].'%')
                    ->orWhere('address', 'like', '%'.$filters['search'].'%');
            });
        }

        if (! empty($filters['status'])) {
            $builder->where('status', $filters['status']);
        }

        return $builder->get()->map(fn ($house) => [
            'ID' => $house->id,
            'House Number' => $house->house_number,
            'Estate' => $house->estate->name,
            'Address' => $house->address,
            'Status' => $house->status,
            'Meter Number' => $house->meter_number,
            'Created At' => $this->exportHelper->formatDate($house->created_at),
            'Updated At' => $this->exportHelper->formatDate($house->updated_at),
        ]);
    }

    public function getContactsExportData(array $filters = []): Collection
    {
        $builder = Contact::with(['house.estate']);

        if (! empty($filters['estate_id'])) {
            $builder->whereHas('house', function ($q) use ($filters): void {
                $q->where('estate_id', $filters['estate_id']);
            });
        }

        if (! empty($filters['search'])) {
            $builder->where(function ($q) use ($filters): void {
                $q->where('name', 'like', '%'.$filters['search'].'%')
                    ->orWhere('email', 'like', '%'.$filters['search'].'%')
                    ->orWhere('phone', 'like', '%'.$filters['search'].'%');
            });
        }

        if (! empty($filters['contact_type'])) {
            $builder->where('contact_type', $filters['contact_type']);
        }

        return $builder->get()->map(fn ($contact) => [
            'ID' => $contact->id,
            'Name' => $contact->name,
            'Email' => $contact->email,
            'Phone' => $contact->phone,
            'Contact Type' => $contact->contact_type,
            'House Number' => $contact->house?->house_number,
            'Estate' => $contact->house?->estate->name,
            'Status' => $contact->status,
            'Created At' => $this->exportHelper->formatDate($contact->created_at),
            'Updated At' => $this->exportHelper->formatDate($contact->updated_at),
        ]);
    }

    public function getMeterReadingsExportData(array $filters = []): Collection
    {
        $builder = MeterReading::with(['house.estate', 'user', 'reviewer']);

        if (! empty($filters['estate_id'])) {
            $builder->whereHas('house', function ($q) use ($filters): void {
                $q->where('estate_id', $filters['estate_id']);
            });
        }

        if (! empty($filters['house_id'])) {
            $builder->where('house_id', $filters['house_id']);
        }

        if (! empty($filters['status'])) {
            $builder->where('status', $filters['status']);
        }

        if (! empty($filters['date_from'])) {
            $builder->whereDate('reading_date', '>=', $filters['date_from']);
        }

        if (! empty($filters['date_to'])) {
            $builder->whereDate('reading_date', '<=', $filters['date_to']);
        }

        return $builder->get()->map(fn ($reading) => [
            'ID' => $reading->id,
            'House Number' => $reading->house->house_number,
            'Estate' => $reading->house->estate->name,
            'Reading Date' => $this->exportHelper->formatDate($reading->reading_date),
            'Current Reading' => $reading->current_reading,
            'Previous Reading' => $reading->previous_reading,
            'Consumption' => $reading->consumption,
            'Status' => $reading->status,
            'Submitted By' => $reading->user?->name,
            'Reviewed By' => $reading->reviewer?->name,
            'Created At' => $this->exportHelper->formatDate($reading->created_at),
            'Updated At' => $this->exportHelper->formatDate($reading->updated_at),
        ]);
    }

    public function getInvoicesExportData(array $filters = []): Collection
    {
        $builder = Invoice::with(['house.estate', 'waterRate']);

        if (! empty($filters['estate_id'])) {
            $builder->whereHas('house', function ($q) use ($filters): void {
                $q->where('estate_id', $filters['estate_id']);
            });
        }

        if (! empty($filters['house_id'])) {
            $builder->where('house_id', $filters['house_id']);
        }

        if (! empty($filters['status'])) {
            $builder->where('status', $filters['status']);
        }

        if (! empty($filters['date_from'])) {
            $builder->whereDate('billing_period_start', '>=', $filters['date_from']);
        }

        if (! empty($filters['date_to'])) {
            $builder->whereDate('billing_period_end', '<=', $filters['date_to']);
        }

        return $builder->get()->map(fn ($invoice) => [
            'ID' => $invoice->id,
            'Invoice Number' => $invoice->invoice_number,
            'House Number' => $invoice->house->house_number,
            'Estate' => $invoice->house->estate->name,
            'Billing Period' => $this->exportHelper->formatDate($invoice->billing_period_start).' to '.$this->exportHelper->formatDate($invoice->billing_period_end),
            'Consumption' => $invoice->consumption,
            'Rate per Unit' => $invoice->rate_per_unit,
            'Amount' => $invoice->total_amount,
            'Status' => $invoice->status,
            'Due Date' => $this->exportHelper->formatDate($invoice->due_date),
            'Created At' => $this->exportHelper->formatDate($invoice->created_at),
            'Updated At' => $this->exportHelper->formatDate($invoice->updated_at),
        ]);
    }

    public function getWaterRatesExportData(array $filters = []): Collection
    {
        $builder = WaterRate::with('estate');

        if (! empty($filters['estate_id'])) {
            $builder->where('estate_id', $filters['estate_id']);
        }

        if (! empty($filters['search'])) {
            $builder->where(function ($q) use ($filters): void {
                $q->where('name', 'like', '%'.$filters['search'].'%')
                    ->orWhere('description', 'like', '%'.$filters['search'].'%');
            });
        }

        if (! empty($filters['date_from'])) {
            $builder->whereDate('effective_from', '>=', $filters['date_from']);
        }

        if (! empty($filters['date_to'])) {
            $builder->whereDate('effective_from', '<=', $filters['date_to']);
        }

        return $builder->get()->map(fn ($rate) => [
            'ID' => $rate->id,
            'Name' => $rate->name,
            'Estate' => $rate->estate->name,
            'Rate per Unit' => $rate->rate_per_unit,
            'Effective From' => $this->exportHelper->formatDate($rate->effective_from),
            'Effective To' => $rate->effective_to ? $this->exportHelper->formatDate($rate->effective_to) : 'Ongoing',
            'Description' => $rate->description,
            'Status' => $rate->effective_to && $rate->effective_to->isPast() ? 'Expired' : 'Active',
            'Created At' => $this->exportHelper->formatDate($rate->created_at),
            'Updated At' => $this->exportHelper->formatDate($rate->updated_at),
        ]);
    }
}
