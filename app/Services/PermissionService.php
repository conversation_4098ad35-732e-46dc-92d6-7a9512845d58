<?php

namespace App\Services;

use App\Models\User;
use Illuminate\Support\Collection;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class PermissionService
{
    /**
     * Get all available permissions grouped by category
     */
    public function getAllPermissionsGrouped(): array
    {
        $permissions = Permission::all()->map(fn ($permission) => [
            'name' => $permission->name,
            'description' => $permission->description ?? null,
        ])->groupBy(fn ($permission) => $this->getPermissionCategory($permission['name']));

        return $permissions->toArray();
    }

    /**
     * Get all roles with their permissions
     */
    public function getAllRolesWithPermissions(): Collection
    {
        return Role::with('permissions')->get();
    }

    /**
     * Get user's permissions (delegates to Spatie)
     */
    public function getUserPermissions(User $user): array
    {
        return $user->getAllPermissions()->pluck('name')->toArray();
    }

    /**
     * Get user's roles (delegates to Spatie)
     */
    public function getUserRoles(User $user): array
    {
        return $user->roles()->pluck('name')->toArray();
    }

    /**
     * Assign permissions to user (delegates to Spatie)
     */
    public function assignPermissionsToUser(User $user, array $permissions): void
    {
        $user->syncPermissions($permissions);
    }

    /**
     * Assign roles to user (delegates to Spatie)
     */
    public function assignRolesToUser(User $user, array $roles): void
    {
        $user->syncRoles($roles);
    }

    /**
     * Check if user has specific permission (delegates to Spatie)
     */
    public function userHasPermission(User $user, string $permission): bool
    {
        return $user->hasPermissionTo($permission);
    }

    /**
     * Check if user has any of the given permissions (delegates to Spatie)
     */
    public function userHasAnyPermission(User $user, array $permissions): bool
    {
        return $user->hasAnyPermission($permissions);
    }

    /**
     * Check if user has all of the given permissions (delegates to Spatie)
     */
    public function userHasAllPermissions(User $user, array $permissions): bool
    {
        return $user->hasAllPermissions($permissions);
    }

    /**
     * Get permission category based on permission name
     */
    private function getPermissionCategory(string $permissionName): string
    {
        $categories = [
            'dashboard' => ['view-', '-dashboard'],
            'estates' => ['estates.'],
            'houses' => ['houses.'],
            'contacts' => ['contacts.'],
            'readings' => ['readings.'],
            'invoices' => ['invoices.'],
            'accounts' => ['accounts.'],
            'payments' => ['payments.'],
            'reports' => ['reports.'],
            'analytics' => ['analytics.'],
            'export' => ['export.'],
            'users' => ['users.', 'manage_all'],
            'system' => ['system.', 'settings.'],
            'whatsapp' => ['whatsapp.'],
            'resident' => ['resident.'],
            'rates' => ['rates.'],
        ];

        foreach ($categories as $category => $patterns) {
            foreach ($patterns as $pattern) {
                if (str_contains($permissionName, $pattern)) {
                    return ucfirst($category);
                }
            }
        }

        return 'Other';
    }

    /**
     * Get dashboard permissions for user
     */
    public function getDashboardPermissions(User $user): array
    {
        $dashboardPermissions = [
            'view-manager-dashboard',
            'view-reviewer-dashboard',
            'view-caretaker-dashboard',
            'view-resident-dashboard',
        ];

        return array_intersect($this->getUserPermissions($user), $dashboardPermissions);
    }

    /**
     * Get available dashboard views for user
     */
    public function getAvailableDashboardViews(User $user): array
    {
        $permissions = $this->getDashboardPermissions($user);
        $views = [];

        foreach ($permissions as $permission) {
            $views[] = str_replace('view-', '', str_replace('-dashboard', '', $permission));
        }

        return $views;
    }

    /**
     * Check if user can access any dashboard
     */
    public function canAccessAnyDashboard(User $user): bool
    {
        return $this->userHasAnyPermission($user, [
            'view-admin-dashboard',
            'view-manager-dashboard',
            'view-reviewer-dashboard',
            'view-caretaker-dashboard',
            'view-resident-dashboard',
        ]);
    }

    /**
     * Check if user can access specific dashboard view
     */
    public function canAccessDashboardView(User $user, string $view): bool
    {
        $permission = "view-{$view}-dashboard";

        return $this->userHasPermission($user, $permission);
    }

    /**
     * Get estate access permissions for user
     */
    public function getEstateAccessPermissions(User $user): array
    {
        $estatePermissions = [
            'estates.view_all',
            'estates.manage_all',
            'estates.view_assigned',
            'estates.manage_assigned',
        ];

        return array_intersect($this->getUserPermissions($user), $estatePermissions);
    }

    /**
     * Get house access permissions for user
     */
    public function getHouseAccessPermissions(User $user): array
    {
        $housePermissions = [
            'houses.view_all',
            'houses.manage_all',
            'houses.view_assigned',
            'houses.manage_assigned',
            'houses.view_own',
        ];

        return array_intersect($this->getUserPermissions($user), $housePermissions);
    }

    /**
     * Get reading permissions for user
     */
    public function getReadingPermissions(User $user): array
    {
        $readingPermissions = [
            'readings.view_all',
            'readings.create_all',
            'readings.edit_all',
            'readings.delete_all',
            'readings.review_all',
            'readings.view_assigned',
            'readings.create_assigned',
            'readings.edit_assigned',
            'readings.approve_assigned',
            'readings.validate',
            'readings.view_own',
        ];

        return array_intersect($this->getUserPermissions($user), $readingPermissions);
    }

    /**
     * Get payment permissions for user
     */
    public function getPaymentPermissions(User $user): array
    {
        $paymentPermissions = [
            'payments.view_all',
            'payments.view_assigned',
            'payments.view_own',
            'payments.view_history_all',
            'payments.view_history_assigned',
            'payments.view_history_own',
            'payments.create_all',
            'payments.create_assigned',
            'payments.edit_all',
            'payments.edit_assigned',
            'payments.approve_all',
            'payments.approve_assigned',
            'payments.export_all',
            'payments.export_assigned',
            'payments.reconcile_assigned',
        ];

        return array_intersect($this->getUserPermissions($user), $paymentPermissions);
    }

    /**
     * Get invoice permissions for user
     */
    public function getInvoicePermissions(User $user): array
    {
        $invoicePermissions = [
            'invoices.view_all',
            'invoices.create_manual',
            'invoices.edit_all',
            'invoices.delete_all',
            'invoices.view_assigned',
            'invoices.generate_assigned',
            'invoices.edit_assigned',
            'invoices.delete_assigned',
            'invoices.adjust_assigned',
            'invoices.export_assigned',
            'invoices.approve_assigned',
            'invoices.send_assigned',
            'invoices.view_own',
        ];

        return array_intersect($this->getUserPermissions($user), $invoicePermissions);
    }

    /**
     * Create a new permission
     */
    public function createPermission(string $name, ?string $description = null): Permission
    {
        return Permission::firstOrCreate([
            'name' => $name,
            'guard_name' => 'web',
        ], [
            'description' => $description ?? $name,
        ]);
    }

    /**
     * Create a new role
     */
    public function createRole(string $name, ?string $description = null): Role
    {
        return Role::firstOrCreate([
            'name' => $name,
            'guard_name' => 'web',
        ], [
            'description' => $description ?? $name,
        ]);
    }

    /**
     * Delete a permission
     */
    public function deletePermission(string $name): bool
    {
        $permission = Permission::where('name', $name)->first();
        if ($permission) {
            return $permission->delete();
        }

        return false;
    }

    /**
     * Delete a role (delegates to Spatie)
     */
    public function deleteRole(string $name): bool
    {
        $role = Role::where('name', $name)->first();
        if ($role && $role->name !== 'admin') { // Prevent deleting admin role
            return $role->delete();
        }

        return false;
    }
}
