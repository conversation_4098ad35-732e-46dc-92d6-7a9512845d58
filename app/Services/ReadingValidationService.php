<?php

namespace App\Services;

use App\Models\MeterReading;
use App\Services\Validation\BasicValidationRule;
use App\Services\Validation\ConsumptionValidationRule;
use App\Services\Validation\ContextualRule;
use App\Services\Validation\PatternAnomalyRule;
use App\Services\Validation\RuleEngine;
use App\Services\Validation\StatisticalAnomalyRule;
use Illuminate\Support\Collection;

class ReadingValidationService
{
    private readonly RuleEngine $ruleEngine;

    public function __construct()
    {
        $this->ruleEngine = new RuleEngine;
        $this->initializeDefaultRules();
    }

    private function initializeDefaultRules(): void
    {
        $this->ruleEngine
            ->addRule(new BasicValidationRule)
            ->addRule(new ConsumptionValidationRule)
            ->addRule(new StatisticalAnomalyRule)
            ->addRule(new PatternAnomalyRule)
            ->addRule(new ContextualRule);
    }

    public function validateReading(MeterReading $meterReading): array
    {
        $validationResult = $this->ruleEngine->validate($meterReading);

        return $validationResult->toArray();
    }

    public function getRuleEngine(): RuleEngine
    {
        return $this->ruleEngine;
    }

    public function addCustomRule($rule): self
    {
        $this->ruleEngine->addRule($rule);

        return $this;
    }

    public function removeRule(string $ruleName): self
    {
        $this->ruleEngine->removeRule($ruleName);

        return $this;
    }

    public function getAvailableRules(): array
    {
        return $this->ruleEngine->getRules()->map(fn ($rule) => [
            'name' => $rule->getName(),
            'description' => $rule->getDescription(),
            'severity' => $rule->getSeverity(),
        ])->toArray();
    }

    public function validateAndSave(MeterReading $meterReading): bool
    {
        $validationResult = $this->validateReading($meterReading);

        // Update reading with validation results
        $meterReading->validation_results = $validationResult;
        $meterReading->validation_status = $validationResult['risk_level'] === 'low' && $validationResult['confidence_score'] >= 80 ? 'approved' : 'pending';
        $meterReading->confidence_score = $validationResult['confidence_score'];
        $meterReading->risk_level = $validationResult['risk_level'];
        $meterReading->validated_at = now();
        $meterReading->validated_by = auth()->id();

        return $meterReading->save();
    }

    public function getFlaggedReadings(?string $riskLevel = null, int $limit = 50): Collection
    {
        $query = MeterReading::where('validation_status', '!=', 'approved')
            ->orWhere('risk_level', '!=', 'low');

        if ($riskLevel) {
            $query->where('risk_level', $riskLevel);
        }

        return $query->with(['house', 'house.estate'])
            ->orderBy('risk_level', 'desc')
            ->orderBy('confidence_score', 'asc')
            ->limit($limit)
            ->get();
    }

    public function getValidationStats(): array
    {
        $total = MeterReading::count();
        $approved = MeterReading::where('validation_status', 'approved')->count();
        $pending = MeterReading::where('validation_status', 'pending')->count();
        $flagged = MeterReading::where('risk_level', '!=', 'low')->count();

        return [
            'total_readings' => $total,
            'approved_count' => $approved,
            'pending_count' => $pending,
            'flagged_count' => $flagged,
            'approval_rate' => $total > 0 ? round(($approved / $total) * 100, 2) : 0,
            'flag_rate' => $total > 0 ? round(($flagged / $total) * 100, 2) : 0,
        ];
    }
}
