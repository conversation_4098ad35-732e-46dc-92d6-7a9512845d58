<?php

namespace App\Services;

use App\Models\House;
use App\Models\Invoice;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use PDF;

class PdfGenerationService
{
    public function generateInvoicePdf(Invoice $invoice): string
    {
        try {
            // Load the invoice with relationships
            $invoice->load(['house.estate', 'house.primaryContact', 'meterReading', 'waterRate']);

            // Generate PDF
            $pdf = PDF::loadView('pdf.invoice', [
                'invoice' => $invoice,
                'company' => $this->getCompanyDetails(),
            ]);

            // Set paper size and orientation
            $pdf->setPaper('A4', 'portrait');

            // Generate filename
            $filename = "invoice-{$invoice->invoice_number}-{$invoice->created_at->format('Y-m-d')}.pdf";
            $path = "invoices/{$filename}";

            // Save to storage
            Storage::put($path, $pdf->output());

            // Update invoice with PDF path
            $invoice->update(['pdf_path' => $path]);

            Log::info('PDF generated successfully', [
                'invoice_id' => $invoice->id,
                'invoice_number' => $invoice->invoice_number,
                'path' => $path,
            ]);

            return $path;

        } catch (\Exception $e) {
            Log::error('Failed to generate PDF', [
                'invoice_id' => $invoice->id,
                'error' => $e->getMessage(),
            ]);

            throw new \Exception('Failed to generate PDF: '.$e->getMessage(), $e->getCode(), $e);
        }
    }

    public function downloadInvoicePdf(Invoice $invoice)
    {
        if (! $invoice->pdf_path) {
            throw new \Exception('PDF not available for this invoice');
        }

        if (! Storage::exists($invoice->pdf_path)) {
            throw new \Exception('PDF file not found');
        }

        return Storage::download($invoice->pdf_path, "invoice-{$invoice->invoice_number}.pdf");
    }

    public function regenerateInvoicePdf(Invoice $invoice): string
    {
        // Delete existing PDF if it exists
        if ($invoice->pdf_path && Storage::exists($invoice->pdf_path)) {
            Storage::delete($invoice->pdf_path);
        }

        // Generate new PDF
        return $this->generateInvoicePdf($invoice);
    }

    public function generateStatementPdf(House $house, Collection $invoices, Collection $transactions, ?string $startDate = null, ?string $endDate = null): string
    {
        try {
            // Calculate opening balance
            $openingBalance = $this->calculateOpeningBalance($house, $startDate);

            // Calculate summary
            $summary = [
                'opening_balance' => $openingBalance,
                'total_invoices' => $invoices->sum('total_due'),
                'total_payments' => $transactions->where('transaction_type', 'payment')->sum('amount'),
                'total_adjustments' => $transactions->where('transaction_type', 'adjustment')->sum('amount'),
                'closing_balance' => $openingBalance + $invoices->sum('total_due') - $transactions->where('transaction_type', 'payment')->sum('amount') + $transactions->where('transaction_type', 'adjustment')->sum('amount'),
            ];

            // Generate PDF
            $pdf = PDF::loadView('pdf.statement', [
                'house' => $house,
                'invoices' => $invoices,
                'transactions' => $transactions,
                'summary' => $summary,
                'company' => $this->getCompanyDetails(),
                'period' => [
                    'start' => $startDate ?? now()->subMonths(3)->format('Y-m-d'),
                    'end' => $endDate ?? now()->format('Y-m-d'),
                ],
            ]);

            // Set paper size and orientation
            $pdf->setPaper('A4', 'portrait');

            // Generate filename
            $filename = "statement-{$house->house_number}-".now()->format('Y-m-d').'.pdf';
            $path = "statements/{$filename}";

            // Ensure directory exists
            Storage::makeDirectory('statements');

            // Save to storage
            Storage::put($path, $pdf->output());

            // Get absolute path for download
            $absolutePath = Storage::path($path);

            Log::info('Statement PDF generated successfully', [
                'house_id' => $house->id,
                'house_number' => $house->house_number,
                'path' => $path,
                'absolute_path' => $absolutePath,
            ]);

            return $absolutePath;

        } catch (\Exception $e) {
            Log::error('Failed to generate statement PDF', [
                'house_id' => $house->id,
                'error' => $e->getMessage(),
            ]);

            throw new \Exception('Failed to generate statement PDF: '.$e->getMessage(), $e->getCode(), $e);
        }
    }

    private function calculateOpeningBalance(House $house, ?string $startDate = null): float
    {
        if (! $startDate) {
            return 0;
        }

        $transactionsBefore = \App\Models\AccountTransaction::where('house_account_id', $house->account->id)
            ->whereDate('created_at', '<', $startDate)
            ->sum('amount');

        $invoicesBefore = Invoice::where('house_id', $house->id)
            ->whereDate('created_at', '<', $startDate)
            ->sum('total_due');

        return $invoicesBefore - $transactionsBefore;
    }

    private function getCompanyDetails(): array
    {
        return [
            'name' => config('app.name', 'Water Management System'),
            'address' => config('app.company_address', '123 Main Street, City, Country'),
            'phone' => config('app.company_phone', '+*********** 789'),
            'email' => config('app.company_email', '<EMAIL>'),
            'logo' => config('app.company_logo', null),
        ];
    }
}
