<?php

namespace App\Services;

use Carbon\Carbon;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;

class ValidationHelper
{
    /**
     * Common validation rules.
     */
    public const COMMON_RULES = [
        'required' => 'required',
        'nullable' => 'nullable',
        'string' => 'string',
        'integer' => 'integer',
        'numeric' => 'numeric',
        'boolean' => 'boolean',
        'email' => 'email',
        'date' => 'date',
        'date_format' => 'date_format:',
        'after' => 'after:',
        'before' => 'before:',
        'in' => 'in:',
        'not_in' => 'not_in:',
        'min' => 'min:',
        'max' => 'max:',
        'between' => 'between:',
        'alpha' => 'alpha',
        'alpha_num' => 'alpha_num',
        'alpha_dash' => 'alpha_dash',
        'url' => 'url',
        'active_url' => 'active_url',
        'ip' => 'ip',
        'ipv4' => 'ipv4',
        'ipv6' => 'ipv6',
    ];

    /**
     * Validate input data against rules.
     *
     * @throws ValidationException
     */
    public function validate(array $data, array $rules, array $messages = []): array
    {
        $validator = Validator::make($data, $rules, $messages);

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }

        return $validator->validated();
    }

    /**
     * Validate email format.
     */
    public function isValidEmail(string $email): bool
    {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }

    /**
     * Validate phone number format.
     */
    public function isValidPhone(string $phone): bool
    {
        // Basic phone validation - can be enhanced based on specific requirements
        $phone = preg_replace('/[^0-9+]/', '', $phone);

        return strlen((string) $phone) >= 10 && strlen((string) $phone) <= 15;
    }

    /**
     * Validate date format.
     */
    public function isValidDate(string $date, string $format = 'Y-m-d'): bool
    {
        $dateObj = \DateTime::createFromFormat($format, $date);

        return $dateObj && $dateObj->format($format) === $date;
    }

    /**
     * Validate that date is not in the past.
     */
    public function isDateNotInPast(string $date, string $format = 'Y-m-d'): bool
    {
        if (! $this->isValidDate($date, $format)) {
            return false;
        }

        $inputDate = Carbon::createFromFormat($format, $date);

        return $inputDate->greaterThanOrEqualTo(Carbon::today());
    }

    /**
     * Validate that end date is after start date.
     */
    public function isEndDateAfterStartDate(string $startDate, string $endDate, string $format = 'Y-m-d'): bool
    {
        if (! $this->isValidDate($startDate, $format) || ! $this->isValidDate($endDate, $format)) {
            return false;
        }

        $start = Carbon::createFromFormat($format, $startDate);
        $end = Carbon::createFromFormat($format, $endDate);

        return $end->greaterThan($start);
    }

    /**
     * Validate numeric range.
     */
    public function isNumericInRange(float $value, float $min, float $max): bool
    {
        return $value >= $min && $value <= $max;
    }

    /**
     * Validate string length.
     */
    public function isStringLengthValid(string $string, int $min, int $max): bool
    {
        $length = strlen($string);

        return $length >= $min && $length <= $max;
    }

    /**
     * Validate file upload.
     *
     * @param  int  $maxSize  in KB
     *
     * @throws ValidationException
     */
    public function validateFile(array $file, array $allowedMimes = [], int $maxSize = 2048): array
    {
        $rules = [
            'file' => 'required|file|max:'.($maxSize * 1024),
        ];

        if ($allowedMimes !== []) {
            $rules['file'] .= '|mimes:'.implode(',', $allowedMimes);
        }

        return $this->validate($file, $rules);
    }

    /**
     * Generate unique slug from string.
     */
    public function generateSlug(string $string, string $separator = '-'): string
    {
        return Str::slug($string, $separator);
    }

    /**
     * Validate password strength.
     */
    public function validatePasswordStrength(
        string $password,
        int $minLength = 8,
        bool $requireUppercase = true,
        bool $requireLowercase = true,
        bool $requireNumber = true,
        bool $requireSpecialChar = true
    ): array {
        $errors = [];

        if (strlen($password) < $minLength) {
            $errors[] = "Password must be at least {$minLength} characters long.";
        }

        if ($requireUppercase && ! preg_match('/[A-Z]/', $password)) {
            $errors[] = 'Password must contain at least one uppercase letter.';
        }

        if ($requireLowercase && ! preg_match('/[a-z]/', $password)) {
            $errors[] = 'Password must contain at least one lowercase letter.';
        }

        if ($requireNumber && ! preg_match('/\d/', $password)) {
            $errors[] = 'Password must contain at least one number.';
        }

        if ($requireSpecialChar && ! preg_match('/[^A-Za-z0-9]/', $password)) {
            $errors[] = 'Password must contain at least one special character.';
        }

        return $errors;
    }

    /**
     * Validate IBAN format.
     */
    public function isValidIban(string $iban): bool
    {
        // Basic IBAN validation - can be enhanced with more sophisticated checks
        $iban = strtoupper(str_replace([' ', '-'], '', $iban));

        if (! preg_match('/^[A-Z]{2}\d{2}[A-Z0-9]{1,30}$/', $iban)) {
            return false;
        }

        // Check digit validation (simplified)
        $countryCode = substr($iban, 0, 2);
        $ibanBody = substr($iban, 4);

        // Move first 4 characters to the end
        $rearranged = $ibanBody.$countryCode.'00';

        // Replace letters with numbers
        $numericIban = '';
        for ($i = 0; $i < strlen($rearranged); $i++) {
            $char = $rearranged[$i];
            $numericIban .= ctype_alpha($char) ? ord($char) - 55 : $char;
        }

        // Calculate mod 97
        $remainder = (int) $numericIban % 97;

        return $remainder === 1;
    }

    /**
     * Validate tax ID/VAT number format.
     */
    public function isValidTaxId(string $taxId, string $countryCode = 'KE'): bool
    {
        // This is a simplified validation - should be enhanced based on specific country requirements
        $taxId = strtoupper(str_replace([' ', '-', ','], '', $taxId));

        return match ($countryCode) {
            // Kenyan VAT format: KB1234567
            'KE' => preg_match('/^KB\d{7}$/', $taxId),
            // US EIN format: 12-3456789
            'US' => preg_match('/^\d{2}-\d{7}$/', $taxId),
            // Generic alphanumeric validation for other countries
            default => preg_match('/^[A-Z0-9]{8,20}$/', $taxId),
        };
    }

    /**
     * Validate meter reading.
     */
    public function validateMeterReading(
        float $reading,
        float $previousReading,
        float $minConsumption = 0,
        float $maxConsumption = 1000
    ): array {
        $errors = [];

        if ($reading < 0) {
            $errors[] = 'Meter reading cannot be negative.';
        }

        if ($reading < $previousReading) {
            $errors[] = 'Current reading cannot be less than previous reading.';
        }

        $consumption = $reading - $previousReading;

        if ($consumption < $minConsumption) {
            $errors[] = "Consumption cannot be less than {$minConsumption} units.";
        }

        if ($consumption > $maxConsumption) {
            $errors[] = "Consumption cannot exceed {$maxConsumption} units.";
        }

        return $errors;
    }

    /**
     * Validate water rate parameters.
     */
    public function validateWaterRate(array $rateData): array
    {
        $errors = [];

        if (! isset($rateData['name']) || empty($rateData['name'])) {
            $errors[] = 'Rate name is required.';
        }

        if (! isset($rateData['rate']) || ! is_numeric($rateData['rate']) || $rateData['rate'] < 0) {
            $errors[] = 'Valid rate amount is required.';
        }

        if (! isset($rateData['effective_from']) || ! $this->isValidDate($rateData['effective_from'])) {
            $errors[] = 'Valid effective date is required.';
        }

        if (isset($rateData['effective_to']) && ! empty($rateData['effective_to'])) {
            if (! $this->isValidDate($rateData['effective_to'])) {
                $errors[] = 'Valid effective to date is required.';
            } elseif (isset($rateData['effective_from']) && ! $this->isEndDateAfterStartDate(
                $rateData['effective_from'],
                $rateData['effective_to']
            )) {
                $errors[] = 'Effective to date must be after effective from date.';
            }
        }

        return $errors;
    }

    /**
     * Validate invoice data.
     */
    public function validateInvoice(array $invoiceData): array
    {
        $errors = [];

        if (! isset($invoiceData['house_id']) || ! is_numeric($invoiceData['house_id'])) {
            $errors[] = 'Valid house ID is required.';
        }

        if (! isset($invoiceData['invoice_date']) || ! $this->isValidDate($invoiceData['invoice_date'])) {
            $errors[] = 'Valid invoice date is required.';
        }

        if (! isset($invoiceData['due_date']) || ! $this->isValidDate($invoiceData['due_date'])) {
            $errors[] = 'Valid due date is required.';
        } elseif (isset($invoiceData['invoice_date']) && ! $this->isEndDateAfterStartDate(
            $invoiceData['invoice_date'],
            $invoiceData['due_date']
        )) {
            $errors[] = 'Due date must be after invoice date.';
        }

        if (! isset($invoiceData['total_amount']) || ! is_numeric($invoiceData['total_amount']) || $invoiceData['total_amount'] < 0) {
            $errors[] = 'Valid total amount is required.';
        }

        if (! isset($invoiceData['status']) || ! in_array($invoiceData['status'], ['draft', 'pending', 'paid', 'overdue', 'cancelled'])) {
            $errors[] = 'Valid invoice status is required.';
        }

        return $errors;
    }

    /**
     * Sanitize input data.
     */
    public function sanitize(array $data): array
    {
        $sanitized = [];

        foreach ($data as $key => $value) {
            if (is_string($value)) {
                $sanitized[$key] = trim($value);
                $sanitized[$key] = stripslashes($sanitized[$key]);
                $sanitized[$key] = htmlspecialchars($sanitized[$key], ENT_QUOTES, 'UTF-8');
            } else {
                $sanitized[$key] = $value;
            }
        }

        return $sanitized;
    }

    /**
     * Check if array has required keys.
     */
    public function hasRequiredKeys(array $data, array $requiredKeys): array
    {
        $missing = [];

        foreach ($requiredKeys as $requiredKey) {
            if (! array_key_exists($requiredKey, $data) || (is_null($data[$requiredKey]) && $data[$requiredKey] !== 0 && $data[$requiredKey] !== false)) {
                $missing[] = $requiredKey;
            }
        }

        return $missing;
    }

    /**
     * Validate UUID format.
     */
    public function isValidUuid(string $uuid): bool
    {
        return preg_match('/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i', $uuid) === 1;
    }

    /**
     * Validate URL format.
     */
    public function isValidUrl(string $url): bool
    {
        return filter_var($url, FILTER_VALIDATE_URL) !== false;
    }
}
