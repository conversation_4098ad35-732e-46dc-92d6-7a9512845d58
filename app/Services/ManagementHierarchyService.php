<?php

namespace App\Services;

use App\Models\PermissionAuditLog;
use App\Models\User;
use Illuminate\Support\Facades\DB;

class ManagementHierarchyService
{
    public function __construct(
        private readonly PermissionService $permissionService
    ) {}

    /**
     * Assign subordinates to a manager
     */
    public function assignSubordinates(User $user, array $subordinateIds, string $relationship): void
    {
        DB::transaction(function () use ($user, $subordinateIds, $relationship): void {
            // Get current relationships for comparison
            $currentSubordinates = $user->subordinates()->pluck('subordinate_id')->toArray();

            // Remove existing relationships
            $user->subordinates()->detach();

            // Add new relationships
            $relationships = collect($subordinateIds)->map(fn ($subordinateId) => [
                'subordinate_id' => $subordinateId,
                'relationship' => $relationship,
            ]);

            if ($relationships->isNotEmpty()) {
                $user->subordinates()->attach($relationships);
            }

            // Ensure estate assignments align with hierarchy
            $this->syncEstateAssignments($user, $subordinateIds);

            // Log the hierarchy changes
            $this->logHierarchyChanges($user, $currentSubordinates, $subordinateIds, $relationship);
        });
    }

    /**
     * Get managed users for a manager
     */
    public function getManagedUsers(User $user)
    {
        return $user->subordinates()->with('assignedEstates')->get();
    }

    /**
     * Validate management relationship
     */
    public function validateManagementRelationship(User $manager, User $subordinate): bool
    {
        return $manager->subordinates()->where('subordinate_id', $subordinate->id)->exists();
    }

    /**
     * Get managers for a user
     */
    public function getUserManagers(User $user)
    {
        return $user->managers()->get();
    }

    /**
     * Check if user can manage another user
     */
    public function canManageUser(User $manager, User $subordinate): bool
    {
        // Admin can manage everyone
        if ($manager->can('users.manage_all')) {
            return true;
        }

        // Manager can manage reviewers and caretakers
        if ($this->permissionService->hasPermission($manager, 'users.manage')) {
            return $this->permissionService->hasPermission($subordinate, 'billing.view_all') ||
                   $this->permissionService->hasPermission($subordinate, 'estate.view_assigned');
        }

        // Reviewer can manage caretakers
        if ($this->permissionService->hasPermission($manager, 'billing.view_all')) {
            return $this->permissionService->hasPermission($subordinate, 'estate.view_assigned');
        }

        return false;
    }

    /**
     * Get hierarchy level for user
     */
    public function getHierarchyLevel(User $user): int
    {
        if ($this->permissionService->hasPermission($user, 'users.manage_all')) {
            return 1;
        }

        if ($this->permissionService->hasPermission($user, 'users.manage')) {
            return 2;
        }

        if ($this->permissionService->hasPermission($user, 'billing.view_all')) {
            return 3;
        }

        if ($this->permissionService->hasPermission($user, 'estate.view_assigned')) {
            return 4;
        }

        return 5; // Resident
    }

    /**
     * Get all users in management chain
     */
    public function getManagementChain(User $user): \Illuminate\Support\Collection
    {
        $chain = [];
        $currentManagers = $this->getUserManagers($user);

        foreach ($currentManagers as $currentManager) {
            $chain[] = $currentManager;
            $chain = array_merge($chain, $this->getManagementChain($currentManager)->toArray());
        }

        return collect(array_unique($chain));
    }

    /**
     * Get all subordinates in hierarchy
     */
    public function getAllSubordinates(User $user): \Illuminate\Support\Collection
    {
        $subordinates = [];
        $directSubordinates = $this->getManagedUsers($user);

        foreach ($directSubordinates as $directSubordinate) {
            $subordinates[] = $directSubordinate;
            $subordinates = array_merge($subordinates, $this->getAllSubordinates($directSubordinate)->toArray());
        }

        return collect(array_unique($subordinates));
    }

    /**
     * Remove management relationship
     */
    public function removeManagementRelationship(User $manager, User $subordinate): void
    {
        DB::transaction(function () use ($manager, $subordinate): void {
            $relationship = $manager->subordinates()->where('subordinate_id', $subordinate->id)->first();

            if ($relationship) {
                $manager->subordinates()->detach($subordinate->id);

                // Log the removal
                PermissionAuditLog::create([
                    'user_id' => $manager->id,
                    'action' => 'management_relationship_removed',
                    'details' => json_encode([
                        'manager_id' => $manager->id,
                        'manager_name' => $manager->name,
                        'subordinate_id' => $subordinate->id,
                        'subordinate_name' => $subordinate->name,
                        'relationship' => $relationship->pivot->relationship,
                    ]),
                    'target_type' => 'management_hierarchy',
                    'target_id' => "{$manager->id}_{$subordinate->id}",
                    'ip_address' => request()->ip(),
                    'user_agent' => request()->userAgent(),
                ]);
            }
        });
    }

    /**
     * Sync estate assignments based on hierarchy
     */
    private function syncEstateAssignments(User $user, array $subordinateIds): void
    {
        $managerEstates = $user->assignedEstates()->pluck('estates.id')->toArray();

        foreach ($subordinateIds as $subordinateId) {
            $subordinate = User::findOrFail($subordinateId);

            // Assign manager's estates to subordinate with required pivot fields
            $pivotData = [];
            foreach ($managerEstates as $managerEstate) {
                $pivotData[$managerEstate] = [
                    'assigned_by' => $user->id,
                    'assigned_at' => now(),
                ];
            }

            if ($pivotData !== []) {
                $subordinate->assignedEstates()->syncWithoutDetaching($pivotData);
            }
        }
    }

    /**
     * Log hierarchy changes
     */
    private function logHierarchyChanges(User $user, array $oldSubordinates, array $newSubordinates, string $relationship): void
    {
        $addedSubordinates = array_diff($newSubordinates, $oldSubordinates);
        $removedSubordinates = array_diff($oldSubordinates, $newSubordinates);

        if ($addedSubordinates !== [] || $removedSubordinates !== []) {
            PermissionAuditLog::create([
                'user_id' => $user->id,
                'action' => 'management_hierarchy_updated',
                'details' => json_encode([
                    'manager_id' => $user->id,
                    'manager_name' => $user->name,
                    'relationship' => $relationship,
                    'added_subordinates' => $addedSubordinates,
                    'removed_subordinates' => $removedSubordinates,
                    'total_subordinates' => count($newSubordinates),
                ]),
                'target_type' => 'management_hierarchy',
                'target_id' => (string) $user->id,
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
            ]);
        }
    }
}
