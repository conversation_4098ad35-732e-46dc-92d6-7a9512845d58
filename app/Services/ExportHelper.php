<?php

namespace App\Services;

use App\Exports\AgingReportExport;
use App\Exports\CustomerStatementExport;
use App\Exports\DynamicExport;
use App\Exports\InvoiceReportExport;
use App\Exports\ManagementReportExport;
use App\Exports\RevenueReportExport;
use App\Jobs\ProcessExportJob;
use App\Models\ExportJob;
use App\Models\ExportTemplate;
use App\Repositories\ExportJobRepository;
use App\Repositories\ExportTemplateRepository;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;

class ExportHelper
{
    public function __construct(
        private readonly ExportTemplateRepository $exportTemplateRepository,
        private readonly ExportJobRepository $exportJobRepository
    ) {}

    /**
     * Available export types.
     */
    public const EXPORT_TYPES = [
        'dynamic' => 'Dynamic Export',
        'aging_report' => 'Aging Report',
        'customer_statement' => 'Customer Statement',
        'invoice_report' => 'Invoice Report',
        'management_report' => 'Management Report',
        'revenue_report' => 'Revenue Report',
    ];

    /**
     * Get available export templates.
     */
    public function getExportTemplates(): Collection
    {
        return $this->exportTemplateRepository->all();
    }

    /**
     * Get export template by ID.
     */
    public function getExportTemplate(int $id): ?ExportTemplate
    {
        return $this->exportTemplateRepository->find($id);
    }

    /**
     * Create a new export template.
     */
    public function createExportTemplate(array $data): ExportTemplate
    {
        return $this->exportTemplateRepository->create($data);
    }

    /**
     * Update an export template.
     */
    public function updateExportTemplate(int $id, array $data): ?ExportTemplate
    {
        return $this->exportTemplateRepository->update($id, $data);
    }

    /**
     * Delete an export template.
     */
    public function deleteExportTemplate(int $id): bool
    {
        return $this->exportTemplateRepository->delete($id);
    }

    /**
     * Get export class by type.
     */
    public function getExportClass(string $type): ?string
    {
        $exportClasses = [
            'dynamic' => DynamicExport::class,
            'aging_report' => AgingReportExport::class,
            'customer_statement' => CustomerStatementExport::class,
            'invoice_report' => InvoiceReportExport::class,
            'management_report' => ManagementReportExport::class,
            'revenue_report' => RevenueReportExport::class,
        ];

        return $exportClasses[$type] ?? null;
    }

    /**
     * Prepare export data based on type and filters.
     */
    public function prepareExportData(string $type, array $filters = []): array
    {
        return match ($type) {
            'dynamic' => $this->prepareDynamicExportData($filters),
            'aging_report' => $this->prepareAgingReportData($filters),
            'customer_statement' => $this->prepareCustomerStatementData($filters),
            'invoice_report' => $this->prepareInvoiceReportData($filters),
            'management_report' => $this->prepareManagementReportData($filters),
            'revenue_report' => $this->prepareRevenueReportData($filters),
            default => ['data' => [], 'headers' => []],
        };
    }

    /**
     * Prepare data for dynamic export.
     */
    protected function prepareDynamicExportData(array $filters): array
    {
        // This would typically query the database based on filters
        // For now, returning empty structure
        return [
            'data' => [],
            'headers' => [],
            'model' => $filters['model'] ?? null,
            'columns' => $filters['columns'] ?? [],
        ];
    }

    /**
     * Prepare data for aging report.
     */
    protected function prepareAgingReportData(array $filters): array
    {
        // This would typically query invoices and calculate aging
        // For now, returning empty structure
        return [
            'data' => [],
            'headers' => ['Customer', 'Total Due', 'Current', '1-30 Days', '31-60 Days', '61-90 Days', 'Over 90 Days'],
            'estate_id' => $filters['estate_id'] ?? null,
            'as_of_date' => $filters['as_of_date'] ?? now()->format('Y-m-d'),
        ];
    }

    /**
     * Prepare data for customer statement.
     */
    protected function prepareCustomerStatementData(array $filters): array
    {
        // This would typically query invoices, payments, and adjustments for a specific customer
        // For now, returning empty structure
        return [
            'data' => [],
            'headers' => ['Date', 'Description', 'Debit', 'Credit', 'Balance'],
            'customer_id' => $filters['customer_id'] ?? null,
            'start_date' => $filters['start_date'] ?? null,
            'end_date' => $filters['end_date'] ?? null,
        ];
    }

    /**
     * Prepare data for invoice report.
     */
    protected function prepareInvoiceReportData(array $filters): array
    {
        // This would typically query invoices based on filters
        // For now, returning empty structure
        return [
            'data' => [],
            'headers' => ['Invoice #', 'Date', 'Customer', 'Amount', 'Status', 'Due Date'],
            'estate_id' => $filters['estate_id'] ?? null,
            'status' => $filters['status'] ?? null,
            'start_date' => $filters['start_date'] ?? null,
            'end_date' => $filters['end_date'] ?? null,
        ];
    }

    /**
     * Prepare data for management report.
     */
    protected function prepareManagementReportData(array $filters): array
    {
        // This would typically gather various KPIs and metrics
        // For now, returning empty structure
        return [
            'data' => [],
            'headers' => ['Metric', 'Value'],
            'estate_id' => $filters['estate_id'] ?? null,
            'period' => $filters['period'] ?? 'monthly',
        ];
    }

    /**
     * Prepare data for revenue report.
     */
    protected function prepareRevenueReportData(array $filters): array
    {
        // This would typically query payments and group by period
        // For now, returning empty structure
        return [
            'data' => [],
            'headers' => ['Period', 'Revenue', 'Invoices', 'Payments'],
            'estate_id' => $filters['estate_id'] ?? null,
            'start_date' => $filters['start_date'] ?? null,
            'end_date' => $filters['end_date'] ?? null,
        ];
    }

    /**
     * Generate and store export file.
     *
     * @param  string  $format  (xlsx, csv, pdf)
     */
    public function generateExportFile(string $type, array $data, string $filename, string $format = 'xlsx'): ?string
    {
        try {
            $exportClass = $this->getExportClass($type);

            if (! $exportClass) {
                throw new \Exception("Export type '{$type}' not supported.");
            }

            $exportInstance = new $exportClass($data);

            $filePath = "exports/{$filename}.{$format}";

            match ($format) {
                'xlsx' => Excel::store($exportInstance, $filePath, 'public'),
                'csv' => Excel::store($exportInstance, $filePath, 'public', \Maatwebsite\Excel\Excel::CSV),
                default => throw new \Exception("Format '{$format}' not supported."),
            };

            return Storage::url($filePath);

        } catch (\Exception $e) {
            \Log::error('Export generation failed: '.$e->getMessage());

            return null;
        }
    }

    /**
     * Create and track an export job.
     */
    public function createExportJob(string $type, array $filters, string $format, int $userId): ExportJob
    {
        $exportJob = $this->exportJobRepository->create([
            'type' => $type,
            'filters' => json_encode($filters),
            'format' => $format,
            'user_id' => $userId,
            'status' => 'pending',
        ]);

        // Dispatch job to process export
        ProcessExportJob::dispatch($exportJob);

        return $exportJob;
    }

    /**
     * Get export job status.
     */
    public function getExportJob(int $jobId): ?ExportJob
    {
        return $this->exportJobRepository->find($jobId);
    }

    /**
     * Get user's export jobs.
     */
    public function getUserExportJobs(int $userId): Collection
    {
        return $this->exportJobRepository->where([['user_id', '=', $userId]])->sortByDesc('created_at');
    }

    /**
     * Download exported file.
     *
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse|null
     */
    public function downloadExportFile(string $path)
    {
        try {
            if (Storage::disk('public')->exists($path)) {
                return Storage::disk('public')->download($path);
            }

            return null;
        } catch (\Exception $e) {
            \Log::error('Export download failed: '.$e->getMessage());

            return null;
        }
    }

    /**
     * Clean up old export files.
     */
    public function cleanupOldExports(int $daysToKeep = 30): int
    {
        $cutoffDate = now()->subDays($daysToKeep);

        $conditions = [
            ['created_at', '<', $cutoffDate->format('Y-m-d H:i:s')],
            ['status', '=', 'completed'],
        ];
        $oldJobs = $this->exportJobRepository->where($conditions);

        $deletedCount = 0;

        foreach ($oldJobs as $oldJob) {
            if (property_exists($oldJob, 'file_path') && $oldJob->file_path) {
                Storage::disk('public')->delete($oldJob->file_path);
            }
            $this->exportJobRepository->delete($oldJob->id); // Assuming delete method takes ID
            $deletedCount++;
        }

        return $deletedCount;
    }

    /**
     * Get export file extension based on type.
     */
    public function getDefaultFileExtension(string $type): string
    {
        // Most exports are Excel files by default
        return 'xlsx';
    }

    /**
     * Generate a unique filename for export.
     */
    public function generateExportFilename(string $type, string $userId): string
    {
        $timestamp = now()->format('Ymd_His');
        $extension = $this->getDefaultFileExtension($type);
        $typeName = str_replace('_', '-', $type);

        return "{$typeName}_export_{$userId}_{$timestamp}.{$extension}";
    }

    /**
     * Validate export filters.
     */
    public function validateExportFilters(string $type, array $filters): array
    {
        $errors = [];

        // Add type-specific validation rules
        switch ($type) {
            case 'customer_statement':
                if (empty($filters['customer_id'])) {
                    $errors[] = 'Customer ID is required for customer statement export.';
                }
                break;

            case 'aging_report':
                if (! empty($filters['as_of_date']) && ! strtotime((string) $filters['as_of_date'])) {
                    $errors[] = 'Invalid "as of date" format.';
                }
                break;
        }

        // Common validation
        if (! in_array($type, array_keys(self::EXPORT_TYPES))) {
            $errors[] = "Invalid export type: {$type}";
        }

        return $errors;
    }

    /**
     * Format date for export.
     *
     * @param  \Carbon\Carbon|string  $date
     */
    public function formatDate($date, string $format = 'Y-m-d H:i:s'): string
    {
        if ($date instanceof \Carbon\Carbon) {
            return $date->format($format);
        }

        if (is_string($date)) {
            try {
                $carbonDate = \Carbon\Carbon::parse($date);

                return $carbonDate->format($format);
            } catch (\Exception) {
                return $date; // Return original if parsing fails
            }
        }

        return (string) $date;
    }
}
