<?php

namespace App\Services;

use App\Models\House;
use App\Models\Invoice;
use App\Models\MeterReading;
use App\Models\WaterRate;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class InvoiceGenerationService
{
    public function generateFromReading(MeterReading $meterReading)
    {
        try {
            DB::beginTransaction();

            // Get the house and water rate
            $house = $meterReading->house;
            $waterRate = WaterRate::where('estate_id', $house->estate_id)
                ->where('is_active', true)
                ->first();

            if (! $waterRate) {
                throw new \Exception('No active water rate found for this estate');
            }

            // Calculate consumption
            $consumption = $meterReading->consumption;
            $amount = $this->calculateAmount($consumption, $waterRate);

            // Check for previous unpaid invoices
            $previousBalance = Invoice::where('house_id', $house->id)
                ->where('status', 'pending')
                ->sum('amount');

            // Create invoice
            $invoice = Invoice::create([
                'house_id' => $house->id,
                'meter_reading_id' => $meterReading->id,
                'invoice_number' => $this->generateInvoiceNumber(),
                'period_start' => $meterReading->reading_date->copy()->subMonth(),
                'period_end' => $meterReading->reading_date,
                'consumption' => $consumption,
                'rate_per_unit' => $waterRate->rate_per_unit,
                'amount' => $amount,
                'previous_balance' => $previousBalance,
                'total_amount' => $amount + $previousBalance,
                'due_date' => now()->addDays(14),
                'status' => 'pending',
            ]);

            // Generate PDF invoice
            $this->generatePdfInvoice($invoice);

            // Send WhatsApp notification
            $this->sendWhatsAppNotification($invoice);

            DB::commit();

            return $invoice;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    private function calculateAmount($consumption, $waterRate)
    {
        $amount = 0;
        $remainingConsumption = $consumption;

        // Apply tiered pricing
        foreach ($waterRate->tiers as $tier) {
            if ($remainingConsumption <= 0) {
                break;
            }

            $tierConsumption = min($remainingConsumption, $tier['max_consumption'] - $tier['min_consumption']);
            $tierAmount = $tierConsumption * $tier['rate_per_unit'];
            $amount += $tierAmount;
            $remainingConsumption -= $tierConsumption;
        }

        return $amount;
    }

    private function generateInvoiceNumber()
    {
        return 'INV-'.date('Y-m').'-'.str_pad(mt_rand(100000, 999999), 6, '0', STR_PAD_LEFT);
    }

    private function generatePdfInvoice($invoice)
    {
        $pdfGenerationService = app(PdfGenerationService::class);

        return $pdfGenerationService->generateInvoicePdf($invoice);
    }

    private function sendWhatsAppNotification($invoice)
    {
        try {
            $whatsAppService = app(WhatsAppService::class);
            $whatsAppService->sendInvoiceNotification($invoice);

            Log::info('WhatsApp notification sent for invoice', [
                'invoice_id' => $invoice->id,
                'invoice_number' => $invoice->invoice_number,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to send WhatsApp notification for invoice', [
                'invoice_id' => $invoice->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    // Removed duplicate calculateAmount method

    public function calculatePreviousBalance($houseId)
    {
        return Invoice::where('house_id', $houseId)
            ->where('status', 'pending')
            ->sum('amount');
    }

    // Removed duplicate sendWhatsAppNotification method
}
