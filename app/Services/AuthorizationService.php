<?php

namespace App\Services;

use App\Models\User;
use App\Repositories\UserRepository;
use Illuminate\Database\Eloquent\Model;

class AuthorizationService
{
    public function __construct(
        private readonly UserRepository $userRepository
    ) {}

    /**
     * Check if user can manage an entity (create, read, update, delete)
     */
    public function canManageEntity(User $user, Model $model, string $permission, ?string $entityType = null)
    {
        // Check for global permission
        if ($user->can("{$permission}_all")) {
            return true;
        }

        // Check for assigned permission
        if ($user->can("{$permission}_assigned")) {
            return $this->isEntityAssignedToUser($user, $model, $entityType);
        }

        return false;
    }

    /**
     * Check if user can delete an entity
     */
    public function canDeleteEntity(User $user, Model $model, ?string $entityType = null)
    {
        return $this->canManageEntity($user, $model, 'delete', $entityType);
    }

    /**
     * Check if user can update an entity
     */
    public function canUpdateEntity(User $user, Model $model, ?string $entityType = null)
    {
        return $this->canManageEntity($user, $model, 'update', $entityType);
    }

    /**
     * Check if user can view an entity
     */
    public function canViewEntity(User $user, Model $model, ?string $entityType = null)
    {
        return $this->canManageEntity($user, $model, 'view', $entityType);
    }

    /**
     * Check if user can create entities in an estate
     */
    public function canCreateInEstate(User $user, $estateId)
    {
        if ($user->can('create_all')) {
            return true;
        }

        if ($user->can('create_assigned')) {
            // Assuming UserRepository might have a method like this, or we adapt
            // For now, this part might still need direct Eloquent if specific relationship logic is complex
            // and not easily abstractable into a repository method without significant changes.
            // A simple getAssignedEstateIds could be added to UserRepository if it doesn't exist.
            $assignedEstateIds = $this->userRepository->getAssignedEstateIds($user->id);

            return in_array($estateId, $assignedEstateIds);
        }

        return false;
    }

    /**
     * Check if user can access estate data
     */
    public function canAccessEstate(User $user, $estateId)
    {
        if ($user->can('access_all_estates')) {
            return true;
        }

        $assignedEstateIds = $this->userRepository->getAssignedEstateIds($user->id);

        return in_array($estateId, $assignedEstateIds);
    }

    /**
     * Check if user can perform bulk actions
     */
    public function canPerformBulkAction(User $user, array $entityIds, string $permission, string $entityType)
    {
        if ($user->can("{$permission}_all")) {
            return true;
        }

        if ($user->can("{$permission}_assigned")) {
            // Check if all entities are assigned to the user
            foreach ($entityIds as $entityId) {
                $entity = $this->getEntityById($entityId, $entityType);
                if (! $entity || ! $this->isEntityAssignedToUser($user, $entity, $entityType)) {
                    return false;
                }
            }

            return true;
        }

        return false;
    }

    /**
     * Get user's accessible estate IDs
     */
    public function getAccessibleEstateIds(User $user)
    {
        if ($user->can('access_all_estates')) {
            return null; // null means all estates
        }

        return $this->userRepository->getAssignedEstateIds($user->id);
    }

    /**
     * Filter query by user's accessible estates
     */
    public function filterByAccessibleEstates($query, User $user, $relationship = 'estate')
    {
        $accessibleEstateIds = $this->getAccessibleEstateIds($user);

        if ($accessibleEstateIds === null) {
            return $query; // User can access all estates
        }

        return $query->whereHas($relationship, function ($subQuery) use ($accessibleEstateIds): void {
            $subQuery->whereIn('id', $accessibleEstateIds);
        });
    }

    /**
     * Check if entity is assigned to user
     */
    private function isEntityAssignedToUser(User $user, Model $model, ?string $entityType = null)
    {
        if ($entityType === null) {
            $entityType = strtolower(class_basename($model));
        }

        switch ($entityType) {
            case 'estate':
                return in_array($model->id, $this->userRepository->getAssignedEstateIds($user->id));

            case 'house':
                return in_array($model->estate_id, $this->userRepository->getAssignedEstateIds($user->id));

            case 'contact':
            case 'meter_reading':
            case 'invoice':
                // Ensure model has house relationship and house has estate
                if (isset($model->house->estate_id)) {
                    return in_array($model->house->estate_id, $this->userRepository->getAssignedEstateIds($user->id));
                }

                return false;

            default:
                // For custom entities, check if they have an estate_id or estate relationship
                if (isset($model->estate_id)) {
                    return in_array($model->estate_id, $this->userRepository->getAssignedEstateIds($user->id));
                }

                if (method_exists($model, 'estate') && $model->estate) {
                    return in_array($model->estate->id, $this->userRepository->getAssignedEstateIds($user->id));
                }

                return false;
        }
    }

    /**
     * Get entity by ID and type
     */
    private function getEntityById($entityId, string $entityType)
    {
        $modelClass = $this->getModelClass($entityType);

        if (! $modelClass) {
            return null;
        }

        // This part still uses Eloquent directly.
        // Ideally, each model would have its own repository, and we'd call $modelRepository->find($entityId).
        // For now, we'll leave it as is, as creating repositories for all models on the fly is out of scope.
        // A more advanced solution might involve a generic find method in a BaseRepository if all models shared it.
        return $modelClass::find($entityId);
    }

    /**
     * Get model class from entity type
     */
    private function getModelClass(string $entityType)
    {
        $modelMap = [
            'estate' => \App\Models\Estate::class,
            'house' => \App\Models\House::class,
            'contact' => \App\Models\Contact::class,
            'meter_reading' => \App\Models\MeterReading::class,
            'invoice' => \App\Models\Invoice::class,
            'water_rate' => \App\Models\WaterRate::class,
            'user' => \App\Models\User::class,
        ];

        return $modelMap[$entityType] ?? null;
    }

    /**
     * Check if user has any of the given permissions
     */
    public function hasAnyPermission(User $user, array $permissions)
    {
        foreach ($permissions as $permission) {
            if ($user->can($permission)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if user has all of the given permissions
     */
    public function hasAllPermissions(User $user, array $permissions)
    {
        foreach ($permissions as $permission) {
            if (! $user->can($permission)) {
                return false;
            }
        }

        return true;
    }
}
