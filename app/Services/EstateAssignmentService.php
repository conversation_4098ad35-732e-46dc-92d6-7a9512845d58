<?php

namespace App\Services;

use App\Models\Estate;
use App\Models\User;
use App\Repositories\EstateRepository;
use App\Repositories\PermissionAuditLogRepository;
use App\Repositories\UserRepository;
use Illuminate\Support\Facades\DB;

class EstateAssignmentService
{
    public function __construct(
        private readonly EstateRepository $estateRepository,
        private readonly UserRepository $userRepository,
        private readonly PermissionAuditLogRepository $permissionAuditLogRepository,
        private readonly PermissionService $permissionService
    ) {}

    /**
     * Assign user to estates
     */
    public function assignUserToEstates(User $user, array $estateIds, User $assignedBy): void
    {
        DB::transaction(function () use ($user, $estateIds, $assignedBy): void {
            // Get current assignments for comparison
            $currentAssignments = $this->userRepository->getAssignedEstateIds($user->id);

            // Remove existing assignments
            $user->assignedEstates()->detach();

            // Add new assignments
            $pivotData = [];
            foreach ($estateIds as $estateId) {
                $pivotData[$estateId] = [
                    'assigned_by' => $assignedBy->id,
                    'assigned_at' => now(),
                ];
            }

            if ($pivotData !== []) {
                $user->assignedEstates()->attach($pivotData);
            }

            // Log the assignment changes
            $this->logAssignmentChanges($user, $currentAssignments, $estateIds, $assignedBy);
        });
    }

    /**
     * Get accessible estates for user
     */
    public function getAccessibleEstates(User $user)
    {
        if ($this->permissionService->hasPermission($user, 'estate.view_all')) {
            return $this->estateRepository->getAllEstates()->pluck('name', 'id');
        }

        // Assuming UserRepository can fetch assigned estates with relationships
        // If not, this part might still need direct Eloquent or a new method in UserRepository
        $assignedEstates = $this->userRepository->getAssignedEstates($user->id);

        return $assignedEstates->pluck('name', 'id'); // This might need adjustment if houses are needed
    }

    /**
     * Validate estate access for user
     */
    public function validateEstateAccess(User $user, Estate $estate): bool
    {
        if ($this->permissionService->hasPermission($user, 'estate.view_all')) {
            return true;
        }

        return in_array($estate->id, $this->userRepository->getAssignedEstateIds($user->id));
    }

    /**
     * Bulk assign users to estates
     */
    public function bulkAssignUsersToEstates(array $userIds, array $estateIds, User $assignedBy): void
    {
        DB::transaction(function () use ($userIds, $estateIds, $assignedBy): void {
            foreach ($userIds as $userId) {
                $user = $this->userRepository->find($userId);
                if ($user instanceof \App\Models\User) {
                    $this->assignUserToEstates($user, $estateIds, $assignedBy);
                }
            }
        });
    }

    /**
     * Remove user from estate
     */
    public function removeUserFromEstate(User $user, Estate $estate, User $removedBy): void
    {
        DB::transaction(function () use ($user, $estate, $removedBy): void {
            $user->assignedEstates()->detach($estate->id);

            // Log the removal
            $this->permissionAuditLogRepository->create([
                'user_id' => $removedBy->id,
                'action' => 'estate_assignment_removed',
                'details' => json_encode([
                    'user_id' => $user->id,
                    'user_name' => $user->name,
                    'estate_id' => $estate->id,
                    'estate_name' => $estate->name,
                ]),
                'target_type' => 'estate_assignment',
                'target_id' => "{$user->id}_{$estate->id}",
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
            ]);
        });
    }

    /**
     * Get users assigned to estate
     */
    public function getUsersAssignedToEstate(Estate $estate)
    {
        // This is tricky with UserRepository as it's user-centric.
        // For now, we might need to keep this direct Eloquent or find a way
        // to query users based on estate assignments through the repository.
        // A potential UserRepository method could be `getUsersByEstateId($estateId)`.
        return User::whereHas('assignedEstates', function ($query) use ($estate): void {
            $query->where('estates.id', $estate->id);
        })->get();
    }

    /**
     * Check if user is assigned to any estate
     */
    public function isUserAssignedToAnyEstate(User $user): bool
    {
        return $this->userRepository->getAssignedEstateIds($user->id) !== [];
    }

    /**
     * Get assignment history for user
     */
    public function getUserAssignmentHistory(User $user)
    {
        return $this->permissionAuditLogRepository->where([
            ['target_type', '=', 'estate_assignment'],
            ['target_id', 'like', "{$user->id}_%"],
        ])->sortByDesc('created_at');
    }

    /**
     * Log assignment changes
     */
    private function logAssignmentChanges(User $user, array $oldAssignments, array $newAssignments, User $assignedBy): void
    {
        $addedEstates = array_diff($newAssignments, $oldAssignments);
        $removedEstates = array_diff($oldAssignments, $newAssignments);

        if ($addedEstates !== [] || $removedEstates !== []) {
            $this->permissionAuditLogRepository->create([
                'user_id' => $assignedBy->id,
                'action' => 'estate_assignment_updated',
                'details' => json_encode([
                    'user_id' => $user->id,
                    'user_name' => $user->name,
                    'added_estates' => $addedEstates,
                    'removed_estates' => $removedEstates,
                    'total_estates' => count($newAssignments),
                ]),
                'target_type' => 'estate_assignment',
                'target_id' => (string) $user->id,
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
            ]);
        }
    }
}
