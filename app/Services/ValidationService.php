<?php

namespace App\Services;

use App\Models\Contact;
use App\Models\House;
use App\Repositories\ContactRepository;
use App\Repositories\EstateRepository;
use App\Repositories\HouseRepository;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class ValidationService
{
    public function __construct(
        private readonly EstateRepository $estateRepository,
        private readonly HouseRepository $houseRepository,
        private readonly ContactRepository $contactRepository
    ) {}

    public function validateEstate(array $data, ?int $excludeId = null): array
    {
        // Check if user has permission to create/edit estates
        $user = auth()->user();
        if (! $user->hasPermissionTo('estates.create') && ! $user->hasPermissionTo('estates.edit')) {
            throw new \Illuminate\Auth\Access\AuthorizationException('You do not have permission to perform this action.');
        }

        $rules = [
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:50|unique:estates,code'.($excludeId ? ",{$excludeId}" : ''),
            'location' => 'nullable|string|max:255',
            'manager_name' => 'nullable|string|max:255',
            'manager_phone' => 'nullable|string|max:20',
            'manager_email' => 'nullable|email|max:255',
            'status' => 'required|in:active,inactive',
            'description' => 'nullable|string|max:1000',
            'total_houses' => 'nullable|integer|min:0',
            'occupied_houses' => 'nullable|integer|min:0',
            'vacant_houses' => 'nullable|integer|min:0',
        ];

        $validator = Validator::make($data, $rules);

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }

        return $validator->validated();
    }

    public function validateHouse(array $data, ?int $excludeId = null): array
    {
        // Check if user has permission to create/edit houses
        $user = auth()->user();
        if (! $user->hasPermissionTo('houses.create') && ! $user->hasPermissionTo('houses.edit')) {
            throw new \Illuminate\Auth\Access\AuthorizationException('You do not have permission to perform this action.');
        }

        $rules = [
            'estate_id' => 'required|exists:estates,id',
            'house_number' => [
                'required',
                'string',
                'max:50',
                new \App\Rules\UniqueHouseNumber($data['estate_id'] ?? null, $excludeId),
            ],
            'meter_number' => [
                'nullable',
                'string',
                'max:50',
                new \App\Rules\UniqueMeterNumber($excludeId),
            ],
            'house_type' => 'required|in:apartment,bungalow,maisonette,townhouse,studio',
            'bedrooms' => 'nullable|integer|min:0|max:20',
            'bathrooms' => 'nullable|integer|min:0|max:10',
            'square_footage' => 'nullable|numeric|min:0',
            'monthly_rent' => 'nullable|numeric|min:0',
            'security_deposit' => 'nullable|numeric|min:0',
            'occupancy_status' => 'required|in:vacant,occupied,maintenance',
            'occupancy_date' => 'nullable|date',
            'vacancy_date' => 'nullable|date|after_or_equal:occupancy_date',
            'description' => 'nullable|string|max:1000',
            'notes' => 'nullable|string|max:2000',
        ];

        $validator = Validator::make($data, $rules);

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }

        return $validator->validated();
    }

    public function validateContact(array $data, ?int $excludeId = null): array
    {
        // Check if user has permission to create/edit contacts
        $user = auth()->user();
        if (! $user->hasPermissionTo('contacts.create') && ! $user->hasPermissionTo('contacts.edit')) {
            throw new \Illuminate\Auth\Access\AuthorizationException('You do not have permission to perform this action.');
        }

        $rules = [
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'nullable|email|max:255|unique:contacts,email'.($excludeId ? ",{$excludeId}" : ''),
            'phone' => 'nullable|string|max:20',
            'id_number' => 'nullable|string|max:50|unique:contacts,id_number'.($excludeId ? ",{$excludeId}" : ''),
            'contact_type' => 'required|in:tenant,owner,agent,emergency',
            'company_name' => 'nullable|string|max:255',
            'address' => 'nullable|string|max:500',
            'city' => 'nullable|string|max:100',
            'state' => 'nullable|string|max:100',
            'postal_code' => 'nullable|string|max:20',
            'country' => 'nullable|string|max:100',
            'date_of_birth' => 'nullable|date',
            'emergency_contact_name' => 'nullable|string|max:255',
            'emergency_contact_phone' => 'nullable|string|max:20',
            'notes' => 'nullable|string|max:2000',
            'is_active' => 'boolean',
        ];

        $validator = Validator::make($data, $rules);

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }

        return $validator->validated();
    }

    public function validateHouseContactAssignment(array $data): array
    {
        // Check if user has permission to manage house contact assignments
        $user = auth()->user();
        if (! $user->hasPermissionTo('house_contacts.create') && ! $user->hasPermissionTo('house_contacts.edit')) {
            throw new \Illuminate\Auth\Access\AuthorizationException('You do not have permission to perform this action.');
        }

        $rules = [
            'house_id' => 'required|exists:houses,id',
            'contact_id' => 'required|exists:contacts,id',
            'is_primary' => 'boolean',
            'relationship' => 'required|string|max:50',
            'start_date' => 'required|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ];

        $validator = Validator::make($data, $rules);

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }

        return $validator->validated();
    }

    public function validateContactHouseAssignment(array $data): array
    {
        // Check if user has permission to manage contact house assignments
        $user = auth()->user();
        if (! $user->hasPermissionTo('house_contacts.create') && ! $user->hasPermissionTo('house_contacts.edit')) {
            throw new \Illuminate\Auth\Access\AuthorizationException('You do not have permission to perform this action.');
        }

        $rules = [
            'contact_id' => 'required|exists:contacts,id',
            'house_ids' => 'required|array',
            'house_ids.*' => 'exists:houses,id',
            'is_primary' => 'boolean',
            'relationship' => 'required|string|max:50',
            'start_date' => 'required|date',
        ];

        $validator = Validator::make($data, $rules);

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }

        return $validator->validated();
    }

    public function validateHouseNumberUniqueness(string $houseNumber, int $estateId, ?int $excludeId = null): bool
    {
        // Use the repository to check for uniqueness
        return $this->houseRepository->isHouseNumberUnique($houseNumber, $estateId, $excludeId);
    }

    public function validateMeterNumberUniqueness(?string $meterNumber, ?int $excludeId = null): bool
    {
        if ($meterNumber === null || $meterNumber === '' || $meterNumber === '0') {
            return true;
        }

        // Use the repository to check for uniqueness
        return $this->houseRepository->isMeterNumberUnique($meterNumber, $excludeId);
    }

    public function validateContactEmailUniqueness(?string $email, ?int $excludeId = null): bool
    {
        if ($email === null || $email === '' || $email === '0') {
            return true;
        }

        // Use the repository to check for uniqueness
        return $this->contactRepository->isEmailUnique($email, $excludeId);
    }

    public function validateContactIdNumberUniqueness(?string $idNumber, ?int $excludeId = null): bool
    {
        if ($idNumber === null || $idNumber === '' || $idNumber === '0') {
            return true;
        }

        // Use the repository to check for uniqueness
        return $this->contactRepository->isIdNumberUnique($idNumber, $excludeId);
    }

    public function validateHouseContactUniqueness(int $houseId, int $contactId, ?int $excludeId = null): bool
    {
        // Use the repository to check for uniqueness
        return $this->houseRepository->isHouseContactUnique($houseId, $contactId, $excludeId);
    }

    public function validatePrimaryContactLimit(int $houseId, ?int $excludeId = null): bool
    {
        // Use the repository to check primary contact limit
        return $this->houseRepository->getPrimaryContactCount($houseId, $excludeId) < 1;
    }

    public function validateDateRange(array $data): bool
    {
        if (empty($data['start_date']) || empty($data['end_date'])) {
            return true;
        }

        return strtotime((string) $data['start_date']) <= strtotime((string) $data['end_date']);
    }

    public function validateHouseOccupancyStatus(int $houseId, string $newStatus): bool
    {
        // Use the repository to check if house exists and has primary contact
        return $this->houseRepository->canSetOccupancyStatus($houseId, $newStatus);
    }

    public function validateContactAssignmentDates(int $contactId, array $assignments): bool
    {
        foreach ($assignments as $assignment) {
            if (! $this->validateDateRange($assignment)) {
                return false;
            }

            // Use the repository to check for overlapping assignments
            if ($this->houseRepository->hasOverlappingContactAssignment(
                $contactId,
                $assignment['house_id'],
                $assignment['start_date'],
                $assignment['end_date'] ?? null,
                $assignment['id'] ?? null
            )) {
                return false;
            }
        }

        return true;
    }

    public function validateEstateExists(int $estateId): bool
    {
        return $this->estateRepository->find($estateId) instanceof \Illuminate\Database\Eloquent\Model;
    }

    public function validateHouseExists(int $houseId): bool
    {
        return $this->houseRepository->find($houseId) instanceof \Illuminate\Database\Eloquent\Model;
    }

    public function validateContactExists(int $contactId): bool
    {
        return $this->contactRepository->find($contactId) instanceof \Illuminate\Database\Eloquent\Model;
    }

    public function validateEstateHasHouses(int $estateId): bool
    {
        return $this->estateRepository->getHouseCount($estateId) > 0;
    }

    public function validateHouseHasContacts(int $houseId): bool
    {
        return $this->houseRepository->getContactCount($houseId) > 0;
    }

    public function validateContactHasHouses(int $contactId): bool
    {
        return $this->contactRepository->getHouseCount($contactId) > 0;
    }

    public function validateEstateStatus(string $status): bool
    {
        return in_array($status, ['active', 'inactive']);
    }

    public function validateHouseStatus(string $status): bool
    {
        return in_array($status, ['vacant', 'occupied', 'maintenance']);
    }

    public function validateContactType(string $type): bool
    {
        return in_array($type, ['tenant', 'owner', 'agent', 'emergency']);
    }

    public function validateEstateCodeUniqueness(string $code, ?int $excludeId = null): bool
    {
        return $this->estateRepository->isCodeUnique($code, $excludeId);
    }

    public function validateEstateData(array $data, ?int $excludeId = null): array
    {
        $rules = [
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:50|unique:estates,code'.($excludeId ? ",{$excludeId}" : ''),
            'location' => 'nullable|string|max:255',
            'manager_name' => 'nullable|string|max:255',
            'manager_phone' => 'nullable|string|max:20',
            'manager_email' => 'nullable|email|max:255',
            'status' => 'required|in:active,inactive',
            'description' => 'nullable|string|max:1000',
            'total_houses' => 'nullable|integer|min:0',
            'occupied_houses' => 'nullable|integer|min:0',
            'vacant_houses' => 'nullable|integer|min:0',
        ];

        $validator = Validator::make($data, $rules);

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }

        return $validator->validated();
    }

    public function validateHouseData(array $data, ?int $excludeId = null): array
    {
        $rules = [
            'estate_id' => 'required|exists:estates,id',
            'house_number' => [
                'required',
                'string',
                'max:50',
                new \App\Rules\UniqueHouseNumber($data['estate_id'] ?? null, $excludeId),
            ],
            'meter_number' => [
                'nullable',
                'string',
                'max:50',
                new \App\Rules\UniqueMeterNumber($excludeId),
            ],
            'house_type' => 'required|in:apartment,bungalow,maisonette,townhouse,studio',
            'bedrooms' => 'nullable|integer|min:0|max:20',
            'bathrooms' => 'nullable|integer|min:0|max:10',
            'square_footage' => 'nullable|numeric|min:0',
            'monthly_rent' => 'nullable|numeric|min:0',
            'security_deposit' => 'nullable|numeric|min:0',
            'occupancy_status' => 'required|in:vacant,occupied,maintenance',
            'occupancy_date' => 'nullable|date',
            'vacancy_date' => 'nullable|date|after_or_equal:occupancy_date',
            'description' => 'nullable|string|max:1000',
            'notes' => 'nullable|string|max:2000',
        ];

        $validator = Validator::make($data, $rules);

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }

        return $validator->validated();
    }

    public function validateContactData(array $data, ?int $excludeId = null): array
    {
        $rules = [
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'nullable|email|max:255|unique:contacts,email'.($excludeId ? ",{$excludeId}" : ''),
            'phone' => 'nullable|string|max:20',
            'id_number' => 'nullable|string|max:50|unique:contacts,id_number'.($excludeId ? ",{$excludeId}" : ''),
            'contact_type' => 'required|in:tenant,owner,agent,emergency',
            'company_name' => 'nullable|string|max:255',
            'address' => 'nullable|string|max:500',
            'city' => 'nullable|string|max:100',
            'state' => 'nullable|string|max:100',
            'postal_code' => 'nullable|string|max:20',
            'country' => 'nullable|string|max:100',
            'date_of_birth' => 'nullable|date',
            'emergency_contact_name' => 'nullable|string|max:255',
            'emergency_contact_phone' => 'nullable|string|max:20',
            'notes' => 'nullable|string|max:2000',
            'is_active' => 'boolean',
        ];

        $validator = Validator::make($data, $rules);

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }

        return $validator->validated();
    }

    public function getValidationMessages(): array
    {
        return [
            'estate' => [
                'name.required' => 'Estate name is required.',
                'code.required' => 'Estate code is required.',
                'code.unique' => 'Estate code already exists.',
                'status.required' => 'Estate status is required.',
                'status.in' => 'Invalid estate status.',
            ],
            'house' => [
                'estate_id.required' => 'Estate is required.',
                'estate_id.exists' => 'Selected estate does not exist.',
                'house_number.required' => 'House number is required.',
                'house_type.required' => 'House type is required.',
                'house_type.in' => 'Invalid house type.',
                'occupancy_status.required' => 'Occupancy status is required.',
                'occupancy_status.in' => 'Invalid occupancy status.',
                'vacancy_date.after_or_equal' => 'Vacancy date must be after or equal to occupancy date.',
            ],
            'contact' => [
                'first_name.required' => 'First name is required.',
                'last_name.required' => 'Last name is required.',
                'email.email' => 'Please provide a valid email address.',
                'email.unique' => 'Email address already exists.',
                'contact_type.required' => 'Contact type is required.',
                'contact_type.in' => 'Invalid contact type.',
            ],
            'house_contact' => [
                'house_id.required' => 'House is required.',
                'house_id.exists' => 'Selected house does not exist.',
                'contact_id.required' => 'Contact is required.',
                'contact_id.exists' => 'Selected contact does not exist.',
                'relationship.required' => 'Relationship is required.',
                'start_date.required' => 'Start date is required.',
                'start_date.date' => 'Invalid start date format.',
                'end_date.date' => 'Invalid end date format.',
                'end_date.after_or_equal' => 'End date must be after or equal to start date.',
            ],
        ];
    }
}
