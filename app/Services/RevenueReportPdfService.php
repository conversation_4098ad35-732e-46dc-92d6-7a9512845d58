<?php

declare(strict_types=1);

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use PDF;

class RevenueReportPdfService
{
    public function generateRevenueReportPdf(array $revenueData): string
    {
        try {
            // Generate PDF
            $pdf = PDF::loadView('pdf.revenue-report', [
                'revenueData' => $revenueData,
                'company' => $this->getCompanyDetails(),
                'generatedAt' => now(),
            ]);

            // Set paper size and orientation
            $pdf->setPaper('A4', 'landscape');

            // Generate filename
            $filename = 'revenue-report-'.now()->format('Y-m-d-His').'.pdf';
            $path = "reports/revenue/{$filename}";

            // Save to storage
            Storage::put($path, $pdf->output());

            Log::info('Revenue report PDF generated successfully', [
                'path' => $path,
                'total_revenue' => $revenueData['total_revenue'] ?? 0,
            ]);

            return $path;

        } catch (\Exception $e) {
            Log::error('Failed to generate revenue report PDF', [
                'error' => $e->getMessage(),
            ]);

            throw new \Exception('Failed to generate revenue report PDF: '.$e->getMessage(), $e->getCode(), $e);
        }
    }

    private function getCompanyDetails(): array
    {
        return [
            'name' => config('app.name', 'Water Management System'),
            'address' => config('app.company_address', '123 Main Street, City, Country'),
            'phone' => config('app.company_phone', '+*********** 789'),
            'email' => config('app.company_email', '<EMAIL>'),
            'logo' => config('app.company_logo', null),
        ];
    }
}
