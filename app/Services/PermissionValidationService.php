<?php

namespace App\Services;

use App\Models\Estate;
use App\Models\User;
use Illuminate\Support\Facades\Cache;

class PermissionValidationService
{
    /**
     * Validate if user has a specific permission with optional estate scoping
     */
    public function validateUserPermission(User $user, string $permission, ?Estate $estate = null): bool
    {
        // 1. Check direct permission first (<PERSON><PERSON> handles all permission logic)
        if (! $user->hasPermissionTo($permission)) {
            return false;
        }

        // 2. Validate estate assignment if required
        if ($this->permissionRequiresEstate($permission)) {
            return $this->validateEstateAccess($user, $estate);
        }

        return true;
    }

    /**
     * Get user's effective permissions using Spatie
     */
    public function getUserPermissions(User $user): array
    {
        $cacheKey = "user_permissions_{$user->id}";

        return Cache::remember($cacheKey, now()->addMinutes(15), fn () =>
            // <PERSON><PERSON> automatically handles role permissions and user overrides
            $user->getAllPermissions()->pluck('name')->toArray());
    }

    /**
     * Check if user can access a specific estate
     */
    public function canAccessEstate(User $user, Estate $estate): bool
    {
        if ($user->hasRole('admin')) {
            return true;
        }

        return $user->assignedEstates()->where('estates.id', $estate->id)->exists();
    }

    /**
     * Check if user can access a specific house
     */
    public function canAccessHouse(User $user, $house): bool
    {
        if ($user->hasRole('admin')) {
            return true;
        }

        if ($user->hasRole('resident')) {
            return $user->contacts()->where('house_id', $house->id)->exists();
        }

        return $user->assignedEstates()->where('estates.id', $house->estate_id)->exists();
    }

    /**
     * Get user's accessible estates
     */
    public function getAccessibleEstates(User $user)
    {
        if ($user->hasRole('admin')) {
            return \App\Models\Estate::pluck('name', 'id');
        }

        return $user->assignedEstates()->with('houses')->pluck('name', 'id');
    }

    /**
     * Invalidate user's permission cache
     */
    public function invalidateUserCache(User $user): void
    {
        Cache::forget("user_permissions_{$user->id}");
        // Spatie has its own cache clearing mechanism
        $user->forgetCachedPermissions();
    }

    /**
     * Check if permission requires estate assignment
     * With simplified permissions, we use a different approach
     */
    private function permissionRequiresEstate(string $permission): bool
    {
        // For simplified permissions, non-admin users generally need estate assignment
        // for most operations except system-wide and personal ones
        $systemWidePermissions = [
            'dashboard.access',
            'system.settings',
            'system.audit',
            'system.export',
            'users.assign_roles',
            'users.manage',
            'rates.create',
            'rates.delete',
        ];

        $personalPermissions = [
            'resident.portal',
            'resident.inquiries',
            'resident.messages',
            'resident.payments',
        ];

        return ! in_array($permission, $systemWidePermissions) && ! in_array($permission, $personalPermissions);
    }

    /**
     * Validate estate access for user
     */
    private function validateEstateAccess(User $user, ?Estate $estate): bool
    {
        if (! $estate instanceof \App\Models\Estate) {
            return false;
        }

        // Admin users have access to all estates
        if ($user->hasRole('admin')) {
            return true;
        }

        return $user->assignedEstates()->where('estates.id', $estate->id)->exists();
    }

    /**
     * Check if user can manage another user
     */
    public function canManageUser(User $manager, User $subordinate): bool
    {
        // Admin can manage everyone
        if ($manager->hasRole('admin')) {
            return true;
        }

        // Manager can manage reviewers and caretakers
        if ($manager->hasRole('manager')) {
            return $subordinate->hasRole('reviewer') ||
                   $subordinate->hasRole('caretaker');
        }

        // Reviewer can manage caretakers
        if ($manager->hasRole('reviewer')) {
            return $subordinate->hasRole('caretaker');
        }

        return false;
    }

    /**
     * Check if user has any of the given permissions
     */
    public function hasAnyPermission(User $user, array $permissions): bool
    {
        return $user->hasAnyPermission($permissions);
    }

    /**
     * Check if user has all of the given permissions
     */
    public function hasAllPermissions(User $user, array $permissions): bool
    {
        return $user->hasAllPermissions($permissions);
    }

    /**
     * Get user's roles
     */
    public function getUserRoles(User $user): array
    {
        return $user->getRoleNames()->toArray();
    }

    /**
     * Check if user has a specific role
     */
    public function hasRole(User $user, string $role): bool
    {
        return $user->hasRole($role);
    }

    /**
     * Check if user has any of the given roles
     */
    public function hasAnyRole(User $user, array $roles): bool
    {
        return $user->hasAnyRole($roles);
    }

    /**
     * Check if user has all of the given roles
     */
    public function hasAllRoles(User $user, array $roles): bool
    {
        return $user->hasAllRoles($roles);
    }
}
