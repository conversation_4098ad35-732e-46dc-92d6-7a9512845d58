<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\AccountTransaction;
use App\Models\HouseAccount;
use App\Models\Invoice;
use App\Models\User;
use App\Repositories\AccountTransactionRepository;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class AccountBalanceService
{
    public function __construct(
        private readonly AccountTransactionRepository $accountTransactionRepository
    ) {}

    /**
     * Create a new house account for a house
     */
    public function createHouseAccount(int $houseId, float $initialBalance = 0): HouseAccount
    {
        return HouseAccount::create([
            'house_id' => $houseId,
            'current_balance' => $initialBalance,
            'total_credit' => $initialBalance > 0 ? $initialBalance : 0,
            'total_debit' => $initialBalance < 0 ? abs($initialBalance) : 0,
            'last_transaction_date' => null,
        ]);
    }

    /**
     * Get or create house account for a house
     */
    public function getOrCreateHouseAccount(int $houseId): HouseAccount
    {
        $account = HouseAccount::where('house_id', $houseId)->first();

        if (! $account) {
            $account = $this->createHouseAccount($houseId);
        }

        return $account;
    }

    /**
     * Process an invoice transaction (debit)
     */
    public function processInvoiceTransaction(int $houseId, Invoice $invoice, float $amount, ?User $user = null): AccountTransaction
    {
        return DB::transaction(function () use ($houseId, $invoice, $amount, $user) {
            $houseAccount = $this->getOrCreateHouseAccount($houseId);

            $balanceBefore = $houseAccount->current_balance;
            $balanceAfter = $balanceBefore - $amount;

            $accountTransaction = $this->accountTransactionRepository->create([
                'house_account_id' => $houseAccount->id,
                'transaction_type' => 'invoice',
                'reference_type' => 'Invoice',
                'reference_id' => $invoice->id,
                'amount' => $amount,
                'balance_before' => $balanceBefore,
                'balance_after' => $balanceAfter,
                'description' => "Invoice #{$invoice->invoice_number}",
                'user_id' => $user?->id,
                'transaction_reference' => 'TXN-'.uniqid().'-'.time(),
                'currency' => 'KES',
                'exchange_rate' => 1.0,
            ]);

            $houseAccount->updateBalance(-$amount);

            Log::info('Invoice transaction processed', [
                'transaction_id' => $accountTransaction->id,
                'house_account_id' => $houseAccount->id,
                'invoice_id' => $invoice->id,
                'amount' => $amount,
                'balance_before' => $balanceBefore,
                'balance_after' => $balanceAfter,
            ]);

            return $accountTransaction;
        });
    }

    /**
     * Process a payment transaction (credit)
     */
    public function processPaymentTransaction(int $houseId, float $amount, string $description, ?User $user = null, ?string $referenceType = null, ?int $referenceId = null): AccountTransaction
    {
        return DB::transaction(function () use ($houseId, $amount, $description, $user, $referenceType, $referenceId) {
            $houseAccount = $this->getOrCreateHouseAccount($houseId);

            $balanceBefore = $houseAccount->current_balance;
            $balanceAfter = $balanceBefore + $amount;

            $accountTransaction = $this->accountTransactionRepository->create([
                'house_account_id' => $houseAccount->id,
                'transaction_type' => 'payment',
                'reference_type' => $referenceType ?? 'Payment',
                'reference_id' => $referenceId ?? 0, // Provide default value
                'amount' => $amount,
                'balance_before' => $balanceBefore,
                'balance_after' => $balanceAfter,
                'description' => $description,
                'user_id' => $user?->id,
                'transaction_reference' => 'TXN-'.uniqid().'-'.time(),
                'currency' => 'KES',
                'exchange_rate' => 1.0,
            ]);

            $houseAccount->updateBalance($amount);

            Log::info('Payment transaction processed', [
                'transaction_id' => $accountTransaction->id,
                'house_account_id' => $houseAccount->id,
                'amount' => $amount,
                'balance_before' => $balanceBefore,
                'balance_after' => $balanceAfter,
            ]);

            return $accountTransaction;
        });
    }

    /**
     * Process an adjustment transaction
     */
    public function processAdjustmentTransaction(int $houseId, float $amount, string $description, ?User $user = null): AccountTransaction
    {
        return DB::transaction(function () use ($houseId, $amount, $description, $user) {
            $houseAccount = $this->getOrCreateHouseAccount($houseId);

            $balanceBefore = $houseAccount->current_balance;
            $balanceAfter = $balanceBefore + $amount; // Positive amount reduces debt, negative increases debt

            $accountTransaction = $this->accountTransactionRepository->create([
                'house_account_id' => $houseAccount->id,
                'transaction_type' => 'adjustment',
                'reference_type' => 'Adjustment',
                'reference_id' => null,
                'amount' => abs($amount),
                'balance_before' => $balanceBefore,
                'balance_after' => $balanceAfter,
                'description' => $description,
                'user_id' => $user?->id,
                'transaction_reference' => 'TXN-'.uniqid().'-'.time(),
                'currency' => 'KES',
                'exchange_rate' => 1.0,
            ]);

            $houseAccount->updateBalance($amount);

            Log::info('Adjustment transaction processed', [
                'transaction_id' => $accountTransaction->id,
                'house_account_id' => $houseAccount->id,
                'amount' => $amount,
                'balance_before' => $balanceBefore,
                'balance_after' => $balanceAfter,
            ]);

            return $accountTransaction;
        });
    }

    /**
     * Process a credit note transaction
     */
    public function processCreditNoteTransaction(int $houseId, float $amount, string $description, ?User $user = null, ?int $referenceId = null): AccountTransaction
    {
        return DB::transaction(function () use ($houseId, $amount, $description, $user, $referenceId) {
            $houseAccount = $this->getOrCreateHouseAccount($houseId);

            $balanceBefore = $houseAccount->current_balance;
            $balanceAfter = $balanceBefore + $amount;

            $accountTransaction = $this->accountTransactionRepository->create([
                'house_account_id' => $houseAccount->id,
                'transaction_type' => 'credit_note',
                'reference_type' => 'CreditNote',
                'reference_id' => $referenceId,
                'amount' => $amount,
                'balance_before' => $balanceBefore,
                'balance_after' => $balanceAfter,
                'description' => $description,
                'user_id' => $user?->id,
                'transaction_reference' => 'TXN-'.uniqid().'-'.time(),
                'currency' => 'KES',
                'exchange_rate' => 1.0,
            ]);

            $houseAccount->updateBalance($amount);

            Log::info('Credit note transaction processed', [
                'transaction_id' => $accountTransaction->id,
                'house_account_id' => $houseAccount->id,
                'amount' => $amount,
                'balance_before' => $balanceBefore,
                'balance_after' => $balanceAfter,
            ]);

            return $accountTransaction;
        });
    }

    /**
     * Get current balance for a house
     */
    public function getCurrentBalance(int $houseId): float
    {
        $houseAccount = $this->getOrCreateHouseAccount($houseId);

        return $houseAccount->current_balance;
    }

    /**
     * Get account statement for a house
     */
    public function getAccountStatement(int $houseId, ?\DateTime $startDate = null, ?\DateTime $endDate = null)
    {
        $houseAccount = $this->getOrCreateHouseAccount($houseId);

        $conditions = [
            ['house_account_id', '=', $houseAccount->id],
        ];

        if ($startDate instanceof \DateTime) {
            $conditions[] = ['created_at', '>=', $startDate->format('Y-m-d H:i:s')];
        }
        if ($endDate instanceof \DateTime) {
            $conditions[] = ['created_at', '<=', $endDate->format('Y-m-d H:i:s')];
        }

        $transactions = $this->accountTransactionRepository->where($conditions);

        return [
            'account' => $houseAccount,
            'transactions' => $transactions->sortByDesc('created_at'),
            'opening_balance' => $this->calculateOpeningBalance($startDate),
            'closing_balance' => $houseAccount->current_balance,
        ];
    }

    /**
     * Calculate opening balance for a given date
     */
    private function calculateOpeningBalance(?\DateTime $date): float
    {
        if (! $date instanceof \DateTime) {
            return 0;
        }

        $transactionsBeforeDate = $this->accountTransactionRepository->where([['created_at', '<', $date->format('Y-m-d H:i:s')]]);

        $totalCredits = $transactionsBeforeDate->filter(fn ($transaction) => in_array($transaction->transaction_type, ['payment', 'credit_note']))->sum('amount');
        $totalDebits = $transactionsBeforeDate->filter(fn ($transaction) => in_array($transaction->transaction_type, ['invoice', 'adjustment']))->sum('amount');

        return $totalCredits - $totalDebits;
    }

    /**
     * Check if house has sufficient balance for a given amount
     */
    public function hasSufficientBalance(int $houseId, float $amount): bool
    {
        $houseAccount = $this->getOrCreateHouseAccount($houseId);

        return $houseAccount->hasSufficientBalance($amount);
    }

    /**
     * Get outstanding balance (amount owed)
     */
    public function getOutstandingBalance(int $houseId): float
    {
        $houseAccount = $this->getOrCreateHouseAccount($houseId);

        return $houseAccount->getOutstandingBalance();
    }

    /**
     * Get credit balance (amount in credit)
     */
    public function getCreditBalance(int $houseId): float
    {
        $houseAccount = $this->getOrCreateHouseAccount($houseId);

        return $houseAccount->getCreditBalance();
    }
}
