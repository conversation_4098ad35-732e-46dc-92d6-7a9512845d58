<?php

namespace App\Services;

class CalculationHelper
{
    /**
     * Calculate the total amount for a set of line items.
     */
    public function calculateTotalAmount(array $lineItems): float
    {
        $total = 0;
        foreach ($lineItems as $lineItem) {
            if (isset($lineItem['amount']) && is_numeric($lineItem['amount'])) {
                $total += (float) $lineItem['amount'];
            }
        }

        return round($total, 2);
    }

    /**
     * Calculate tax amount.
     */
    public function calculateTax(float $amount, float $taxRate = 0.16): float
    {
        return round($amount * $taxRate, 2);
    }

    /**
     * Calculate subtotal (amount before tax).
     */
    public function calculateSubtotal(float $totalAmount, float $taxAmount): float
    {
        return round($totalAmount - $taxAmount, 2);
    }

    /**
     * Calculate due date based on invoice date and terms.
     *
     * @param  int  $paymentTerms  (in days)
     */
    public function calculateDueDate(string $invoiceDate, int $paymentTerms = 30): string
    {
        return date('Y-m-d', strtotime($invoiceDate.' +'.$paymentTerms.' days'));
    }

    /**
     * Calculate days overdue.
     */
    public function calculateDaysOverdue(string $dueDate): int
    {
        $due = new \DateTime($dueDate);
        $today = new \DateTime;
        $dateInterval = $today->diff($due);

        return $dateInterval->days > 0 ? $dateInterval->days : 0;
    }

    /**
     * Calculate water consumption difference.
     */
    public function calculateWaterConsumption(float $currentReading, float $previousReading): float
    {
        return max(0, $currentReading - $previousReading);
    }

    /**
     * Calculate estimated bill based on consumption and rate.
     */
    public function calculateEstimatedBill(float $consumption, float $rate, float $fixedCharge = 0): float
    {
        return round(($consumption * $rate) + $fixedCharge, 2);
    }

    /**
     * Calculate VAT amount.
     */
    public function calculateVat(float $amount, float $vatRate = 0.16): float
    {
        return round($amount * $vatRate, 2);
    }

    /**
     * Calculate discount amount.
     */
    public function calculateDiscount(float $amount, float $discountPercentage): float
    {
        return round($amount * ($discountPercentage / 100), 2);
    }

    /**
     * Calculate final amount after discount.
     */
    public function calculateAmountAfterDiscount(float $amount, float $discountAmount): float
    {
        return max(0, round($amount - $discountAmount, 2));
    }

    /**
     * Calculate percentage.
     */
    public function calculatePercentage(float $value, float $total): float
    {
        if ($total == 0) {
            return 0;
        }

        return round(($value / $total) * 100, 2);
    }

    /**
     * Calculate total revenue for a period.
     */
    public function calculateTotalRevenue(array $payments, string $startDate, string $endDate): float
    {
        $total = 0;
        $start = new \DateTime($startDate);
        $end = new \DateTime($endDate);

        foreach ($payments as $payment) {
            if (isset($payment['created_at']) && isset($payment['amount'])) {
                $paymentDate = new \DateTime($payment['created_at']);
                if ($paymentDate >= $start && $paymentDate <= $end) {
                    $total += (float) $payment['amount'];
                }
            }
        }

        return round($total, 2);
    }

    /**
     * Calculate aging report data.
     */
    public function calculateAgingReport(array $invoices): array
    {
        $aging = [
            'current' => 0,
            '1_30_days' => 0,
            '31_60_days' => 0,
            '61_90_days' => 0,
            'over_90_days' => 0,
        ];

        $today = new \DateTime;

        foreach ($invoices as $invoice) {
            if (! isset($invoice['amount']) || ! isset($invoice['due_date'])) {
                continue;
            }

            $amount = (float) $invoice['amount'];
            $dueDate = new \DateTime($invoice['due_date']);
            $daysOverdue = $today->diff($dueDate)->days;

            if ($daysOverdue <= 0) {
                $aging['current'] += $amount;
            } elseif ($daysOverdue <= 30) {
                $aging['1_30_days'] += $amount;
            } elseif ($daysOverdue <= 60) {
                $aging['31_60_days'] += $amount;
            } elseif ($daysOverdue <= 90) {
                $aging['61_90_days'] += $amount;
            } else {
                $aging['over_90_days'] += $amount;
            }
        }

        return $aging;
    }

    /**
     * Calculate prorated amount for a partial period.
     */
    public function calculateProratedAmount(
        float $fullAmount,
        string $startDate,
        string $endDate,
        string $periodStartDate,
        string $periodEndDate
    ): float {
        $start = new \DateTime($startDate);
        $end = new \DateTime($endDate);
        $periodStart = new \DateTime($periodStartDate);
        $periodEnd = new \DateTime($periodEndDate);

        // Ensure dates are within the billing period
        $effectiveStart = $periodStart < $start ? $start : $periodStart;
        $effectiveEnd = $periodEnd > $end ? $end : $periodEnd;

        if ($effectiveStart > $effectiveEnd) {
            return 0;
        }

        $totalDays = $end->diff($start)->days;
        $proratedDays = $effectiveEnd->diff($effectiveStart)->days;

        if ($totalDays <= 0) {
            return 0;
        }

        return round(($fullAmount / $totalDays) * $proratedDays, 2);
    }

    /**
     * Calculate average daily consumption from meter readings.
     *
     * @param  \Illuminate\Support\Collection  $readings
     */
    public function calculateAverageDailyConsumption($readings): float
    {
        if ($readings->isEmpty()) {
            return 0;
        }

        $totalConsumption = $readings->sum('consumption');
        $uniqueDays = $readings->groupBy(fn ($reading) => $reading->created_at->format('Y-m-d'))->count();

        if ($uniqueDays === 0) {
            return 0;
        }

        return round($totalConsumption / $uniqueDays, 2);
    }

    /**
     * Calculate collection rate from invoices grouped by status.
     *
     * @param  \Illuminate\Support\Collection  $invoicesByStatus
     */
    public function calculateCollectionRate($invoicesByStatus): float
    {
        $total = $invoicesByStatus->sum('amount');
        $paid = $invoicesByStatus->get('paid')?->sum('amount') ?? 0;

        return $total > 0 ? round(($paid / $total) * 100, 2) : 0;
    }

    /**
     * Calculate average value.
     */
    public function calculateAverage(float $total, int $count): float
    {
        if ($count === 0) {
            return 0;
        }

        return round($total / $count, 2);
    }
}
