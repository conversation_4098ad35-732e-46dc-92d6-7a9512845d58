<?php

namespace App\Services;

use App\Models\Contact;
use App\Models\Estate;
use App\Models\House;
use App\Repositories\EstateRepository;
use App\Repositories\HouseRepository;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

class HouseSearchService
{
    public function __construct(
        private readonly EstateRepository $estateRepository,
        private readonly HouseRepository $houseRepository
    ) {}

    public function searchEstates(array $filters = []): Builder
    {
        // For searchEstates, we'll use the repository to get a base query
        // and then apply any filters that are complex or not in the repository yet.
        // Ideally, all filters would be in the repository.
        $query = $this->estateRepository->getModel()->newQuery();

        if (! empty($filters['search'])) {
            $query->where(function (Builder $builder) use ($filters): void {
                $builder->where('name', 'like', "%{$filters['search']}%")
                    ->orWhere('code', 'like', "%{$filters['search']}%")
                    ->orWhere('location', 'like', "%{$filters['search']}%")
                    ->orWhere('manager_name', 'like', "%{$filters['search']}%");
            });
        }

        if (! empty($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (! empty($filters['manager_name'])) {
            $query->where('manager_name', 'like', "%{$filters['manager_name']}%");
        }

        if (! empty($filters['location'])) {
            $query->where('location', 'like', "%{$filters['location']}%");
        }

        if (isset($filters['has_vacant_houses'])) {
            $query->whereHas('houses', function (Builder $builder): void {
                $builder->where('occupancy_status', 'vacant');
            });
        }

        return $query;
    }

    public function searchHouses(array $filters = []): LengthAwarePaginator
    {
        // Use the repository to get filtered houses
        return $this->houseRepository->getFilteredHouses($filters);
    }

    public function searchContacts(array $filters = []): Builder
    {
        $query = Contact::query()->with(['houses.estate']);

        if (! empty($filters['search'])) {
            $query->where(function (Builder $builder) use ($filters): void {
                $builder->where('first_name', 'like', "%{$filters['search']}%")
                    ->orWhere('last_name', 'like', "%{$filters['search']}%")
                    ->orWhere('email', 'like', "%{$filters['search']}%")
                    ->orWhere('phone', 'like', "%{$filters['search']}%")
                    ->orWhere('id_number', 'like', "%{$filters['search']}%")
                    ->orWhere('company_name', 'like', "%{$filters['search']}%");
            });
        }

        if (! empty($filters['first_name'])) {
            $query->where('first_name', 'like', "%{$filters['first_name']}%");
        }

        if (! empty($filters['last_name'])) {
            $query->where('last_name', 'like', "%{$filters['last_name']}%");
        }

        if (! empty($filters['email'])) {
            $query->where('email', 'like', "%{$filters['email']}%");
        }

        if (! empty($filters['phone'])) {
            $query->where('phone', 'like', "%{$filters['phone']}%");
        }

        if (! empty($filters['contact_type'])) {
            $query->where('contact_type', $filters['contact_type']);
        }

        if (! empty($filters['house_id'])) {
            $query->whereHas('houses', function (Builder $builder) use ($filters): void {
                $builder->where('houses.id', $filters['house_id']);
            });
        }

        if (! empty($filters['estate_id'])) {
            $query->whereHas('houses.estate', function (Builder $builder) use ($filters): void {
                $builder->where('estates.id', $filters['estate_id']);
            });
        }

        if (isset($filters['is_primary'])) {
            $query->whereHas('houses', function (Builder $builder) use ($filters): void {
                $builder->where('is_primary', $filters['is_primary']);
            });
        }

        if (isset($filters['active_only'])) {
            $query->where('is_active', true);
        }

        return $query;
    }

    public function getEstateStatistics(int $estateId): array
    {
        // Use the repository to get estate with statistics
        $estate = $this->estateRepository->getEstateWithRelations($estateId);

        if (! $estate instanceof \App\Models\Estate) {
            return [
                'total_houses' => 0,
                'occupied_houses' => 0,
                'vacant_houses' => 0,
                'maintenance_houses' => 0,
                'occupancy_rate' => 0,
            ];
        }

        // Calculate statistics based on loaded houses
        $totalHouses = $estate->houses_count ?? 0;
        $occupiedHouses = $estate->houses->where('occupancy_status', 'occupied')->count();
        $vacantHouses = $estate->houses->where('occupancy_status', 'vacant')->count();
        $maintenanceHouses = $estate->houses->where('occupancy_status', 'maintenance')->count();

        return [
            'total_houses' => $totalHouses,
            'occupied_houses' => $occupiedHouses,
            'vacant_houses' => $vacantHouses,
            'maintenance_houses' => $maintenanceHouses,
            'occupancy_rate' => $totalHouses > 0
                ? round(($occupiedHouses / $totalHouses) * 100, 2)
                : 0,
        ];
    }

    public function getHouseOccupancyReport(): Collection
    {
        // Use the repository to get all active estates with their house counts
        $estatesWithHouseCounts = $this->estateRepository->getEstatesWithHouseCounts();

        return $estatesWithHouseCounts->map(function (Estate $estate) {
            $totalHouses = $estate->houses_count ?? 0;
            $occupiedHouses = $estate->houses->where('occupancy_status', 'occupied')->count();
            $vacantHouses = $estate->houses->where('occupancy_status', 'vacant')->count();

            return [
                'estate_id' => $estate->id,
                'estate_name' => $estate->name,
                'total_houses' => $totalHouses,
                'occupied_houses' => $occupiedHouses,
                'vacant_houses' => $vacantHouses,
                'occupancy_rate' => $totalHouses > 0
                    ? round(($occupiedHouses / $totalHouses) * 100, 2)
                    : 0,
            ];
        });
    }

    public function getContactHouseAssignments(int $contactId): Collection
    {
        $contact = Contact::with(['houses' => function ($query): void {
            $query->withPivot(['is_primary', 'relationship', 'start_date', 'end_date'])
                ->orderBy('pivot_is_primary', 'desc')
                ->orderBy('pivot_start_date', 'desc');
        }])->findOrFail($contactId);

        return $contact->houses->map(fn ($house) => [
            'house_id' => $house->id,
            'house_number' => $house->house_number,
            'estate_name' => $house->estate->name,
            'is_primary' => $house->pivot->is_primary,
            'relationship' => $house->pivot->relationship,
            'start_date' => $house->pivot->start_date,
            'end_date' => $house->pivot->end_date,
            'current' => $house->pivot->end_date === null,
        ]);
    }
}
