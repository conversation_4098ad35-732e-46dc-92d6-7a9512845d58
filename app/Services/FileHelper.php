<?php

namespace App\Services;

use Carbon\Carbon;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class FileHelper
{
    /**
     * Default disk for file storage.
     */
    private const DEFAULT_DISK = 'public';

    /**
     * Default path for uploads.
     */
    private const DEFAULT_UPLOAD_PATH = 'uploads';

    /**
     * Allowed file types.
     */
    private const ALLOWED_TYPES = [
        'image' => ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'],
        'document' => ['pdf', 'doc', 'docx', 'txt', 'rtf', 'odt'],
        'spreadsheet' => ['xls', 'xlsx', 'csv', 'ods'],
        'presentation' => ['ppt', 'pptx', 'odp'],
        'video' => ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm'],
        'audio' => ['mp3', 'wav', 'ogg', 'flac', 'aac'],
        'archive' => ['zip', 'rar', '7z', 'tar', 'gz'],
        'code' => ['php', 'js', 'css', 'html', 'json', 'xml', 'sql', 'py', 'java', 'cpp'],
    ];

    /**
     * Upload a file.
     */
    public function uploadFile(
        UploadedFile $uploadedFile,
        string $path = self::DEFAULT_UPLOAD_PATH,
        string $disk = self::DEFAULT_DISK,
        array $allowedMimes = []
    ): array {
        try {
            // Validate file if allowed mimes are provided
            if ($allowedMimes !== []) {
                $extension = strtolower($uploadedFile->getClientOriginalExtension());
                if (! in_array($extension, $allowedMimes)) {
                    return [
                        'success' => false,
                        'message' => 'File type not allowed.',
                    ];
                }
            }

            // Generate unique filename
            $filename = $this->generateUniqueFilename($uploadedFile->getClientOriginalName(), $path);

            // Store file
            $filePath = $uploadedFile->storeAs($path, $filename, $disk);

            return [
                'success' => true,
                'path' => $filePath,
                'url' => Storage::url($filePath),
                'filename' => $filename,
                'original_name' => $uploadedFile->getClientOriginalName(),
                'size' => $uploadedFile->getSize(),
                'mime_type' => $uploadedFile->getMimeType(),
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'File upload failed: '.$e->getMessage(),
            ];
        }
    }

    /**
     * Upload multiple files.
     */
    public function uploadMultipleFiles(
        array $files,
        string $path = self::DEFAULT_UPLOAD_PATH,
        string $disk = self::DEFAULT_DISK,
        array $allowedMimes = []
    ): array {
        $results = [];

        foreach ($files as $file) {
            if ($file instanceof UploadedFile) {
                $results[] = $this->uploadFile($file, $path, $disk, $allowedMimes);
            }
        }

        return $results;
    }

    /**
     * Delete a file.
     */
    public function deleteFile(string $path, string $disk = self::DEFAULT_DISK): bool
    {
        try {
            if (Storage::disk($disk)->exists($path)) {
                return Storage::disk($disk)->delete($path);
            }

            return false;
        } catch (\Exception $e) {
            \Log::error('File deletion failed: '.$e->getMessage());

            return false;
        }
    }

    /**
     * Move a file.
     */
    public function moveFile(string $fromPath, string $toPath, string $disk = self::DEFAULT_DISK): bool
    {
        try {
            if (Storage::disk($disk)->exists($fromPath)) {
                Storage::disk($disk)->move($fromPath, $toPath);

                return true;
            }

            return false;
        } catch (\Exception $e) {
            \Log::error('File move failed: '.$e->getMessage());

            return false;
        }
    }

    /**
     * Copy a file.
     */
    public function copyFile(string $fromPath, string $toPath, string $disk = self::DEFAULT_DISK): bool
    {
        try {
            if (Storage::disk($disk)->exists($fromPath)) {
                Storage::disk($disk)->copy($fromPath, $toPath);

                return true;
            }

            return false;
        } catch (\Exception $e) {
            \Log::error('File copy failed: '.$e->getMessage());

            return false;
        }
    }

    /**
     * Get file size in human readable format.
     */
    public function formatFileSize(int $bytes, int $precision = 2): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        $bytes /= (1 << (10 * $pow));

        return round($bytes, $precision).' '.$units[$pow];
    }

    /**
     * Get file extension from MIME type.
     */
    public function getExtensionFromMimeType(string $mimeType): ?string
    {
        $mimeToExtension = [
            'image/jpeg' => 'jpg',
            'image/png' => 'png',
            'image/gif' => 'gif',
            'image/bmp' => 'bmp',
            'image/svg+xml' => 'svg',
            'application/pdf' => 'pdf',
            'application/msword' => 'doc',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document' => 'docx',
            'text/plain' => 'txt',
            'text/rtf' => 'rtf',
            'application/vnd.oasis.opendocument.text' => 'odt',
            'application/vnd.ms-excel' => 'xls',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' => 'xlsx',
            'text/csv' => 'csv',
            'application/vnd.oasis.opendocument.spreadsheet' => 'ods',
            'application/vnd.ms-powerpoint' => 'ppt',
            'application/vnd.openxmlformats-officedocument.presentationml.presentation' => 'pptx',
            'application/vnd.oasis.opendocument.presentation' => 'odp',
            'video/mp4' => 'mp4',
            'video/x-msvideo' => 'avi',
            'video/quicktime' => 'mov',
            'video/x-ms-wmv' => 'wmv',
            'video/x-flv' => 'flv',
            'video/webm' => 'webm',
            'audio/mpeg' => 'mp3',
            'audio/wav' => 'wav',
            'audio/ogg' => 'ogg',
            'audio/flac' => 'flac',
            'audio/aac' => 'aac',
            'application/zip' => 'zip',
            'application/x-rar-compressed' => 'rar',
            'application/x-7z-compressed' => '7z',
            'application/x-tar' => 'tar',
            'application/gzip' => 'gz',
            'text/html' => 'html',
            'application/javascript' => 'js',
            'text/css' => 'css',
            'application/json' => 'json',
            'application/xml' => 'xml',
            'text/xml' => 'xml',
            'text/x-sql' => 'sql',
            'text/x-python' => 'py',
            'text/x-java-source' => 'java',
            'text/x-c++src' => 'cpp',
        ];

        return $mimeToExtension[$mimeType] ?? null;
    }

    /**
     * Get MIME type from file extension.
     */
    public function getMimeTypeFromExtension(string $extension): ?string
    {
        $extensionToMime = [
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'png' => 'image/png',
            'gif' => 'image/gif',
            'bmp' => 'image/bmp',
            'svg' => 'image/svg+xml',
            'pdf' => 'application/pdf',
            'doc' => 'application/msword',
            'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'txt' => 'text/plain',
            'rtf' => 'text/rtf',
            'odt' => 'application/vnd.oasis.opendocument.text',
            'xls' => 'application/vnd.ms-excel',
            'xlsx' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'csv' => 'text/csv',
            'ods' => 'application/vnd.oasis.opendocument.spreadsheet',
            'ppt' => 'application/vnd.ms-powerpoint',
            'pptx' => 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            'odp' => 'application/vnd.oasis.opendocument.presentation',
            'mp4' => 'video/mp4',
            'avi' => 'video/x-msvideo',
            'mov' => 'video/quicktime',
            'wmv' => 'video/x-ms-wmv',
            'flv' => 'video/x-flv',
            'webm' => 'video/webm',
            'mp3' => 'audio/mpeg',
            'wav' => 'audio/wav',
            'ogg' => 'audio/ogg',
            'flac' => 'audio/flac',
            'aac' => 'audio/aac',
            'zip' => 'application/zip',
            'rar' => 'application/x-rar-compressed',
            '7z' => 'application/x-7z-compressed',
            'tar' => 'application/x-tar',
            'gz' => 'application/gzip',
            'html' => 'text/html',
            'htm' => 'text/html',
            'js' => 'application/javascript',
            'css' => 'text/css',
            'json' => 'application/json',
            'xml' => 'application/xml',
            'sql' => 'text/x-sql',
            'py' => 'text/x-python',
            'java' => 'text/x-java-source',
            'cpp' => 'text/x-c++src',
        ];

        return $extensionToMime[strtolower($extension)] ?? null;
    }

    /**
     * Check if file type is allowed.
     */
    public function isFileTypeAllowed(string $extension, array $allowedTypes = []): bool
    {
        if ($allowedTypes === []) {
            return true; // Allow all types if none specified
        }

        $extension = strtolower($extension);

        foreach ($allowedTypes as $allowedType) {
            if (isset(self::ALLOWED_TYPES[strtolower((string) $allowedType)]) &&
                in_array($extension, self::ALLOWED_TYPES[strtolower((string) $allowedType)])) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get allowed file extensions by type.
     */
    public function getAllowedExtensionsByType(string $type): array
    {
        return self::ALLOWED_TYPES[strtolower($type)] ?? [];
    }

    /**
     * Generate unique filename.
     */
    public function generateUniqueFilename(string $originalName, string $path = ''): string
    {
        $extension = pathinfo($originalName, PATHINFO_EXTENSION);
        $filename = pathinfo($originalName, PATHINFO_FILENAME);
        $filename = Str::slug($filename, '-');
        $filename = substr($filename, 0, 50); // Limit filename length

        $uniqueName = $filename;
        $counter = 1;

        while (Storage::disk(self::DEFAULT_DISK)->exists($path.'/'.$uniqueName.'.'.$extension)) {
            $uniqueName = $filename.'-'.$counter;
            $counter++;
        }

        return $uniqueName.'.'.$extension;
    }

    /**
     * Create directory.
     */
    public function createDirectory(string $path, string $disk = self::DEFAULT_DISK): bool
    {
        try {
            if (! Storage::disk($disk)->exists($path)) {
                Storage::disk($disk)->makeDirectory($path);

                return true;
            }

            return false;
        } catch (\Exception $e) {
            \Log::error('Directory creation failed: '.$e->getMessage());

            return false;
        }
    }

    /**
     * Delete directory.
     */
    public function deleteDirectory(string $path, string $disk = self::DEFAULT_DISK): bool
    {
        try {
            if (Storage::disk($disk)->exists($path)) {
                return Storage::disk($disk)->deleteDirectory($path);
            }

            return false;
        } catch (\Exception $e) {
            \Log::error('Directory deletion failed: '.$e->getMessage());

            return false;
        }
    }

    /**
     * List files in directory.
     */
    public function listFiles(string $path, string $disk = self::DEFAULT_DISK): array
    {
        try {
            if (Storage::disk($disk)->exists($path)) {
                return collect(Storage::disk($disk)->files($path))
                    ->map(fn ($file) => [
                        'path' => $file,
                        'url' => Storage::disk($disk)->url($file),
                        'name' => basename($file),
                        'extension' => pathinfo($file, PATHINFO_EXTENSION),
                        'size' => Storage::disk($disk)->size($file),
                        'last_modified' => Storage::disk($disk)->lastModified($file),
                    ])
                    ->sortBy('name')
                    ->values()
                    ->toArray();
            }

            return [];
        } catch (\Exception $e) {
            \Log::error('File listing failed: '.$e->getMessage());

            return [];
        }
    }

    /**
     * List directories in directory.
     */
    public function listDirectories(string $path, string $disk = self::DEFAULT_DISK): array
    {
        try {
            if (Storage::disk($disk)->exists($path)) {
                return collect(Storage::disk($disk)->directories($path))
                    ->map(fn ($dir) => [
                        'path' => $dir,
                        'name' => basename($dir),
                        'last_modified' => Storage::disk($disk)->lastModified($dir),
                    ])
                    ->sortBy('name')
                    ->values()
                    ->toArray();
            }

            return [];
        } catch (\Exception $e) {
            \Log::error('Directory listing failed: '.$e->getMessage());

            return [];
        }
    }

    /**
     * Get file info.
     */
    public function getFileInfo(string $path, string $disk = self::DEFAULT_DISK): ?array
    {
        try {
            if (Storage::disk($disk)->exists($path)) {
                return [
                    'path' => $path,
                    'url' => Storage::disk($disk)->url($path),
                    'name' => basename($path),
                    'extension' => pathinfo($path, PATHINFO_EXTENSION),
                    'size' => Storage::disk($disk)->size($path),
                    'last_modified' => Storage::disk($disk)->lastModified($path),
                    'mime_type' => Storage::disk($disk)->mimeType($path),
                ];
            }

            return null;
        } catch (\Exception $e) {
            \Log::error('File info retrieval failed: '.$e->getMessage());

            return null;
        }
    }

    /**
     * Download file.
     *
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse|null
     */
    public function downloadFile(string $path, string $disk = self::DEFAULT_DISK, ?string $filename = null)
    {
        try {
            if (Storage::disk($disk)->exists($path)) {
                return response()->download(Storage::disk($disk)->path($path), $filename);
            }

            return null;
        } catch (\Exception $e) {
            \Log::error('File download failed: '.$e->getMessage());

            return null;
        }
    }

    /**
     * Clean up old files.
     */
    public function cleanupOldFiles(string $path, int $daysToKeep = 30, string $disk = self::DEFAULT_DISK): int
    {
        $cutoffDate = now()->subDays($daysToKeep);
        $deletedCount = 0;

        try {
            if (Storage::disk($disk)->exists($path)) {
                $files = Storage::disk($disk)->allFiles($path);

                foreach ($files as $file) {
                    $lastModified = Carbon::createFromTimestamp(Storage::disk($disk)->lastModified($file));

                    if ($lastModified->lessThan($cutoffDate) && Storage::disk($disk)->delete($file)) {
                        $deletedCount++;
                    }
                }
            }
        } catch (\Exception $e) {
            \Log::error('File cleanup failed: '.$e->getMessage());
        }

        return $deletedCount;
    }

    /**
     * Get storage usage.
     */
    public function getStorageUsage(string $disk = self::DEFAULT_DISK): array
    {
        try {
            $usedSpace = 0;
            $fileCount = 0;

            $files = Storage::disk($disk)->allFiles();
            foreach ($files as $file) {
                $usedSpace += Storage::disk($disk)->size($file);
                $fileCount++;
            }

            // For local disk, we can try to get total space
            $totalSpace = 0;
            $freeSpace = 0;

            if ($disk === 'local' || $disk === 'public') {
                try {
                    // Get the root path of the disk
                    $config = config("filesystems.disks.{$disk}");
                    if (isset($config['root'])) {
                        $storagePath = $config['root'];
                        if (is_dir($storagePath)) {
                            $totalSpace = disk_total_space($storagePath);
                            $freeSpace = disk_free_space($storagePath);
                        }
                    }
                } catch (\Exception $e) {
                    // If we can't get the disk space, we'll just use 0
                    \Log::warning('Could not get disk space for '.$disk.': '.$e->getMessage());
                }
            }

            return [
                'total_space' => $totalSpace,
                'used_space' => $usedSpace,
                'free_space' => $freeSpace,
                'file_count' => $fileCount,
                'used_percentage' => $totalSpace > 0 ? round(($usedSpace / $totalSpace) * 100, 2) : 0,
            ];
        } catch (\Exception $e) {
            \Log::error('Storage usage retrieval failed: '.$e->getMessage());

            return [
                'total_space' => 0,
                'used_space' => 0,
                'free_space' => 0,
                'file_count' => 0,
                'used_percentage' => 0,
            ];
        }
    }
}
