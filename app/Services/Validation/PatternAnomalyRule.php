<?php

namespace App\Services\Validation;

use App\Models\MeterReading;
use Illuminate\Support\Collection;

class PatternAnomalyRule implements ValidationRule
{
    private const MIN_HISTORY_READINGS = 6;

    public function validate(MeterReading $meterReading): ValidationResult
    {
        $validationResult = new ValidationResult;
        $house = $meterReading->house;
        $history = $this->getReadingHistory($house->id, $meterReading->reading_date);

        if ($history->count() < self::MIN_HISTORY_READINGS) {
            $validationResult->addWarning([
                'type' => 'insufficient_pattern_history',
                'severity' => 'low',
                'message' => 'Insufficient historical data for pattern analysis',
                'required_readings' => self::MIN_HISTORY_READINGS,
                'available_readings' => $history->count(),
            ]);

            return $validationResult;
        }

        // Check for repeating patterns
        $repeatingPattern = $this->detectRepeatingPattern($history, $meterReading);
        if ($repeatingPattern) {
            $validationResult->addWarning([
                'type' => 'repeating_pattern',
                'severity' => 'medium',
                'message' => 'Detected repeating consumption pattern',
                'pattern' => $repeatingPattern,
                'current_value' => $meterReading->consumption,
            ]);
        }

        // Check for cyclic patterns (seasonal)
        $seasonalAnomaly = $this->detectSeasonalAnomaly($history, $meterReading);
        if ($seasonalAnomaly) {
            $validationResult->addAnomaly([
                'type' => 'seasonal_anomaly',
                'severity' => 'medium',
                'message' => 'Consumption deviates from expected seasonal pattern',
                'expected_range' => $seasonalAnomaly['expected_range'],
                'actual_value' => $meterReading->consumption,
                'deviation' => $seasonalAnomaly['deviation'],
            ]);
        }

        // Check for trend anomalies
        $trendAnomaly = $this->detectTrendAnomaly($history, $meterReading);
        if ($trendAnomaly) {
            $validationResult->addAnomaly([
                'type' => 'trend_anomaly',
                'severity' => 'high',
                'message' => 'Consumption breaks established trend',
                'trend_direction' => $trendAnomaly['trend_direction'],
                'trend_strength' => $trendAnomaly['trend_strength'],
                'deviation' => $trendAnomaly['deviation'],
            ]);
        }

        // Check for unusual consistency
        $consistencyAnomaly = $this->detectConsistencyAnomaly($history, $meterReading);
        if ($consistencyAnomaly) {
            $validationResult->addWarning([
                'type' => 'unusual_consistency',
                'severity' => 'low',
                'message' => 'Reading shows unusual consistency compared to historical variation',
                'historical_variation' => $consistencyAnomaly['variation'],
                'current_value' => $meterReading->consumption,
            ]);
        }

        return $validationResult;
    }

    public function getName(): string
    {
        return 'pattern_anomaly';
    }

    public function getDescription(): string
    {
        return 'Detects pattern anomalies including repeating values, seasonal deviations, and trend breaks';
    }

    public function getSeverity(): string
    {
        return 'medium';
    }

    private function getReadingHistory(int $houseId, \Carbon\Carbon $beforeDate, int $limit = 24): Collection
    {
        return MeterReading::where('house_id', $houseId)
            ->where('status', 'approved')
            ->where('reading_date', '<', $beforeDate)
            ->orderBy('reading_date', 'desc')
            ->limit($limit)
            ->get();
    }

    private function detectRepeatingPattern(Collection $history, MeterReading $meterReading): ?array
    {
        $consumptions = $history->pluck('consumption')->toArray();

        // Check for exact repeats in last 3 readings
        $recentReadings = array_slice($consumptions, 0, 3);
        if (count(array_unique($recentReadings)) === 1 && $meterReading->consumption === $recentReadings[0]) {
            return [
                'type' => 'exact_repeat',
                'value' => $meterReading->consumption,
                'occurrences' => 4,
            ];
        }

        // Check for alternating pattern
        if (count($consumptions) >= 4) {
            $lastFour = array_slice($consumptions, 0, 4);
            if ($lastFour[0] === $lastFour[2] && $lastFour[1] === $lastFour[3] && $meterReading->consumption === $lastFour[0]) {
                return [
                    'type' => 'alternating_pattern',
                    'values' => [$lastFour[0], $lastFour[1]],
                    'next_expected' => $lastFour[1],
                ];
            }
        }

        return null;
    }

    private function detectSeasonalAnomaly(Collection $history, MeterReading $meterReading): ?array
    {
        $month = $meterReading->reading_date->month;
        $seasonalData = [];

        // Get historical data for the same month
        foreach ($history as $pastReading) {
            if ($pastReading->reading_date->month === $month) {
                $seasonalData[] = $pastReading->consumption;
            }
        }

        if (count($seasonalData) < 2) {
            return null;
        }

        $seasonalAvg = array_sum($seasonalData) / count($seasonalData);
        $seasonalStdDev = $this->calculateStandardDeviation($seasonalData);

        $expectedRange = [
            'min' => $seasonalAvg - (1.5 * $seasonalStdDev),
            'max' => $seasonalAvg + (1.5 * $seasonalStdDev),
        ];

        if ($meterReading->consumption < $expectedRange['min'] || $meterReading->consumption > $expectedRange['max']) {
            return [
                'expected_range' => $expectedRange,
                'deviation' => abs($meterReading->consumption - $seasonalAvg),
                'seasonal_average' => $seasonalAvg,
            ];
        }

        return null;
    }

    private function detectTrendAnomaly(Collection $history, MeterReading $meterReading): ?array
    {
        if ($history->count() < 6) {
            return null;
        }

        $consumptions = $history->pluck('consumption')->toArray();
        $recentConsumptions = array_slice($consumptions, 0, 6);

        // Calculate trend using linear regression
        $trend = $this->calculateLinearTrend($recentConsumptions);

        // Predict expected value based on trend
        $expectedValue = $trend['slope'] * 6 + $trend['intercept'];
        $deviation = abs($meterReading->consumption - $expectedValue);

        // Check if deviation is significant
        $avgConsumption = array_sum($recentConsumptions) / count($recentConsumptions);
        $relativeDeviation = $avgConsumption > 0 ? $deviation / $avgConsumption : 0;

        if ($relativeDeviation > 0.5 && abs($trend['slope']) > 0.1) {
            return [
                'trend_direction' => $trend['slope'] > 0 ? 'increasing' : 'decreasing',
                'trend_strength' => abs($trend['slope']),
                'deviation' => $deviation,
                'expected_value' => $expectedValue,
            ];
        }

        return null;
    }

    private function detectConsistencyAnomaly(Collection $history, MeterReading $meterReading): ?array
    {
        if ($history->count() < 4) {
            return null;
        }

        $consumptions = $history->pluck('consumption')->toArray();
        $variation = $this->calculateCoefficientOfVariation($consumptions);

        // If historical data shows high variation but current reading is very close to average
        if ($variation > 0.3) {
            $avgConsumption = array_sum($consumptions) / count($consumptions);
            $deviationFromAvg = abs($meterReading->consumption - $avgConsumption) / $avgConsumption;

            if ($deviationFromAvg < 0.05) {
                return [
                    'variation' => $variation,
                    'average' => $avgConsumption,
                ];
            }
        }

        return null;
    }

    private function calculateLinearTrend(array $values): array
    {
        $n = count($values);
        $sumX = array_sum(range(0, $n - 1));
        $sumY = array_sum($values);
        $sumXY = 0;
        $sumXX = 0;

        for ($i = 0; $i < $n; $i++) {
            $sumXY += $i * $values[$i];
            $sumXX += $i * $i;
        }

        $slope = ($n * $sumXY - $sumX * $sumY) / ($n * $sumXX - $sumX * $sumX);
        $intercept = ($sumY - $slope * $sumX) / $n;

        return ['slope' => $slope, 'intercept' => $intercept];
    }

    private function calculateCoefficientOfVariation(array $values): float
    {
        $mean = array_sum($values) / count($values);
        $stdDev = $this->calculateStandardDeviation($values);

        return $mean > 0 ? $stdDev / $mean : 0;
    }

    private function calculateStandardDeviation(array $values): float
    {
        if (count($values) < 2) {
            return 0;
        }

        $mean = array_sum($values) / count($values);
        $squaredDiffs = array_map(fn ($value) => ($value - $mean) ** 2, $values);

        return sqrt(array_sum($squaredDiffs) / count($values));
    }
}
