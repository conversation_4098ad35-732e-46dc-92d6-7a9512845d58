<?php

namespace App\Services\Validation;

use Illuminate\Support\Collection;

class ValidationResult
{
    public Collection $anomalies;

    public Collection $warnings;

    public function __construct(
        public bool $isValid = true,
        array $anomalies = [],
        array $warnings = [],
        public string $riskLevel = 'low',
        public int $confidenceScore = 100,
        public array $metadata = []
    ) {
        $this->anomalies = collect($anomalies);
        $this->warnings = collect($warnings);
    }

    public function addAnomaly(array $anomaly): self
    {
        $this->anomalies->push($anomaly);

        return $this;
    }

    public function addWarning(array $warning): self
    {
        $this->warnings->push($warning);

        return $this;
    }

    public function hasCriticalAnomalies(): bool
    {
        return $this->anomalies->contains('severity', 'critical');
    }

    public function hasHighAnomalies(): bool
    {
        return $this->anomalies->contains('severity', 'high');
    }

    public function hasMediumAnomalies(): bool
    {
        return $this->anomalies->contains('severity', 'medium');
    }

    public function getAnomaliesBySeverity(string $severity): Collection
    {
        return $this->anomalies->where('severity', $severity);
    }

    public function getWarningsBySeverity(string $severity): Collection
    {
        return $this->warnings->where('severity', $severity);
    }

    public function calculateRiskLevel(): self
    {
        if ($this->hasCriticalAnomalies()) {
            $this->riskLevel = 'critical';
        } elseif ($this->hasHighAnomalies()) {
            $this->riskLevel = 'high';
        } elseif ($this->hasMediumAnomalies()) {
            $this->riskLevel = 'medium';
        } else {
            $this->riskLevel = 'low';
        }

        return $this;
    }

    public function calculateConfidenceScore(): self
    {
        $baseScore = 100;

        // Reduce score based on anomalies
        foreach ($this->anomalies as $anomaly) {
            switch ($anomaly['severity']) {
                case 'critical':
                    $baseScore -= 30;
                    break;
                case 'high':
                    $baseScore -= 20;
                    break;
                case 'medium':
                    $baseScore -= 10;
                    break;
            }
        }

        // Reduce score based on warnings
        foreach ($this->warnings as $warning) {
            switch ($warning['severity']) {
                case 'medium':
                    $baseScore -= 5;
                    break;
                case 'low':
                    $baseScore -= 2;
                    break;
            }
        }

        $this->confidenceScore = max(0, min(100, $baseScore));

        return $this;
    }

    public function toArray(): array
    {
        return [
            'is_valid' => $this->isValid,
            'anomalies' => $this->anomalies->toArray(),
            'warnings' => $this->warnings->toArray(),
            'risk_level' => $this->riskLevel,
            'confidence_score' => $this->confidenceScore,
            'metadata' => $this->metadata,
        ];
    }

    public function shouldAutoApprove(): bool
    {
        return $this->isValid &&
               $this->riskLevel === 'low' &&
               $this->confidenceScore >= 80 &&
               $this->anomalies->isEmpty();
    }

    public function requiresManualReview(): bool
    {
        return ! $this->shouldAutoApprove();
    }
}
