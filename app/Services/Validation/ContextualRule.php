<?php

namespace App\Services\Validation;

use App\Models\House;
use App\Models\MeterReading;
use Illuminate\Support\Collection;

class ContextualRule implements ValidationRule
{
    public function validate(MeterReading $meterReading): ValidationResult
    {
        $validationResult = new ValidationResult;

        // Check reading interval
        $intervalAnomaly = $this->checkReadingInterval($meterReading);
        if ($intervalAnomaly) {
            $validationResult->addWarning($intervalAnomaly);
        }

        // Check for meter tampering
        $tamperingAnomaly = $this->checkMeterTampering($meterReading);
        if ($tamperingAnomaly) {
            $validationResult->addAnomaly($tamperingAnomaly);
        }

        // Check neighborhood comparison
        $neighborhoodAnomaly = $this->checkNeighborhoodComparison($meterReading);
        if ($neighborhoodAnomaly) {
            $validationResult->addWarning($neighborhoodAnomaly);
        }

        // Check house occupancy patterns
        $occupancyAnomaly = $this->checkOccupancyPattern($meterReading);
        if ($occupancyAnomaly) {
            $validationResult->addWarning($occupancyAnomaly);
        }

        // Check for duplicate readings
        $duplicateAnomaly = $this->checkDuplicateReadings($meterReading);
        if ($duplicateAnomaly) {
            $validationResult->addAnomaly($duplicateAnomaly);
        }

        return $validationResult;
    }

    public function getName(): string
    {
        return 'contextual_validation';
    }

    public function getDescription(): string
    {
        return 'Validates reading context including intervals, neighborhood comparisons, and occupancy patterns';
    }

    public function getSeverity(): string
    {
        return 'medium';
    }

    private function checkReadingInterval(MeterReading $meterReading): ?array
    {
        $lastReading = MeterReading::where('house_id', $meterReading->house_id)
            ->where('status', 'approved')
            ->where('reading_date', '<', $meterReading->reading_date)
            ->orderBy('reading_date', 'desc')
            ->first();

        if (! $lastReading) {
            return null;
        }

        $daysDiff = $meterReading->reading_date->diffInDays($lastReading->reading_date);

        if ($daysDiff < 20) {
            return [
                'type' => 'too_frequent_reading',
                'severity' => 'medium',
                'message' => 'Reading interval is too frequent',
                'days_since_last' => $daysDiff,
                'expected_range' => '25-35 days',
            ];
        }

        if ($daysDiff > 45) {
            return [
                'type' => 'infrequent_reading',
                'severity' => 'medium',
                'message' => 'Reading interval is too long',
                'days_since_last' => $daysDiff,
                'expected_range' => '25-35 days',
            ];
        }

        return null;
    }

    private function checkMeterTampering(MeterReading $meterReading): ?array
    {
        $lastReading = MeterReading::where('house_id', $meterReading->house_id)
            ->where('status', 'approved')
            ->where('reading_date', '<', $meterReading->reading_date)
            ->orderBy('reading_date', 'desc')
            ->first();

        if (! $lastReading) {
            return null;
        }

        // Check for meter tampering (reading less than previous)
        if ($meterReading->current_reading < $lastReading->current_reading) {
            return [
                'type' => 'meter_tampering',
                'severity' => 'critical',
                'message' => 'Current reading is less than previous reading - possible tampering',
                'current' => $meterReading->current_reading,
                'previous' => $lastReading->current_reading,
                'difference' => $lastReading->current_reading - $meterReading->current_reading,
            ];
        }

        // Check for suspiciously low consumption
        if ($meterReading->consumption < 1 && $lastReading->consumption > 5) {
            return [
                'type' => 'suspiciously_low_consumption',
                'severity' => 'high',
                'message' => 'Suspiciously low consumption compared to previous reading',
                'current_consumption' => $meterReading->consumption,
                'previous_consumption' => $lastReading->consumption,
            ];
        }

        return null;
    }

    private function checkNeighborhoodComparison(MeterReading $meterReading): ?array
    {
        $estate = $meterReading->house->estate;
        $sameMonthReadings = MeterReading::whereHas('house', function ($query) use ($estate): void {
            $query->where('estate_id', $estate->id);
        })
            ->whereMonth('reading_date', $meterReading->reading_date->month)
            ->whereYear('reading_date', $meterReading->reading_date->year)
            ->where('status', 'approved')
            ->where('house_id', '!=', $meterReading->house_id)
            ->get();

        if ($sameMonthReadings->count() < 3) {
            return null;
        }

        $neighborhoodAvg = $sameMonthReadings->avg('consumption');
        $neighborhoodMedian = $this->calculateMedian($sameMonthReadings->pluck('consumption')->toArray());

        $deviationFromAvg = abs($meterReading->consumption - $neighborhoodAvg);
        $relativeDeviation = $neighborhoodAvg > 0 ? $deviationFromAvg / $neighborhoodAvg : 0;

        if ($relativeDeviation > 1.5) {
            return [
                'type' => 'neighborhood_anomaly',
                'severity' => 'medium',
                'message' => 'Consumption significantly differs from neighborhood average',
                'current_consumption' => $meterReading->consumption,
                'neighborhood_average' => round($neighborhoodAvg, 2),
                'neighborhood_median' => round($neighborhoodMedian, 2),
                'deviation_percentage' => round($relativeDeviation * 100, 1),
            ];
        }

        return null;
    }

    private function checkOccupancyPattern(MeterReading $meterReading): ?array
    {
        $house = $meterReading->house;
        $history = $this->getReadingHistory($house->id, $meterReading->reading_date, 6);

        if ($history->count() < 3) {
            return null;
        }

        // Check for vacation patterns (zero or very low consumption)
        $recentReadings = $history->take(3);
        $hasRecentLowConsumption = $recentReadings->contains(fn ($reading) => $reading->consumption < 2);

        if ($hasRecentLowConsumption && $reading->consumption > 10) {
            return [
                'type' => 'occupancy_change',
                'severity' => 'low',
                'message' => 'Possible change in occupancy pattern detected',
                'recent_low_consumption' => true,
                'current_consumption' => $reading->consumption,
            ];
        }

        // Check for consistent low usage pattern
        $allLowConsumption = $history->every(fn ($reading) => $reading->consumption < 5);

        if ($allLowConsumption && $reading->consumption > 15) {
            return [
                'type' => 'pattern_break',
                'severity' => 'medium',
                'message' => 'Break in consistent low usage pattern',
                'historical_pattern' => 'low_consumption',
                'current_consumption' => $reading->consumption,
            ];
        }

        return null;
    }

    private function checkDuplicateReadings(MeterReading $meterReading): ?array
    {
        $duplicate = MeterReading::where('house_id', $meterReading->house_id)
            ->where('current_reading', $meterReading->current_reading)
            ->where('reading_date', '!=', $meterReading->reading_date)
            ->where('status', 'approved')
            ->first();

        if ($duplicate) {
            return [
                'type' => 'duplicate_reading',
                'severity' => 'medium',
                'message' => 'Duplicate reading value detected',
                'duplicate_date' => $duplicate->reading_date->format('Y-m-d'),
                'reading_value' => $meterReading->current_reading,
            ];
        }

        return null;
    }

    private function getReadingHistory(int $houseId, \Carbon\Carbon $beforeDate, int $limit = 12): Collection
    {
        return MeterReading::where('house_id', $houseId)
            ->where('status', 'approved')
            ->where('reading_date', '<', $beforeDate)
            ->orderBy('reading_date', 'desc')
            ->limit($limit)
            ->get();
    }

    private function calculateMedian(array $values): float
    {
        sort($values);
        $count = count($values);

        if ($count === 0) {
            return 0;
        }
        if ($count % 2 === 0) {
            return ($values[$count / 2 - 1] + $values[$count / 2]) / 2;
        }

        return $values[floor($count / 2)];
    }
}
