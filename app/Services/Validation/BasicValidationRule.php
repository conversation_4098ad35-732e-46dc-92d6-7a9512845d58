<?php

namespace App\Services\Validation;

use App\Models\MeterReading;

class BasicValidationRule implements ValidationRule
{
    private array $config;

    public function __construct(array $config = [])
    {
        $this->config = array_merge([
            'max_reasonable_consumption' => 1000,
            'min_reasonable_consumption' => 1,
            'allow_future_dates' => false,
        ], $config);
    }

    public function validate(MeterReading $meterReading): ValidationResult
    {
        $validationResult = new ValidationResult;

        // Check for negative consumption
        if ($meterReading->consumption < 0) {
            $validationResult->addAnomaly([
                'type' => 'negative_consumption',
                'severity' => 'critical',
                'message' => 'Consumption cannot be negative',
                'value' => $meterReading->consumption,
            ]);
            $validationResult->isValid = false;
        }

        // Check for extremely high consumption
        if ($meterReading->consumption > $this->config['max_reasonable_consumption']) {
            $validationResult->addAnomaly([
                'type' => 'extremely_high_consumption',
                'severity' => 'critical',
                'message' => 'Consumption exceeds reasonable maximum',
                'value' => $meterReading->consumption,
                'threshold' => $this->config['max_reasonable_consumption'],
            ]);
            $validationResult->isValid = false;
        }

        // Check for zero consumption (possible meter issue)
        if ($meterReading->consumption == 0) {
            $validationResult->addAnomaly([
                'type' => 'zero_consumption',
                'severity' => 'medium',
                'message' => 'Zero consumption detected - verify meter reading',
                'value' => 0,
            ]);
        }

        // Check reading date is not in future
        if (! $this->config['allow_future_dates'] && $meterReading->reading_date > now()) {
            $validationResult->addAnomaly([
                'type' => 'future_date',
                'severity' => 'critical',
                'message' => 'Reading date cannot be in the future',
                'value' => $meterReading->reading_date->format('Y-m-d'),
            ]);
            $validationResult->isValid = false;
        }

        return $validationResult;
    }

    public function getName(): string
    {
        return 'basic_validation';
    }

    public function getDescription(): string
    {
        return 'Validates basic constraints like negative values, future dates, and reasonable consumption limits';
    }

    public function getSeverity(): string
    {
        return 'critical';
    }
}
