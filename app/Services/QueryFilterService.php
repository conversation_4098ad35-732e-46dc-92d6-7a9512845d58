<?php

namespace App\Services;

use Illuminate\Database\Eloquent\Builder;

class QueryFilterService
{
    /**
     * Apply date range filter to a query
     */
    public function applyDateRange(Builder $builder, $startDate = null, $endDate = null, $dateColumn = 'created_at')
    {
        return $builder->when($startDate, function ($q) use ($startDate, $dateColumn): void {
            $q->where($dateColumn, '>=', $startDate);
        })
            ->when($endDate, function ($q) use ($endDate, $dateColumn): void {
                $q->where($dateColumn, '<=', $endDate);
            });
    }

    /**
     * Apply estate filter to a query
     */
    public function applyEstateFilter(Builder $builder, $estateId = null, $relationship = 'estate')
    {
        return $builder->when($estateId, function ($q) use ($estateId, $relationship): void {
            if ($relationship === 'estate') {
                $q->where('estate_id', $estateId);
            } else {
                $q->whereHas($relationship, function ($subQuery) use ($estateId): void {
                    $subQuery->where('id', $estateId);
                });
            }
        });
    }

    /**
     * Apply status filter to a query
     */
    public function applyStatusFilter(Builder $builder, $status = null, $statusColumn = 'status')
    {
        return $builder->when($status && $status !== 'all', function ($q) use ($status, $statusColumn): void {
            $q->where($statusColumn, $status);
        });
    }

    /**
     * Apply search filter to a query
     */
    public function applySearchFilter(Builder $builder, $search = null, $searchFields = ['name'])
    {
        return $builder->when($search, function ($q) use ($search, $searchFields): void {
            $q->where(function ($subQuery) use ($search, $searchFields): void {
                foreach ($searchFields as $searchField) {
                    $subQuery->orWhere($searchField, 'like', '%'.$search.'%');
                }
            });
        });
    }

    /**
     * Apply user-based filter (assigned estates/houses)
     */
    public function applyUserFilter(Builder $builder, $user, $relationship = 'estate')
    {
        if ($user->can('access_all')) {
            return $builder;
        }

        return $builder->whereHas($relationship, function ($subQuery) use ($user): void {
            $subQuery->whereIn('id', $user->assignedEstates->pluck('id'));
        });
    }

    /**
     * Apply active filter
     */
    public function applyActiveFilter(Builder $builder, $activeOnly = true, $column = 'is_active')
    {
        if ($activeOnly) {
            return $builder->where($column, true);
        }

        return $builder;
    }

    /**
     * Apply sorting to a query
     */
    public function applySorting(Builder $builder, $sortBy = 'created_at', $sortDirection = 'desc')
    {
        return $builder->orderBy($sortBy, $sortDirection);
    }

    /**
     * Apply pagination to a query
     */
    public function applyPagination(Builder $builder, $perPage = 20)
    {
        return $builder->paginate($perPage);
    }

    /**
     * Apply multiple filters at once
     */
    public function applyFilters(Builder $builder, array $filters = [])
    {
        $builder = $this->applyDateRange(
            $builder,
            $filters['date_from'] ?? null,
            $filters['date_to'] ?? null,
            $filters['date_column'] ?? 'created_at'
        );

        $builder = $this->applyEstateFilter(
            $builder,
            $filters['estate_id'] ?? null,
            $filters['estate_relationship'] ?? 'estate'
        );

        $builder = $this->applyStatusFilter(
            $builder,
            $filters['status'] ?? null,
            $filters['status_column'] ?? 'status'
        );

        $builder = $this->applySearchFilter(
            $builder,
            $filters['search'] ?? null,
            $filters['search_fields'] ?? ['name']
        );

        $builder = $this->applyActiveFilter(
            $builder,
            $filters['active_only'] ?? true,
            $filters['active_column'] ?? 'is_active'
        );

        return $this->applySorting(
            $builder,
            $filters['sort_by'] ?? 'created_at',
            $filters['sort_direction'] ?? 'desc'
        );
    }

    /**
     * Get date range for common periods
     */
    public function getDateRange($period)
    {
        $now = now();

        return match ($period) {
            'today' => [
                'start' => $now->startOfDay(),
                'end' => $now->endOfDay(),
            ],
            'yesterday' => [
                'start' => $now->subDay()->startOfDay(),
                'end' => $now->endOfDay(),
            ],
            'this_week' => [
                'start' => $now->startOfWeek(),
                'end' => $now->endOfWeek(),
            ],
            'last_week' => [
                'start' => $now->subWeek()->startOfWeek(),
                'end' => $now->subWeek()->endOfWeek(),
            ],
            'this_month' => [
                'start' => $now->startOfMonth(),
                'end' => $now->endOfMonth(),
            ],
            'last_month' => [
                'start' => $now->subMonth()->startOfMonth(),
                'end' => $now->subMonth()->endOfMonth(),
            ],
            'this_year' => [
                'start' => $now->startOfYear(),
                'end' => $now->endOfYear(),
            ],
            'last_year' => [
                'start' => $now->subYear()->startOfYear(),
                'end' => $now->subYear()->endOfYear(),
            ],
            default => [
                'start' => $now->subDays(30),
                'end' => $now,
            ],
        };
    }

    /**
     * Apply date range by period name
     */
    public function applyDateRangeByPeriod(Builder $builder, $period, $dateColumn = 'created_at')
    {
        $dateRange = $this->getDateRange($period);

        return $builder->whereBetween($dateColumn, [
            $dateRange['start'],
            $dateRange['end'],
        ]);
    }
}
