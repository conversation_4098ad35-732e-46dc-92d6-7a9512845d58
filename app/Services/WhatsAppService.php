<?php

namespace App\Services;

use App\Models\Contact;
use App\Models\Estate;
use App\Models\House;
use App\Models\Invoice;
use App\Models\WhatsAppMessage;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class WhatsAppService
{
    protected string $apiUrl;

    protected string $apiToken;

    protected bool $enabled;

    protected int $maxRetries = 3;

    public function __construct()
    {
        $this->apiUrl = config('services.whatsapp.api_url', '');
        $this->apiToken = config('services.whatsapp.token', '');
        $this->enabled = config('services.whatsapp.enabled', false);
    }

    /**
     * Send a text message via WhatsApp
     *
     * @param  string  $recipient  The recipient's phone number
     * @param  string  $content  The message content
     * @param  Contact|null  $contact  The contact model (optional)
     * @param  House|null  $house  The house model (optional)
     * @param  Estate|null  $estate  The estate model (optional)
     */
    public function sendText(string $recipient, string $content, ?Contact $contact = null, ?House $house = null, ?Estate $estate = null): ?WhatsAppMessage
    {
        if (! $this->enabled) {
            Log::info('WhatsApp messaging is disabled');

            return null;
        }

        $whatsAppMessage = $this->createMessageRecord(
            'text',
            $recipient,
            $content,
            null,
            null,
            null,
            null,
            $contact,
            $house,
            $estate
        );

        if ($whatsAppMessage->status === 'failed') {
            return $whatsAppMessage;
        }

        try {
            $response = $this->makeApiRequest('messages', [
                'messaging_product' => 'whatsapp',
                'recipient_type' => 'individual',
                'to' => $recipient,
                'type' => 'text',
                'text' => [
                    'body' => $content,
                ],
            ]);

            if ($response && isset($response['messages'][0]['id'])) {
                $whatsAppMessage->markAsSent($response['messages'][0]['id'], $response);

                return $whatsAppMessage;
            } else {
                $whatsAppMessage->markAsFailed('Invalid API response');

                return $whatsAppMessage;
            }
        } catch (\Exception $e) {
            Log::error('WhatsApp API error: '.$e->getMessage());
            $whatsAppMessage->markAsFailed($e->getMessage());

            return $whatsAppMessage;
        }
    }

    /**
     * Send a template message via WhatsApp
     *
     * @param  string  $recipient  The recipient's phone number
     * @param  string  $templateName  The template name
     * @param  array  $parameters  The template parameters
     * @param  Contact|null  $contact  The contact model (optional)
     * @param  House|null  $house  The house model (optional)
     * @param  Estate|null  $estate  The estate model (optional)
     * @param  mixed|null  $messageable  The related model (optional)
     */
    public function sendTemplate(
        string $recipient,
        string $templateName,
        array $parameters = [],
        ?Contact $contact = null,
        ?House $house = null,
        ?Estate $estate = null,
        $messageable = null
    ): ?WhatsAppMessage {
        if (! $this->enabled) {
            Log::info('WhatsApp messaging is disabled');

            return null;
        }

        // Format template parameters for WhatsApp API
        $components = $this->formatTemplateComponents($parameters);

        $whatsAppMessage = $this->createMessageRecord(
            'template',
            $recipient,
            "Template: {$templateName}",
            $templateName,
            $parameters,
            null,
            null,
            $contact,
            $house,
            $estate,
            $messageable
        );

        if ($whatsAppMessage->status === 'failed') {
            return $whatsAppMessage;
        }

        try {
            $response = $this->makeApiRequest('messages', [
                'messaging_product' => 'whatsapp',
                'recipient_type' => 'individual',
                'to' => $recipient,
                'type' => 'template',
                'template' => [
                    'name' => $templateName,
                    'language' => [
                        'code' => 'en_US',
                    ],
                    'components' => $components,
                ],
            ]);

            if ($response && isset($response['messages'][0]['id'])) {
                $whatsAppMessage->markAsSent($response['messages'][0]['id'], $response);

                return $whatsAppMessage;
            } else {
                $whatsAppMessage->markAsFailed('Invalid API response');

                return $whatsAppMessage;
            }
        } catch (\Exception $e) {
            Log::error('WhatsApp API error: '.$e->getMessage());
            $whatsAppMessage->markAsFailed($e->getMessage());

            return $whatsAppMessage;
        }
    }

    /**
     * Send an invoice notification via WhatsApp
     *
     * @param  Invoice  $invoice  The invoice to send
     */
    public function sendInvoiceNotification(Invoice $invoice): ?WhatsAppMessage
    {
        $house = $invoice->house;
        $contact = $house->primaryContact;

        if (! $contact || ! $contact->canReceiveMessages()) {
            Log::info('Cannot send invoice notification: no eligible contact found', ['invoice_id' => $invoice->id]);

            return null;
        }

        $parameters = [
            'customer_name' => $contact->name,
            'invoice_number' => $invoice->invoice_number,
            'amount' => number_format($invoice->total_amount, 2),
            'due_date' => $invoice->due_date->format('d/m/Y'),
            'house_number' => $house->house_number,
        ];

        return $this->sendTemplate(
            $contact->whatsapp_number,
            'invoice_delivery',
            $parameters,
            $contact,
            $house,
            $house->estate,
            $invoice
        );
    }

    /**
     * Send a payment reminder via WhatsApp
     *
     * @param  Invoice  $invoice  The invoice to remind about
     */
    public function sendPaymentReminder(Invoice $invoice): ?WhatsAppMessage
    {
        $house = $invoice->house;
        $contact = $house->primaryContact;

        if (! $contact || ! $contact->canReceiveMessages()) {
            Log::info('Cannot send payment reminder: no eligible contact found', ['invoice_id' => $invoice->id]);

            return null;
        }

        $parameters = [
            'customer_name' => $contact->name,
            'invoice_number' => $invoice->invoice_number,
            'amount' => number_format($invoice->total_amount, 2),
            'due_date' => $invoice->due_date->format('d/m/Y'),
            'days_overdue' => now()->diffInDays($invoice->due_date),
        ];

        return $this->sendTemplate(
            $contact->whatsapp_number,
            'payment_reminder',
            $parameters,
            $contact,
            $house,
            $house->estate,
            $invoice
        );
    }

    /**
     * Create a WhatsApp message record in the database
     *
     * @param  string  $messageType  The message type
     * @param  string  $recipient  The recipient's phone number
     * @param  string  $content  The message content
     * @param  string|null  $templateName  The template name (optional)
     * @param  array|null  $parameters  The template parameters (optional)
     * @param  array|null  $interactiveData  The interactive data (optional)
     * @param  string|null  $mediaUrl  The media URL (optional)
     * @param  Contact|null  $contact  The contact model (optional)
     * @param  House|null  $house  The house model (optional)
     * @param  Estate|null  $estate  The estate model (optional)
     * @param  mixed|null  $messageable  The related model (optional)
     */
    protected function createMessageRecord(
        string $messageType,
        string $recipient,
        string $content,
        ?string $templateName = null,
        ?array $parameters = null,
        ?array $interactiveData = null,
        ?string $mediaUrl = null,
        ?Contact $contact = null,
        ?House $house = null,
        ?Estate $estate = null,
        $messageable = null
    ): WhatsAppMessage {
        $data = [
            'sender_id' => auth()->id() ?? 1, // Default to system user if no auth
            'recipient' => $recipient,
            'message_type' => $messageType,
            'content' => $content,
            'template_name' => $templateName,
            'parameters' => $parameters,
            'interactive_data' => $interactiveData,
            'media_url' => $mediaUrl,
        ];

        if ($contact instanceof \App\Models\Contact) {
            $data['recipient_contact_id'] = $contact->id;
        }

        if ($house instanceof \App\Models\House) {
            $data['house_id'] = $house->id;
        }

        if ($estate instanceof \App\Models\Estate) {
            $data['estate_id'] = $estate->id;
        }

        if ($messageable) {
            $data['messageable_type'] = $messageable::class;
            $data['messageable_id'] = $messageable->id;
        }

        return WhatsAppMessage::create($data);
    }

    /**
     * Format template components for WhatsApp API
     *
     * @param  array  $parameters  The template parameters
     */
    protected function formatTemplateComponents(array $parameters): array
    {
        $components = [];

        if ($parameters !== []) {
            $parameters_array = [];

            foreach ($parameters as $parameter) {
                $parameters_array[] = [
                    'type' => 'text',
                    'text' => (string) $parameter,
                ];
            }

            $components[] = [
                'type' => 'body',
                'parameters' => $parameters_array,
            ];
        }

        return $components;
    }

    /**
     * Make an API request to the WhatsApp API
     *
     * @param  string  $endpoint  The API endpoint
     * @param  array  $data  The request data
     */
    protected function makeApiRequest(string $endpoint, array $data): ?array
    {
        $url = rtrim($this->apiUrl, '/').'/'.$endpoint;

        $response = Http::withToken($this->apiToken)
            ->withHeaders([
                'Content-Type' => 'application/json',
            ])
            ->post($url, $data);

        if ($response->successful()) {
            return $response->json();
        }

        Log::error('WhatsApp API request failed', [
            'status' => $response->status(),
            'body' => $response->body(),
            'data' => $data,
        ]);

        return null;
    }

    /**
     * Verify webhook signature
     *
     * @param  string  $signature  The signature from the request header
     * @param  string  $payload  The request body
     */
    public function verifyWebhookSignature(string $signature, string $payload): bool
    {
        $webhookSecret = config('services.whatsapp.webhook_secret');

        if (empty($webhookSecret)) {
            Log::warning('WhatsApp webhook secret is not configured');

            return false;
        }

        $expectedSignature = 'sha256='.hash_hmac('sha256', $payload, (string) $webhookSecret);

        return hash_equals($expectedSignature, $signature);
    }
}
