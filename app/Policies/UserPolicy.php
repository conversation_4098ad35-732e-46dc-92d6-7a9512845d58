<?php

namespace App\Policies;

use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class UserPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any users.
     */
    public function viewAny(User $user): bool
    {
        return $user->can('users.view_all') || $user->can('users.view_assigned');
    }

    /**
     * Determine whether the user can view the user.
     */
    public function view(User $user, User $targetUser): bool
    {
        // Admin can view all users
        if ($user->can('users.view_all')) {
            return true;
        }

        // Users can view assigned users
        if ($user->can('users.view_assigned')) {
            return $this->canManageUser($user, $targetUser);
        }

        // Users can always view themselves
        return $user->id === $targetUser->id;
    }

    /**
     * Determine whether the user can create users.
     */
    public function create(User $user): bool
    {
        return $user->can('users.create_all') || $user->can('users.create_assigned');
    }

    /**
     * Determine whether the user can update the user.
     */
    public function update(User $user, User $targetUser): bool
    {
        // Admin can edit all users
        if ($user->can('users.edit_all')) {
            return true;
        }

        // Users can edit assigned users
        if ($user->can('users.edit_assigned')) {
            return $this->canManageUser($user, $targetUser);
        }

        // Users can edit themselves
        return $user->id === $targetUser->id;
    }

    /**
     * Determine whether the user can delete the user.
     */
    public function delete(User $user, User $targetUser): bool
    {
        // Admin can delete all users
        if ($user->can('users.delete_all')) {
            return true;
        }

        // Users cannot delete themselves
        if ($user->id === $targetUser->id) {
            return false;
        }

        // Users can delete assigned users
        if ($user->can('users.delete_assigned')) {
            return $this->canManageUser($user, $targetUser);
        }

        return false;
    }

    /**
     * Determine whether the user can manage the user.
     */
    public function manage(User $user, User $targetUser): bool
    {
        // Admin can manage all users
        if ($user->can('users.manage_all')) {
            return true;
        }

        return $this->canManageUser($user, $targetUser);
    }

    /**
     * Determine whether the user can assign estates to users.
     */
    public function assignEstates(User $user): bool
    {
        return $user->can('users.assign_estates');
    }

    /**
     * Determine whether the user can assign roles to users.
     */
    public function assignRoles(User $user): bool
    {
        return $user->can('users.assign_roles');
    }

    /**
     * Helper method to determine if user can manage target user
     */
    private function canManageUser(User $manager, User $subordinate): bool
    {
        // Admin can manage everyone
        if ($manager->hasRole('admin')) {
            return true;
        }

        // Manager can manage reviewers and caretakers
        if ($manager->hasRole('manager')) {
            return $subordinate->hasRole('reviewer') || $subordinate->hasRole('caretaker');
        }

        // Reviewer can manage caretakers
        if ($manager->hasRole('reviewer')) {
            return $subordinate->hasRole('caretaker');
        }

        return false;
    }
}
