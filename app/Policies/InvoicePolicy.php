<?php

declare(strict_types=1);

namespace App\Policies;

use App\Models\Invoice;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class InvoicePolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view the invoice.
     */
    public function view(User $user, Invoice $invoice): bool
    {
        if ($user->can('invoices.view_all')) {
            return true;
        }

        if ($user->can('invoices.view_assigned')) {
            return $user->canAccessEstate($invoice->house->estate);
        }

        if ($user->can('invoices.view_own')) {
            return $user->canAccessHouse($invoice->house);
        }

        return false;
    }

    /**
     * Determine whether the user can create invoices.
     */
    public function create(User $user): bool
    {
        return $user->can('invoices.create_manual') ||
               $user->can('invoices.generate_all') ||
               $user->can('invoices.generate_assigned');
    }

    /**
     * Determine whether the user can update the invoice.
     */
    public function update(User $user, Invoice $invoice): bool
    {
        if ($user->can('invoices.edit_all')) {
            return true;
        }

        if ($user->can('invoices.edit_assigned')) {
            return $user->canAccessEstate($invoice->house->estate) &&
                   in_array($invoice->status, ['draft', 'submitted']);
        }

        return false;
    }

    /**
     * Determine whether the user can delete the invoice.
     */
    public function delete(User $user, Invoice $invoice): bool
    {
        if ($user->can('invoices.delete')) {
            return true;
        }

        if ($user->can('invoices.delete_assigned')) {
            return $user->canAccessEstate($invoice->house->estate) &&
                   $invoice->status === 'draft';
        }

        return false;
    }

    /**
     * Determine whether the user can submit the invoice for approval.
     */
    public function submitForApproval(User $user, Invoice $invoice): bool
    {
        return ($user->can('invoices.edit_all') || $user->can('invoices.edit_assigned')) &&
               $user->canAccessEstate($invoice->house->estate) &&
               $invoice->status === 'draft';
    }

    /**
     * Determine whether the user can approve the invoice.
     */
    public function approve(User $user, Invoice $invoice): bool
    {
        return ($user->can('invoices.approve_all') || $user->can('invoices.approve_assigned')) &&
               $user->canAccessEstate($invoice->house->estate) &&
               $invoice->status === 'submitted';
    }

    /**
     * Determine whether the user can reject the invoice.
     */
    public function reject(User $user, Invoice $invoice): bool
    {
        return ($user->can('invoices.approve_all') || $user->can('invoices.approve_assigned')) &&
               $user->canAccessEstate($invoice->house->estate) &&
               $invoice->status === 'submitted';
    }

    /**
     * Determine whether the user can send the invoice.
     */
    public function send(User $user, Invoice $invoice): bool
    {
        return ($user->can('invoices.send_all') || $user->can('invoices.send_assigned')) &&
               $user->canAccessEstate($invoice->house->estate) &&
               $invoice->status === 'approved' &&
               ! is_null($invoice->pdf_path);
    }

    /**
     * Determine whether the user can bulk approve invoices.
     */
    public function bulkApprove(User $user): bool
    {
        return $user->can('invoices.approve_all') || $user->can('invoices.approve_assigned');
    }

    /**
     * Determine whether the user can bulk send invoices.
     */
    public function bulkSend(User $user): bool
    {
        return $user->can('invoices.send_all') || $user->can('invoices.send_assigned');
    }

    /**
     * Determine whether the user can view invoice statistics.
     */
    public function viewStatistics(User $user): bool
    {
        return $user->can('reports.billing_all') ||
               $user->can('reports.billing_assigned');
    }
}
