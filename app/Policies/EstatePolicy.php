<?php

namespace App\Policies;

use App\Models\Estate;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class EstatePolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any estates.
     */
    public function viewAny(User $user): bool
    {
        return $user->can('estates.view_all') || $user->can('estates.view_assigned');
    }

    /**
     * Determine whether the user can view the estate.
     */
    public function view(User $user, Estate $estate): bool
    {
        // Admin can view all estates
        if ($user->can('estates.view_all')) {
            return true;
        }

        // Other users can only view assigned estates
        if ($user->can('estates.view_assigned')) {
            return $user->assignedEstates()->where('estates.id', $estate->id)->exists();
        }

        return false;
    }

    /**
     * Determine whether the user can create estates.
     */
    public function create(User $user): bool
    {
        return $user->can('estates.create');
    }

    /**
     * Determine whether the user can update the estate.
     */
    public function update(User $user, Estate $estate): bool
    {
        // Admin can edit all estates
        if ($user->can('estates.edit_all')) {
            return true;
        }

        // Other users can only edit assigned estates
        if ($user->can('estates.edit_assigned')) {
            return $user->assignedEstates()->where('estates.id', $estate->id)->exists();
        }

        return false;
    }

    /**
     * Determine whether the user can delete the estate.
     */
    public function delete(User $user, Estate $estate): bool
    {
        return $user->can('estates.delete');
    }

    /**
     * Determine whether the user can manage the estate.
     */
    public function manage(User $user, Estate $estate): bool
    {
        // Admin can manage all estates
        if ($user->can('estates.manage_all')) {
            return true;
        }

        // Other users can only manage assigned estates
        if ($user->can('estates.manage_assigned')) {
            return $user->assignedEstates()->where('estates.id', $estate->id)->exists();
        }

        return false;
    }

    /**
     * Determine whether the user can view estate analytics.
     */
    public function viewAnalytics(User $user, Estate $estate): bool
    {
        // Admin can view all analytics
        if ($user->can('analytics.view_all')) {
            return true;
        }

        // Other users can only view assigned estate analytics
        if ($user->can('analytics.view_assigned')) {
            return $user->assignedEstates()->where('estates.id', $estate->id)->exists();
        }

        return false;
    }
}
