<?php

namespace App\Policies;

use App\Models\House;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class HousePolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any houses.
     */
    public function viewAny(User $user): bool
    {
        return $user->can('houses.view_all') || $user->can('houses.view_assigned');
    }

    /**
     * Determine whether the user can view the house.
     */
    public function view(User $user, House $house): bool
    {
        // Admin can view all houses
        if ($user->can('houses.view_all')) {
            return true;
        }

        // Users can view assigned houses
        if ($user->can('houses.view_assigned')) {
            return $user->assignedEstates()->where('estates.id', $house->estate_id)->exists();
        }

        // Residents can view their own house
        if ($user->can('houses.view_own')) {
            return $user->contacts()->where('house_id', $house->id)->exists();
        }

        return false;
    }

    /**
     * Determine whether the user can create houses.
     */
    public function create(User $user): bool
    {
        return $user->can('houses.create');
    }

    /**
     * Determine whether the user can update the house.
     */
    public function update(User $user, House $house): bool
    {
        // Admin can edit all houses
        if ($user->can('houses.edit_all')) {
            return true;
        }

        // Users can edit assigned houses
        if ($user->can('houses.edit_assigned')) {
            return $user->assignedEstates()->where('estates.id', $house->estate_id)->exists();
        }

        return false;
    }

    /**
     * Determine whether the user can delete the house.
     */
    public function delete(User $user, House $house): bool
    {
        return $user->can('houses.delete');
    }

    /**
     * Determine whether the user can manage the house.
     */
    public function manage(User $user, House $house): bool
    {
        // Admin can manage all houses
        if ($user->can('houses.manage_all')) {
            return true;
        }

        // Users can manage assigned houses
        if ($user->can('houses.manage_assigned')) {
            return $user->assignedEstates()->where('estates.id', $house->estate_id)->exists();
        }

        return false;
    }
}
