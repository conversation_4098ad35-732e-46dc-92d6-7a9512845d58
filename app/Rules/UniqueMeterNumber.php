<?php

namespace App\Rules;

use App\Models\House;
use Illuminate\Contracts\Validation\Rule;

class UniqueMeterNumber implements Rule
{
    public function __construct(private $ignoreId = null) {}

    public function passes($attribute, $value)
    {
        $query = House::where('meter_number', $value);

        if ($this->ignoreId) {
            $query->where('id', '!=', $this->ignoreId);
        }

        return ! $query->exists();
    }

    public function message()
    {
        return 'The meter number already exists.';
    }
}
