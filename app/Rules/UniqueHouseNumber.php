<?php

namespace App\Rules;

use App\Models\House;
use Illuminate\Contracts\Validation\Rule;

class UniqueHouseNumber implements Rule
{
    public function __construct(private $estateId, private $ignoreId = null) {}

    public function passes($attribute, $value)
    {
        $query = House::where('estate_id', $this->estateId)
            ->where('house_number', $value);

        if ($this->ignoreId) {
            $query->where('id', '!=', $this->ignoreId);
        }

        return ! $query->exists();
    }

    public function message()
    {
        return 'The house number already exists in this estate.';
    }
}
