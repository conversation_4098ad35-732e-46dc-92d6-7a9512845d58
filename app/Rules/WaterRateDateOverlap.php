<?php

namespace App\Rules;

use App\Models\WaterRate;
use Carbon\Carbon;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class WaterRateDateOverlap implements ValidationRule
{
    public function __construct(protected ?int $estateId, protected ?int $waterRateId = null, protected ?Carbon $effectiveTo = null) {}

    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $effectiveFrom = Carbon::parse($value);
        $effectiveTo = $this->effectiveTo ?? Carbon::parse('9999-12-31'); // Use a very distant future date for null effective_to

        $query = WaterRate::where('estate_id', $this->estateId)
            ->where(function ($q) use ($effectiveFrom, $effectiveTo): void {
                // Check for overlap: (start1 <= end2) AND (end1 >= start2)
                $q->where('effective_from', '<=', $effectiveTo)
                    ->where(function ($subQuery) use ($effectiveFrom): void {
                        $subQuery->whereNull('effective_to')
                            ->orWhere('effective_to', '>=', $effectiveFrom);
                    });
            });

        if ($this->waterRateId) {
            $query->where('id', '!=', $this->waterRateId);
        }

        if ($query->exists()) {
            $fail('The effective date range overlaps with an existing water rate for this estate.');
        }
    }
}
