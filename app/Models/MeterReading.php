<?php

namespace App\Models;

use App\Models\Traits\EstateScopes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $house_id
 * @property int $user_id
 * @property numeric|null $current_reading
 * @property numeric|null $previous_reading
 * @property numeric|null $consumption
 * @property \Illuminate\Support\Carbon $reading_date
 * @property string|null $photo_path
 * @property string|null $location_coordinates
 * @property string|null $notes
 * @property string $status
 * @property int $is_validated
 * @property string $validation_status
 * @property string|null $validation_notes
 * @property int|null $reviewed_by
 * @property \Illuminate\Support\Carbon|null $reviewed_at
 * @property string|null $review_notes
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string $reading_method
 * @property string|null $meter_serial_number
 * @property string|null $estimated_consumption
 * @property int $is_estimated
 * @property string|null $estimation_reason
 * @property string|null $reading_metadata
 * @property string|null $device_id
 * @property string|null $synced_at
 * @property-read mixed $status_color
 * @property-read mixed $status_label
 * @property-read \App\Models\House $house
 * @property-read \App\Models\User|null $reviewer
 * @property-read \App\Models\User $user
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading approved()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading byHouse($houseId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading byUser($userId)
 * @method static \Database\Factories\MeterReadingFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading forAccessibleEstates(\App\Models\User $user)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading forEstate($estate)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading forOwnData(\App\Models\User $user)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading forPeriod($startDate, $endDate)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading forUser(\App\Models\User $user)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading pendingReview()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading rejected()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading submitted()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading whereConsumption($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading whereCurrentReading($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading whereDeviceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading whereEstimatedConsumption($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading whereEstimationReason($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading whereHouseId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading whereIsEstimated($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading whereIsValidated($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading whereLocationCoordinates($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading whereMeterSerialNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading whereNotes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading wherePhotoPath($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading wherePreviousReading($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading whereReadingDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading whereReadingMetadata($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading whereReadingMethod($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading whereReviewNotes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading whereReviewedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading whereReviewedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading whereSyncedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading whereValidationNotes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading whereValidationStatus($value)
 *
 * @mixin \Eloquent
 */
class MeterReading extends Model
{
    use EstateScopes, HasFactory;

    protected $fillable = [
        'house_id',
        'user_id',
        'current_reading',
        'previous_reading',
        'consumption',
        'reading_date',
        'photo_path',
        'notes',
        'status',
        'reviewed_by',
        'reviewed_at',
        'review_notes',
    ];

    protected $casts = [
        'current_reading' => 'decimal:2',
        'previous_reading' => 'decimal:2',
        'consumption' => 'decimal:2',
        'reading_date' => 'date',
        'reviewed_at' => 'datetime',
    ];

    public function house(): BelongsTo
    {
        return $this->belongsTo(House::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function reviewer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'reviewed_by');
    }

    public function scopeSubmitted($query)
    {
        return $query->where('status', 'submitted');
    }

    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    public function scopeRejected($query)
    {
        return $query->where('status', 'rejected');
    }

    public function scopePendingReview($query)
    {
        return $query->whereIn('status', ['submitted', 'reviewed']);
    }

    public function scopeForPeriod($query, $startDate, $endDate)
    {
        return $query->whereBetween('reading_date', [$startDate, $endDate]);
    }

    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeByHouse($query, $houseId)
    {
        return $query->where('house_id', $houseId);
    }

    public function calculateConsumption()
    {
        if ($this->previous_reading === null) {
            $previousReading = $this->house->initial_reading;

            // Get the previous reading for this house
            $lastReading = $this->house->meterReadings()
                ->where('reading_date', '<', $this->reading_date)
                ->orderBy('reading_date', 'desc')
                ->first();

            if ($lastReading) {
                $previousReading = $lastReading->current_reading;
            }

            $this->previous_reading = $previousReading;
        }

        $this->consumption = max(0, $this->current_reading - $this->previous_reading);

        return $this->consumption;
    }

    public function validateReading()
    {
        $errors = [];

        if ($this->current_reading < $this->previous_reading) {
            $errors[] = 'Current reading cannot be less than previous reading';
        }

        if ($this->consumption > 100000) { // 100,000 liters max per month
            $errors[] = 'Consumption seems unusually high (>100,000 liters)';
        }

        if ($this->consumption == 0 && $this->previous_reading > 0) {
            $errors[] = 'Zero consumption detected - please verify';
        }

        return $errors;
    }

    public function approve($reviewerId, $notes = null)
    {
        $this->update([
            'status' => 'approved',
            'reviewed_by' => $reviewerId,
            'reviewed_at' => now(),
            'review_notes' => $notes,
        ]);
    }

    public function reject($reviewerId, $notes)
    {
        $this->update([
            'status' => 'rejected',
            'reviewed_by' => $reviewerId,
            'reviewed_at' => now(),
            'review_notes' => $notes,
        ]);
    }

    public function submit()
    {
        $this->update(['status' => 'submitted']);
    }

    public function isEditable()
    {
        return in_array($this->status, ['draft', 'rejected']);
    }

    public function getStatusColorAttribute()
    {
        return match ($this->status) {
            'draft' => 'gray',
            'submitted' => 'blue',
            'reviewed' => 'yellow',
            'approved' => 'green',
            'rejected' => 'red',
            default => 'gray',
        };
    }

    public function getStatusLabelAttribute()
    {
        return ucfirst($this->status);
    }
}
