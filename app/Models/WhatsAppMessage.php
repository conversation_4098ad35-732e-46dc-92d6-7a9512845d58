<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

/**
 * @property int $id
 * @property int|null $house_id
 * @property int|null $contact_id
 * @property int|null $user_id
 * @property int|null $invoice_id
 * @property string $message_id
 * @property string $phone_number
 * @property string $direction
 * @property string $status
 * @property string $message_type
 * @property string $message_content
 * @property string|null $media_url
 * @property string|null $media_type
 * @property string|null $sender_id
 * @property \Illuminate\Support\Carbon|null $sent_at
 * @property \Illuminate\Support\Carbon|null $delivered_at
 * @property \Illuminate\Support\Carbon|null $read_at
 * @property string|null $error_message
 * @property string|null $metadata
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $campaign_id
 * @property string|null $template_name
 * @property string|null $template_variables
 * @property string|null $message_category
 * @property int $retry_count
 * @property string|null $next_retry_at
 * @property string|null $delivery_metadata
 * @property string|null $conversation_id
 * @property int $is_read
 * @property-read \App\Models\Estate|null $estate
 * @property-read \App\Models\House|null $house
 * @property-read Model|\Eloquent $messageable
 * @property-read \App\Models\Contact|null $recipientContact
 * @property-read \App\Models\User|null $sender
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage byDateRange($startDate, $endDate)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage byStatus($status)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage byTemplate($templateName)
 * @method static \Database\Factories\WhatsAppMessageFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage forContact(\App\Models\Contact $contact)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage forEstate($estateId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage forHouse($houseId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage forInvoice($invoiceId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage incoming()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage outgoing()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage recent($days = 30)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage sent()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage whereCampaignId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage whereContactId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage whereConversationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage whereDeliveredAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage whereDeliveryMetadata($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage whereDirection($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage whereErrorMessage($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage whereHouseId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage whereInvoiceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage whereIsRead($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage whereMediaType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage whereMediaUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage whereMessageCategory($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage whereMessageContent($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage whereMessageId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage whereMessageType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage whereMetadata($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage whereNextRetryAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage wherePhoneNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage whereReadAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage whereRetryCount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage whereSenderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage whereSentAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage whereTemplateName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage whereTemplateVariables($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage whereUserId($value)
 *
 * @mixin \Eloquent
 */
class WhatsAppMessage extends Model
{
    use HasFactory;

    protected $table = 'whatsapp_messages';

    protected $fillable = [
        'message_id',
        'recipient',
        'message_type',
        'content',
        'status',
        'sent_at',
        'delivered_at',
        'read_at',
        'failed_reason',
        'template_name',
        'parameters',
        'response_data',
        'interactive_data',
        'media_url',
        'media_type',
        'conversation_id',
        'retry_count',
        'sender_id',
        'recipient_contact_id',
        'house_id',
        'estate_id',
        'messageable_type',
        'messageable_id',
        'direction',
        'received_at',
    ];

    protected $casts = [
        'sent_at' => 'datetime',
        'delivered_at' => 'datetime',
        'read_at' => 'datetime',
        'received_at' => 'datetime',
        'parameters' => 'array',
        'response_data' => 'array',
        'interactive_data' => 'array',
        'retry_count' => 'integer',
    ];

    public function sender(): BelongsTo
    {
        return $this->belongsTo(User::class, 'sender_id');
    }

    public function recipientContact(): BelongsTo
    {
        return $this->belongsTo(Contact::class, 'recipient_contact_id');
    }

    public function house(): BelongsTo
    {
        return $this->belongsTo(House::class);
    }

    public function estate(): BelongsTo
    {
        return $this->belongsTo(Estate::class);
    }

    public function messageable(): MorphTo
    {
        return $this->morphTo();
    }

    public static function boot()
    {
        parent::boot();

        static::creating(function ($message): void {
            // Ensure recipientContact is loaded if not already
            if (! $message->relationLoaded('recipientContact')) {
                $message->load('recipientContact');
            }

            if (! $message->recipientContact) {
                $message->status = 'failed';
                $message->failed_reason = 'Contact not found';
            } elseif (! $message->recipientContact->canReceiveMessages()) {
                $message->status = 'failed';
                $message->failed_reason = 'Contact is inactive or has disabled notifications';
            }

            if (is_null($message->status)) {
                $message->status = 'pending';
            }
        });
    }

    public function markAsSent(string $messageId, array $responseData = []): void
    {
        $this->update([
            'message_id' => $messageId,
            'status' => 'sent',
            'sent_at' => now(),
            'response_data' => $responseData,
        ]);
    }

    public function markAsDelivered(): void
    {
        $this->update([
            'status' => 'delivered',
            'delivered_at' => now(),
        ]);
    }

    public function markAsRead(): void
    {
        $this->update([
            'status' => 'read',
            'read_at' => now(),
        ]);
    }

    public function markAsFailed(string $reason): void
    {
        $this->update([
            'status' => 'failed',
            'failed_reason' => $reason,
        ]);
    }

    public function markAsReceived(): void
    {
        $this->update([
            'status' => 'received',
            'received_at' => now(),
        ]);
    }

    public function isIncoming(): bool
    {
        return $this->direction === 'incoming';
    }

    public function isOutgoing(): bool
    {
        return $this->direction === 'outgoing' || $this->direction === null;
    }

    public function scopeIncoming($query)
    {
        return $query->where('direction', 'incoming');
    }

    public function scopeOutgoing($query)
    {
        return $query->where('direction', 'outgoing')->orWhereNull('direction');
    }

    public function scopeForContact($query, Contact $contact)
    {
        return $query->where('recipient_contact_id', $contact->id);
    }

    public function getStatusColor(): string
    {
        return match ($this->status) {
            'sent' => 'text-blue-600',
            'delivered' => 'text-green-600',
            'read' => 'text-green-800',
            'failed' => 'text-red-600',
            'pending' => 'text-yellow-600',
            default => 'text-gray-600',
        };
    }

    public function getStatusIcon(): string
    {
        return match ($this->status) {
            'sent' => 'heroicon-o-paper-airplane',
            'delivered' => 'heroicon-o-check-circle',
            'read' => 'heroicon-o-eye',
            'failed' => 'heroicon-o-x-circle',
            'pending' => 'heroicon-o-clock',
            default => 'heroicon-o-question-mark-circle',
        };
    }

    public function incrementRetryCount(): void
    {
        $this->increment('retry_count');
    }

    public function canRetry(): bool
    {
        return $this->retry_count < 3 && $this->status === 'failed';
    }

    public function scopeForInvoice($query, $invoiceId)
    {
        return $query->where('messageable_type', Invoice::class)
            ->where('messageable_id', $invoiceId);
    }

    public function scopeForHouse($query, $houseId)
    {
        return $query->where('house_id', $houseId);
    }

    public function scopeForEstate($query, $estateId)
    {
        return $query->where('estate_id', $estateId);
    }

    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeByTemplate($query, $templateName)
    {
        return $query->where('template_name', $templateName);
    }

    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    public function scopeRecent($query, $days = 30)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    /**
     * Scope a query to only include sent messages.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeSent($query)
    {
        return $query->where('status', 'sent');
    }
}
