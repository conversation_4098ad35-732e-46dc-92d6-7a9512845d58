<?php

namespace App\Models\Traits;

trait ValidationRulesTrait
{
    /**
     * Get common name validation rules
     */
    public static function getNameRules($required = true, $max = 255)
    {
        $rule = $required ? 'required' : 'nullable';

        return $rule.'|string|max:'.$max;
    }

    /**
     * Get common email validation rules
     */
    public static function getEmailRules($required = false, $max = 255)
    {
        $rule = $required ? 'required' : 'nullable';

        return $rule.'|email|max:'.$max;
    }

    /**
     * Get common phone validation rules
     */
    public static function getPhoneRules($required = false, $max = 50)
    {
        $rule = $required ? 'required' : 'nullable';

        return $rule.'|string|max:'.$max;
    }

    /**
     * Get common address validation rules
     */
    public static function getAddressRules($required = false, $max = 500)
    {
        $rule = $required ? 'required' : 'nullable';

        return $rule.'|string|max:'.$max;
    }

    /**
     * Get common code validation rules
     */
    public static function getCodeRules($required = true, $max = 50)
    {
        $rule = $required ? 'required' : 'nullable';

        return $rule.'|string|max:'.$max;
    }

    /**
     * Get common description validation rules
     */
    public static function getDescriptionRules($required = false, $max = 1000)
    {
        $rule = $required ? 'required' : 'nullable';

        return $rule.'|string|max:'.$max;
    }

    /**
     * Get common boolean validation rules
     */
    public static function getBooleanRules($default = true)
    {
        return 'boolean|sometimes';
    }

    /**
     * Get common numeric validation rules
     */
    public static function getNumericRules($required = false, $min = 0)
    {
        $rule = $required ? 'required' : 'nullable';

        return $rule.'|numeric|min:'.$min;
    }

    /**
     * Get common date validation rules
     */
    public static function getDateRules($required = false)
    {
        $rule = $required ? 'required' : 'nullable';

        return $rule.'|date';
    }

    /**
     * Get common foreign key validation rules
     */
    public static function getForeignKeyRules($table, $required = true)
    {
        $rule = $required ? 'required' : 'nullable';

        return $rule.'|exists:'.$table.',id';
    }

    /**
     * Get common unique validation rules
     */
    public static function getUniqueRules($table, $column, $ignoreId = null)
    {
        $rule = 'unique:'.$table.','.$column;

        if ($ignoreId) {
            $rule .= ','.$ignoreId;
        }

        return $rule;
    }

    /**
     * Get common enum validation rules
     */
    public static function getEnumRules($values, $required = true)
    {
        $rule = $required ? 'required' : 'nullable';

        return $rule.'|in:'.implode(',', $values);
    }

    /**
     * Get common file validation rules
     */
    public static function getFileRules($maxSize = 1024, $types = ['jpg', 'jpeg', 'png', 'pdf'])
    {
        return 'nullable|file|max:'.$maxSize.'|mimes:'.implode(',', $types);
    }
}
