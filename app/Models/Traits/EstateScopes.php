<?php

namespace App\Models\Traits;

use App\Models\User;
use Illuminate\Database\Eloquent\Builder;

trait EstateScopes
{
    /**
     * Scope query to records accessible by the given user
     */
    public function scopeForUser(Builder $builder, User $user): Builder
    {
        if ($user->hasRole('admin')) {
            return $builder;
        }

        return $builder->whereIn('estate_id', $user->assignedEstates()->pluck('id'));
    }

    /**
     * Scope query to records for a specific estate
     */
    public function scopeForEstate(Builder $builder, $estate): Builder
    {
        if (is_numeric($estate)) {
            return $builder->where('estate_id', $estate);
        }

        return $builder->where('estate_id', $estate->id);
    }

    /**
     * Scope query to records for estates accessible by user
     */
    public function scopeForAccessibleEstates(Builder $builder, User $user): Builder
    {
        if ($user->hasRole('admin')) {
            return $builder;
        }

        return $builder->whereIn('estate_id', $user->assignedEstates()->pluck('id'));
    }

    /**
     * Scope query to records for user's own data (residents)
     */
    public function scopeForOwnData(Builder $builder, User $user): Builder
    {
        if (! $user->hasRole('resident')) {
            return $builder;
        }

        // For residents, scope to their own houses through contacts
        return $builder->whereIn('house_id', $user->contacts()->pluck('house_id'));
    }
}
