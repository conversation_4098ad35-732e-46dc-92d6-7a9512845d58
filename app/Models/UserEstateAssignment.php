<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\Pivot;

/**
 * @property int $id
 * @property int $user_id
 * @property int $estate_id
 * @property string $role_type
 * @property string $assigned_from
 * @property string|null $assigned_to
 * @property int $is_active
 * @property string|null $notes
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserEstateAssignment newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserEstateAssignment newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserEstateAssignment query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserEstateAssignment whereAssignedFrom($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserEstateAssignment whereAssignedTo($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserEstateAssignment whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserEstateAssignment whereEstateId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserEstateAssignment whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserEstateAssignment whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserEstateAssignment whereNotes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserEstateAssignment whereRoleType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserEstateAssignment whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserEstateAssignment whereUserId($value)
 *
 * @mixin \Eloquent
 */
class UserEstateAssignment extends Pivot
{
    use \Illuminate\Database\Eloquent\Factories\HasFactory;

    protected $table = 'user_estate_assignments';

    protected $fillable = [
        'user_id',
        'estate_id',
        'role_type',
        'assigned_from',
        'assigned_to',
        'is_active',
        'notes',
    ];
}
