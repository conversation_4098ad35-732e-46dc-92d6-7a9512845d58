<?php

namespace App\Models;

use App\Models\Traits\EstateScopes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property int $id
 * @property int $house_id
 * @property int|null $user_id
 * @property string|null $name
 * @property string|null $first_name
 * @property string|null $last_name
 * @property string|null $middle_name
 * @property string|null $email
 * @property string|null $phone
 * @property string|null $whatsapp_number
 * @property string|null $id_number
 * @property \Illuminate\Support\Carbon|null $date_of_birth
 * @property string|null $occupation
 * @property string|null $company
 * @property string|null $postal_address
 * @property string|null $emergency_contact_name
 * @property string|null $emergency_contact_phone
 * @property string $type
 * @property bool $is_primary
 * @property bool $receive_invoices
 * @property bool $receive_notifications
 * @property bool $is_active
 * @property string|null $notes
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property string $preferred_language
 * @property int $email_notifications
 * @property int $sms_notifications
 * @property int $whatsapp_notifications
 * @property string $invoice_delivery_method
 * @property string|null $alternative_email
 * @property string|null $alternative_phone
 * @property string|null $communication_preferences
 * @property-read mixed $display_name
 * @property-read mixed $full_name
 * @property-read mixed $has_whats_app
 * @property-read mixed $preferred_contact
 * @property-read \App\Models\House $house
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\HouseContact> $houseContacts
 * @property-read int|null $house_contacts_count
 * @property-read \App\Models\HouseContact|null $pivot
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\House> $houses
 * @property-read int|null $houses_count
 * @property-read \App\Models\User|null $user
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact active()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact byEmail($email)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact byHouse($houseId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact byName($name)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact byPhone($phone)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact byType($type)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact canReceiveMessages()
 * @method static \Database\Factories\ContactFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact forAccessibleEstates(\App\Models\User $user)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact forEstate($estate)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact forOwnData(\App\Models\User $user)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact forUser(\App\Models\User $user)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact invoiceRecipients()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact notificationRecipients()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact primary()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereAlternativeEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereAlternativePhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereCommunicationPreferences($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereCompany($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereDateOfBirth($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereEmailNotifications($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereEmergencyContactName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereEmergencyContactPhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereFirstName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereHouseId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereIdNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereInvoiceDeliveryMethod($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereIsPrimary($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereLastName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereMiddleName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereNotes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereOccupation($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact wherePhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact wherePostalAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact wherePreferredLanguage($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereReceiveInvoices($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereReceiveNotifications($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereSmsNotifications($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereWhatsappNotifications($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereWhatsappNumber($value)
 *
 * @mixin \Eloquent
 */
class Contact extends Model
{
    use EstateScopes, HasFactory;

    protected $fillable = [
        'user_id',
        'house_id',
        'name',
        'first_name',
        'last_name',
        'middle_name',
        'email',
        'phone',
        'whatsapp_number',
        'id_number',
        'date_of_birth',
        'occupation',
        'company',
        'postal_address',
        'emergency_contact_name',
        'emergency_contact_phone',
        'type',
        'is_primary',
        'receive_invoices',
        'receive_notifications',
        'is_active',
        'notes',
    ];

    protected $casts = [
        'is_primary' => 'boolean',
        'receive_invoices' => 'boolean',
        'receive_notifications' => 'boolean',
        'is_active' => 'boolean',
        'date_of_birth' => 'date',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function house(): BelongsTo
    {
        return $this->belongsTo(House::class);
    }

    public function houseContacts(): HasMany
    {
        return $this->hasMany(HouseContact::class);
    }

    public function houses()
    {
        return $this->belongsToMany(House::class, 'house_contacts')
            ->using(HouseContact::class)
            ->withPivot(['relationship_type', 'start_date', 'end_date', 'is_active', 'notes'])
            ->withTimestamps();
    }

    public function scopePrimary($query)
    {
        return $query->where('is_primary', true);
    }

    public function scopeInvoiceRecipients($query)
    {
        return $query->where('receive_invoices', true);
    }

    public function scopeNotificationRecipients($query)
    {
        return $query->where('receive_notifications', true);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function getDisplayNameAttribute()
    {
        $name = trim($this->first_name.' '.$this->last_name);
        if ($name === '' || $name === '0') {
            $name = $this->name;
        }

        return $name.' ('.ucfirst($this->type).')';
    }

    public function getFullNameAttribute()
    {
        $name = trim($this->first_name.' '.$this->middle_name.' '.$this->last_name);

        return $name ?: $this->name;
    }

    public function getPreferredContactAttribute()
    {
        return $this->whatsapp_number ?? $this->phone ?? $this->email;
    }

    public function getHasWhatsAppAttribute()
    {
        return ! empty($this->whatsapp_number);
    }

    public function scopeByPhone($query, $phone)
    {
        return $query->where('phone', 'like', '%'.$phone.'%');
    }

    public function scopeByEmail($query, $email)
    {
        return $query->where('email', 'like', '%'.$email.'%');
    }

    public function scopeByName($query, $name)
    {
        return $query->where(function ($q) use ($name): void {
            $q->where('first_name', 'like', '%'.$name.'%')
                ->orWhere('last_name', 'like', '%'.$name.'%')
                ->orWhere('name', 'like', '%'.$name.'%');
        });
    }

    public function scopeByHouse($query, $houseId)
    {
        return $query->where('house_id', $houseId);
    }

    public static function validationRules($houseId, $id = null)
    {
        return [
            'house_id' => 'required|exists:houses,id',
            'first_name' => 'required|string|max:100',
            'last_name' => 'required|string|max:100',
            'middle_name' => 'nullable|string|max:100',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:50',
            'whatsapp_number' => 'nullable|string|max:50',
            'id_number' => 'nullable|string|max:50',
            'date_of_birth' => 'nullable|date',
            'occupation' => 'nullable|string|max:100',
            'company' => 'nullable|string|max:255',
            'postal_address' => 'nullable|string|max:500',
            'emergency_contact_name' => 'nullable|string|max:255',
            'emergency_contact_phone' => 'nullable|string|max:50',
            'type' => 'required|in:owner,tenant,caretaker,emergency',
            'is_primary' => 'boolean',
            'receive_invoices' => 'boolean',
            'receive_notifications' => 'boolean',
            'notes' => 'nullable|string',
        ];
    }

    public function canReceiveMessages(): bool
    {
        return $this->is_active &&
               $this->receive_notifications &&
               ! empty($this->whatsapp_number);
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeCanReceiveMessages($query)
    {
        return $query->where('is_active', true)
            ->where('receive_notifications', true)
            ->whereNotNull('whatsapp_number')
            ->where('whatsapp_number', '!=', '');
    }
}
