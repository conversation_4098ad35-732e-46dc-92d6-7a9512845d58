<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property int $id
 * @property int $house_id
 * @property numeric $current_balance
 * @property numeric $total_credit
 * @property numeric $total_debit
 * @property string $available_credit
 * @property string $credit_limit
 * @property \Illuminate\Support\Carbon|null $last_transaction_date
 * @property string|null $last_payment_date
 * @property string|null $last_invoice_date
 * @property string $account_status
 * @property string|null $notes
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read string $balance_status
 * @property-read string $formatted_balance
 * @property-read \App\Models\House $house
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\AccountTransaction> $transactions
 * @property-read int|null $transactions_count
 *
 * @method static \Database\Factories\HouseAccountFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HouseAccount newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HouseAccount newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HouseAccount query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HouseAccount whereAccountStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HouseAccount whereAvailableCredit($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HouseAccount whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HouseAccount whereCreditLimit($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HouseAccount whereCurrentBalance($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HouseAccount whereHouseId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HouseAccount whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HouseAccount whereLastInvoiceDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HouseAccount whereLastPaymentDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HouseAccount whereLastTransactionDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HouseAccount whereNotes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HouseAccount whereTotalCredit($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HouseAccount whereTotalDebit($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HouseAccount whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class HouseAccount extends Model
{
    use HasFactory;

    protected $fillable = [
        'house_id',
        'current_balance',
        'total_credit',
        'total_debit',
        'last_transaction_date',
    ];

    protected $casts = [
        'current_balance' => 'decimal:2',
        'total_credit' => 'decimal:2',
        'total_debit' => 'decimal:2',
        'last_transaction_date' => 'datetime',
    ];

    public function house(): BelongsTo
    {
        return $this->belongsTo(House::class);
    }

    public function transactions(): HasMany
    {
        return $this->hasMany(AccountTransaction::class);
    }

    public function getCurrentBalanceAttribute()
    {
        return $this->attributes['current_balance'] ?? 0;
    }

    public function getFormattedBalanceAttribute(): string
    {
        return number_format($this->current_balance, 2);
    }

    public function getBalanceStatusAttribute(): string
    {
        if ($this->current_balance > 0) {
            return 'credit';
        } elseif ($this->current_balance < 0) {
            return 'debit';
        }

        return 'balanced';
    }

    public function isActive(): bool
    {
        return $this->is_active;
    }

    public function activate(): void
    {
        $this->is_active = true;
        $this->save();
    }

    public function deactivate(): void
    {
        $this->is_active = false;
        $this->save();
    }

    public function updateBalance(float $amount): void
    {
        $this->attributes['current_balance'] += $amount;

        if ($amount > 0) {
            $this->attributes['total_credit'] += $amount;
        } else {
            $this->attributes['total_debit'] += abs($amount);
        }

        $this->attributes['last_transaction_date'] = now();
        $this->save();
    }

    public function hasSufficientBalance(float $amount): bool
    {
        return $this->current_balance >= $amount;
    }

    public function getOutstandingBalance(): float
    {
        return max(0, -$this->current_balance);
    }

    public function getCreditBalance(): float
    {
        return max(0, $this->current_balance);
    }

    public function updateLastTransactionDate($date): void
    {
        $this->attributes['last_transaction_date'] = $date;
        $this->save();
    }
}
