<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $estate_id
 * @property string $rule_name
 * @property string $rule_type
 * @property string $rule_description
 * @property string $rule_config
 * @property string $severity
 * @property bool $is_active
 * @property int $created_by
 * @property int|null $updated_by
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\User $creator
 * @property-read \App\Models\Estate $estate
 * @property-read \App\Models\User|null $updater
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EstateValidationRule active()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EstateValidationRule byType(string $type)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EstateValidationRule newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EstateValidationRule newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EstateValidationRule query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EstateValidationRule whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EstateValidationRule whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EstateValidationRule whereEstateId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EstateValidationRule whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EstateValidationRule whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EstateValidationRule whereRuleConfig($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EstateValidationRule whereRuleDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EstateValidationRule whereRuleName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EstateValidationRule whereRuleType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EstateValidationRule whereSeverity($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EstateValidationRule whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EstateValidationRule whereUpdatedBy($value)
 *
 * @mixin \Eloquent
 */
class EstateValidationRule extends Model
{
    use \Illuminate\Database\Eloquent\Factories\HasFactory;

    protected $fillable = [
        'estate_id',
        'rule_name',
        'rule_type',
        'config',
        'is_active',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'config' => 'array',
        'is_active' => 'boolean',
    ];

    public function estate(): BelongsTo
    {
        return $this->belongsTo(Estate::class);
    }

    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updater(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByType($query, string $type)
    {
        return $query->where('rule_type', $type);
    }

    public function getConfigValue(string $key, $default = null)
    {
        return $this->config[$key] ?? $default;
    }

    public function setConfigValue(string $key, $value): void
    {
        $this->config = array_merge($this->config ?? [], [$key => $value]);
    }
}
