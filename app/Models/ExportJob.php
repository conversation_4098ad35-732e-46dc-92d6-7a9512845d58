<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $user_id
 * @property int|null $export_template_id
 * @property string $status
 * @property string $entity_type
 * @property string $format
 * @property array<array-key, mixed>|null $filters
 * @property string|null $columns
 * @property string|null $file_path
 * @property string|null $file_name
 * @property int $total_records
 * @property int $processed_records
 * @property int $failed_records
 * @property string $progress_percentage
 * @property \Illuminate\Support\Carbon|null $started_at
 * @property \Illuminate\Support\Carbon|null $completed_at
 * @property string|null $failed_at
 * @property string|null $error
 * @property string|null $error_details
 * @property string|null $download_url
 * @property string|null $expires_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\ExportTemplate|null $exportTemplate
 * @property-read \App\Models\User $user
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ExportJob completed()
 * @method static \Database\Factories\ExportJobFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ExportJob failed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ExportJob forUser($userId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ExportJob newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ExportJob newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ExportJob pending()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ExportJob processing()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ExportJob query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ExportJob whereColumns($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ExportJob whereCompletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ExportJob whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ExportJob whereDownloadUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ExportJob whereEntityType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ExportJob whereError($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ExportJob whereErrorDetails($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ExportJob whereExpiresAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ExportJob whereExportTemplateId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ExportJob whereFailedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ExportJob whereFailedRecords($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ExportJob whereFileName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ExportJob whereFilePath($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ExportJob whereFilters($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ExportJob whereFormat($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ExportJob whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ExportJob whereProcessedRecords($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ExportJob whereProgressPercentage($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ExportJob whereStartedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ExportJob whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ExportJob whereTotalRecords($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ExportJob whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ExportJob whereUserId($value)
 *
 * @mixin \Eloquent
 */
class ExportJob extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'export_template_id',
        'status',
        'entity_type',
        'format',
        'filters',
        'file_path',
        'file_name',
        'total_records',
        'started_at',
        'completed_at',
        'error',
    ];

    protected $casts = [
        'filters' => 'array',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function exportTemplate(): BelongsTo
    {
        return $this->belongsTo(ExportTemplate::class);
    }

    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeProcessing($query)
    {
        return $query->where('status', 'processing');
    }

    public function markAsStarted(): void
    {
        $this->update([
            'status' => 'processing',
            'started_at' => now(),
        ]);
    }

    public function markAsCompleted(string $filePath, string $fileName, int $totalRecords): void
    {
        $this->update([
            'status' => 'completed',
            'file_path' => $filePath,
            'file_name' => $fileName,
            'total_records' => $totalRecords,
            'completed_at' => now(),
        ]);
    }

    public function markAsFailed(string $error): void
    {
        $this->update([
            'status' => 'failed',
            'error' => $error,
            'completed_at' => now(),
        ]);
    }
}
