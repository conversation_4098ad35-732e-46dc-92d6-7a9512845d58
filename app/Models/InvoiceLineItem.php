<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $invoice_id
 * @property string $item_type
 * @property string $description
 * @property numeric $quantity
 * @property string $unit_price
 * @property numeric $amount
 * @property string $tax_rate
 * @property string $tax_amount
 * @property string $total_amount
 * @property string|null $notes
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read mixed $formatted_amount
 * @property-read mixed $formatted_quantity
 * @property-read mixed $formatted_rate
 * @property-read \App\Models\Invoice $invoice
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceLineItem newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceLineItem newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceLineItem query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceLineItem whereAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceLineItem whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceLineItem whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceLineItem whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceLineItem whereInvoiceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceLineItem whereItemType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceLineItem whereNotes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceLineItem whereQuantity($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceLineItem whereTaxAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceLineItem whereTaxRate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceLineItem whereTotalAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceLineItem whereUnitPrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceLineItem whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class InvoiceLineItem extends Model
{
    use HasFactory;

    const TYPE_CONSUMPTION = 'consumption';

    const TYPE_BASE_CHARGE = 'base_charge';

    const TYPE_TAX = 'tax';

    const TYPE_LATE_FEE = 'late_fee';

    const TYPE_ADJUSTMENT = 'adjustment';

    const TYPE_PREVIOUS_BALANCE = 'previous_balance';

    protected $fillable = [
        'invoice_id',
        'type',
        'description',
        'quantity',
        'rate',
        'amount',
        'tier',
        'metadata',
    ];

    protected $casts = [
        'quantity' => 'decimal:2',
        'rate' => 'decimal:4',
        'amount' => 'decimal:2',
        'metadata' => 'array',
    ];

    public function invoice(): BelongsTo
    {
        return $this->belongsTo(Invoice::class);
    }

    public function getFormattedAmountAttribute()
    {
        return 'KES '.number_format($this->amount, 2);
    }

    public function getFormattedRateAttribute()
    {
        return $this->rate ? 'KES '.number_format($this->rate, 4) : null;
    }

    public function getFormattedQuantityAttribute()
    {
        return $this->quantity ? number_format($this->quantity, 2).' units' : null;
    }
}
