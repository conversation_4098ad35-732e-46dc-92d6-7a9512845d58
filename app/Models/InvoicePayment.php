<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $invoice_id
 * @property int $account_transaction_id
 * @property numeric $amount
 * @property string $remaining_balance
 * @property string $payment_status
 * @property string $payment_method
 * @property string|null $payment_reference
 * @property string|null $receipt_number
 * @property \Illuminate\Support\Carbon $payment_date
 * @property string|null $notes
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read mixed $formatted_amount
 * @property-read \App\Models\Invoice $invoice
 * @property-read \App\Models\User|null $user
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoicePayment newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoicePayment newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoicePayment query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoicePayment whereAccountTransactionId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoicePayment whereAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoicePayment whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoicePayment whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoicePayment whereInvoiceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoicePayment whereNotes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoicePayment wherePaymentDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoicePayment wherePaymentMethod($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoicePayment wherePaymentReference($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoicePayment wherePaymentStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoicePayment whereReceiptNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoicePayment whereRemainingBalance($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoicePayment whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class InvoicePayment extends Model
{
    use HasFactory;

    const METHOD_BANK_TRANSFER = 'bank_transfer';

    const METHOD_MOBILE_MONEY = 'mobile_money';

    const METHOD_CASH = 'cash';

    const METHOD_CHECK = 'check';

    const METHOD_ONLINE = 'online';

    protected $fillable = [
        'invoice_id',
        'amount',
        'payment_date',
        'payment_method',
        'reference_number',
        'transaction_id',
        'notes',
        'user_id',
        'metadata',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'payment_date' => 'date',
        'metadata' => 'array',
    ];

    public function invoice(): BelongsTo
    {
        return $this->belongsTo(Invoice::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function getFormattedAmountAttribute()
    {
        return 'KES '.number_format($this->amount, 2);
    }

    public static function generateReferenceNumber(): string
    {
        return 'PAY-'.now()->format('Ymd').'-'.strtoupper(substr(uniqid(), -6));
    }
}
