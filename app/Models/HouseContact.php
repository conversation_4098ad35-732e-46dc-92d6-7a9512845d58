<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\Pivot;

/**
 * @property int $id
 * @property int $house_id
 * @property int $contact_id
 * @property \Illuminate\Support\Carbon $start_date
 * @property \Illuminate\Support\Carbon|null $end_date
 * @property string $relationship_type
 * @property int $is_current
 * @property string|null $notes
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property bool $is_active
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HouseContact newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HouseContact newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HouseContact query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HouseContact whereContactId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HouseContact whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HouseContact whereEndDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HouseContact whereHouseId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HouseContact whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HouseContact whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HouseContact whereIsCurrent($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HouseContact whereNotes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HouseContact whereRelationshipType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HouseContact whereStartDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HouseContact whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class HouseContact extends Pivot
{
    use \Illuminate\Database\Eloquent\Factories\HasFactory;

    protected $table = 'house_contacts';

    protected $fillable = [
        'house_id',
        'contact_id',
        'relationship_type',
        'start_date',
        'end_date',
        'is_active',
        'notes',
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'is_active' => 'boolean',
    ];
}
