<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property-read \App\Models\User|null $user
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PermissionAuditLog forAction($action)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PermissionAuditLog forTarget($targetType, $targetId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PermissionAuditLog forUser($userId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PermissionAuditLog latest()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PermissionAuditLog newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PermissionAuditLog newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PermissionAuditLog query()
 *
 * @mixin \Eloquent
 */
class PermissionAuditLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'action',
        'details',
        'target_type',
        'target_id',
        'permission_name',
        'target_user_id',
        'ip_address',
        'user_agent',
    ];

    protected $casts = [
        'details' => 'array',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeForAction($query, $action)
    {
        return $query->where('action', $action);
    }

    public function scopeForTarget($query, $targetType, $targetId)
    {
        return $query->where('target_type', $targetType)
            ->where('target_id', $targetId);
    }

    public function scopeLatest($query)
    {
        return $query->orderBy('created_at', 'desc');
    }
}
