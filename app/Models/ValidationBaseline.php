<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $estate_id
 * @property string $baseline_type
 * @property string $metric_name
 * @property string $baseline_value
 * @property string|null $upper_threshold
 * @property string|null $lower_threshold
 * @property string $calculation_method
 * @property string|null $calculation_config
 * @property string $effective_from
 * @property string|null $effective_to
 * @property int $is_active
 * @property string|null $notes
 * @property int $created_by
 * @property int|null $updated_by
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Estate $estate
 * @property-read float $iqr
 * @property-read float $lower_bound
 * @property-read float $upper_bound
 * @property-read \App\Models\House|null $house
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ValidationBaseline byEstate(int $estateId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ValidationBaseline byHouse(int $houseId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ValidationBaseline byType(string $type)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ValidationBaseline latest()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ValidationBaseline newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ValidationBaseline newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ValidationBaseline query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ValidationBaseline whereBaselineType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ValidationBaseline whereBaselineValue($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ValidationBaseline whereCalculationConfig($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ValidationBaseline whereCalculationMethod($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ValidationBaseline whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ValidationBaseline whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ValidationBaseline whereEffectiveFrom($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ValidationBaseline whereEffectiveTo($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ValidationBaseline whereEstateId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ValidationBaseline whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ValidationBaseline whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ValidationBaseline whereLowerThreshold($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ValidationBaseline whereMetricName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ValidationBaseline whereNotes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ValidationBaseline whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ValidationBaseline whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ValidationBaseline whereUpperThreshold($value)
 *
 * @mixin \Eloquent
 */
class ValidationBaseline extends Model
{
    use \Illuminate\Database\Eloquent\Factories\HasFactory;

    protected $fillable = [
        'estate_id',
        'house_id',
        'baseline_type',
        'average_consumption',
        'median_consumption',
        'std_deviation',
        'q1_consumption',
        'q3_consumption',
        'sample_size',
        'baseline_date',
        'monthly_averages',
    ];

    protected $casts = [
        'average_consumption' => 'decimal:2',
        'median_consumption' => 'decimal:2',
        'std_deviation' => 'decimal:2',
        'q1_consumption' => 'decimal:2',
        'q3_consumption' => 'decimal:2',
        'sample_size' => 'integer',
        'baseline_date' => 'date',
        'monthly_averages' => 'array',
    ];

    public function estate(): BelongsTo
    {
        return $this->belongsTo(Estate::class);
    }

    public function house(): BelongsTo
    {
        return $this->belongsTo(House::class);
    }

    public function scopeByType($query, string $type)
    {
        return $query->where('baseline_type', $type);
    }

    public function scopeByEstate($query, int $estateId)
    {
        return $query->where('estate_id', $estateId);
    }

    public function scopeByHouse($query, int $houseId)
    {
        return $query->where('house_id', $houseId);
    }

    public function scopeLatest($query)
    {
        return $query->orderBy('baseline_date', 'desc');
    }

    public function getIqrAttribute(): float
    {
        return $this->q3_consumption - $this->q1_consumption;
    }

    public function getLowerBoundAttribute(): float
    {
        return $this->q1_consumption - (1.5 * $this->getIqrAttribute());
    }

    public function getUpperBoundAttribute(): float
    {
        return $this->q3_consumption + (1.5 * $this->getIqrAttribute());
    }

    public function getMonthlyAverage(int $month): ?float
    {
        return $this->monthly_averages[$month] ?? null;
    }
}
