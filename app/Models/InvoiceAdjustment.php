<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $invoice_id
 * @property int $user_id
 * @property string $adjustment_type
 * @property string $reason
 * @property numeric $amount
 * @property string|null $percentage
 * @property string $status
 * @property int|null $approved_by
 * @property string|null $approved_at
 * @property string|null $approval_notes
 * @property string|null $notes
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read mixed $formatted_amount
 * @property-read \App\Models\Invoice $invoice
 * @property-read \App\Models\User $user
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceAdjustment newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceAdjustment newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceAdjustment query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceAdjustment whereAdjustmentType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceAdjustment whereAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceAdjustment whereApprovalNotes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceAdjustment whereApprovedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceAdjustment whereApprovedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceAdjustment whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceAdjustment whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceAdjustment whereInvoiceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceAdjustment whereNotes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceAdjustment wherePercentage($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceAdjustment whereReason($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceAdjustment whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceAdjustment whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceAdjustment whereUserId($value)
 *
 * @mixin \Eloquent
 */
class InvoiceAdjustment extends Model
{
    use HasFactory;

    const TYPE_CREDIT = 'credit';

    const TYPE_DEBIT = 'debit';

    const TYPE_REFUND = 'refund';

    const TYPE_CORRECTION = 'correction';

    protected $fillable = [
        'invoice_id',
        'type',
        'amount',
        'reason',
        'description',
        'user_id',
        'adjustment_date',
        'reference_number',
        'metadata',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'adjustment_date' => 'date',
        'metadata' => 'array',
    ];

    public function invoice(): BelongsTo
    {
        return $this->belongsTo(Invoice::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function getFormattedAmountAttribute()
    {
        $prefix = $this->type === self::TYPE_CREDIT || $this->type === self::TYPE_REFUND ? '-' : '+';

        return $prefix.' KES '.number_format(abs($this->amount), 2);
    }

    public static function generateReferenceNumber(): string
    {
        return 'ADJ-'.now()->format('Ymd').'-'.strtoupper(substr(uniqid(), -6));
    }
}
