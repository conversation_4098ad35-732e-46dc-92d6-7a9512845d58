<?php

namespace App\Jobs;

use App\Exports\DynamicExport;
use App\Models\ExportJob;
use App\Services\ReportExportService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;

class ProcessExportJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $deleteWhenMissingModels = true;

    public function __construct(
        public ExportJob $exportJob
    ) {
        $this->onQueue('exports');
    }

    public function handle(): void
    {
        try {
            $this->exportJob->markAsStarted();

            $reportExportService = new ReportExportService;
            $data = $this->getExportData($reportExportService);

            if ($data->isEmpty()) {
                $this->exportJob->markAsFailed('No data found for the specified filters');

                return;
            }

            $fileName = $this->generateFileName();
            $filePath = $this->saveExportFile($data, $fileName);

            $this->exportJob->markAsCompleted($filePath, $fileName, $data->count());

        } catch (\Exception $e) {
            $this->exportJob->markAsFailed($e->getMessage());
            throw $e;
        }
    }

    private function getExportData(ReportExportService $reportExportService)
    {
        $filters = $this->exportJob->filters ?? [];

        return match ($this->exportJob->entity_type) {
            'estates' => $reportExportService->getEstatesExportData($filters),
            'houses' => $reportExportService->getHousesExportData($filters),
            'contacts' => $reportExportService->getContactsExportData($filters),
            'meter_readings' => $reportExportService->getMeterReadingsExportData($filters),
            'invoices' => $reportExportService->getInvoicesExportData($filters),
            'water_rates' => $reportExportService->getWaterRatesExportData($filters),
            default => throw new \InvalidArgumentException("Unsupported export entity type: {$this->exportJob->entity_type}"),
        };
    }

    private function generateFileName(): string
    {
        $timestamp = now()->format('Y-m-d_H-i-s');
        $entityType = $this->exportJob->entity_type;
        $userId = $this->exportJob->user_id;

        return "exports/{$userId}/{$entityType}_{$timestamp}.{$this->exportJob->format}";
    }

    private function saveExportFile($data, string $fileName): string
    {
        $dynamicExport = new DynamicExport($data, $this->exportJob->entity_type, $this->exportJob->format);

        match ($this->exportJob->format) {
            'xlsx' => Excel::store($dynamicExport, $fileName, 'local', \Maatwebsite\Excel\Excel::XLSX),
            'csv' => Excel::store($dynamicExport, $fileName, 'local', \Maatwebsite\Excel\Excel::CSV),
            'pdf' => $this->generatePdf($data, $fileName),
            default => throw new \InvalidArgumentException("Unsupported export format: {$this->exportJob->format}"),
        };

        return $fileName;
    }

    private function generatePdf($data, string $fileName): string
    {
        $pdf = \PDF::loadView('exports.pdf', [
            'data' => $data,
            'entityType' => $this->exportJob->entity_type,
            'exportJob' => $this->exportJob,
        ]);

        Storage::disk('local')->put($fileName, $pdf->output());

        return $fileName;
    }
}
