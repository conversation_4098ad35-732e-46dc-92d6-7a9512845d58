<?php

declare(strict_types=1);

namespace App\Repositories;

use App\Models\User;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class UserRepository
{
    public function find(int $id): ?User
    {
        return User::find($id);
    }

    public function findByEmail(string $email): ?User
    {
        return User::where('email', $email)->first();
    }

    public function create(array $data): User
    {
        return User::create($data);
    }

    public function update(int $id, array $data): ?User
    {
        $user = $this->find($id);
        if ($user instanceof \App\Models\User) {
            $user->update($data);
        }

        return $user;
    }

    public function delete(int $id): bool
    {
        $user = $this->find($id);

        return $user instanceof \App\Models\User ? $user->delete() : false;
    }

    public function all(): Collection
    {
        return User::all();
    }

    public function where(array $conditions): Collection
    {
        return User::where($conditions)->get();
    }

    public function whereIn(string $column, array $values): Collection
    {
        return User::whereIn($column, $values)->get();
    }

    public function withCount(array $relations): Collection
    {
        return User::withCount($relations)->get();
    }

    public function with(array $relations): Collection
    {
        return User::with($relations)->get();
    }

    public function paginate(int $perPage = 15): LengthAwarePaginator
    {
        return User::paginate($perPage);
    }

    public function hasRole(int $userId, string $roleName): bool
    {
        $user = $this->find($userId);

        return $user && $user->hasRole($roleName);
    }

    public function assignRoles(int $userId, array $roles): void
    {
        $user = $this->find($userId);
        if ($user instanceof \App\Models\User) {
            $user->syncRoles($roles);
        }
    }

    public function assignPermissions(int $userId, array $permissions): void
    {
        $user = $this->find($userId);
        if ($user instanceof \App\Models\User) {
            $user->syncPermissions($permissions);
        }
    }

    public function getAssignedEstates(int $userId): Collection
    {
        $user = $this->find($userId);

        return $user instanceof \App\Models\User ? $user->assignedEstates()->get() : collect();
    }

    public function getSubordinates(int $userId): Collection
    {
        $user = $this->find($userId);

        return $user instanceof \App\Models\User ? $user->subordinates()->with('assignedEstates')->get() : collect();
    }

    public function getManagers(int $userId): Collection
    {
        $user = $this->find($userId);

        return $user instanceof \App\Models\User ? $user->managers()->get() : collect();
    }

    public function getContacts(int $userId): Collection
    {
        $user = $this->find($userId);

        return $user instanceof \App\Models\User ? $user->contacts()->get() : collect();
    }

    public function getAllPermissions(int $userId): Collection
    {
        $user = $this->find($userId);

        return $user instanceof \App\Models\User ? $user->getAllPermissions() : collect();
    }

    public function getRoleNames(int $userId): Collection
    {
        $user = $this->find($userId);

        return $user instanceof \App\Models\User ? $user->getRoleNames() : collect();
    }

    public function getAssignedEstateIds(int $userId): array
    {
        $user = $this->find($userId);

        return $user instanceof \App\Models\User ? $user->assignedEstates()->pluck('id')->toArray() : [];
    }
}
