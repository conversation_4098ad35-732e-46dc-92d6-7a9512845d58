<?php

declare(strict_types=1);

namespace App\Repositories;

use App\Models\PermissionAuditLog;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class PermissionAuditLogRepository
{
    public function find(int $id): ?PermissionAuditLog
    {
        return PermissionAuditLog::find($id);
    }

    public function create(array $data): PermissionAuditLog
    {
        return PermissionAuditLog::create($data);
    }

    public function update(int $id, array $data): ?PermissionAuditLog
    {
        $log = $this->find($id);
        if ($log instanceof \App\Models\PermissionAuditLog) {
            $log->update($data);
        }

        return $log;
    }

    public function delete(int $id): bool
    {
        $log = $this->find($id);

        return $log instanceof \App\Models\PermissionAuditLog ? $log->delete() : false;
    }

    public function all(): Collection
    {
        return PermissionAuditLog::all();
    }

    public function where(array $conditions): Collection
    {
        return PermissionAuditLog::where($conditions)->get();
    }

    public function whereIn(string $column, array $values): Collection
    {
        return PermissionAuditLog::whereIn($column, $values)->get();
    }

    public function with(array $relations): Collection
    {
        return PermissionAuditLog::with($relations)->get();
    }

    public function paginate(int $perPage = 15): LengthAwarePaginator
    {
        return PermissionAuditLog::paginate($perPage);
    }

    public function whereUserId(int $userId): Collection
    {
        return $this->where(['user_id' => $userId]);
    }

    public function whereAction(string $action): Collection
    {
        return $this->where(['action' => $action]);
    }

    public function whereTargetType(string $targetType): Collection
    {
        return $this->where(['target_type' => $targetType]);
    }

    public function whereTargetId(string $targetId): Collection
    {
        return $this->where(['target_id' => $targetId]);
    }

    public function whereDate(string $column, string $operator, string $value): Collection
    {
        return PermissionAuditLog::whereDate($column, $operator, $value)->get();
    }

    public function whereBetween(string $column, array $values): Collection
    {
        return PermissionAuditLog::whereBetween($column, $values)->get();
    }

    public function whereHas(string $relation, callable $callback): Collection
    {
        return PermissionAuditLog::whereHas($relation, $callback)->get();
    }

    public function orderBy(string $column, string $direction = 'desc'): Collection
    {
        return PermissionAuditLog::orderBy($column, $direction)->get();
    }

    public function latest(?int $limit = null): Collection
    {
        $query = PermissionAuditLog::latest();
        if ($limit) {
            $query->limit($limit);
        }

        return $query->get();
    }

    public function getAssignmentHistoryForUser(int $userId): Collection
    {
        return $this->whereTargetType('estate_assignment')
            ->where('target_id', 'like', "{$userId}_%")
            ->orderBy('created_at', 'desc')
            ->get();
    }

    public function getLogsByIp(string $ipAddress): Collection
    {
        return $this->where(['ip_address' => $ipAddress])->latest()->get();
    }

    public function getLogsByUserAgent(string $userAgent): Collection
    {
        return $this->where(['user_agent' => $userAgent])->latest()->get();
    }

    public function getLogsByDateRange(\DateTime $startDate, \DateTime $endDate): Collection
    {
        return $this->whereBetween('created_at', [$startDate->format('Y-m-d H:i:s'), $endDate->format('Y-m-d H:i:s')])->latest()->get();
    }

    public function getLogsByActionAndDateRange(string $action, \DateTime $startDate, \DateTime $endDate): Collection
    {
        return $this->where(['action' => $action])
            ->whereBetween('created_at', [$startDate->format('Y-m-d H:i:s'), $endDate->format('Y-m-d H:i:s')])
            ->latest()
            ->get();
    }
}
