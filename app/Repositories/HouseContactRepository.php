<?php

declare(strict_types=1);

namespace App\Repositories;

use App\Models\HouseContact;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class HouseContactRepository
{
    public function find(int $id): ?HouseContact
    {
        return HouseContact::find($id);
    }

    public function create(array $data): HouseContact
    {
        return HouseContact::create($data);
    }

    public function update(int $id, array $data): ?HouseContact
    {
        $contact = $this->find($id);
        if ($contact && $contact->update($data)) {
            return $contact;
        }

        return null; // Explicitly return null if update fails or contact not found
    }

    public function delete(int $id): bool
    {
        $contact = $this->find($id);

        return $contact instanceof \App\Models\HouseContact ? $contact->delete() : false;
    }

    public function all(): Collection
    {
        return HouseContact::all();
    }

    public function where(array $conditions): Collection
    {
        return HouseContact::where($conditions)->get();
    }

    public function whereIn(string $column, array $values): Collection
    {
        return HouseContact::whereIn($column, $values)->get();
    }

    public function with(array $relations): Collection
    {
        return HouseContact::with($relations)->get();
    }

    public function paginate(int $perPage = 15): LengthAwarePaginator
    {
        return HouseContact::paginate($perPage);
    }

    public function whereHouseId(int $houseId): Collection
    {
        return $this->where(['house_id' => $houseId]);
    }

    public function whereContactId(int $contactId): Collection
    {
        return $this->where(['contact_id' => $contactId]);
    }

    public function whereIsPrimary(bool $isPrimary = true): Collection
    {
        return $this->where(['is_primary' => $isPrimary]);
    }

    public function whereDate(string $column, string $operator, string $value): Collection
    {
        return HouseContact::whereDate($column, $operator, $value)->get();
    }

    public function whereBetween(string $column, array $values): Collection
    {
        return HouseContact::whereBetween($column, $values)->get();
    }

    public function whereHas(string $relation, callable $callback): Collection
    {
        return HouseContact::whereHas($relation, $callback)->get();
    }

    public function orderBy(string $column, string $direction = 'asc'): Collection
    {
        return HouseContact::orderBy($column, $direction)->get();
    }

    public function latest(?int $limit = null): Collection
    {
        $query = HouseContact::latest();
        if ($limit) {
            $query->limit($limit);
        }

        return $query->get();
    }

    public function getPrimaryContactByHouseId(int $houseId): ?HouseContact
    {
        return HouseContact::where('house_id', $houseId)
            ->where('is_primary', true)
            ->first();
    }

    public function getContactsByHouseId(int $houseId): Collection
    {
        return $this->whereHouseId($houseId)->orderBy('is_primary', 'desc')->get();
    }

    public function getHousesByContactId(int $contactId): Collection
    {
        return $this->whereContactId($contactId)->with('house')->get();
    }

    public function updateOrCreate(array $attributes, array $values): HouseContact
    {
        return HouseContact::updateOrCreate($attributes, $values);
    }

    public function setPrimaryContact(int $houseId, int $contactId): bool
    {
        // First, remove primary status from other contacts for this house
        $this->whereHouseId($houseId)->where('is_primary', true)->update(['is_primary' => false]);

        // Then set the new primary contact
        $contact = $this->find($contactId);
        if ($contact && $contact->house_id === $houseId) {
            return $contact->update(['is_primary' => true]);
        }

        return false;
    }

    public function getContactsByDateRange(\DateTime $startDate, \DateTime $endDate): Collection
    {
        return $this->whereBetween('created_at', [$startDate->format('Y-m-d H:i:s'), $endDate->format('Y-m-d H:i:s')])->latest()->get();
    }

    public function searchContacts(string $searchTerm, ?int $houseId = null): Collection
    {
        $query = HouseContact::query();

        if ($houseId) {
            $query->where('house_id', $houseId);
        }

        return $query->whereHas('contact', function ($q) use ($searchTerm): void {
            $q->where('name', 'like', '%'.$searchTerm.'%')
                ->orWhere('email', 'like', '%'.$searchTerm.'%')
                ->orWhere('phone', 'like', '%'.$searchTerm.'%');
        })->with('contact', 'house')->get();
    }
}
