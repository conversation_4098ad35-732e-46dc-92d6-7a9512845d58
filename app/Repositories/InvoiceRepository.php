<?php

namespace App\Repositories;

use App\Models\Invoice;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class InvoiceRepository extends BaseRepository
{
    public function __construct(Invoice $invoice)
    {
        parent::__construct($invoice);
    }

    /**
     * Get invoices by house
     */
    public function getInvoicesByHouse(int $houseId, array $columns = ['*']): Collection
    {
        return $this->query->where('house_id', $houseId)->get($columns);
    }

    /**
     * Get invoices by estate
     */
    public function getInvoicesByEstate(int $estateId, array $columns = ['*']): Collection
    {
        return $this->query->whereHas('house', function ($query) use ($estateId): void {
            $query->where('estate_id', $estateId);
        })->get($columns);
    }

    /**
     * Get invoices by status
     */
    public function getInvoicesByStatus(string $status, array $columns = ['*']): Collection
    {
        return $this->query->where('status', $status)->get($columns);
    }

    /**
     * Get invoices by date range
     */
    public function getInvoicesByDateRange(string $startDate, string $endDate, array $columns = ['*']): Collection
    {
        return $this->query->whereBetween('created_at', [$startDate, $endDate])->get($columns);
    }

    /**
     * Get invoices by billing period
     */
    public function getInvoicesByBillingPeriod(string $periodStart, string $periodEnd, array $columns = ['*']): Collection
    {
        return $this->query->where('period_start', '>=', $periodStart)
            ->where('period_end', '<=', $periodEnd)
            ->get($columns);
    }

    /**
     * Get overdue invoices
     */
    public function getOverdueInvoices(array $columns = ['*']): Collection
    {
        return $this->query->where('status', 'sent')
            ->where('due_date', '<', now())
            ->get($columns);
    }

    /**
     * Get pending invoices
     */
    public function getPendingInvoices(array $columns = ['*']): Collection
    {
        return $this->query->where('status', 'pending')->get($columns);
    }

    /**
     * Get paid invoices
     */
    public function getPaidInvoices(array $columns = ['*']): Collection
    {
        return $this->query->where('status', 'paid')->get($columns);
    }

    /**
     * Get invoices with house and estate details
     */
    public function getInvoicesWithDetails(array $columns = ['*']): Collection
    {
        return $this->query->with(['house.estate', 'waterRate'])->get($columns);
    }

    /**
     * Get invoice statistics
     */
    public function getStatistics(?int $estateId = null): array
    {
        $query = $this->query;

        if ($estateId) {
            $query->whereHas('house', function ($q) use ($estateId): void {
                $q->where('estate_id', $estateId);
            });
        }

        return [
            'total_invoices' => (clone $query)->count(),
            'total_amount' => (clone $query)->sum('amount'),
            'paid_invoices' => (clone $query)->where('status', 'paid')->count(),
            'paid_amount' => (clone $query)->where('status', 'paid')->sum('amount'),
            'pending_invoices' => (clone $query)->where('status', 'pending')->count(),
            'pending_amount' => (clone $query)->where('status', 'pending')->sum('amount'),
            'overdue_invoices' => (clone $query)->where('status', 'overdue')->count(),
            'overdue_amount' => (clone $query)->where('status', 'overdue')->sum('amount'),
            'sent_invoices' => (clone $query)->where('status', 'sent')->count(),
            'draft_invoices' => (clone $query)->where('status', 'draft')->count(),
        ];
    }

    /**
     * Get invoice statistics for date range
     */
    public function getStatisticsForDateRange(string $startDate, string $endDate, ?int $estateId = null): array
    {
        $builder = $this->query->whereBetween('created_at', [$startDate, $endDate]);

        if ($estateId) {
            $builder->whereHas('house', function ($q) use ($estateId): void {
                $q->where('estate_id', $estateId);
            });
        }

        return [
            'total_invoices' => (clone $builder)->count(),
            'total_amount' => (clone $builder)->sum('amount'),
            'paid_invoices' => (clone $builder)->where('status', 'paid')->count(),
            'paid_amount' => (clone $builder)->where('status', 'paid')->sum('amount'),
            'overdue_invoices' => (clone $builder)->where('status', 'overdue')->count(),
            'overdue_amount' => (clone $builder)->where('status', 'overdue')->sum('amount'),
        ];
    }

    /**
     * Search invoices by invoice number or house number
     */
    public function searchInvoices(string $searchTerm): Collection
    {
        return $this->query->where(function ($query) use ($searchTerm): void {
            $query->where('invoice_number', 'like', '%'.$searchTerm.'%')
                ->orWhereHas('house', function ($q) use ($searchTerm): void {
                    $q->where('house_number', 'like', '%'.$searchTerm.'%');
                });
        })->get();
    }

    /**
     * Get invoices with pagination and filters
     */
    public function getFilteredInvoices(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $builder = $this->query->with(['house.estate', 'waterRate']);

        // Apply estate filter
        if (! empty($filters['estate_id'])) {
            $builder->whereHas('house', function ($q) use ($filters): void {
                $q->where('estate_id', $filters['estate_id']);
            });
        }

        // Apply house filter
        if (! empty($filters['house_id'])) {
            $builder->where('house_id', $filters['house_id']);
        }

        // Apply status filter
        if (! empty($filters['status'])) {
            $builder->where('status', $filters['status']);
        }

        // Apply date range filters
        if (! empty($filters['date_from'])) {
            $builder->where('period_start', '>=', $filters['date_from']);
        }

        if (! empty($filters['date_to'])) {
            $builder->where('period_end', '<=', $filters['date_to']);
        }

        // Apply search filter
        if (! empty($filters['search'])) {
            $builder->where(function ($q) use ($filters): void {
                $q->where('invoice_number', 'like', '%'.$filters['search'].'%')
                    ->orWhereHas('house', function ($q) use ($filters): void {
                        $q->where('house_number', 'like', '%'.$filters['search'].'%');
                    });
            });
        }

        // Apply sorting
        $sortBy = $filters['sort_by'] ?? 'created_at';
        $sortDirection = $filters['sort_direction'] ?? 'desc';
        $builder->orderBy($sortBy, $sortDirection);

        return $builder->paginate($perPage);
    }

    /**
     * Get invoices for a specific user based on permissions
     */
    public function getInvoicesForUser(int $userId, bool $canManageAll = false, ?int $estateId = null): Collection
    {
        $query = $this->query;

        if (! $canManageAll) {
            $query->whereHas('house.estate.users', function ($query) use ($userId): void {
                $query->where('users.id', $userId);
            });
        }

        if ($estateId) {
            $query->whereHas('house', function ($q) use ($estateId): void {
                $q->where('estate_id', $estateId);
            });
        }

        return $query->get();
    }

    /**
     * Get invoices due within days
     */
    public function getInvoicesDueWithinDays(int $days): Collection
    {
        return $this->query->where('status', 'sent')
            ->whereBetween('due_date', [now(), now()->addDays($days)])
            ->get();
    }

    /**
     * Get invoices with payments
     */
    public function getInvoicesWithPayments(): Collection
    {
        return $this->query->with(['payments'])->whereHas('payments')->get();
    }

    /**
     * Get invoices without payments
     */
    public function getInvoicesWithoutPayments(): Collection
    {
        return $this->query->whereDoesntHave('payments')->get();
    }

    /**
     * Get latest invoice for house
     */
    public function getLatestInvoiceForHouse(int $houseId): ?Invoice
    {
        return $this->query->where('house_id', $houseId)
            ->orderBy('created_at', 'desc')
            ->first();
    }

    /**
     * Get unpaid invoices for house
     */
    public function getUnpaidInvoicesForHouse(int $houseId): Collection
    {
        return $this->query->where('house_id', $houseId)
            ->whereIn('status', ['pending', 'sent', 'overdue'])
            ->get();
    }

    /**
     * Bulk update invoice status
     */
    public function bulkUpdateStatus(array $invoiceIds, string $status): int
    {
        $data = ['status' => $status];

        if ($status === 'sent') {
            $data['sent_at'] = now();
        } elseif ($status === 'paid') {
            $data['paid_at'] = now();
        }

        return $this->query->whereIn('id', $invoiceIds)->update($data);
    }

    /**
     * Get invoices for reporting
     */
    public function getInvoicesForReport(array $filters = []): Collection
    {
        $builder = $this->query->with(['house.estate', 'waterRate']);

        // Apply date filter
        if (! empty($filters['date_from']) && ! empty($filters['date_to'])) {
            $startDate = Carbon::parse($filters['date_from']);
            $endDate = Carbon::parse($filters['date_to']);
            $builder->whereBetween('created_at', [$startDate, $endDate]);
        }

        // Apply status filter
        if (! empty($filters['status']) && $filters['status'] !== 'all') {
            if ($filters['status'] === 'overdue') {
                $builder->where('status', 'sent')->where('due_date', '<', now());
            } else {
                $builder->where('status', $filters['status']);
            }
        }

        // Apply estate filter
        if (! empty($filters['estate_id'])) {
            $builder->whereHas('house.estate', function ($q) use ($filters): void {
                $q->where('id', $filters['estate_id']);
            });
        }

        return $builder->orderBy('created_at', 'desc')->get();
    }

    /**
     * Get invoice aging report
     */
    public function getInvoiceAgingReport(?int $estateId = null): array
    {
        $builder = $this->query->where('status', '!=', 'paid');

        if ($estateId) {
            $builder->whereHas('house', function ($q) use ($estateId): void {
                $q->where('estate_id', $estateId);
            });
        }

        $currentDate = now();

        return [
            'current' => (clone $builder)->where('due_date', '>=', $currentDate)->sum('amount'),
            '1_30_days' => (clone $builder)->where('due_date', '<', $currentDate)
                ->where('due_date', '>=', $currentDate->subDays(30))
                ->sum('amount'),
            '31_60_days' => (clone $builder)->where('due_date', '<', $currentDate->subDays(30))
                ->where('due_date', '>=', $currentDate->subDays(60))
                ->sum('amount'),
            '61_90_days' => (clone $builder)->where('due_date', '<', $currentDate->subDays(60))
                ->where('due_date', '>=', $currentDate->subDays(90))
                ->sum('amount'),
            'over_90_days' => (clone $builder)->where('due_date', '<', $currentDate->subDays(90))->sum('amount'),
        ];
    }

    /**
     * Check if invoice can be deleted
     */
    public function canDeleteInvoice(int $invoiceId): bool
    {
        $model = $this->findOrFail($invoiceId);

        return ! $model->payments()->exists() && ! $model->sent_at;
    }

    /**
     * Get count of payments for an invoice
     */
    public function getPaymentCount(int $invoiceId): int
    {
        return $this->findOrFail($invoiceId)->payments()->count();
    }
}
