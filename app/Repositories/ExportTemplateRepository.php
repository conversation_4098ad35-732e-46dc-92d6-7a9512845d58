<?php

declare(strict_types=1);

namespace App\Repositories;

use App\Models\ExportTemplate;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class ExportTemplateRepository
{
    public function find(int $id): ?ExportTemplate
    {
        return ExportTemplate::find($id);
    }

    public function create(array $data): ExportTemplate
    {
        return ExportTemplate::create($data);
    }

    public function update(int $id, array $data): ?ExportTemplate
    {
        $template = $this->find($id);
        if ($template instanceof \App\Models\ExportTemplate) {
            $template->update($data);
        }

        return $template;
    }

    public function delete(int $id): bool
    {
        $template = $this->find($id);

        return $template instanceof \App\Models\ExportTemplate ? $template->delete() : false;
    }

    public function all(): Collection
    {
        return ExportTemplate::all();
    }

    public function where(array $conditions): Collection
    {
        return ExportTemplate::where($conditions)->get();
    }

    public function whereIn(string $column, array $values): Collection
    {
        return ExportTemplate::whereIn($column, $values)->get();
    }

    public function with(array $relations): Collection
    {
        return ExportTemplate::with($relations)->get();
    }

    public function paginate(int $perPage = 15): LengthAwarePaginator
    {
        return ExportTemplate::paginate($perPage);
    }

    public function whereName(string $name): Collection
    {
        return $this->where(['name' => $name]);
    }

    public function whereType(string $type): Collection
    {
        return $this->where(['type' => $type]);
    }

    public function whereIsActive(bool $isActive = true): Collection
    {
        return $this->where(['is_active' => $isActive]);
    }

    public function whereDate(string $column, string $operator, string $value): Collection
    {
        return ExportTemplate::whereDate($column, $operator, $value)->get();
    }

    public function whereBetween(string $column, array $values): Collection
    {
        return ExportTemplate::whereBetween($column, $values)->get();
    }

    public function whereHas(string $relation, callable $callback): Collection
    {
        return ExportTemplate::whereHas($relation, $callback)->get();
    }

    public function orderBy(string $column, string $direction = 'asc'): Collection
    {
        return ExportTemplate::orderBy($column, $direction)->get();
    }

    public function latest(?int $limit = null): Collection
    {
        $query = ExportTemplate::latest();
        if ($limit) {
            $query->limit($limit);
        }

        return $query->get();
    }

    public function getActiveTemplates(): Collection
    {
        return $this->whereIsActive(true)->orderBy('name')->get();
    }

    public function getTemplateByName(string $name): ?ExportTemplate
    {
        return ExportTemplate::where('name', $name)->first();
    }

    public function updateOrCreateByName(string $name, array $data): ExportTemplate
    {
        return ExportTemplate::updateOrCreate(
            ['name' => $name],
            $data
        );
    }

    public function deactivateTemplate(int $id): bool
    {
        $template = $this->find($id);
        if ($template instanceof \App\Models\ExportTemplate) {
            return $template->update(['is_active' => false]);
        }

        return false;
    }

    public function activateTemplate(int $id): bool
    {
        $template = $this->find($id);
        if ($template instanceof \App\Models\ExportTemplate) {
            return $template->update(['is_active' => true]);
        }

        return false;
    }

    public function getTemplatesByDateRange(\DateTime $startDate, \DateTime $endDate): Collection
    {
        return $this->whereBetween('created_at', [$startDate->format('Y-m-d H:i:s'), $endDate->format('Y-m-d H:i:s')])->latest()->get();
    }

    public function searchTemplates(string $searchTerm): Collection
    {
        return ExportTemplate::where(function ($q) use ($searchTerm): void {
            $q->where('name', 'like', '%'.$searchTerm.'%')
                ->orWhere('description', 'like', '%'.$searchTerm.'%');
        })->orderBy('name')->get();
    }
}
