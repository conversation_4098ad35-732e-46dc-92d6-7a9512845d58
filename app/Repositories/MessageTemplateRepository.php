<?php

declare(strict_types=1);

namespace App\Repositories;

use App\Models\MessageTemplate;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class MessageTemplateRepository
{
    public function find(int $id): ?MessageTemplate
    {
        return MessageTemplate::find($id);
    }

    public function findByName(string $name): ?MessageTemplate
    {
        return MessageTemplate::where('name', $name)->first();
    }

    public function create(array $data): MessageTemplate
    {
        return MessageTemplate::create($data);
    }

    public function update(int $id, array $data): ?MessageTemplate
    {
        $template = $this->find($id);
        if ($template instanceof \App\Models\MessageTemplate) {
            $template->update($data);
        }

        return $template;
    }

    public function delete(int $id): bool
    {
        $template = $this->find($id);

        return $template instanceof \App\Models\MessageTemplate ? $template->delete() : false;
    }

    public function all(): Collection
    {
        return MessageTemplate::all();
    }

    public function where(array $conditions): Collection
    {
        return MessageTemplate::where($conditions)->get();
    }

    public function whereIn(string $column, array $values): Collection
    {
        return MessageTemplate::whereIn($column, $values)->get();
    }

    public function with(array $relations): Collection
    {
        return MessageTemplate::with($relations)->get();
    }

    public function paginate(int $perPage = 15): LengthAwarePaginator
    {
        return MessageTemplate::paginate($perPage);
    }

    public function whereName(string $name): Collection
    {
        return $this->where(['name' => $name]);
    }

    public function whereType(string $type): Collection
    {
        return $this->where(['type' => $type]);
    }

    public function whereIsActive(bool $isActive = true): Collection
    {
        return $this->where(['is_active' => $isActive]);
    }

    public function whereDate(string $column, string $operator, string $value): Collection
    {
        return MessageTemplate::whereDate($column, $operator, $value)->get();
    }

    public function whereBetween(string $column, array $values): Collection
    {
        return MessageTemplate::whereBetween($column, $values)->get();
    }

    public function whereHas(string $relation, callable $callback): Collection
    {
        return MessageTemplate::whereHas($relation, $callback)->get();
    }

    public function orderBy(string $column, string $direction = 'asc'): Collection
    {
        return MessageTemplate::orderBy($column, $direction)->get();
    }

    public function latest(?int $limit = null): Collection
    {
        $query = MessageTemplate::latest();
        if ($limit) {
            $query->limit($limit);
        }

        return $query->get();
    }

    public function getActiveTemplatesByType(string $type): Collection
    {
        return $this->whereType($type)->whereIsActive(true)->orderBy('name')->get();
    }

    public function getTemplateByNameAndType(string $name, string $type): ?MessageTemplate
    {
        return MessageTemplate::where('name', $name)
            ->where('type', $type)
            ->where('is_active', true)
            ->first();
    }

    public function updateOrCreateByName(string $name, array $data): MessageTemplate
    {
        return MessageTemplate::updateOrCreate(
            ['name' => $name, 'type' => $data['type'] ?? 'whatsapp'],
            $data
        );
    }

    public function deactivateTemplate(int $id): bool
    {
        $template = $this->find($id);
        if ($template instanceof \App\Models\MessageTemplate) {
            return $template->update(['is_active' => false]);
        }

        return false;
    }

    public function activateTemplate(int $id): bool
    {
        $template = $this->find($id);
        if ($template instanceof \App\Models\MessageTemplate) {
            return $template->update(['is_active' => true]);
        }

        return false;
    }

    public function getTemplatesByDateRange(\DateTime $startDate, \DateTime $endDate): Collection
    {
        return $this->whereBetween('created_at', [$startDate->format('Y-m-d H:i:s'), $endDate->format('Y-m-d H:i:s')])->latest()->get();
    }

    public function searchTemplates(string $searchTerm, ?string $type = null): Collection
    {
        $query = MessageTemplate::query();

        if ($type) {
            $query->where('type', $type);
        }

        return $query->where(function ($q) use ($searchTerm): void {
            $q->where('name', 'like', '%'.$searchTerm.'%')
                ->orWhere('content', 'like', '%'.$searchTerm.'%')
                ->orWhere('subject', 'like', '%'.$searchTerm.'%');
        })->orderBy('name')->get();
    }
}
