<?php

declare(strict_types=1);

namespace App\Repositories;

use App\Models\AccountTransaction;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class AccountTransactionRepository
{
    public function find(int $id): ?AccountTransaction
    {
        return AccountTransaction::find($id);
    }

    public function create(array $data): AccountTransaction
    {
        return AccountTransaction::create($data);
    }

    public function update(int $id, array $data): ?AccountTransaction
    {
        $transaction = $this->find($id);
        if ($transaction instanceof \App\Models\AccountTransaction) {
            $transaction->update($data);
        }

        return $transaction;
    }

    public function delete(int $id): bool
    {
        $transaction = $this->find($id);

        return $transaction instanceof \App\Models\AccountTransaction ? $transaction->delete() : false;
    }

    public function all(): Collection
    {
        return AccountTransaction::all();
    }

    public function where(array $conditions): Collection
    {
        return AccountTransaction::where($conditions)->get();
    }

    public function whereIn(string $column, array $values): Collection
    {
        return AccountTransaction::whereIn($column, $values)->get();
    }

    public function with(array $relations): Collection
    {
        return AccountTransaction::with($relations)->get();
    }

    public function paginate(int $perPage = 15): LengthAwarePaginator
    {
        return AccountTransaction::paginate($perPage);
    }

    public function whereDate(string $column, string $operator, string $value): Collection
    {
        return AccountTransaction::whereDate($column, $operator, $value)->get();
    }

    public function whereBetween(string $column, array $values): Collection
    {
        return AccountTransaction::whereBetween($column, $values)->get();
    }

    public function whereHas(string $relation, callable $callback): Collection
    {
        return AccountTransaction::whereHas($relation, $callback)->get();
    }

    public function whereHouseAccountId(int $houseAccountId): Collection
    {
        return $this->where(['house_account_id' => $houseAccountId]);
    }

    public function whereDateCreatedBefore(\DateTime $date): Collection
    {
        return $this->whereDate('created_at', '<', $date->format('Y-m-d'));
    }

    public function whereDateCreatedAfter(\DateTime $date): Collection
    {
        return $this->whereDate('created_at', '>', $date->format('Y-m-d'));
    }

    public function whereDateCreatedBetween(\DateTime $startDate, \DateTime $endDate): Collection
    {
        return $this->whereBetween('created_at', [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')]);
    }

    public function sumByHouseAccountId(int $houseAccountId, string $column = 'amount'): float
    {
        return AccountTransaction::where('house_account_id', $houseAccountId)->sum($column);
    }

    public function sumByHouseAccountIdAndDateRange(int $houseAccountId, \DateTime $startDate, \DateTime $endDate, string $column = 'amount'): float
    {
        return AccountTransaction::where('house_account_id', $houseAccountId)
            ->whereBetween('created_at', [$startDate->format('Y-m-d H:i:s'), $endDate->format('Y-m-d H:i:s')])
            ->sum($column);
    }

    public function getCreditsByHouseAccountId(int $houseAccountId): Collection
    {
        return $this->whereHouseAccountId($houseAccountId)->filter(fn ($transaction) => in_array($transaction->transaction_type, ['payment', 'credit_note', 'adjustment']));
    }

    public function getDebitsByHouseAccountId(int $houseAccountId): Collection
    {
        return $this->whereHouseAccountId($houseAccountId)->filter(fn ($transaction) => $transaction->transaction_type == 'invoice');
    }

    public function orderBy(string $column, string $direction = 'asc'): Collection
    {
        return AccountTransaction::orderBy($column, $direction)->get();
    }

    public function latest(?int $limit = null): Collection
    {
        $query = AccountTransaction::latest();
        if ($limit) {
            $query->limit($limit);
        }

        return $query->get();
    }
}
