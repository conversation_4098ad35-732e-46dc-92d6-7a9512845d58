<?php

namespace App\Repositories;

use App\Models\WaterRate;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class WaterRateRepository extends BaseRepository
{
    public function __construct(WaterRate $waterRate)
    {
        parent::__construct($waterRate);
    }

    /**
     * Get active water rates
     */
    public function getActiveRates(array $columns = ['*']): Collection
    {
        return $this->query->where('is_active', true)->get($columns);
    }

    /**
     * Get water rates by estate ID
     */
    public function getByEstateId(int $estateId, array $columns = ['*']): Collection
    {
        return $this->query->where('estate_id', $estateId)->get($columns);
    }

    /**
     * Get current water rates for a specific date
     */
    public function getCurrentRates(string $date, array $columns = ['*']): Collection
    {
        return $this->query->where('effective_from', '<=', $date)
            ->where(function ($query) use ($date): void {
                $query->whereNull('effective_to')
                    ->orWhere('effective_to', '>=', $date);
            })
            ->get($columns);
    }

    /**
     * Get water rates by name
     */
    public function getByName(string $name, array $columns = ['*']): Collection
    {
        return $this->query->where('name', 'like', '%'.$name.'%')->get($columns);
    }

    /**
     * Get water rates by effective date range
     */
    public function getByEffectiveDateRange(string $startDate, string $endDate, array $columns = ['*']): Collection
    {
        return $this->query->whereBetween('effective_from', [$startDate, $endDate])->get($columns);
    }

    /**
     * Get expired water rates
     */
    public function getExpiredRates(array $columns = ['*']): Collection
    {
        return $this->query->whereNotNull('effective_to')
            ->where('effective_to', '<', now())
            ->get($columns);
    }

    /**
     * Get water rates with estate relationship
     */
    public function getWithEstate(array $columns = ['*']): Collection
    {
        return $this->query->with('estate')->get($columns);
    }

    /**
     * Get water rates by rate per unit
     */
    public function getByRatePerUnit(float $rate, array $columns = ['*']): Collection
    {
        return $this->query->where('rate_per_unit', $rate)->get($columns);
    }

    /**
     * Get water rates by minimum charge
     */
    public function getByMinimumCharge(float $minimumCharge, array $columns = ['*']): Collection
    {
        return $this->query->where('minimum_charge', $minimumCharge)->get($columns);
    }

    /**
     * Get water rates by fixed charge
     */
    public function getByFixedCharge(float $fixedCharge, array $columns = ['*']): Collection
    {
        return $this->query->where('fixed_charge', $fixedCharge)->get($columns);
    }

    /**
     * Get water rates by minimum units
     */
    public function getByMinimumUnits(float $minimumUnits, array $columns = ['*']): Collection
    {
        return $this->query->where('minimum_units', $minimumUnits)->get($columns);
    }

    /**
     * Get water rates with pagination
     */
    public function getPaginated(int $perPage = 15, array $columns = ['*']): LengthAwarePaginator
    {
        return $this->query->paginate($perPage, $columns);
    }

    /**
     * Search water rates
     */
    public function search(string $search, array $searchableFields = ['name', 'description']): Collection
    {
        return $this->query->where(function ($query) use ($search, $searchableFields): void {
            foreach ($searchableFields as $searchableField) {
                $query->orWhere($searchableField, 'like', '%'.$search.'%');
            }
        })->get();
    }

    /**
     * Get water rates with filters
     */
    public function getWithFilters(array $filters): Collection
    {
        $query = $this->query;

        if (isset($filters['estate_id'])) {
            $query->where('estate_id', $filters['estate_id']);
        }

        if (isset($filters['is_active'])) {
            $query->where('is_active', $filters['is_active']);
        }

        if (isset($filters['name'])) {
            $query->where('name', 'like', '%'.$filters['name'].'%');
        }

        if (isset($filters['description'])) {
            $query->where('description', 'like', '%'.$filters['description'].'%');
        }

        if (isset($filters['rate_per_unit_min'])) {
            $query->where('rate_per_unit', '>=', $filters['rate_per_unit_min']);
        }

        if (isset($filters['rate_per_unit_max'])) {
            $query->where('rate_per_unit', '<=', $filters['rate_per_unit_max']);
        }

        if (isset($filters['effective_from'])) {
            $query->where('effective_from', '>=', $filters['effective_from']);
        }

        if (isset($filters['effective_to'])) {
            $query->where('effective_to', '<=', $filters['effective_to']);
        }

        if (isset($filters['date'])) {
            $query->where('effective_from', '<=', $filters['date'])
                ->where(function ($q) use ($filters): void {
                    $q->whereNull('effective_to')
                        ->orWhere('effective_to', '>=', $filters['date']);
                });
        }

        return $query->get();
    }

    /**
     * Get the latest water rate for an estate
     */
    public function getLatestForEstate(int $estateId): ?WaterRate
    {
        return $this->query->where('estate_id', $estateId)
            ->where('is_active', true)
            ->orderBy('effective_from', 'desc')
            ->first();
    }

    /**
     * Get water rates that are effective on a specific date
     */
    public function getEffectiveOnDate(string $date, int $estateId): Collection
    {
        return $this->query->where('estate_id', $estateId)
            ->where('effective_from', '<=', $date)
            ->where(function ($query) use ($date): void {
                $query->whereNull('effective_to')
                    ->orWhere('effective_to', '>=', $date);
            })
            ->orderBy('effective_from', 'desc')
            ->get();
    }

    /**
     * Get water rates by status
     */
    public function getByStatus(bool $isActive, array $columns = ['*']): Collection
    {
        return $this->query->where('is_active', $isActive)->get($columns);
    }

    /**
     * Get water rates with sorting
     */
    public function getWithSorting(string $sortBy = 'name', string $sortOrder = 'asc', array $columns = ['*']): Collection
    {
        return $this->query->orderBy($sortBy, $sortOrder)->get($columns);
    }
}
