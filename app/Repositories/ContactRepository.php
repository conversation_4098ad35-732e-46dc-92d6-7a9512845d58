<?php

namespace App\Repositories;

use App\Models\Contact;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class ContactRepository extends BaseRepository
{
    public function __construct(Contact $contact)
    {
        parent::__construct($contact);
    }

    /**
     * Get contacts by house
     */
    public function getContactsByHouse(int $houseId, array $columns = ['*']): Collection
    {
        return $this->query->where('house_id', $houseId)->get($columns);
    }

    /**
     * Get primary contacts
     */
    public function getPrimaryContacts(array $columns = ['*']): Collection
    {
        return $this->query->where('is_primary', true)->get($columns);
    }

    /**
     * Get primary contact for house
     */
    public function getPrimaryContactForHouse(int $houseId): ?Contact
    {
        return $this->query->where('house_id', $houseId)
            ->where('is_primary', true)
            ->first();
    }

    /**
     * Get contacts by relationship type
     */
    public function getContactsByRelationship(string $relationship, array $columns = ['*']): Collection
    {
        return $this->query->where('relationship', $relationship)->get($columns);
    }

    /**
     * Get active contacts
     */
    public function getActiveContacts(array $columns = ['*']): Collection
    {
        return $this->query->where('is_active', true)->get($columns);
    }

    /**
     * Get inactive contacts
     */
    public function getInactiveContacts(array $columns = ['*']): Collection
    {
        return $this->query->where('is_active', false)->get($columns);
    }

    /**
     * Get contacts by estate
     */
    public function getContactsByEstate(int $estateId, array $columns = ['*']): Collection
    {
        return $this->query->whereHas('house', function ($query) use ($estateId): void {
            $query->where('estate_id', $estateId);
        })->get($columns);
    }

    /**
     * Get contacts with house and estate details
     */
    public function getContactsWithDetails(array $columns = ['*']): Collection
    {
        return $this->query->with(['house.estate'])->get($columns);
    }

    /**
     * Search contacts by name, email, or phone
     */
    public function searchContacts(string $searchTerm): Collection
    {
        return $this->query->where(function ($query) use ($searchTerm): void {
            $query->where('first_name', 'like', '%'.$searchTerm.'%')
                ->orWhere('last_name', 'like', '%'.$searchTerm.'%')
                ->orWhere('email', 'like', '%'.$searchTerm.'%')
                ->orWhere('phone', 'like', '%'.$searchTerm.'%')
                ->orWhere('alternative_phone', 'like', '%'.$searchTerm.'%');
        })->get();
    }

    /**
     * Get contact statistics
     */
    public function getStatistics(?int $estateId = null): array
    {
        $query = $this->query;

        if ($estateId) {
            $query->whereHas('house', function ($q) use ($estateId): void {
                $q->where('estate_id', $estateId);
            });
        }

        return [
            'total_contacts' => (clone $query)->count(),
            'primary_contacts' => (clone $query)->where('is_primary', true)->count(),
            'active_contacts' => (clone $query)->where('is_active', true)->count(),
            'inactive_contacts' => (clone $query)->where('is_active', false)->count(),
            'owner_contacts' => (clone $query)->where('relationship', 'owner')->count(),
            'tenant_contacts' => (clone $query)->where('relationship', 'tenant')->count(),
            'relative_contacts' => (clone $query)->where('relationship', 'relative')->count(),
        ];
    }

    /**
     * Get contacts with pagination and filters
     */
    public function getFilteredContacts(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $builder = $this->query->with(['house.estate']);

        // Apply estate filter
        if (! empty($filters['estate_id'])) {
            $builder->whereHas('house', function ($q) use ($filters): void {
                $q->where('estate_id', $filters['estate_id']);
            });
        }

        // Apply house filter
        if (! empty($filters['house_id'])) {
            $builder->where('house_id', $filters['house_id']);
        }

        // Apply relationship filter
        if (! empty($filters['relationship'])) {
            $builder->where('relationship', $filters['relationship']);
        }

        // Apply primary contact filter
        if (isset($filters['is_primary'])) {
            $builder->where('is_primary', $filters['is_primary']);
        }

        // Apply active status filter
        if (isset($filters['is_active'])) {
            $builder->where('is_active', $filters['is_active']);
        }

        // Apply search filter
        if (! empty($filters['search'])) {
            $builder->where(function ($q) use ($filters): void {
                $q->where('first_name', 'like', '%'.$filters['search'].'%')
                    ->orWhere('last_name', 'like', '%'.$filters['search'].'%')
                    ->orWhere('email', 'like', '%'.$filters['search'].'%')
                    ->orWhere('phone', 'like', '%'.$filters['search'].'%')
                    ->orWhereHas('house', function ($q) use ($filters): void {
                        $q->where('house_number', 'like', '%'.$filters['search'].'%');
                    });
            });
        }

        // Apply sorting
        $sortBy = $filters['sort_by'] ?? 'last_name';
        $sortDirection = $filters['sort_direction'] ?? 'asc';
        $builder->orderBy($sortBy, $sortDirection);

        return $builder->paginate($perPage);
    }

    /**
     * Get contacts for a specific user based on permissions
     */
    public function getContactsForUser(int $userId, bool $canManageAll = false, ?int $estateId = null): Collection
    {
        $query = $this->query;

        if (! $canManageAll) {
            $query->whereHas('house.estate.users', function ($query) use ($userId): void {
                $query->where('users.id', $userId);
            });
        }

        if ($estateId) {
            $query->whereHas('house', function ($q) use ($estateId): void {
                $q->where('estate_id', $estateId);
            });
        }

        return $query->get();
    }

    /**
     * Get contacts by email
     */
    public function getContactsByEmail(string $email): Collection
    {
        return $this->findAllBy('email', $email);
    }

    /**
     * Get contacts by phone
     */
    public function getContactsByPhone(string $phone): Collection
    {
        return $this->query->where(function ($query) use ($phone): void {
            $query->where('phone', $phone)->orWhere('alternative_phone', $phone);
        })->get();
    }

    /**
     * Get contacts with invoices
     */
    public function getContactsWithInvoices(): Collection
    {
        return $this->query->whereHas('invoices')->get();
    }

    /**
     * Get contacts without invoices
     */
    public function getContactsWithoutInvoices(): Collection
    {
        return $this->query->whereDoesntHave('invoices')->get();
    }

    /**
     * Set primary contact for house
     */
    public function setPrimaryContact(int $houseId, int $contactId): bool
    {
        // Remove primary status from all contacts for this house
        $this->query->where('house_id', $houseId)->update(['is_primary' => false]);

        // Set the new primary contact
        return $this->query->where('id', $contactId)->update(['is_primary' => true]);
    }

    /**
     * Bulk update contact status
     */
    public function bulkUpdateActiveStatus(array $contactIds, bool $isActive): int
    {
        return $this->query->whereIn('id', $contactIds)->update(['is_active' => $isActive]);
    }

    /**
     * Bulk update primary status
     */
    public function bulkUpdatePrimaryStatus(array $contactIds, bool $isPrimary): int
    {
        return $this->query->whereIn('id', $contactIds)->update(['is_primary' => $isPrimary]);
    }

    /**
     * Get contact full names
     */
    public function getContactFullNames(): Collection
    {
        return $this->query->selectRaw('id, CONCAT(first_name, " ", last_name) as full_name')->get();
    }

    /**
     * Get contacts for WhatsApp notifications
     */
    public function getContactsForWhatsApp(?int $estateId = null): Collection
    {
        $builder = $this->query->where('is_active', true)
            ->whereNotNull('phone')
            ->where('phone', '!=', '');

        if ($estateId) {
            $builder->whereHas('house', function ($q) use ($estateId): void {
                $q->where('estate_id', $estateId);
            });
        }

        return $builder->get();
    }

    /**
     * Get contacts by relationship and estate
     */
    public function getContactsByRelationshipAndEstate(string $relationship, int $estateId): Collection
    {
        return $this->query->where('relationship', $relationship)
            ->whereHas('house', function ($q) use ($estateId): void {
                $q->where('estate_id', $estateId);
            })->get();
    }

    /**
     * Check if contact can be deleted
     */
    public function canDeleteContact(int $contactId): bool
    {
        $model = $this->findOrFail($contactId);

        return ! $model->invoices()->exists();
    }

    /**
     * Get contacts created in date range
     */
    public function getContactsCreatedBetween(string $startDate, string $endDate, ?int $estateId = null): Collection
    {
        $builder = $this->query->whereBetween('created_at', [$startDate, $endDate]);

        if ($estateId) {
            $builder->whereHas('house', function ($q) use ($estateId): void {
                $q->where('estate_id', $estateId);
            });
        }

        return $builder->get();
    }

    /**
     * Get contacts with overdue payments
     */
    public function getContactsWithOverduePayments(?int $estateId = null): Collection
    {
        $builder = $this->query->whereHas('invoices', function ($q): void {
            $q->where('status', 'overdue');
        });

        if ($estateId) {
            $builder->whereHas('house', function ($q) use ($estateId): void {
                $q->where('estate_id', $estateId);
            });
        }

        return $builder->get();
    }

    /**
     * Check if an email is unique across all contacts
     */
    public function isEmailUnique(?string $email, ?int $excludeId = null): bool
    {
        if ($email === null || $email === '' || $email === '0') {
            return true;
        }

        $builder = $this->query->where('email', $email);

        if ($excludeId) {
            $builder->where('id', '!=', $excludeId);
        }

        return ! $builder->exists();
    }

    /**
     * Check if an ID number is unique across all contacts
     */
    public function isIdNumberUnique(?string $idNumber, ?int $excludeId = null): bool
    {
        if ($idNumber === null || $idNumber === '' || $idNumber === '0') {
            return true;
        }

        $builder = $this->query->where('id_number', $idNumber);

        if ($excludeId) {
            $builder->where('id', '!=', $excludeId);
        }

        return ! $builder->exists();
    }

    /**
     * Get count of houses for a contact
     */
    public function getHouseCount(int $contactId): int
    {
        return $this->findOrFail($contactId)->houses()->count();
    }
}
