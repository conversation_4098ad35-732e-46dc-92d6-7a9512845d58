<?php

declare(strict_types=1);

namespace App\Repositories;

use App\Models\WhatsAppMessage;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class WhatsAppMessageRepository
{
    public function find(int $id): ?WhatsAppMessage
    {
        return WhatsAppMessage::find($id);
    }

    public function create(array $data): WhatsAppMessage
    {
        return WhatsAppMessage::create($data);
    }

    public function update(int $id, array $data): ?WhatsAppMessage
    {
        $message = $this->find($id);
        if ($message instanceof \App\Models\WhatsAppMessage) {
            $message->update($data);
        }

        return $message;
    }

    public function delete(int $id): bool
    {
        $message = $this->find($id);

        return $message instanceof \App\Models\WhatsAppMessage ? $message->delete() : false;
    }

    public function all(): Collection
    {
        return WhatsAppMessage::all();
    }

    public function where(array $conditions): Collection
    {
        return WhatsAppMessage::where($conditions)->get();
    }

    public function whereIn(string $column, array $values): Collection
    {
        return WhatsAppMessage::whereIn($column, $values)->get();
    }

    public function with(array $relations): Collection
    {
        return WhatsAppMessage::with($relations)->get();
    }

    public function paginate(int $perPage = 15): LengthAwarePaginator
    {
        return WhatsAppMessage::paginate($perPage);
    }

    public function whereRecipient(string $recipient): Collection
    {
        return $this->where(['recipient' => $recipient]);
    }

    public function whereStatus(string $status): Collection
    {
        return $this->where(['status' => $status]);
    }

    public function whereMessageType(string $messageType): Collection
    {
        return $this->where(['message_type' => $messageType]);
    }

    public function whereContactId(int $contactId): Collection
    {
        return $this->where(['recipient_contact_id' => $contactId]);
    }

    public function whereHouseId(int $houseId): Collection
    {
        return $this->where(['house_id' => $houseId]);
    }

    public function whereEstateId(int $estateId): Collection
    {
        return $this->where(['estate_id' => $estateId]);
    }

    public function whereMessageable(string $type, int $id): Collection
    {
        return $this->where([
            'messageable_type' => $type,
            'messageable_id' => $id,
        ]);
    }

    public function whereDate(string $column, string $operator, string $value): Collection
    {
        return WhatsAppMessage::whereDate($column, $operator, $value)->get();
    }

    public function whereBetween(string $column, array $values): Collection
    {
        return WhatsAppMessage::whereBetween($column, $values)->get();
    }

    public function whereHas(string $relation, callable $callback): Collection
    {
        return WhatsAppMessage::whereHas($relation, $callback)->get();
    }

    public function orderBy(string $column, string $direction = 'desc'): Collection
    {
        return WhatsAppMessage::orderBy($column, $direction)->get();
    }

    public function latest(?int $limit = null): Collection
    {
        $query = WhatsAppMessage::latest();
        if ($limit) {
            $query->limit($limit);
        }

        return $query->get();
    }

    public function getSentMessages(): Collection
    {
        return $this->whereStatus('sent');
    }

    public function getFailedMessages(): Collection
    {
        return $this->whereStatus('failed');
    }

    public function getPendingMessages(): Collection
    {
        return $this->whereStatus('pending');
    }

    public function getMessagesByContact(int $contactId): Collection
    {
        return $this->whereContactId($contactId)->latest()->get();
    }

    public function getMessagesByHouse(int $houseId): Collection
    {
        return $this->whereHouseId($houseId)->latest()->get();
    }

    public function getMessagesByEstate(int $estateId): Collection
    {
        return $this->whereEstateId($estateId)->latest()->get();
    }

    public function getMessagesByDateRange(\DateTime $startDate, \DateTime $endDate): Collection
    {
        return $this->whereBetween('created_at', [$startDate->format('Y-m-d H:i:s'), $endDate->format('Y-m-d H:i:s')])->latest()->get();
    }

    public function getMessagesByTypeAndDateRange(string $messageType, \DateTime $startDate, \DateTime $endDate): Collection
    {
        return $this->where(['message_type' => $messageType])
            ->whereBetween('created_at', [$startDate->format('Y-m-d H:i:s'), $endDate->format('Y-m-d H:i:s')])
            ->latest()
            ->get();
    }

    public function markAsSent(int $id, array $apiResponse = []): ?WhatsAppMessage
    {
        return $this->update($id, [
            'status' => 'sent',
            'message_id' => $apiResponse['messages'][0]['id'] ?? null,
            'api_response' => $apiResponse,
            'sent_at' => now(),
        ]);
    }

    public function markAsFailed(int $id, string $errorMessage): ?WhatsAppMessage
    {
        return $this->update($id, [
            'status' => 'failed',
            'error_message' => $errorMessage,
            'failed_at' => now(),
        ]);
    }

    public function markAsPending(int $id): ?WhatsAppMessage
    {
        return $this->update($id, [
            'status' => 'pending',
            'queued_at' => now(),
        ]);
    }
}
