<?php

namespace App\Repositories;

use App\Models\MeterReading;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class MeterReadingRepository extends BaseRepository
{
    public function __construct(MeterReading $meterReading)
    {
        parent::__construct($meterReading);
    }

    /**
     * Get meter readings by house ID
     */
    public function getByHouseId(int $houseId, array $columns = ['*']): Collection
    {
        return $this->query->where('house_id', $houseId)->get($columns);
    }

    /**
     * Get meter readings by status
     */
    public function getByStatus(string $status, array $columns = ['*']): Collection
    {
        return $this->query->where('status', $status)->get($columns);
    }

    /**
     * Get meter readings by date range
     */
    public function getByDateRange(string $startDate, string $endDate, array $columns = ['*']): Collection
    {
        return $this->query->whereBetween('reading_date', [$startDate, $endDate])->get($columns);
    }

    /**
     * Get meter readings by estate ID
     */
    public function getByEstateId(int $estateId, array $columns = ['*']): Collection
    {
        return $this->query->whereHas('house', function ($query) use ($estateId): void {
            $query->where('estate_id', $estateId);
        })->get($columns);
    }

    /**
     * Get pending meter readings
     */
    public function getPendingReadings(array $columns = ['*']): Collection
    {
        return $this->query->where('status', 'pending')->get($columns);
    }

    /**
     * Get approved meter readings
     */
    public function getApprovedReadings(array $columns = ['*']): Collection
    {
        return $this->query->where('status', 'approved')->get($columns);
    }

    /**
     * Get meter readings with user and reviewer relationships
     */
    public function getWithRelationships(array $columns = ['*']): Collection
    {
        return $this->query->with(['user', 'reviewer', 'house.estate'])->get($columns);
    }

    /**
     * Get meter readings by user ID
     */
    public function getByUserId(int $userId, array $columns = ['*']): Collection
    {
        return $this->query->where('user_id', $userId)->get($columns);
    }

    /**
     * Get meter readings by reviewer ID
     */
    public function getByReviewerId(int $reviewerId, array $columns = ['*']): Collection
    {
        return $this->query->where('reviewer_id', $reviewerId)->get($columns);
    }

    /**
     * Get meter readings with consumption greater than specified value
     */
    public function getWithConsumptionGreaterThan(float $consumption, array $columns = ['*']): Collection
    {
        return $this->query->where('consumption', '>', $consumption)->get($columns);
    }

    /**
     * Get meter readings with consumption less than specified value
     */
    public function getWithConsumptionLessThan(float $consumption, array $columns = ['*']): Collection
    {
        return $this->query->where('consumption', '<', $consumption)->get($columns);
    }

    /**
     * Get meter readings by reading date
     */
    public function getByReadingDate(string $readingDate, array $columns = ['*']): Collection
    {
        return $this->query->whereDate('reading_date', $readingDate)->get($columns);
    }

    /**
     * Get meter readings by billing period
     */
    public function getByBillingPeriod(string $startDate, string $endDate, array $columns = ['*']): Collection
    {
        return $this->query->whereBetween('reading_date', [$startDate, $endDate])->get($columns);
    }

    /**
     * Get meter readings with pagination
     */
    public function getPaginated(int $perPage = 15, array $columns = ['*']): LengthAwarePaginator
    {
        return $this->query->paginate($perPage, $columns);
    }

    /**
     * Search meter readings
     */
    public function search(string $search, array $searchableFields = ['house_number', 'current_reading', 'previous_reading']): Collection
    {
        return $this->query->whereHas('house', function ($query) use ($search, $searchableFields): void {
            $query->where(function ($q) use ($search, $searchableFields): void {
                foreach ($searchableFields as $searchableField) {
                    if ($searchableField === 'house_number') {
                        $q->orWhere('house_number', 'like', '%'.$search.'%');
                    } else {
                        $q->orWhere($searchableField, 'like', '%'.$search.'%');
                    }
                }
            });
        })->get();
    }

    /**
     * Get meter readings with filters
     */
    public function getWithFilters(array $filters): Collection
    {
        $query = $this->query;

        if (isset($filters['estate_id'])) {
            $query->whereHas('house', function ($q) use ($filters): void {
                $q->where('estate_id', $filters['estate_id']);
            });
        }

        if (isset($filters['house_id'])) {
            $query->where('house_id', $filters['house_id']);
        }

        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (isset($filters['user_id'])) {
            $query->where('user_id', $filters['user_id']);
        }

        if (isset($filters['reviewer_id'])) {
            $query->where('reviewer_id', $filters['reviewer_id']);
        }

        if (isset($filters['date_from'])) {
            $query->whereDate('reading_date', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->whereDate('reading_date', '<=', $filters['date_to']);
        }

        if (isset($filters['consumption_min'])) {
            $query->where('consumption', '>=', $filters['consumption_min']);
        }

        if (isset($filters['consumption_max'])) {
            $query->where('consumption', '<=', $filters['consumption_max']);
        }

        return $query->get();
    }
}
