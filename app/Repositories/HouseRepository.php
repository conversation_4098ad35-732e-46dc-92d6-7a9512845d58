<?php

namespace App\Repositories;

use App\Models\House;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class HouseRepository extends BaseRepository
{
    public function __construct(House $house)
    {
        parent::__construct($house);
    }

    /**
     * Get houses by estate
     */
    public function getHousesByEstate(int $estateId, array $columns = ['*']): Collection
    {
        return $this->query->where('estate_id', $estateId)->get($columns);
    }

    /**
     * Get houses by estate with pagination
     */
    public function getHousesByEstatePaginated(int $estateId, int $perPage = 15): LengthAwarePaginator
    {
        return $this->query->where('estate_id', $estateId)->paginate($perPage);
    }

    /**
     * Get house by number within estate
     */
    public function findByHouseNumber(int $estateId, string $houseNumber): ?House
    {
        return $this->query->where('estate_id', $estateId)
            ->where('house_number', $houseNumber)
            ->first();
    }

    /**
     * Get occupied houses
     */
    public function getOccupiedHouses(array $columns = ['*']): Collection
    {
        return $this->query->where('occupancy_status', 'occupied')->get($columns);
    }

    /**
     * Get vacant houses
     */
    public function getVacantHouses(array $columns = ['*']): Collection
    {
        return $this->query->where('occupancy_status', 'vacant')->get($columns);
    }

    /**
     * Get houses by occupancy status
     */
    public function getHousesByOccupancyStatus(string $status, array $columns = ['*']): Collection
    {
        return $this->query->where('occupancy_status', $status)->get($columns);
    }

    /**
     * Get houses with contacts
     */
    public function getHousesWithContacts(?int $estateId = null): Collection
    {
        $query = $this->query->with(['contacts' => function ($query): void {
            $query->where('is_primary', true);
        }]);

        if ($estateId) {
            $query->where('estate_id', $estateId);
        }

        return $query->get();
    }

    /**
     * Get houses with meter readings
     */
    public function getHousesWithLatestReadings(?int $estateId = null): Collection
    {
        $builder = $this->query->with(['latestMeterReading']);

        if ($estateId) {
            $builder->where('estate_id', $estateId);
        }

        return $builder->get();
    }

    /**
     * Get houses with latest invoices
     */
    public function getHousesWithLatestInvoices(?int $estateId = null): Collection
    {
        $builder = $this->query->with(['latestInvoice']);

        if ($estateId) {
            $builder->where('estate_id', $estateId);
        }

        return $builder->get();
    }

    /**
     * Get house statistics
     */
    public function getStatistics(?int $estateId = null): array
    {
        $query = $this->query;

        if ($estateId) {
            $query->where('estate_id', $estateId);
        }

        return [
            'total_houses' => (clone $query)->count(),
            'occupied_houses' => (clone $query)->where('occupancy_status', 'occupied')->count(),
            'vacant_houses' => (clone $query)->where('occupancy_status', 'vacant')->count(),
            'maintenance_houses' => (clone $query)->where('occupancy_status', 'maintenance')->count(),
            'active_houses' => (clone $query)->where('is_active', true)->count(),
            'inactive_houses' => (clone $query)->where('is_active', false)->count(),
        ];
    }

    /**
     * Search houses by house number or address
     */
    public function searchHouses(string $searchTerm, ?int $estateId = null): Collection
    {
        $query = $this->query->where(function ($query) use ($searchTerm): void {
            $query->where('house_number', 'like', '%'.$searchTerm.'%')
                ->orWhere('address', 'like', '%'.$searchTerm.'%');
        });

        if ($estateId) {
            $query->where('estate_id', $estateId);
        }

        return $query->get();
    }

    /**
     * Get houses by unit type
     */
    public function getHousesByUnitType(string $unitType, ?int $estateId = null): Collection
    {
        $builder = $this->query->where('unit_type', $unitType);

        if ($estateId) {
            $builder->where('estate_id', $estateId);
        }

        return $builder->get();
    }

    /**
     * Get houses with overdue invoices
     */
    public function getHousesWithOverdueInvoices(?int $estateId = null): Collection
    {
        $query = $this->query->whereHas('invoices', function ($query): void {
            $query->where('status', 'overdue');
        });

        if ($estateId) {
            $query->where('estate_id', $estateId);
        }

        return $query->get();
    }

    /**
     * Get houses with pending invoices
     */
    public function getHousesWithPendingInvoices(?int $estateId = null): Collection
    {
        $query = $this->query->whereHas('invoices', function ($query): void {
            $query->where('status', 'pending');
        });

        if ($estateId) {
            $query->where('estate_id', $estateId);
        }

        return $query->get();
    }

    /**
     * Check if house can be deleted
     */
    public function canDeleteHouse(int $houseId): bool
    {
        $model = $this->findOrFail($houseId);

        return ! $model->meterReadings()->exists() &&
               ! $model->invoices()->exists() &&
               ! $model->contacts()->exists();
    }

    /**
     * Get houses with pagination and filters
     */
    public function getFilteredHouses(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = $this->query->with(['estate', 'contacts' => function ($query): void {
            $query->where('is_primary', true);
        }]);

        // Apply estate filter
        if (! empty($filters['estate_id'])) {
            $query->where('estate_id', $filters['estate_id']);
        }

        // Apply search filter
        if (! empty($filters['search'])) {
            $query->where(function ($q) use ($filters): void {
                $q->where('house_number', 'like', '%'.$filters['search'].'%')
                    ->orWhere('address', 'like', '%'.$filters['search'].'%');
            });
        }

        // Apply occupancy status filter
        if (! empty($filters['occupancy_status'])) {
            $query->where('occupancy_status', $filters['occupancy_status']);
        }

        // Apply unit type filter
        if (! empty($filters['unit_type'])) {
            $query->where('unit_type', $filters['unit_type']);
        }

        // Apply active status filter
        if (isset($filters['is_active'])) {
            $query->where('is_active', $filters['is_active']);
        }

        // Apply sorting
        $sortBy = $filters['sort_by'] ?? 'house_number';
        $sortDirection = $filters['sort_direction'] ?? 'asc';
        $query->orderBy($sortBy, $sortDirection);

        return $query->paginate($perPage);
    }

    /**
     * Get houses for a specific user based on permissions
     */
    public function getHousesForUser(int $userId, bool $canManageAll = false, ?int $estateId = null): Collection
    {
        $query = $this->query;

        if (! $canManageAll) {
            $query->whereHas('estate.users', function ($query) use ($userId): void {
                $query->where('users.id', $userId);
            });
        }

        if ($estateId) {
            $query->where('estate_id', $estateId);
        }

        return $query->get();
    }

    /**
     * Get houses with account balances
     */
    public function getHousesWithAccountBalances(?int $estateId = null): Collection
    {
        $builder = $this->query->with(['houseAccount']);

        if ($estateId) {
            $builder->where('estate_id', $estateId);
        }

        return $builder->get();
    }

    /**
     * Bulk update house occupancy status
     */
    public function bulkUpdateOccupancyStatus(array $houseIds, string $status): int
    {
        return $this->query->whereIn('id', $houseIds)->update(['occupancy_status' => $status]);
    }

    /**
     * Bulk update house active status
     */
    public function bulkUpdateActiveStatus(array $houseIds, bool $isActive): int
    {
        return $this->query->whereIn('id', $houseIds)->update(['is_active' => $isActive]);
    }

    /**
     * Get houses by contact
     */
    public function getHousesByContact(int $contactId): Collection
    {
        return $this->query->whereHas('contacts', function ($query) use ($contactId): void {
            $query->where('contacts.id', $contactId);
        })->get();
    }

    /**
     * Get houses created in date range
     */
    public function getHousesCreatedBetween(string $startDate, string $endDate, ?int $estateId = null): Collection
    {
        $builder = $this->query->whereBetween('created_at', [$startDate, $endDate]);

        if ($estateId) {
            $builder->where('estate_id', $estateId);
        }

        return $builder->get();
    }

    /**
     * Check if a house number is unique within an estate
     */
    public function isHouseNumberUnique(string $houseNumber, int $estateId, ?int $excludeId = null): bool
    {
        $builder = $this->query->where('house_number', $houseNumber)
            ->where('estate_id', $estateId);

        if ($excludeId) {
            $builder->where('id', '!=', $excludeId);
        }

        return ! $builder->exists();
    }

    /**
     * Check if a meter number is unique across all houses
     */
    public function isMeterNumberUnique(?string $meterNumber, ?int $excludeId = null): bool
    {
        if ($meterNumber === null || $meterNumber === '' || $meterNumber === '0') {
            return true;
        }

        $builder = $this->query->where('meter_number', $meterNumber);

        if ($excludeId) {
            $builder->where('id', '!=', $excludeId);
        }

        return ! $builder->exists();
    }

    /**
     * Check if a house-contact assignment is unique
     */
    public function isHouseContactUnique(int $houseId, int $contactId, ?int $excludeId = null): bool
    {
        $query = $this->model->contacts()->where('contacts.id', $contactId);

        if ($excludeId) {
            $query->wherePivot('id', '!=', $excludeId);
        }

        return ! $query->exists();
    }

    /**
     * Get the count of primary contacts for a house
     */
    public function getPrimaryContactCount(int $houseId, ?int $excludeId = null): int
    {
        $query = $this->model->contacts()->wherePivot('is_primary', true);

        if ($excludeId) {
            $query->wherePivot('id', '!=', $excludeId);
        }

        return $query->count();
    }

    /**
     * Check if a house can be set to a specific occupancy status
     */
    public function canSetOccupancyStatus(int $houseId, string $newStatus): bool
    {
        $house = $this->find($houseId);

        if (! $house instanceof \Illuminate\Database\Eloquent\Model) {
            return false;
        }

        // Cannot mark as occupied if no primary contact
        if ($newStatus === 'occupied') {
            return $this->model->contacts()
                ->wherePivot('is_primary', true)
                ->whereNull('end_date')
                ->exists();
        }

        return true;
    }

    /**
     * Check if a contact has overlapping assignments for a specific house
     */
    public function hasOverlappingContactAssignment(
        int $contactId,
        int $houseId,
        string $startDate,
        ?string $endDate = null,
        ?int $excludeId = null
    ): bool {
        $query = $this->model->contacts()->where('contacts.id', $contactId)
            ->wherePivot('house_id', $houseId);

        if ($excludeId) {
            $query->wherePivot('id', '!=', $excludeId);
        }

        // Check for any existing assignment that overlaps with the new dates
        return $query->where(function ($q) use ($startDate, $endDate): void {
            if ($endDate) {
                // New assignment has both start and end dates
                $q->where(function ($subQuery) use ($startDate, $endDate): void {
                    // Existing assignment starts before new assignment ends
                    $subQuery->where('start_date', '<=', $endDate)
                        ->where(function ($dateQuery) use ($startDate): void {
                            // And existing assignment ends after new assignment starts
                            $dateQuery->whereNull('end_date')
                                ->orWhere('end_date', '>=', $startDate);
                        });
                    // OR existing assignment completely encompasses new assignment
                    $subQuery->orWhere(function ($encompassQuery) use ($startDate, $endDate): void {
                        $encompassQuery->where('start_date', '<=', $startDate)
                            ->where(function ($endQuery) use ($endDate): void {
                                $endQuery->whereNull('end_date')
                                    ->orWhere('end_date', '>=', $endDate);
                            });
                    });
                });
            } else {
                // New assignment has only start date (open-ended)
                $q->where(function ($subQuery) use ($startDate): void {
                    // Existing assignment starts before new assignment starts
                    $subQuery->where('start_date', '<=', $startDate)
                        ->where(function ($dateQuery) use ($startDate): void {
                            // And existing assignment is ongoing or ends after new assignment starts
                            $dateQuery->whereNull('end_date')
                                ->orWhere('end_date', '>=', $startDate);
                        });
                });
            }
        })->exists();
    }

    /**
     * Get count of contacts for a house
     */
    public function getContactCount(int $houseId): int
    {
        return $this->findOrFail($houseId)->contacts()->count();
    }
}
