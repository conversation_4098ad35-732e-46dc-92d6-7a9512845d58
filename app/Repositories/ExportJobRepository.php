<?php

declare(strict_types=1);

namespace App\Repositories;

use App\Models\ExportJob;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class ExportJobRepository
{
    public function find(int $id): ?ExportJob
    {
        return ExportJob::find($id);
    }

    public function create(array $data): ExportJob
    {
        return ExportJob::create($data);
    }

    public function update(int $id, array $data): ?ExportJob
    {
        $job = $this->find($id);
        if ($job instanceof \App\Models\ExportJob) {
            $job->update($data);
        }

        return $job;
    }

    public function delete(int $id): bool
    {
        $job = $this->find($id);

        return $job instanceof \App\Models\ExportJob ? $job->delete() : false;
    }

    public function all(): Collection
    {
        return ExportJob::all();
    }

    public function where(array $conditions): Collection
    {
        return ExportJob::where($conditions)->get();
    }

    public function whereIn(string $column, array $values): Collection
    {
        return ExportJob::whereIn($column, $values)->get();
    }

    public function with(array $relations): Collection
    {
        return ExportJob::with($relations)->get();
    }

    public function paginate(int $perPage = 15): LengthAwarePaginator
    {
        return ExportJob::paginate($perPage);
    }

    public function whereUserId(int $userId): Collection
    {
        return $this->where(['user_id' => $userId]);
    }

    public function whereType(string $type): Collection
    {
        return $this->where(['type' => $type]);
    }

    public function whereStatus(string $status): Collection
    {
        return $this->where(['status' => $status]);
    }

    public function whereDate(string $column, string $operator, string $value): Collection
    {
        return ExportJob::whereDate($column, $operator, $value)->get();
    }

    public function whereBetween(string $column, array $values): Collection
    {
        return ExportJob::whereBetween($column, $values)->get();
    }

    public function whereHas(string $relation, callable $callback): Collection
    {
        return ExportJob::whereHas($relation, $callback)->get();
    }

    public function orderBy(string $column, string $direction = 'desc'): Collection
    {
        return ExportJob::orderBy($column, $direction)->get();
    }

    public function latest(?int $limit = null): Collection
    {
        $query = ExportJob::latest();
        if ($limit) {
            $query->limit($limit);
        }

        return $query->get();
    }

    public function getUserJobs(int $userId): Collection
    {
        return $this->whereUserId($userId)->orderBy('created_at', 'desc')->get();
    }

    public function getPendingJobs(): Collection
    {
        return $this->whereStatus('pending')->latest()->get();
    }

    public function getProcessingJobs(): Collection
    {
        return $this->whereStatus('processing')->latest()->get();
    }

    public function getCompletedJobs(): Collection
    {
        return $this->whereStatus('completed')->latest()->get();
    }

    public function getFailedJobs(): Collection
    {
        return $this->whereStatus('failed')->latest()->get();
    }

    public function getJobsByDateRange(\DateTime $startDate, \DateTime $endDate): Collection
    {
        return $this->whereBetween('created_at', [$startDate->format('Y-m-d H:i:s'), $endDate->format('Y-m-d H:i:s')])->latest()->get();
    }

    public function getJobsByTypeAndDateRange(string $type, \DateTime $startDate, \DateTime $endDate): Collection
    {
        return $this->where(['type' => $type])
            ->whereBetween('created_at', [$startDate->format('Y-m-d H:i:s'), $endDate->format('Y-m-d H:i:s')])
            ->latest()
            ->get();
    }

    public function getJobsByStatusAndDateRange(string $status, \DateTime $startDate, \DateTime $endDate): Collection
    {
        return $this->where(['status' => $status])
            ->whereBetween('created_at', [$startDate->format('Y-m-d H:i:s'), $endDate->format('Y-m-d H:i:s')])
            ->latest()
            ->get();
    }

    public function markAsProcessing(int $id): ?ExportJob
    {
        return $this->update($id, [
            'status' => 'processing',
            'started_at' => now(),
        ]);
    }

    public function markAsCompleted(int $id, ?string $filePath = null): ?ExportJob
    {
        return $this->update($id, [
            'status' => 'completed',
            'file_path' => $filePath,
            'completed_at' => now(),
        ]);
    }

    public function markAsFailed(int $id, string $errorMessage): ?ExportJob
    {
        return $this->update($id, [
            'status' => 'failed',
            'error_message' => $errorMessage,
            'failed_at' => now(),
        ]);
    }

    public function getOldCompletedJobs(int $daysToKeep = 30): Collection
    {
        $cutoffDate = now()->subDays($daysToKeep);

        return $this->whereStatus('completed')
            ->whereDate('created_at', '<', $cutoffDate->format('Y-m-d'))
            ->latest()
            ->get();
    }
}
