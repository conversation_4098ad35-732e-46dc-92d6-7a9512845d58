<?php

namespace App\Repositories;

use App\Models\Estate;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class EstateRepository extends BaseRepository
{
    public function __construct(Estate $estate)
    {
        parent::__construct($estate);
    }

    /**
     * Get all estates
     */
    public function getAllEstates(): Collection
    {
        return $this->all();
    }

    /**
     * Get active estates
     */
    public function getActiveEstates(array $columns = ['*']): Collection
    {
        return $this->query->where('is_active', true)->get($columns);
    }

    /**
     * Get estates with house counts
     */
    public function getEstatesWithHouseCounts(): Collection
    {
        return $this->query->withCount(['houses', 'houses as occupied_houses_count' => function ($query): void {
            $query->whereHas('contacts');
        }])->get();
    }

    /**
     * Get estate by code
     */
    public function findByCode(string $code): ?Estate
    {
        return $this->findBy('code', $code);
    }

    /**
     * Get estates for a specific user based on permissions
     */
    public function getEstatesForUser(int $userId, bool $canManageAll = false): Collection
    {
        if ($canManageAll) {
            return $this->all();
        }

        return $this->query->whereHas('users', function ($query) use ($userId): void {
            $query->where('users.id', $userId);
        })->get();
    }

    /**
     * Get estate statistics
     */
    public function getStatistics(): array
    {
        return [
            'total_estates' => $this->count(),
            'active_estates' => $this->query->where('is_active', true)->count(),
            'inactive_estates' => $this->query->where('is_active', false)->count(),
            'total_houses' => $this->query->withCount('houses')->get()->sum('houses_count'),
            'occupied_houses' => $this->query->withCount(['houses as occupied_count' => function ($query): void {
                $query->whereHas('contacts');
            }])->get()->sum('occupied_count'),
        ];
    }

    /**
     * Search estates by name or code
     */
    public function search(string $searchTerm): Collection
    {
        return $this->query->where(function ($query) use ($searchTerm): void {
            $query->where('name', 'like', '%'.$searchTerm.'%')
                ->orWhere('code', 'like', '%'.$searchTerm.'%');
        })->get();
    }

    /**
     * Get estates with water rates
     */
    public function getEstatesWithWaterRates(): Collection
    {
        return $this->query->with(['waterRates' => function ($query): void {
            $query->where('is_active', true)->orderBy('effective_from', 'desc');
        }])->get();
    }

    /**
     * Get estate with all related data
     */
    public function getEstateWithRelations(int $estateId): ?Estate
    {
        return $this->query->with([
            'houses' => function ($query): void {
                $query->withCount('contacts')
                    ->with(['contacts' => function ($contactQuery): void {
                        $contactQuery->where('is_primary', true);
                    }]);
            },
            'waterRates' => function ($query): void {
                $query->orderBy('effective_from', 'desc');
            },
            'users',
        ])->find($estateId);
    }

    /**
     * Check if estate can be deleted
     */
    public function canDeleteEstate(int $estateId): bool
    {
        $model = $this->findOrFail($estateId);

        return ! $model->houses()->exists() && ! $model->waterRates()->exists();
    }

    /**
     * Get estates by city
     */
    public function getEstatesByCity(string $city): Collection
    {
        return $this->findAllBy('city', $city);
    }

    /**
     * Get estates by status
     */
    public function getEstatesByStatus(bool $isActive): Collection
    {
        return $this->findAllBy('is_active', $isActive);
    }

    /**
     * Get estates with pagination and filters
     */
    public function getFilteredEstates(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = $this->query;

        // Apply search filter
        if (! empty($filters['search'])) {
            $query->where(function ($q) use ($filters): void {
                $q->where('name', 'like', '%'.$filters['search'].'%')
                    ->orWhere('code', 'like', '%'.$filters['search'].'%');
            });
        }

        // Apply status filter
        if (isset($filters['is_active'])) {
            $query->where('is_active', $filters['is_active']);
        }

        // Apply city filter
        if (! empty($filters['city'])) {
            $query->where('city', $filters['city']);
        }

        // Apply sorting
        $sortBy = $filters['sort_by'] ?? 'created_at';
        $sortDirection = $filters['sort_direction'] ?? 'desc';
        $query->orderBy($sortBy, $sortDirection);

        return $query->paginate($perPage);
    }

    /**
     * Get unique cities from estates
     */
    public function getUniqueCities(): Collection
    {
        return $this->query->distinct()->pluck('city')->filter()->sort();
    }

    /**
     * Get estate with active water rate
     */
    public function getEstateWithActiveWaterRate(int $estateId): ?Estate
    {
        return $this->query->with(['waterRates' => function ($query): void {
            $query->where('is_active', true)->first();
        }])->find($estateId);
    }

    /**
     * Bulk update estate status
     */
    public function bulkUpdateStatus(array $estateIds, bool $isActive): int
    {
        return $this->query->whereIn('id', $estateIds)->update(['is_active' => $isActive]);
    }

    /**
     * Get estates created in date range
     */
    public function getEstatesCreatedBetween(string $startDate, string $endDate): Collection
    {
        return $this->query->whereBetween('created_at', [$startDate, $endDate])->get();
    }

    /**
     * Get count of houses for an estate
     */
    public function getHouseCount(int $estateId): int
    {
        return $this->findOrFail($estateId)->houses()->count();
    }
}
