<?php

namespace App\Repositories;

use App\Repositories\Contracts\BaseRepositoryInterface;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Pagination\LengthAwarePaginator;

abstract class BaseRepository implements BaseRepositoryInterface
{
    protected Builder $query;

    public function __construct(protected Model $model)
    {
        $this->query = $this->model->newQuery();
    }

    public function all(array $columns = ['*']): Collection
    {
        return $this->query->get($columns);
    }

    public function paginate(int $perPage = 15, array $columns = ['*']): LengthAwarePaginator
    {
        return $this->query->paginate($perPage, $columns);
    }

    public function find(int $id, array $columns = ['*']): ?Model
    {
        return $this->query->find($id, $columns);
    }

    public function findOrFail(int $id, array $columns = ['*']): Model
    {
        return $this->query->findOrFail($id, $columns);
    }

    public function create(array $data): Model
    {
        return $this->model->create($data);
    }

    public function update(int $id, array $data): Model
    {
        $model = $this->findOrFail($id);
        $model->update($data);

        return $model->fresh();
    }

    public function delete(int $id): bool
    {
        $model = $this->findOrFail($id);

        return $model->delete();
    }

    public function findBy(string $field, $value, array $columns = ['*']): ?Model
    {
        return $this->query->where($field, $value)->first($columns);
    }

    public function findAllBy(string $field, $value, array $columns = ['*']): Collection
    {
        return $this->query->where($field, $value)->get($columns);
    }

    public function where(array $conditions, array $columns = ['*']): Collection
    {
        $query = $this->query;

        foreach ($conditions as $field => $value) {
            if (is_array($value)) {
                $query->where($field, $value[0], $value[1]);
            } else {
                $query->where($field, $value);
            }
        }

        return $query->get($columns);
    }

    public function firstWhere(array $conditions, array $columns = ['*']): ?Model
    {
        $query = $this->query;

        foreach ($conditions as $field => $value) {
            if (is_array($value)) {
                $query->where($field, $value[0], $value[1]);
            } else {
                $query->where($field, $value);
            }
        }

        return $query->first($columns);
    }

    public function updateOrCreate(array $attributes, array $values = []): Model
    {
        return $this->model->updateOrCreate($attributes, $values);
    }

    public function firstOrCreate(array $attributes, array $values = []): Model
    {
        return $this->model->firstOrCreate($attributes, $values);
    }

    public function count(): int
    {
        return $this->query->count();
    }

    public function sum(string $field): float
    {
        return $this->query->sum($field);
    }

    public function avg(string $field): float
    {
        return $this->query->avg($field);
    }

    public function max(string $field)
    {
        return $this->query->max($field);
    }

    public function min(string $field)
    {
        return $this->query->min($field);
    }

    public function with(array $relations): self
    {
        $this->query = $this->query->with($relations);

        return $this;
    }

    public function orderBy(string $field, string $direction = 'asc'): self
    {
        $this->query = $this->query->orderBy($field, $direction);

        return $this;
    }

    public function whereClause(string $field, $operator, $value = null): self
    {
        $this->query = $this->query->where($field, $operator, $value);

        return $this;
    }

    public function whereIn(string $field, array $values): self
    {
        $this->query = $this->query->whereIn($field, $values);

        return $this;
    }

    public function whereNotIn(string $field, array $values): self
    {
        $this->query = $this->query->whereNotIn($field, $values);

        return $this;
    }

    public function whereNull(string $field): self
    {
        $this->query = $this->query->whereNull($field);

        return $this;
    }

    public function whereNotNull(string $field): self
    {
        $this->query = $this->query->whereNotNull($field);

        return $this;
    }

    public function whereBetween(string $field, array $values): self
    {
        $this->query = $this->query->whereBetween($field, $values);

        return $this;
    }

    public function whereNotBetween(string $field, array $values): self
    {
        $this->query = $this->query->whereNotBetween($field, $values);

        return $this;
    }

    public function whereHas(string $relation, ?\Closure $callback = null): self
    {
        $this->query = $this->query->whereHas($relation, $callback);

        return $this;
    }

    public function orWhere(string $field, $operator, $value = null): self
    {
        $this->query = $this->query->orWhere($field, $operator, $value);

        return $this;
    }

    public function limit(int $limit): self
    {
        $this->query = $this->query->limit($limit);

        return $this;
    }

    public function getQuery()
    {
        return $this->query;
    }

    public function get(array $columns = ['*']): Collection
    {
        return $this->query->get($columns);
    }

    public function first(array $columns = ['*']): ?Model
    {
        return $this->query->first($columns);
    }

    public function applyFilters(array $filters): self
    {
        foreach ($filters as $field => $value) {
            if (! empty($value)) {
                $this->query = $this->query->where($field, $value);
            }
        }

        return $this;
    }

    public function applySearch(string $search, array $searchableFields): self
    {
        if ($search !== '' && $search !== '0') {
            $this->query = $this->query->where(function ($query) use ($search, $searchableFields): void {
                foreach ($searchableFields as $searchableField) {
                    $query->orWhere($searchableField, 'like', '%'.$search.'%');
                }
            });
        }

        return $this;
    }

    public function withTrashed(): self
    {
        if (method_exists($this->model, 'withTrashed')) {
            $this->query = $this->query->withTrashed();
        }

        return $this;
    }

    public function onlyTrashed(): self
    {
        if (method_exists($this->model, 'onlyTrashed')) {
            $this->query = $this->query->onlyTrashed();
        }

        return $this;
    }

    public function restore(int $id): bool
    {
        if (method_exists($this->model, 'restore')) {
            $model = $this->withTrashed()->findOrFail($id);

            return $model->restore();
        }

        return false;
    }

    public function forceDelete(int $id): bool
    {
        if (method_exists($this->model, 'forceDelete')) {
            $model = $this->withTrashed()->findOrFail($id);

            return $model->forceDelete();
        }

        return false;
    }

    /**
     * Reset the query builder
     */
    public function resetQuery(): self
    {
        $this->query = $this->model->newQuery();

        return $this;
    }

    /**
     * Get the model instance
     */
    public function getModel(): Model
    {
        return $this->model;
    }

    /**
     * Set the model instance
     */
    public function setModel(Model $model): self
    {
        $this->model = $model;
        $this->query = $model->newQuery();

        return $this;
    }

    /**
     * Get all records as a collection
     */
    public function getAll(): Collection
    {
        return $this->model->all();
    }

    /**
     * Check if a record exists by field
     */
    public function existsBy(string $field, $value): bool
    {
        return $this->model->where($field, $value)->exists();
    }

    /**
     * Check if a record is unique by field(s)
     */
    public function isUnique(array $conditions, ?int $excludeId = null): bool
    {
        $query = $this->model->where(function ($query) use ($conditions): void {
            foreach ($conditions as $field => $value) {
                if (is_array($value)) {
                    $query->where($field, $value[0], $value[1]);
                } else {
                    $query->where($field, $value);
                }
            }
        });

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return $query->count() === 0;
    }

    /**
     * Get count of related records
     */
    public function getRelatedCount(string $relation, ?array $conditions = null): int
    {
        $query = $this->model->$relation();

        if ($conditions) {
            $query->where($conditions);
        }

        return $query->count();
    }

    /**
     * Check if code is unique
     */
    public function isCodeUnique(string $code, ?int $excludeId = null): bool
    {
        return $this->isUnique(['code' => $code], $excludeId);
    }
}
