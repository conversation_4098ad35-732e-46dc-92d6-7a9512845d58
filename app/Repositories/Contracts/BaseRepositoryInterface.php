<?php

namespace App\Repositories\Contracts;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Pagination\LengthAwarePaginator;

interface BaseRepositoryInterface
{
    /**
     * Get all records
     */
    public function all(array $columns = ['*']): Collection;

    /**
     * Get paginated records
     */
    public function paginate(int $perPage = 15, array $columns = ['*']): LengthAwarePaginator;

    /**
     * Find record by ID
     */
    public function find(int $id, array $columns = ['*']): ?Model;

    /**
     * Find record by ID or throw exception
     */
    public function findOrFail(int $id, array $columns = ['*']): Model;

    /**
     * Create new record
     */
    public function create(array $data): Model;

    /**
     * Update existing record
     */
    public function update(int $id, array $data): Model;

    /**
     * Delete record by ID
     */
    public function delete(int $id): bool;

    /**
     * Find records by field
     */
    public function findBy(string $field, $value, array $columns = ['*']): ?Model;

    /**
     * Find multiple records by field
     */
    public function findAllBy(string $field, $value, array $columns = ['*']): Collection;

    /**
     * Get records with where conditions
     */
    public function where(array $conditions, array $columns = ['*']): Collection;

    /**
     * Get first record matching conditions
     */
    public function firstWhere(array $conditions, array $columns = ['*']): ?Model;

    /**
     * Create or update record
     */
    public function updateOrCreate(array $attributes, array $values = []): Model;

    /**
     * Get count of records
     */
    public function count(): int;

    /**
     * Get sum of a field
     */
    public function sum(string $field): float;

    /**
     * Get average of a field
     */
    public function avg(string $field): float;

    /**
     * Get max value of a field
     */
    public function max(string $field);

    /**
     * Get min value of a field
     */
    public function min(string $field);

    /**
     * Get records with relationships
     */
    public function with(array $relations): self;

    /**
     * Order records by field
     */
    public function orderBy(string $field, string $direction = 'asc'): self;

    /**
     * Add where clause
     */
    public function whereClause(string $field, $operator, $value = null): self;

    /**
     * Add whereIn clause
     */
    public function whereIn(string $field, array $values): self;

    /**
     * Add whereNotIn clause
     */
    public function whereNotIn(string $field, array $values): self;

    /**
     * Add whereNull clause
     */
    public function whereNull(string $field): self;

    /**
     * Add whereNotNull clause
     */
    public function whereNotNull(string $field): self;

    /**
     * Add whereBetween clause
     */
    public function whereBetween(string $field, array $values): self;

    /**
     * Add whereNotBetween clause
     */
    public function whereNotBetween(string $field, array $values): self;

    /**
     * Add whereHas clause
     */
    public function whereHas(string $relation, ?\Closure $callback = null): self;

    /**
     * Add orWhere clause
     */
    public function orWhere(string $field, $operator, $value = null): self;

    /**
     * Limit results
     */
    public function limit(int $limit): self;

    /**
     * Get the query builder instance
     */
    public function getQuery();

    /**
     * Execute the query and get results
     */
    public function get(array $columns = ['*']): Collection;

    /**
     * Execute the query and get first result
     */
    public function first(array $columns = ['*']): ?Model;

    /**
     * Apply search filters
     */
    public function applyFilters(array $filters): self;

    /**
     * Apply search query
     */
    public function applySearch(string $search, array $searchableFields): self;

    /**
     * Get with trashed records
     */
    public function withTrashed(): self;

    /**
     * Get only trashed records
     */
    public function onlyTrashed(): self;

    /**
     * Restore trashed record
     */
    public function restore(int $id): bool;

    /**
     * Force delete record
     */
    public function forceDelete(int $id): bool;

    /**
     * Get all records as a collection
     */
    public function getAll(): Collection;

    /**
     * Check if a record exists by field
     */
    public function existsBy(string $field, $value): bool;

    /**
     * Check if a record is unique by field(s)
     */
    public function isUnique(array $conditions, ?int $excludeId = null): bool;

    /**
     * Get count of related records
     */
    public function getRelatedCount(string $relation, ?array $conditions = null): int;

    /**
     * Check if code is unique
     */
    public function isCodeUnique(string $code, ?int $excludeId = null): bool;
}
