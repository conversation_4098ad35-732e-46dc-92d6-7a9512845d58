<?php

namespace App\Traits;

use Illuminate\Support\Facades\Auth;

/**
 * Consolidated permission trait that leverages <PERSON><PERSON>mission
 * Replaces PermissionAware and WithPermissionChecks traits
 */
trait HasPermissions
{
    /**
     * Get current authenticated user
     */
    protected function currentUser()
    {
        return Auth::user();
    }

    /**
     * Check if current user has specific permission
     */
    protected function hasPermission(string $permission): bool
    {
        $user = $this->currentUser();

        return $user && $user->hasPermissionTo($permission);
    }

    /**
     * Check if current user has any of the given permissions
     */
    protected function hasAnyPermission(array $permissions): bool
    {
        $user = $this->currentUser();

        return $user && $user->hasAnyPermission($permissions);
    }

    /**
     * Check if current user has all of the given permissions
     */
    protected function hasAllPermissions(array $permissions): bool
    {
        $user = $this->currentUser();

        return $user && $user->hasAllPermissions($permissions);
    }

    /**
     * Check if current user has specific role
     */
    protected function hasRole(string $role): bool
    {
        $user = $this->currentUser();

        return $user && $user->hasRole($role);
    }

    /**
     * Check if current user has any of the given roles
     */
    protected function hasAnyRole(array $roles): bool
    {
        $user = $this->currentUser();

        return $user && $user->hasAnyRole($roles);
    }

    /**
     * Check if user is admin
     */
    protected function isAdmin(): bool
    {
        return $this->hasRole('admin');
    }

    /**
     * Check if user is manager
     */
    protected function isManager(): bool
    {
        $user = $this->currentUser();

        return $user && $user->hasRole('manager');
    }

    /**
     * Check if user is reviewer
     */
    protected function isReviewer(): bool
    {
        $user = $this->currentUser();

        return $user && $user->hasRole('reviewer');
    }

    /**
     * Check if user is caretaker
     */
    protected function isCaretaker(): bool
    {
        $user = $this->currentUser();

        return $user && $user->hasRole('caretaker');
    }

    /**
     * Check if user is resident
     */
    protected function isResident(): bool
    {
        $user = $this->currentUser();

        return $user && $user->hasRole('resident');
    }

    // Estate Access Methods

    /**
     * Check if current user can access estates
     */
    protected function canAccessEstates(): bool
    {
        return $this->hasAnyPermission([
            'estates.view_all',
            'estates.view_assigned',
        ]);
    }

    /**
     * Check if current user can manage estates
     */
    protected function canManageEstates(): bool
    {
        return $this->hasAnyPermission([
            'estates.manage_all',
            'estates.manage_assigned',
        ]);
    }

    /**
     * Check if user can access specific estate
     */
    protected function canAccessEstate($estateId): bool
    {
        if ($this->isAdmin()) {
            return true;
        }

        $user = $this->currentUser();

        return $user && $user->assignedEstates()->where('estate_id', $estateId)->exists();
    }

    // House Access Methods

    /**
     * Check if current user can access houses
     */
    protected function canAccessHouses(): bool
    {
        return $this->hasAnyPermission([
            'houses.view_all',
            'houses.view_assigned',
            'houses.view_own',
        ]);
    }

    /**
     * Check if current user can manage houses
     */
    protected function canManageHouses(): bool
    {
        return $this->hasAnyPermission([
            'houses.manage_all',
            'houses.manage_assigned',
        ]);
    }

    /**
     * Check if user can access specific house
     */
    protected function canAccessHouse($house): bool
    {
        if ($this->isAdmin()) {
            return true;
        }

        return $this->canAccessEstate($house->estate_id);
    }

    // Invoice and Payment Methods

    /**
     * Check if current user can access invoices
     */
    protected function canAccessInvoices(): bool
    {
        return $this->hasAnyPermission([
            'invoices.view_all',
            'invoices.view_assigned',
            'invoices.view_own',
        ]);
    }

    /**
     * Check if current user can create invoices
     */
    protected function canCreateInvoices(): bool
    {
        return $this->hasAnyPermission([
            'invoices.create_manual',
            'invoices.generate_assigned',
        ]);
    }

    /**
     * Check if current user can manage invoices
     */
    protected function canManageInvoices(): bool
    {
        return $this->hasAnyPermission([
            'invoices.edit_all',
            'invoices.edit_assigned',
            'invoices.adjust_assigned',
        ]);
    }

    /**
     * Check if current user can access payments
     */
    protected function canAccessPayments(): bool
    {
        return $this->hasAnyPermission([
            'payments.view_all',
            'payments.view_assigned',
            'payments.view_own',
        ]);
    }

    /**
     * Check if current user can create payments
     */
    protected function canCreatePayments(): bool
    {
        return $this->hasAnyPermission([
            'payments.create_all',
            'payments.create_assigned',
        ]);
    }

    /**
     * Check if current user can manage payments
     */
    protected function canManagePayments(): bool
    {
        return $this->hasAnyPermission([
            'payments.edit_all',
            'payments.edit_assigned',
            'payments.approve_all',
            'payments.approve_assigned',
        ]);
    }

    // Reading Methods

    /**
     * Check if current user can access readings
     */
    protected function canAccessReadings(): bool
    {
        return $this->hasAnyPermission([
            'readings.view_all',
            'readings.view_assigned',
            'readings.view_own',
        ]);
    }

    /**
     * Check if current user can create readings
     */
    protected function canCreateReadings(): bool
    {
        return $this->hasAnyPermission([
            'readings.create_all',
            'readings.create_assigned',
        ]);
    }

    /**
     * Check if current user can review readings
     */
    protected function canReviewReadings(): bool
    {
        return $this->hasAnyPermission([
            'readings.review_all',
            'readings.approve_assigned',
            'readings.validate',
        ]);
    }

    // System Access Methods

    /**
     * Check if current user can manage users
     */
    protected function canManageUsers(): bool
    {
        return $this->hasPermission('users.manage_all');
    }

    /**
     * Check if current user can manage system settings
     */
    protected function canManageSystem(): bool
    {
        return $this->hasAnyPermission([
            'system.settings.view',
            'audit.view_logs',
        ]);
    }

    /**
     * Check if current user can export data
     */
    protected function canExportData(): bool
    {
        return $this->hasAnyPermission([
            'export.data_all',
            'export.data_assigned',
        ]);
    }

    /**
     * Check if current user can access analytics
     */
    protected function canAccessAnalytics(): bool
    {
        return $this->hasAnyPermission([
            'analytics.view_all',
            'analytics.view_assigned',
        ]);
    }

    /**
     * Check if current user can access reports
     */
    protected function canAccessReports(): bool
    {
        return $this->hasAnyPermission([
            'reports.view_all',
            'reports.view_assigned',
        ]);
    }

    // Authorization Methods

    /**
     * Abort if user doesn't have permission
     */
    protected function authorizePermission(string $permission, ?string $message = null): void
    {
        if (! $this->hasPermission($permission)) {
            $message ??= 'You do not have permission to perform this action.';
            abort(403, $message);
        }
    }

    /**
     * Abort if user cannot access estate
     */
    protected function authorizeEstateAccess($estateId, ?string $message = null): void
    {
        if (! $this->canAccessEstate($estateId)) {
            $message ??= 'You do not have permission to access this estate.';
            abort(403, $message);
        }
    }

    /**
     * Abort if user cannot access house
     */
    protected function authorizeHouseAccess($house, ?string $message = null): void
    {
        if (! $this->canAccessHouse($house)) {
            $message ??= 'You do not have permission to access this house.';
            abort(403, $message);
        }
    }

    // Data Access Methods

    /**
     * Get user's accessible estates
     */
    protected function getAccessibleEstates()
    {
        if ($this->isAdmin()) {
            return \App\Models\Estate::all();
        }

        $user = $this->currentUser();

        return $user ? $user->assignedEstates : collect();
    }

    /**
     * Get user's accessible houses
     */
    protected function getAccessibleHouses()
    {
        if ($this->isAdmin()) {
            return \App\Models\House::all();
        }

        return \App\Models\House::whereHas('estate', function ($query): void {
            $query->whereIn('estate_id', $this->getAccessibleEstates()->pluck('id'));
        })->get();
    }

    // Query Filtering Methods

    /**
     * Filter query by user's accessible estates
     */
    protected function filterByAccessibleEstates($query, string $estateColumn = 'estate_id')
    {
        if ($this->isAdmin()) {
            return $query;
        }

        return $query->whereIn($estateColumn, $this->getAccessibleEstates()->pluck('id'));
    }

    /**
     * Filter query by user's accessible houses
     */
    protected function filterByAccessibleHouses($query, string $houseColumn = 'house_id')
    {
        if ($this->isAdmin()) {
            return $query;
        }

        return $query->whereIn($houseColumn, $this->getAccessibleHouses()->pluck('id'));
    }

    /**
     * Apply estate access scope to query
     */
    protected function applyEstateScope($query, $user = null)
    {
        $user = $user ?: $this->currentUser();
        if (! $user) {
            return $query->whereRaw('1 = 0'); // No access
        }

        if ($user->hasPermissionTo('estates.view_all')) {
            return $query;
        }

        if ($user->hasPermissionTo('estates.view_assigned')) {
            return $query->whereIn('id', $user->assignedEstates()->pluck('id'));
        }

        return $query->whereRaw('1 = 0'); // No access
    }

    /**
     * Apply house access scope to query
     */
    protected function applyHouseScope($query, $user = null)
    {
        $user = $user ?: $this->currentUser();
        if (! $user) {
            return $query->whereRaw('1 = 0'); // No access
        }

        if ($user->hasPermissionTo('houses.view_all')) {
            return $query;
        }

        if ($user->hasPermissionTo('houses.view_assigned')) {
            return $query->whereIn('estate_id', $user->assignedEstates()->pluck('id'));
        }

        if ($user->hasPermissionTo('houses.view_own')) {
            return $query->whereIn('id', $user->contacts()->pluck('house_id'));
        }

        return $query->whereRaw('1 = 0'); // No access
    }

    /**
     * Apply invoice access scope to query
     */
    protected function applyInvoiceScope($query, $user = null)
    {
        $user = $user ?: $this->currentUser();
        if (! $user) {
            return $query->whereRaw('1 = 0'); // No access
        }

        if ($user->hasPermissionTo('invoices.view_all')) {
            return $query;
        }

        if ($user->hasPermissionTo('invoices.view_assigned')) {
            return $query->whereHas('house', function ($q) use ($user): void {
                $q->whereIn('estate_id', $user->assignedEstates()->pluck('id'));
            });
        }

        if ($user->hasPermissionTo('invoices.view_own')) {
            return $query->whereHas('house', function ($q) use ($user): void {
                $q->whereIn('id', $user->contacts()->pluck('house_id'));
            });
        }

        return $query->whereRaw('1 = 0'); // No access
    }
}
