<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\ExportJob;
use App\Models\ExportTemplate;
use App\Services\PermissionService;
use App\Services\ReportExportService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class ExportController extends Controller
{
    protected $permissionService;

    public function __construct(PermissionService $permissionService)
    {
        $this->permissionService = $permissionService;
    }

    public function initiate(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'entity_type' => 'required|in:estates,houses,contacts,meter_readings,invoices,water_rates',
            'format' => 'required|in:xlsx,csv,pdf',
            'filters' => 'nullable|array',
            'export_template_id' => 'nullable|exists:export_templates,id',
            'export_name' => 'required|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Check template ownership if provided
        if ($request->export_template_id) {
            $template = ExportTemplate::find($request->export_template_id);
            if (! $template || ($template->user_id !== Auth::id() && ! $template->is_public)) {
                return response()->json(['error' => 'Export template not found or access denied'], 404);
            }
        }

        $exportJob = ExportJob::create([
            'user_id' => Auth::id(),
            'export_template_id' => $request->export_template_id,
            'status' => 'pending',
            'entity_type' => $request->entity_type,
            'format' => $request->format,
            'filters' => $request->filters ?? [],
            'file_name' => $request->export_name.'.'.$request->format,
        ]);

        \App\Jobs\ProcessExportJob::dispatch($exportJob);

        return response()->json([
            'message' => 'Export job created successfully',
            'export_job' => [
                'id' => $exportJob->id,
                'status' => $exportJob->status,
                'file_name' => $exportJob->file_name,
                'created_at' => $exportJob->created_at,
            ],
        ], 201);
    }

    public function index(Request $request)
    {
        $query = ExportJob::forUser(Auth::id());

        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        if ($request->has('entity_type')) {
            $query->where('entity_type', $request->entity_type);
        }

        $lengthAwarePaginator = $query->with('exportTemplate')
            ->latest()
            ->paginate($request->get('per_page', 15));

        return response()->json($lengthAwarePaginator);
    }

    public function show($id)
    {
        $exportJob = ExportJob::forUser(Auth::id())->with('exportTemplate')->findOrFail($id);

        return response()->json($exportJob);
    }

    public function download($id)
    {
        $exportJob = ExportJob::forUser(Auth::id())->findOrFail($id);

        if ($exportJob->status !== 'completed' || ! $exportJob->file_path) {
            return response()->json(['error' => 'Export is not ready for download'], 400);
        }

        $filePath = storage_path('app/'.$exportJob->file_path);

        if (! file_exists($filePath)) {
            return response()->json(['error' => 'Export file not found'], 404);
        }

        return response()->download($filePath, $exportJob->file_name);
    }

    public function destroy($id)
    {
        $exportJob = ExportJob::forUser(Auth::id())->findOrFail($id);

        // Delete file if exists
        if ($exportJob->file_path && \Storage::exists($exportJob->file_path)) {
            \Storage::delete($exportJob->file_path);
        }

        $exportJob->delete();

        return response()->json(['message' => 'Export deleted successfully']);
    }

    public function retry($id)
    {
        $exportJob = ExportJob::forUser(Auth::id())->findOrFail($id);

        if ($exportJob->status !== 'failed') {
            return response()->json(['error' => 'Only failed exports can be retried'], 400);
        }

        $exportJob->update([
            'status' => 'pending',
            'started_at' => null,
            'completed_at' => null,
            'error' => null,
        ]);

        \App\Jobs\ProcessExportJob::dispatch($exportJob);

        return response()->json(['message' => 'Export job retried successfully']);
    }

    public function templates(Request $request)
    {
        $query = ExportTemplate::forUser(Auth::id());

        if ($request->has('entity_type')) {
            $query->where('entity_type', $request->entity_type);
        }

        $lengthAwarePaginator = $query->latest()->paginate($request->get('per_page', 15));

        return response()->json($lengthAwarePaginator);
    }

    public function storeTemplate(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'entity_type' => 'required|in:estates,houses,contacts,meter_readings,invoices,water_rates',
            'format' => 'required|in:xlsx,csv,pdf',
            'columns' => 'required|array|min:1',
            'filters' => 'nullable|array',
            'is_public' => 'boolean',
            'is_scheduled' => 'boolean',
            'schedule_frequency' => 'required_if:is_scheduled,true|in:daily,weekly,monthly,quarterly',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $template = ExportTemplate::create([
            'user_id' => Auth::id(),
            'name' => $request->name,
            'entity_type' => $request->entity_type,
            'format' => $request->format,
            'columns' => $request->columns,
            'filters' => $request->filters ?? [],
            'is_public' => $request->is_public ?? false,
            'is_scheduled' => $request->is_scheduled ?? false,
            'schedule_frequency' => $request->schedule_frequency,
        ]);

        return response()->json($template, 201);
    }

    public function showTemplate($id)
    {
        $template = ExportTemplate::forUser(Auth::id())->findOrFail($id);

        return response()->json($template);
    }

    public function updateTemplate(Request $request, $id)
    {
        $template = ExportTemplate::forUser(Auth::id())->findOrFail($id);

        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|required|string|max:255',
            'entity_type' => 'sometimes|required|in:estates,houses,contacts,meter_readings,invoices,water_rates',
            'format' => 'sometimes|required|in:xlsx,csv,pdf',
            'columns' => 'sometimes|required|array|min:1',
            'filters' => 'nullable|array',
            'is_public' => 'boolean',
            'is_scheduled' => 'boolean',
            'schedule_frequency' => 'required_if:is_scheduled,true|in:daily,weekly,monthly,quarterly',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $template->update($request->all());

        return response()->json($template);
    }

    public function destroyTemplate($id)
    {
        $template = ExportTemplate::forUser(Auth::id())->findOrFail($id);
        $template->delete();

        return response()->json(['message' => 'Export template deleted successfully']);
    }

    public function previewData(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'entity_type' => 'required|in:estates,houses,contacts,meter_readings,invoices,water_rates',
            'filters' => 'nullable|array',
            'limit' => 'nullable|integer|min:1|max:100',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $reportExportService = new ReportExportService;
        $limit = $request->get('limit', 10);

        $data = match ($request->entity_type) {
            'estates' => $reportExportService->getEstatesExportData($request->filters ?? [])->take($limit),
            'houses' => $reportExportService->getHousesExportData($request->filters ?? [])->take($limit),
            'contacts' => $reportExportService->getContactsExportData($request->filters ?? [])->take($limit),
            'meter_readings' => $reportExportService->getMeterReadingsExportData($request->filters ?? [])->take($limit),
            'invoices' => $reportExportService->getInvoicesExportData($request->filters ?? [])->take($limit),
            'water_rates' => $reportExportService->getWaterRatesExportData($request->filters ?? [])->take($limit),
            default => throw new \InvalidArgumentException("Unsupported export entity type: {$request->entity_type}"),
        };

        return response()->json([
            'data' => $data,
            'total_count' => $data->count(),
            'sample_columns' => $data->isNotEmpty() ? array_keys($data->first()) : [],
        ]);
    }
}
