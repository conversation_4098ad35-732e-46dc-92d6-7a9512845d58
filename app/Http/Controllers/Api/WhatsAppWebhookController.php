<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Contact;
use App\Models\WhatsAppMessage;
use App\Services\PermissionService;
use App\Services\WhatsAppService;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;

class WhatsAppWebhookController extends Controller
{
    public function __construct(
        protected WhatsAppService $whatsAppService,
        protected PermissionService $permissionService
    ) {}

    /**
     * Handle the webhook verification request from WhatsApp
     */
    public function verify(Request $request): Response
    {
        $mode = $request->query('hub_mode');
        $token = $request->query('hub_verify_token');
        $challenge = $request->query('hub_challenge');

        $webhookVerifyToken = config('services.whatsapp.webhook_verify_token');

        if ($mode === 'subscribe' && $token === $webhookVerifyToken) {
            Log::info('WhatsApp webhook verified successfully');

            return response($challenge, 200);
        }

        Log::warning('WhatsApp webhook verification failed', [
            'mode' => $mode,
            'token' => 'REDACTED',
            'challenge' => $challenge,
        ]);

        return response('Verification failed', 403);
    }

    /**
     * Handle incoming webhook notifications from WhatsApp
     */
    public function handle(Request $request): Response
    {
        // Check user permissions for WhatsApp webhook access
        $user = auth()->guard('web')->user();

        if (! $user || ! $this->permissionService->userHasPermission($user, 'whatsapp.settings')) {
            throw new AuthorizationException('You do not have permission to access WhatsApp webhook.');
        }

        $signature = $request->header('X-Hub-Signature-256');
        $payload = $request->getContent();

        // Verify webhook signature
        if (! $this->whatsAppService->verifyWebhookSignature($signature, $payload)) {
            Log::warning('Invalid WhatsApp webhook signature');

            return response('Unauthorized', 401);
        }

        $data = $request->json()->all();
        Log::debug('WhatsApp webhook received', ['data' => $data]);

        try {
            // Process different types of notifications
            if (isset($data['entry']) && ! empty($data['entry'])) {
                foreach ($data['entry'] as $entry) {
                    if (isset($entry['changes']) && ! empty($entry['changes'])) {
                        foreach ($entry['changes'] as $change) {
                            $this->processChange($change);
                        }
                    }
                }
            }

            return response('Webhook processed', 200);
        } catch (\Exception $e) {
            Log::error('Error processing WhatsApp webhook: '.$e->getMessage(), [
                'exception' => $e,
                'data' => $data,
            ]);

            return response('Internal error', 500);
        }
    }

    /**
     * Process a change notification from WhatsApp
     */
    protected function processChange(array $change): void
    {
        if (! isset($change['value']) || ! isset($change['value']['messaging_product']) || $change['value']['messaging_product'] !== 'whatsapp') {
            return;
        }

        // Check user permissions for processing WhatsApp changes
        $user = auth()->guard('web')->user();
        if (! $user || ! $this->permissionService->userHasPermission($user, 'whatsapp.settings')) {
            Log::warning('User attempted to process WhatsApp change without permission', ['user_id' => $user->id ?? null]);

            return;
        }

        $value = $change['value'];

        // Handle status updates
        if (isset($value['statuses']) && ! empty($value['statuses'])) {
            foreach ($value['statuses'] as $status) {
                $this->processStatusUpdate($status);
            }
        }

        // Handle incoming messages
        if (isset($value['messages']) && ! empty($value['messages'])) {
            foreach ($value['messages'] as $message) {
                $this->processIncomingMessage($message);
            }
        }
    }

    /**
     * Process a status update from WhatsApp
     */
    protected function processStatusUpdate(array $status): void
    {
        if (! isset($status['id']) || ! isset($status['status'])) {
            return;
        }

        $messageId = $status['id'];
        $statusType = $status['status'];

        $message = \App\Models\WhatsAppMessage::where('message_id', $messageId)->first();

        if (! $message) {
            Log::warning('WhatsApp message not found for status update', ['message_id' => $messageId]);

            return;
        }

        Log::info('Processing WhatsApp status update', [
            'message_id' => $messageId,
            'status' => $statusType,
        ]);

        switch ($statusType) {
            case 'sent':
                $message->markAsSent($messageId);
                break;
            case 'delivered':
                $message->markAsDelivered();
                break;
            case 'read':
                $message->markAsRead();
                break;
            case 'failed':
                $reason = $status['errors'][0]['title'] ?? 'Unknown error';
                $message->markAsFailed($reason);
                break;
        }
    }

    /**
     * Process an incoming message from WhatsApp
     */
    protected function processIncomingMessage(array $message): void
    {
        if (! isset($message['from']) || ! isset($message['type'])) {
            Log::warning('Invalid WhatsApp message structure', ['message' => $message]);

            return;
        }

        // Check user permissions for processing incoming messages
        $user = auth()->guard('web')->user();
        if (! $user || ! $this->permissionService->userHasPermission($user, 'whatsapp.settings')) {
            Log::warning('User attempted to process incoming WhatsApp message without permission', ['user_id' => $user->id ?? null]);

            return;
        }

        $from = $message['from'];
        $type = $message['type'];
        $timestamp = $message['timestamp'] ?? now();

        Log::info('Processing WhatsApp message', [
            'from' => $from,
            'type' => $type,
            'timestamp' => $timestamp,
        ]);

        // Find contact by phone number
        $contact = \App\Models\Contact::where('phone', $from)
            ->orWhere('whatsapp_number', $from)
            ->first();

        if (! $contact) {
            Log::info('Unknown contact sent message', ['from' => $from]);
            $this->handleUnknownContact($from, $message);

            return;
        }

        // Create incoming message record
        $incomingMessage = \App\Models\WhatsAppMessage::create([
            'sender_id' => null, // Incoming from customer
            'recipient' => $from,
            'message_type' => 'incoming_'.$type,
            'content' => $this->extractMessageContent($message),
            'direction' => 'incoming',
            'status' => 'received',
            'recipient_contact_id' => $contact->id,
            'house_id' => $contact->house_id,
            'estate_id' => $contact->house->estate_id ?? null,
            'message_id' => $message['id'] ?? null,
            'received_at' => now(),
        ]);

        // Route message based on type
        switch ($type) {
            case 'text':
                $this->handleTextMessage($contact, $message, $incomingMessage);
                break;
            case 'interactive':
                $this->handleInteractiveMessage($contact, $message, $incomingMessage);
                break;
            case 'image':
                $this->handleImageMessage($contact, $message, $incomingMessage);
                break;
            case 'document':
                $this->handleDocumentMessage($contact, $message, $incomingMessage);
                break;
            default:
                Log::info('Unhandled message type', ['type' => $type, 'from' => $from]);
                $this->sendAutoReply($contact, "Thank you for your message. We've received it and will get back to you soon.");
        }
    }

    /**
     * Extract message content based on type
     */
    protected function extractMessageContent(array $message): string
    {
        $type = $message['type'];

        switch ($type) {
            case 'text':
                return $message['text']['body'] ?? '';
            case 'interactive':
                $interactiveType = $message['interactive']['type'] ?? '';
                if ($interactiveType === 'button_reply') {
                    return 'Button reply: '.($message['interactive']['button_reply']['title'] ?? '');
                } elseif ($interactiveType === 'list_reply') {
                    return 'List reply: '.($message['interactive']['list_reply']['title'] ?? '');
                }

                return "Interactive message: {$interactiveType}";
            case 'image':
                return 'Image: '.($message['image']['caption'] ?? 'No caption');
            case 'document':
                return 'Document: '.($message['document']['filename'] ?? 'Unknown filename');
            default:
                return "Message type: {$type}";
        }
    }

    /**
     * Handle text messages
     */
    protected function handleTextMessage(Contact $contact, array $message, WhatsAppMessage $whatsAppMessage): void
    {
        // Check user permissions for handling text messages
        $user = auth()->guard('web')->user();
        if (! $user || ! $this->permissionService->userHasPermission($user, 'whatsapp.settings')) {
            Log::warning('User attempted to handle WhatsApp text message without permission', ['user_id' => $user->id ?? null]);

            return;
        }

        $text = strtolower(trim($message['text']['body'] ?? ''));

        // Handle common keywords
        if (in_array($text, ['hello', 'hi', 'hey'])) {
            $this->sendWelcomeMessage($contact);
        } elseif (in_array($text, ['balance', 'bill', 'invoice'])) {
            $this->sendBalanceInfo($contact);
        } elseif (in_array($text, ['help', 'support'])) {
            $this->sendHelpMessage($contact);
        } elseif (in_array($text, ['reading', 'meter'])) {
            $this->sendMeterReadingInstructions($contact);
        } else {
            $this->sendAutoReply($contact, 'Thank you for your message. Our team will assist you shortly.');
        }
    }

    /**
     * Handle interactive messages (button clicks, list selections)
     */
    protected function handleInteractiveMessage(Contact $contact, array $message, WhatsAppMessage $whatsAppMessage): void
    {
        // Check user permissions for handling interactive messages
        $user = auth()->guard('web')->user();
        if (! $user || ! $this->permissionService->userHasPermission($user, 'whatsapp.settings')) {
            Log::warning('User attempted to handle WhatsApp interactive message without permission', ['user_id' => $user->id ?? null]);

            return;
        }

        $interactive = $message['interactive'] ?? [];
        $type = $interactive['type'] ?? '';

        if ($type === 'button_reply') {
            $buttonId = $interactive['button_reply']['id'] ?? '';
            $buttonTitle = $interactive['button_reply']['title'] ?? '';

            Log::info('Button reply received', [
                'contact' => $contact->id,
                'button_id' => $buttonId,
                'button_title' => $buttonTitle,
            ]);

            match ($buttonId) {
                'pay_invoice' => $this->sendPaymentInstructions($contact),
                'view_balance' => $this->sendBalanceInfo($contact),
                'submit_reading' => $this->sendMeterReadingInstructions($contact),
                'contact_support' => $this->sendSupportInfo($contact),
                default => $this->sendAutoReply($contact, "We've received your selection: {$buttonTitle}"),
            };
        } elseif ($type === 'list_reply') {
            $listId = $interactive['list_reply']['id'] ?? '';
            $listTitle = $interactive['list_reply']['title'] ?? '';

            Log::info('List reply received', [
                'contact' => $contact->id,
                'list_id' => $listId,
                'list_title' => $listTitle,
            ]);

            $this->sendAutoReply($contact, "We've received your selection: {$listTitle}");
        }
    }

    /**
     * Handle image messages
     */
    protected function handleImageMessage(Contact $contact, array $message, WhatsAppMessage $whatsAppMessage): void
    {
        // Check user permissions for handling image messages
        $user = auth()->guard('web')->user();
        if (! $user || ! $this->permissionService->userHasPermission($user, 'whatsapp.settings')) {
            Log::warning('User attempted to handle WhatsApp image message without permission', ['user_id' => $user->id ?? null]);

            return;
        }

        $caption = strtolower($message['image']['caption'] ?? '');

        // Check if this might be a meter reading submission
        if (str_contains($caption, 'reading') || str_contains($caption, 'meter')) {
            $this->handleMeterReadingSubmission($contact, $message, $whatsAppMessage);
        } else {
            $this->sendAutoReply($contact, "Thank you for sending the image. If this is a meter reading, please include 'reading' or 'meter' in the caption.");
        }
    }

    /**
     * Handle document messages
     */
    protected function handleDocumentMessage(Contact $contact, array $message, WhatsAppMessage $whatsAppMessage): void
    {
        // Check user permissions for handling document messages
        $user = auth()->guard('web')->user();
        if (! $user || ! $this->permissionService->userHasPermission($user, 'whatsapp.settings')) {
            Log::warning('User attempted to handle WhatsApp document message without permission', ['user_id' => $user->id ?? null]);

            return;
        }

        $filename = $message['document']['filename'] ?? 'Unknown document';
        $this->sendAutoReply($contact, "Thank you for sending the document '{$filename}'. Our team will review it.");
    }

    /**
     * Handle messages from unknown contacts
     */
    protected function handleUnknownContact(string $from, array $message): void
    {
        $this->whatsAppService->sendText($from, 'Welcome to Water Management System. Please register your contact information with your estate manager to receive full service.');
    }

    /**
     * Send welcome message
     */
    protected function sendWelcomeMessage(Contact $contact): void
    {
        $message = "Hello {$contact->name}! Welcome to Water Management System. How can we help you today?\n\n";
        $message .= "You can reply with:\n";
        $message .= "• 'balance' for your current balance\n";
        $message .= "• 'reading' for meter reading instructions\n";
        $message .= "• 'help' for support options";

        $this->sendAutoReply($contact, $message);
    }

    /**
     * Send balance information
     */
    protected function sendBalanceInfo(Contact $contact): void
    {
        if (! $contact->house) {
            $this->sendAutoReply($contact, 'No house information found for your contact.');

            return;
        }

        $outstandingInvoices = \App\Models\Invoice::where('house_id', $contact->house_id)
            ->where('status', '!=', 'paid')
            ->withSum('payments', 'amount')
            ->get();

        if ($outstandingInvoices->isEmpty()) {
            $this->sendAutoReply($contact, 'Great news! You have no outstanding invoices. Thank you for being up to date with your payments.');

            return;
        }

        $message = "Your current balance information:\n\n";
        $totalBalance = 0;

        foreach ($outstandingInvoices as $outstandingInvoice) {
            $balance = $outstandingInvoice->total_amount - ($outstandingInvoice->payments_sum_amount ?? 0);
            $totalBalance += $balance;

            $message .= "Invoice #{$outstandingInvoice->invoice_number}\n";
            $message .= 'Amount: KES '.number_format($balance, 2)."\n";
            $message .= 'Due: '.$outstandingInvoice->due_date->format('M d, Y')."\n\n";
        }

        $message .= 'Total Balance: KES '.number_format($totalBalance, 2)."\n\n";
        $message .= 'To make a payment, please contact your estate manager or use our payment options.';

        $this->sendAutoReply($contact, $message);
    }

    /**
     * Send meter reading instructions
     */
    protected function sendMeterReadingInstructions(Contact $contact): void
    {
        $message = "To submit your meter reading:\n\n";
        $message .= "1. Take a clear photo of your water meter\n";
        $message .= "2. Send the photo via WhatsApp with the caption 'reading' or 'meter'\n";
        $message .= "3. Make sure the meter numbers are clearly visible\n\n";
        $message .= 'Our team will process your reading and update your account accordingly.';

        $this->sendAutoReply($contact, $message);
    }

    /**
     * Handle meter reading submission
     */
    protected function handleMeterReadingSubmission(Contact $contact, array $message, WhatsAppMessage $whatsAppMessage): void
    {
        // Check user permissions for handling meter reading submissions
        $user = auth()->guard('web')->user();
        if (! $user || ! $this->permissionService->userHasPermission($user, 'whatsapp.settings')) {
            Log::warning('User attempted to handle WhatsApp meter reading submission without permission', ['user_id' => $user->id ?? null]);

            return;
        }

        if (! $contact->house) {
            $this->sendAutoReply($contact, 'No house information found for your contact.');

            return;
        }

        // Get or create a system user for WhatsApp submissions
        $systemUser = \App\Models\User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'System User',
                'password' => bcrypt('password'),
                'role' => 'manager',
            ]
        );

        // Create a meter reading record with pending status
        $reading = \App\Models\MeterReading::create([
            'house_id' => $contact->house_id,
            'user_id' => $systemUser->id,
            'current_reading' => null, // Will be filled by staff
            'reading_date' => now(),
            'status' => 'submitted', // Use a valid status from the enum
            'notes' => 'Submitted via WhatsApp with image',
        ]);

        // Store the image media information
        if (isset($message['image'])) {
            $whatsAppMessage->update([
                'media_url' => $message['image']['id'] ?? null,
                'media_type' => 'image',
                'messageable_type' => \App\Models\MeterReading::class,
                'messageable_id' => $reading->id,
            ]);
        }

        $this->sendAutoReply($contact, "Thank you for submitting your meter reading! Your reading has been received (ID: {$reading->id}) and is pending verification. Our team will process it within 24 hours.");
    }

    /**
     * Send help message
     */
    protected function sendHelpMessage(Contact $contact): void
    {
        $message = "Water Management System Help:\n\n";
        $message .= "Available commands:\n";
        $message .= "• 'hello' - Welcome message\n";
        $message .= "• 'balance' - Check your outstanding balance\n";
        $message .= "• 'reading' - Submit meter reading\n";
        $message .= "• 'help' - This help message\n\n";
        $message .= 'For urgent issues, please contact your estate manager directly.';

        $this->sendAutoReply($contact, $message);
    }

    /**
     * Send payment instructions
     */
    protected function sendPaymentInstructions(Contact $contact): void
    {
        $message = "Payment Options:\n\n";
        $message .= "1. M-Pesa: Pay to [Business Number]\n";
        $message .= "2. Bank Transfer: [Bank Details]\n";
        $message .= "3. Cash: Pay at estate office\n\n";
        $message .= 'Please include your invoice number as reference when making payment.';

        $this->sendAutoReply($contact, $message);
    }

    /**
     * Send support information
     */
    protected function sendSupportInfo(Contact $contact): void
    {
        $message = "Support Information:\n\n";
        $message .= "For immediate assistance, please contact:\n";
        $message .= "• Phone: [Support Phone]\n";
        $message .= "• Email: [Support Email]\n";
        $message .= "• Office Hours: 9 AM - 5 PM, Monday to Friday\n\n";
        $message .= 'For emergencies, contact your estate manager directly.';

        $this->sendAutoReply($contact, $message);
    }

    /**
     * Send auto reply
     */
    protected function sendAutoReply(Contact $contact, string $message): void
    {
        try {
            $this->whatsAppService->sendText($contact->whatsapp_number, $message, $contact);
        } catch (\Exception $e) {
            Log::error('Failed to send auto reply', [
                'contact_id' => $contact->id,
                'error' => $e->getMessage(),
            ]);
        }
    }
}
