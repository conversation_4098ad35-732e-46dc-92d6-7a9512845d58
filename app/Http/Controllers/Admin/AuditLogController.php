<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ActivityLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AuditLogController extends Controller
{
    public function export(Request $request)
    {
        // Check if user has permission to export audit logs
        if (! Auth::user()->can('audit.export_logs')) {
            abort(403, 'You do not have permission to export audit logs.');
        }
        // Get filters from session
        $filters = session('activity_logs_export_filters', []);

        // Build query with filters
        $builder = ActivityLog::with('user');

        // Apply search filter
        if (! empty($filters['search'])) {
            $builder->where(function ($q) use ($filters): void {
                $q->where('description', 'like', '%'.$filters['search'].'%')
                    ->orWhere('action', 'like', '%'.$filters['search'].'%')
                    ->orWhere('entity_type', 'like', '%'.$filters['search'].'%');
            });
        }

        // Apply user filter
        if (! empty($filters['user_id'])) {
            $builder->where('user_id', $filters['user_id']);
        }

        // Apply action filter
        if (! empty($filters['action'])) {
            $builder->where('action', $filters['action']);
        }

        // Apply entity filter
        if (! empty($filters['entity_type'])) {
            $builder->where('entity_type', $filters['entity_type']);
        }

        // Apply date filters
        if (! empty($filters['date_from'])) {
            $builder->whereDate('created_at', '>=', $filters['date_from']);
        }
        if (! empty($filters['date_to'])) {
            $builder->whereDate('created_at', '<=', $filters['date_to']);
        }

        // Apply sorting
        $sortBy = $filters['sort_by'] ?? 'created_at';
        $sortDirection = $filters['sort_direction'] ?? 'desc';
        $builder->orderBy($sortBy, $sortDirection);

        // Get all matching logs (limit to prevent memory issues)
        $logs = $builder->limit(10000)->get();

        // Generate CSV content
        $filename = 'activity_logs_'.now()->format('Y-m-d_H-i-s').'.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="'.$filename.'"',
            'Cache-Control' => 'must-revalidate, post-check=0, pre-check=0',
            'Expires' => '0',
            'Pragma' => 'public',
        ];

        $callback = function () use ($logs): void {
            $file = fopen('php://output', 'w');

            // Add BOM for proper UTF-8 encoding in Excel
            fprintf($file, chr(0xEF).chr(0xBB).chr(0xBF));

            // Add CSV headers
            fputcsv($file, [
                'ID',
                'Timestamp',
                'User',
                'User Role',
                'Action',
                'Entity Type',
                'Entity ID',
                'Description',
                'IP Address',
                'User Agent',
            ]);

            // Add data rows
            foreach ($logs as $log) {
                fputcsv($file, [
                    $log->id,
                    $log->created_at->format('Y-m-d H:i:s'),
                    $log->user ? $log->user->name : 'System',
                    $log->user ? $log->user->role->label() : 'N/A',
                    $log->action,
                    $log->entity_type ?? '',
                    $log->entity_id ?? '',
                    $log->description,
                    $log->ip_address ?? '',
                    $log->user_agent ?? '',
                ]);
            }

            fclose($file);
        };

        // Clear the session filters
        session()->forget('activity_logs_export_filters');

        return response()->stream($callback, 200, $headers);
    }
}
