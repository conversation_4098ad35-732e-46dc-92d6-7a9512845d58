<?php

namespace App\Http\Middleware;

use App\Models\Estate;
use App\Services\PermissionService;
use Closure;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class EstateAccessMiddleware
{
    protected $permissionService;

    public function __construct(PermissionService $permissionService)
    {
        $this->permissionService = $permissionService;
    }

    public function handle(Request $request, Closure $next)
    {
        $estateId = $request->route('estate');
        $user = $request->user();

        // If no estate ID in route, continue without estate-specific checks
        if (! $estateId) {
            return $next($request);
        }

        try {
            $estate = Estate::findOrFail($estateId);

            // Check if user is authenticated
            if (! $user) {
                throw new AuthenticationException('Unauthenticated.');
            }

            // Determine required permission based on route method and user role
            $requiredPermission = $this->getRequiredPermission($request, $estate, $user);

            // Check if user has the required permission
            if (! $this->permissionService->userHasPermission($user, $requiredPermission)) {
                Log::warning('Unauthorized estate access attempt', [
                    'user_id' => $user->id,
                    'estate_id' => $estateId,
                    'required_permission' => $requiredPermission,
                    'ip' => $request->ip(),
                    'path' => $request->path(),
                ]);

                throw new AuthorizationException('You do not have permission to access this estate.');
            }

            // Add estate to request for downstream use
            $request->merge(['accessible_estate' => $estate]);

            return $next($request);

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException) {
            // Estate not found
            abort(404, 'Estate not found.');
        }

        return null;
    }

    /**
     * Determine the required permission based on route method and context
     */
    protected function getRequiredPermission(Request $request, Estate $estate, $user): string
    {
        $method = $request->method();
        $path = $request->path();

        // Admin users have access to all estates
        if ($user->hasRole('admin')) {
            return 'estates.manage_all';
        }

        // Check for specific estate permissions
        if (str_contains($path, 'estates')) {
            if (in_array($method, ['POST', 'PUT', 'PATCH', 'DELETE'])) {
                // Estate modification requires management permission
                if ($this->permissionService->userHasPermission($user, 'estates.manage_assigned')) {
                    return 'estates.manage_assigned';
                }

                return 'estates.view_all'; // Fallback for modification attempts
            }

            // Estate viewing
            if ($this->permissionService->userHasPermission($user, 'estates.view_assigned')) {
                return 'estates.view_assigned';
            }
        }

        // Default to view permission
        return 'estates.view_all';
    }
}
