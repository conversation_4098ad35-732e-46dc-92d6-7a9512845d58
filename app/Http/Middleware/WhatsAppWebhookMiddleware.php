<?php

namespace App\Http\Middleware;

use App\Services\PermissionService;
use Closure;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class WhatsAppWebhookMiddleware
{
    protected $permissionService;

    public function __construct(PermissionService $permissionService)
    {
        $this->permissionService = $permissionService;
    }

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        // For webhook verification (GET requests), allow public access
        if ($request->isMethod('GET')) {
            return $next($request);
        }

        // For webhook processing (POST requests), require authentication and permissions
        $user = $request->user();

        // Check if user is authenticated
        if (! $user) {
            throw new AuthorizationException('Unauthenticated.');
        }

        // Check if user has WhatsApp settings permission
        if (! $this->permissionService->userHasPermission($user, 'whatsapp.settings')) {
            throw new AuthorizationException('You do not have permission to access WhatsApp webhook.');
        }

        return $next($request);
    }

    /**
     * Handle a failed authorization attempt.
     */
    public function handleFailedAuthorization(Request $request): JsonResponse
    {
        return response()->json([
            'error' => 'Forbidden',
            'message' => 'You do not have permission to access this resource.',
            'status_code' => 403,
        ], 403);
    }
}
