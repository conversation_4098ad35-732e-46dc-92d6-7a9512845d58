<?php

namespace App\Http\Middleware;

use App\Models\House;
use App\Services\PermissionService;
use Closure;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class HouseAccessMiddleware
{
    protected $permissionService;

    public function __construct(PermissionService $permissionService)
    {
        $this->permissionService = $permissionService;
    }

    public function handle(Request $request, Closure $next)
    {
        $houseId = $request->route('house');
        $user = $request->user();

        // If no house ID in route, continue without house-specific checks
        if (! $houseId) {
            return $next($request);
        }

        try {
            $house = House::findOrFail($houseId);

            // Check if user is authenticated
            if (! $user) {
                throw new AuthenticationException('Unauthenticated.');
            }

            // Determine required permission based on route method and user role
            $requiredPermission = $this->getRequiredPermission($request, $house, $user);

            // Check if user has the required permission
            if (! $this->permissionService->userHasPermission($user, $requiredPermission)) {
                Log::warning('Unauthorized house access attempt', [
                    'user_id' => $user->id,
                    'house_id' => $houseId,
                    'estate_id' => $house->estate_id,
                    'required_permission' => $requiredPermission,
                    'ip' => $request->ip(),
                    'path' => $request->path(),
                ]);

                throw new AuthorizationException('You do not have permission to access this house.');
            }

            // Add house to request for downstream use
            $request->merge(['accessible_house' => $house]);

            return $next($request);

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException) {
            // House not found
            abort(404, 'House not found.');
        }

        return null;
    }

    /**
     * Determine the required permission based on route method and context
     */
    protected function getRequiredPermission(Request $request, House $house, $user): string
    {
        $method = $request->method();
        $path = $request->path();

        // Admin users have access to all houses
        if ($user->hasRole('admin')) {
            return 'houses.manage_all';
        }

        // Resident users can access their own houses
        if ($user->hasRole('resident')) {
            if ($user->contacts()->where('house_id', $house->id)->exists()) {
                return 'houses.view_own';
            }

            // Residents without direct access need at least view permission
            return 'houses.view_all';
        }

        // Check for specific house permissions
        if (str_contains($path, 'houses')) {
            if (in_array($method, ['POST', 'PUT', 'PATCH', 'DELETE'])) {
                // House modification requires management permission
                if ($this->permissionService->userHasPermission($user, 'houses.manage_assigned')) {
                    return 'houses.manage_assigned';
                }

                return 'houses.view_all'; // Fallback for modification attempts
            }

            // House viewing
            if ($this->permissionService->userHasPermission($user, 'houses.view_assigned')) {
                return 'houses.view_assigned';
            }
        }

        // Default to view permission
        return 'houses.view_all';
    }
}
