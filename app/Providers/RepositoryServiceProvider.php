<?php

namespace App\Providers;

use App\Models\AccountTransaction;
use App\Models\Contact;
use App\Models\Estate;
use App\Models\ExportJob;
use App\Models\ExportTemplate;
use App\Models\House;
use App\Models\HouseContact;
use App\Models\Invoice;
use App\Models\MessageTemplate;
use App\Models\MeterReading;
use App\Models\PermissionAuditLog;
use App\Models\User;
use App\Models\WaterRate;
use App\Models\WhatsAppMessage;
use App\Repositories\AccountTransactionRepository;
use App\Repositories\BaseRepository;
use App\Repositories\ContactRepository;
use App\Repositories\Contracts\BaseRepositoryInterface;
use App\Repositories\EstateRepository;
use App\Repositories\ExportJobRepository;
use App\Repositories\ExportTemplateRepository;
use App\Repositories\HouseContactRepository;
use App\Repositories\HouseRepository;
use App\Repositories\InvoiceRepository;
use App\Repositories\MessageTemplateRepository;
use App\Repositories\MeterReadingRepository;
use App\Repositories\PermissionAuditLogRepository;
use App\Repositories\UserRepository;
use App\Repositories\WaterRateRepository;
use App\Repositories\WhatsAppMessageRepository;
use App\Services\CalculationHelper;
use App\Services\ExportHelper;
use App\Services\FileHelper;
use App\Services\PermissionHelper;
use App\Services\ValidationHelper;
use Illuminate\Support\ServiceProvider;

class RepositoryServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Bind base repository
        $this->app->bind(BaseRepositoryInterface::class, BaseRepository::class);

        // Bind specific repositories as singletons for better performance
        $this->app->singleton(EstateRepository::class, fn ($app) => new EstateRepository($app->make(Estate::class)));
        $this->app->singleton(HouseRepository::class, fn ($app) => new HouseRepository($app->make(House::class)));
        $this->app->singleton(HouseContactRepository::class, fn ($app) => new HouseContactRepository($app->make(HouseContact::class)));
        $this->app->singleton(InvoiceRepository::class, fn ($app) => new InvoiceRepository($app->make(Invoice::class)));
        $this->app->singleton(ContactRepository::class, fn ($app) => new ContactRepository($app->make(Contact::class)));
        $this->app->singleton(MeterReadingRepository::class, fn ($app) => new MeterReadingRepository($app->make(MeterReading::class)));
        $this->app->singleton(WaterRateRepository::class, fn ($app) => new WaterRateRepository($app->make(WaterRate::class)));
        $this->app->singleton(UserRepository::class, fn ($app) => new UserRepository($app->make(User::class)));
        $this->app->singleton(AccountTransactionRepository::class, fn ($app) => new AccountTransactionRepository($app->make(AccountTransaction::class)));
        $this->app->singleton(PermissionAuditLogRepository::class, fn ($app) => new PermissionAuditLogRepository($app->make(PermissionAuditLog::class)));
        $this->app->singleton(WhatsAppMessageRepository::class, fn ($app) => new WhatsAppMessageRepository($app->make(WhatsAppMessage::class)));
        $this->app->singleton(MessageTemplateRepository::class, fn ($app) => new MessageTemplateRepository($app->make(MessageTemplate::class)));
        $this->app->singleton(ExportTemplateRepository::class, fn ($app) => new ExportTemplateRepository($app->make(ExportTemplate::class)));
        $this->app->singleton(ExportJobRepository::class, fn ($app) => new ExportJobRepository($app->make(ExportJob::class)));

        // Register utility helpers as singletons
        $this->app->singleton(CalculationHelper::class);
        $this->app->singleton(ExportHelper::class);
        $this->app->singleton(ValidationHelper::class);
        $this->app->singleton(PermissionHelper::class);
        $this->app->singleton(FileHelper::class);
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
