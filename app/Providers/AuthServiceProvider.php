<?php

namespace App\Providers;

use App\Models\Estate;
use App\Models\House;
use App\Models\Invoice;
use App\Models\User;
use App\Policies\EstatePolicy;
use App\Policies\HousePolicy;
use App\Policies\InvoicePolicy;
use App\Policies\UserPolicy;
use App\Services\PermissionValidationService;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Gate;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The model to policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        Estate::class => EstatePolicy::class,
        House::class => HousePolicy::class,
        Invoice::class => InvoicePolicy::class,
        User::class => UserPolicy::class,
    ];

    /**
     * Register any authentication / authorization services.
     */
    public function boot(): void
    {
        $this->registerPolicies();

        // Register essential gates that are not covered by policies
        $this->registerEssentialGates();
    }

    /**
     * Register essential gates that are not covered by policies
     */
    private function registerEssentialGates(): void
    {
        // Role-based gates using Spatie roles
        Gate::define('is-admin', fn ($user) => $user->hasRole('admin'));

        Gate::define('is-manager', fn ($user) => $user->hasRole('manager'));

        Gate::define('is-reviewer', fn ($user) => $user->hasRole('reviewer'));

        Gate::define('is-caretaker', fn ($user) => $user->hasRole('caretaker'));

        Gate::define('is-resident', fn ($user) => $user->hasRole('resident'));

        // Estate and house access gates (used by PermissionValidationService)
        Gate::define('access-estate', fn ($user, $estate) => app(PermissionValidationService::class)->canAccessEstate($user, $estate));

        Gate::define('access-house', fn ($user, $house) => app(PermissionValidationService::class)->canAccessHouse($user, $house));

        // System-level permission gates (not model-specific)
        Gate::define('system.settings.view', fn ($user) => $user->hasPermissionTo('system.settings.view'));

        Gate::define('system.settings.manage', fn ($user) => $user->hasPermissionTo('system.settings.manage'));

        Gate::define('audit.logs.view', fn ($user) => $user->hasPermissionTo('audit.logs.view'));

        Gate::define('audit.logs.export', fn ($user) => $user->hasPermissionTo('audit.logs.export'));

        // Dashboard access gates
        Gate::define('view-admin-dashboard', fn ($user) => $user->hasPermissionTo('view-admin-dashboard'));

        Gate::define('view-manager-dashboard', fn ($user) => $user->hasPermissionTo('view-manager-dashboard'));

        Gate::define('view-reviewer-dashboard', fn ($user) => $user->hasPermissionTo('view-reviewer-dashboard'));

        Gate::define('view-caretaker-dashboard', fn ($user) => $user->hasPermissionTo('view-caretaker-dashboard'));

        Gate::define('view-resident-dashboard', fn ($user) => $user->hasPermissionTo('view-resident-dashboard'));
    }
}
