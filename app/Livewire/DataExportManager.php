<?php

namespace App\Livewire;

use App\Models\ExportJob;
use App\Models\ExportTemplate;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\WithPagination;

class DataExportManager extends Component
{
    use WithPagination;

    public $showCreateModal = false;

    public $showTemplatesModal = false;

    public $entityType = 'estates';

    public $format = 'xlsx';

    public $selectedTemplate;

    public $filters = [];

    public $exportName = '';

    public $pollingInterval = 5; // seconds

    protected $rules = [
        'entityType' => 'required|in:estates,houses,contacts,meter_readings,invoices,water_rates',
        'format' => 'required|in:xlsx,csv,pdf',
        'exportName' => 'required|string|max:255',
    ];

    public function mount()
    {
        $this->exportName = 'Export-'.now()->format('Y-m-d');
    }

    public function createExport()
    {
        $this->validate();

        // Check user permissions for export
        $user = Auth::user();

        if (! $user->hasPermissionTo('export.data_all') && (! $user->hasPermissionTo('export.data_assigned') && ! $user->hasPermissionTo('export.data_own'))) {
            throw new AuthorizationException('You do not have permission to export data.');
        }

        // If template is selected, use its settings
        if ($this->selectedTemplate) {
            $template = ExportTemplate::find($this->selectedTemplate);
            if ($template) {
                $this->entityType = $template->entity_type;
                $this->format = $template->format;
                $this->filters = $template->filters ?? [];
            }
        }

        $exportJob = ExportJob::create([
            'user_id' => Auth::id(),
            'export_template_id' => $this->selectedTemplate,
            'status' => 'pending',
            'entity_type' => $this->entityType,
            'format' => $this->format,
            'filters' => $this->filters,
            'file_name' => $this->exportName.'.'.$this->format,
        ]);

        // Dispatch job for processing
        \App\Jobs\ProcessExportJob::dispatch($exportJob);

        $this->reset(['showCreateModal', 'selectedTemplate', 'filters', 'exportName']);
        $this->exportName = 'Export-'.now()->format('Y-m-d');

        session()->flash('message', 'Export job created successfully! It will be processed in the background.');
    }

    public function downloadExport(ExportJob $exportJob)
    {
        $user = Auth::user();

        // Check permissions for downloading export
        if (! $user->hasPermissionTo('export.data_all') && (! $user->hasPermissionTo('export.data_assigned') && ! $user->hasPermissionTo('export.data_own'))) {
            throw new AuthorizationException('You do not have permission to download this export.');
        }

        if ($exportJob->user_id !== Auth::id()) {
            abort(403);
        }

        if ($exportJob->status !== 'completed' || ! $exportJob->file_path) {
            session()->flash('error', 'Export is not ready for download.');

            return null;
        }

        return response()->download(storage_path('app/'.$exportJob->file_path), $exportJob->file_name);
    }

    public function deleteExport(ExportJob $exportJob)
    {
        $user = Auth::user();

        // Check permissions for deleting export
        if (! $user->hasPermissionTo('export.data_all') && (! $user->hasPermissionTo('export.data_assigned') && ! $user->hasPermissionTo('export.data_own'))) {
            throw new AuthorizationException('You do not have permission to delete this export.');
        }

        if ($exportJob->user_id !== Auth::id()) {
            abort(403);
        }

        // Delete file if exists
        if ($exportJob->file_path && \Storage::exists($exportJob->file_path)) {
            \Storage::delete($exportJob->file_path);
        }

        $exportJob->delete();
        session()->flash('message', 'Export deleted successfully.');
    }

    public function retryExport(ExportJob $exportJob)
    {
        $user = Auth::user();

        // Check permissions for retrying export
        if (! $user->hasPermissionTo('export.data_all') && (! $user->hasPermissionTo('export.data_assigned') && ! $user->hasPermissionTo('export.data_own'))) {
            throw new AuthorizationException('You do not have permission to retry this export.');
        }

        if ($exportJob->user_id !== Auth::id()) {
            abort(403);
        }

        if ($exportJob->status !== 'failed') {
            session()->flash('error', 'Only failed exports can be retried.');

            return;
        }

        // Reset job status and dispatch again
        $exportJob->update([
            'status' => 'pending',
            'started_at' => null,
            'completed_at' => null,
            'error' => null,
        ]);

        \App\Jobs\ProcessExportJob::dispatch($exportJob);

        session()->flash('message', 'Export job retried successfully!');
    }

    public function getPollingInterval()
    {
        // Only poll if there are pending or processing jobs
        $hasActiveJobs = ExportJob::forUser(Auth::id())
            ->whereIn('status', ['pending', 'processing'])
            ->exists();

        return $hasActiveJobs ? $this->pollingInterval * 1000 : null; // Return milliseconds or null
    }

    public function render()
    {
        $lengthAwarePaginator = ExportJob::forUser(Auth::id())
            ->with('exportTemplate')
            ->latest()
            ->paginate(15);

        $templates = ExportTemplate::forUser(Auth::id())
            ->forEntityType($this->entityType)
            ->get();

        $availableEntities = [
            'estates' => 'Estates',
            'houses' => 'Houses',
            'contacts' => 'Contacts',
            'meter_readings' => 'Meter Readings',
            'invoices' => 'Invoices',
            'water_rates' => 'Water Rates',
        ];

        return view('livewire.data-export-manager', [
            'exportJobs' => $lengthAwarePaginator,
            'templates' => $templates,
            'availableEntities' => $availableEntities,
            'pendingJobs' => ExportJob::forUser(Auth::id())->pending()->count(),
            'processingJobs' => ExportJob::forUser(Auth::id())->processing()->count(),
        ]);
    }
}
