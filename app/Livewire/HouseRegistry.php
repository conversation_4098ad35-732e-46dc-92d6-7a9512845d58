<?php

namespace App\Livewire;

use App\Models\Estate;
use App\Models\House;
use App\Traits\HasPermissions;
// Removed WithPermissionChecks - functionality moved to HasPermissions
use Livewire\Component;
use Livewire\WithPagination;

class HouseRegistry extends Component
{
    use HasPermissions;
    use WithPagination;

    public $showCreateModal = false;

    public $showEditModal = false;

    public $showDeleteModal = false;

    public $houseToEdit;

    public $houseToDelete;

    public $search = '';

    public $estateFilter = '';

    public $occupancyFilter = '';

    public $sortBy = 'house_number';

    public $sortDirection = 'asc';

    public $form = [
        'house_number' => '',
        'estate_id' => '',
        'meter_number' => '',
        'address' => '',
    ];

    protected function rules()
    {
        $rules = [
            'form.house_number' => 'required|string|max:50',
            'form.estate_id' => 'required|exists:estates,id',
            'form.meter_number' => 'nullable|string|max:50|unique:houses,meter_number',
            'form.address' => 'nullable|string|max:500',
        ];

        if ($this->houseToEdit) {
            $rules['form.meter_number'] .= ','.$this->houseToEdit;
        }

        return $rules;
    }

    public function openCreateModal()
    {
        $this->reset('form', 'houseToEdit');
        $this->showCreateModal = true;
    }

    public function openEditModal($houseId)
    {
        $this->reset('form');
        $house = House::findOrFail($houseId);

        $this->form = [
            'house_number' => $house->house_number,
            'estate_id' => $house->estate_id,
            'meter_number' => $house->meter_number ?? '',
            'address' => $house->address ?? '',
        ];

        $this->houseToEdit = $houseId;
        $this->showEditModal = true;
    }

    public function confirmDelete($houseId)
    {
        $this->houseToDelete = $houseId;
        $this->showDeleteModal = true;
    }

    public function save()
    {
        $this->validate();

        if ($this->houseToEdit) {
            $house = House::findOrFail($this->houseToEdit);
            $house->update($this->form);
            $this->dispatch('house-saved', message: 'House updated successfully.');
        } else {
            House::create($this->form);
            $this->dispatch('house-saved', message: 'House created successfully.');
        }

        $this->reset(['showCreateModal', 'showEditModal', 'form', 'houseToEdit']);
    }

    public function delete()
    {
        if ($this->houseToDelete) {
            $house = House::findOrFail($this->houseToDelete);

            // Check if house has contacts
            if ($house->contacts()->count() > 0) {
                $this->addError('deletion', 'Cannot delete house that has contacts associated with it.');

                return;
            }

            // Check if house has meter readings
            if ($house->meterReadings()->count() > 0) {
                $this->addError('deletion', 'Cannot delete house that has meter readings associated with it.');

                return;
            }

            // Check if house has invoices
            if ($house->invoices()->count() > 0) {
                $this->addError('deletion', 'Cannot delete house that has invoices associated with it.');

                return;
            }

            $house->delete();
            $this->dispatch('house-deleted', message: 'House deleted successfully.');
        }

        $this->reset(['showDeleteModal', 'houseToDelete']);
    }

    public function getUserRole()
    {
        if ($this->isAdmin()) {
            return 'admin';
        }
        if ($this->hasPermission('estate.view_all')) {
            return 'manager';
        }
        if ($this->hasPermission('billing.view_all')) {
            return 'reviewer';
        }
        if ($this->hasPermission('estate.view_assigned')) {
            return 'caretaker';
        }

        return 'guest';
    }

    public function getHouseShowRoute($house)
    {
        $role = $this->getUserRole();

        return match ($role) {
            'manager' => route('management.houses.show', $house),
            'reviewer' => route('reviewer.houses.show', $house),
            'caretaker' => route('caretaker.houses.show', $house),
            default => route('houses.show', $house), // Fallback to generic route
        };
    }

    public function getHouseEditRoute($house)
    {
        $role = $this->getUserRole();

        return match ($role) {
            'manager' => route('management.houses.edit', $house),
            'reviewer' => route('reviewer.houses.edit', $house),
            'caretaker' => route('caretaker.houses.edit', $house),
            default => route('houses.edit', $house), // Fallback to generic route
        };
    }

    public function getHouseCreateRoute($estateId = null)
    {
        $role = $this->getUserRole();
        $params = $estateId ? ['estate' => $estateId] : [];

        return match ($role) {
            'manager' => route('management.houses.create', $params),
            'reviewer' => route('reviewer.houses.create', $params),
            'caretaker' => route('caretaker.houses.create', $params),
            default => route('houses.create', $params), // Fallback to generic route
        };
    }

    public function render()
    {
        $builder = House::with(['estate', 'contacts']);

        // Apply search filter
        if ($this->search) {
            $builder->where('house_number', 'like', '%'.$this->search.'%')
                ->orWhere('address', 'like', '%'.$this->search.'%');
        }

        // Apply estate filter
        if ($this->estateFilter) {
            $builder->where('estate_id', $this->estateFilter);
        }

        // Apply occupancy filter
        if ($this->occupancyFilter === 'occupied') {
            $builder->whereHas('contacts');
        } elseif ($this->occupancyFilter === 'vacant') {
            $builder->whereDoesntHave('contacts');
        }

        // Apply sorting
        $builder->orderBy($this->sortBy, $this->sortDirection);

        $lengthAwarePaginator = $builder->paginate(10);

        // Calculate statistics
        $totalHouses = House::count();
        $occupiedHouses = House::whereHas('contacts')->count();
        $vacantHouses = $totalHouses - $occupiedHouses;
        $housesWithMeters = House::whereNotNull('meter_number')->count();

        return view('livewire.house-registry', [
            'houses' => $lengthAwarePaginator,
            'estates' => Estate::orderBy('name')->get(),
            'totalHouses' => $totalHouses,
            'occupiedHouses' => $occupiedHouses,
            'vacantHouses' => $vacantHouses,
            'housesWithMeters' => $housesWithMeters,
        ]);
    }
}
