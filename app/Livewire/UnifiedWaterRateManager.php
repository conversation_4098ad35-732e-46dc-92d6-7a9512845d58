<?php

namespace App\Livewire;

use App\Models\Estate;
use App\Models\WaterRate;
use App\Rules\WaterRateDateOverlap;
use App\Traits\HasPermissions;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\WithPagination;

class UnifiedWaterRateManager extends Component
{
    use HasPermissions, WithPagination;

    // Mode management
    public $mode = 'list'; // list, create, edit

    public $estateId;

    public $waterRateId;

    // List view properties
    public $search = '';

    public $sortBy = 'effective_from';

    public $sortDirection = 'desc';

    public array $filters = [];

    // Form properties
    public $name;

    public $rate_per_unit = 0.0;

    public $minimum_charge = 0.0;

    public $minimum_units = 0;

    public $fixed_charge = 0.0;

    public $effective_from;

    public $effective_to;

    public $is_active = true;

    public $description;

    // Collections
    public $estates = [];

    protected $queryString = ['filters'];

    protected function rules()
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'rate_per_unit' => ['required', 'numeric', 'min:0'],
            'minimum_charge' => ['nullable', 'numeric', 'min:0'],
            'minimum_units' => ['nullable', 'integer', 'min:0'],
            'fixed_charge' => ['nullable', 'numeric', 'min:0'],
            'effective_from' => [
                'required',
                'date',
                new WaterRateDateOverlap(
                    $this->estateId,
                    $this->waterRateId,
                    $this->effective_to
                ),
            ],
            'effective_to' => ['nullable', 'date', 'after_or_equal:effective_from'],
            'is_active' => ['boolean'],
            'description' => ['nullable', 'string', 'max:1000'],
        ];
    }

    protected $messages = [
        'name.required' => 'Rate name is required.',
        'rate_per_unit.required' => 'Rate per unit is required.',
        'rate_per_unit.numeric' => 'Rate per unit must be a number.',
        'rate_per_unit.min' => 'Rate per unit cannot be negative.',
        'effective_from.required' => 'Effective from date is required.',
        'effective_to.after_or_equal' => 'Effective to date must be after or equal to effective from date.',
    ];

    public function mount()
    {
        $this->authorizeAccess();
        $this->syncFiltersFromProperties();
        $this->loadCollections();
        $this->setDefaultValues();
    }

    private function authorizeAccess()
    {
        if (! $this->hasAnyPermission(['rates.view_all', 'rates.view_assigned'])) {
            abort(403, 'You do not have permission to view water rates.');
        }
    }

    private function syncFiltersFromProperties()
    {
        $this->filters = [
            'search' => $this->search,
            'sortBy' => $this->sortBy,
            'sortDirection' => $this->sortDirection,
        ];
    }

    private function loadCollections()
    {
        if ($this->hasPermission('estates.view_all')) {
            $this->estates = Estate::orderBy('name')->get();
        } elseif ($this->hasPermission('estates.view_assigned')) {
            $this->estates = Auth::user()->assignedEstates()->orderBy('name')->get();
        } else {
            $this->estates = collect();
        }
    }

    private function setDefaultValues()
    {
        $this->effective_from = now()->format('Y-m-d');
        $this->is_active = true;
    }

    // Mode management
    public function setMode($mode, $waterRateId = null, $estateId = null)
    {
        // Check permissions for mode changes
        if ($mode === 'create' && ! $this->hasPermission('rates.create')) {
            abort(403, 'You do not have permission to create water rates.');
        }

        if (($mode === 'edit' || $mode === 'delete') && ! $this->hasAnyPermission(['rates.edit_all', 'rates.edit_assigned'])) {
            abort(403, 'You do not have permission to edit water rates.');
        }

        $this->mode = $mode;
        $this->waterRateId = $waterRateId;
        $this->estateId = $estateId;

        if ($mode === 'create') {
            $this->resetForm();
            $this->setDefaultValues();
        } elseif ($mode === 'edit' && $waterRateId) {
            $this->loadWaterRate($waterRateId);
        }
    }

    public function resetToList()
    {
        $this->mode = 'list';
        $this->resetForm();
    }

    private function resetForm()
    {
        $this->reset([
            'name', 'rate_per_unit', 'minimum_charge', 'minimum_units',
            'fixed_charge', 'effective_from', 'effective_to', 'is_active', 'description',
        ]);
    }

    private function loadWaterRate($waterRateId)
    {
        $waterRate = WaterRate::findOrFail($waterRateId);
        $this->estateId = $waterRate->estate_id;
        $this->name = $waterRate->name;
        $this->rate_per_unit = $waterRate->rate_per_unit;
        $this->minimum_charge = $waterRate->minimum_charge;
        $this->minimum_units = $waterRate->minimum_units;
        $this->fixed_charge = $waterRate->fixed_charge;
        $this->effective_from = $waterRate->effective_from->format('Y-m-d');
        $this->effective_to = $waterRate->effective_to ? $waterRate->effective_to->format('Y-m-d') : null;
        $this->is_active = $waterRate->is_active;
        $this->description = $waterRate->description;
    }

    // Form actions
    public function save()
    {
        $this->validate();

        // Check permissions
        if ($this->mode === 'create' && ! $this->hasPermission('rates.create')) {
            abort(403, 'You do not have permission to create water rates.');
        }

        if ($this->mode === 'edit' && ! $this->hasAnyPermission(['rates.edit_all', 'rates.edit_assigned'])) {
            abort(403, 'You do not have permission to edit water rates.');
        }

        // Ensure dates are properly formatted
        $effectiveFrom = $this->effective_from;
        $effectiveTo = $this->effective_to;

        if (is_string($effectiveFrom)) {
            $effectiveFrom = Carbon::parse($effectiveFrom);
        }

        if (is_string($effectiveTo)) {
            $effectiveTo = Carbon::parse($effectiveTo);
        }

        $waterRateData = [
            'estate_id' => $this->estateId,
            'name' => $this->name,
            'rate_per_unit' => (float) $this->rate_per_unit,
            'minimum_charge' => (float) ($this->minimum_charge ?: 0),
            'minimum_units' => (int) ($this->minimum_units ?: 0),
            'fixed_charge' => (float) ($this->fixed_charge ?: 0),
            'effective_from' => $effectiveFrom,
            'effective_to' => $effectiveTo,
            'is_active' => (bool) ($this->is_active ?? true),
            'description' => $this->description,
        ];

        if ($this->mode === 'create') {
            WaterRate::create($waterRateData);
            $message = 'Water rate created successfully.';
        } else {
            $waterRate = WaterRate::findOrFail($this->waterRateId);
            $waterRate->update($waterRateData);
            $message = 'Water rate updated successfully.';
        }

        session()->flash('message', $message);
        $this->resetToList();
    }

    // List view data
    public function getWaterRatesProperty()
    {
        $query = WaterRate::with('estate');

        // Apply permission-based scoping
        $query = $this->applyWaterRateScope($query);

        // Apply estate filter
        if ($this->estateId) {
            $query->where('estate_id', $this->estateId);
        }

        // Apply search filter
        if ($this->search) {
            $query->where(function ($q): void {
                $q->where('name', 'like', '%'.$this->search.'%')
                    ->orWhereHas('estate', function ($q): void {
                        $q->where('name', 'like', '%'.$this->search.'%');
                    });
            });
        }

        // Apply sorting
        $query->orderBy($this->sortBy, $this->sortDirection);

        return $query->paginate(20);
    }

    public function getEstatesProperty()
    {
        if ($this->hasPermission('estates.view_all')) {
            return Estate::orderBy('name')->get();
        } elseif ($this->hasPermission('estates.view_assigned')) {
            return Auth::user()->assignedEstates()->orderBy('name')->get();
        } else {
            return collect();
        }
    }

    // Filter methods
    public function updatedSearch($value)
    {
        $this->syncFilters();
    }

    public function updatedEstateId($value)
    {
        $this->syncFilters();
    }

    public function updatedSortBy($value)
    {
        $this->syncFilters();
    }

    public function updatedSortDirection($value)
    {
        $this->syncFilters();
    }

    private function syncFilters()
    {
        $this->filters = [
            'search' => $this->search,
            'estateId' => $this->estateId,
            'sortBy' => $this->sortBy,
            'sortDirection' => $this->sortDirection,
        ];
    }

    public function resetFilters()
    {
        $this->reset(['search', 'estateId']);
        $this->sortBy = 'effective_from';
        $this->sortDirection = 'desc';
        $this->syncFilters();
    }

    public function toggleSort($field)
    {
        if ($this->sortBy === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortBy = $field;
            $this->sortDirection = 'asc';
        }
        $this->syncFilters();
    }

    // Individual actions
    public function delete($waterRateId)
    {
        if (! $this->hasPermission('rates.delete')) {
            abort(403, 'You do not have permission to delete water rates.');
        }

        $waterRate = WaterRate::findOrFail($waterRateId);

        // Check if user can delete this specific water rate
        if (! $this->canDeleteWaterRate($waterRate)) {
            abort(403, 'You do not have permission to delete this water rate.');
        }

        // Check if the water rate is in use by any invoices
        $invoiceCount = \App\Models\Invoice::where('water_rate_id', $waterRate->id)->count();

        if ($invoiceCount > 0) {
            session()->flash('error', 'Cannot delete water rate that is in use by invoices');

            return;
        }

        $waterRate->delete();
        session()->flash('message', 'Water rate deleted successfully!');
    }

    public function toggleActive($waterRateId)
    {
        if (! $this->hasAnyPermission(['rates.edit_all', 'rates.edit_assigned'])) {
            abort(403, 'You do not have permission to edit water rates.');
        }

        $waterRate = WaterRate::findOrFail($waterRateId);

        // Check if user can edit this specific water rate
        if (! $this->canEditWaterRate($waterRate)) {
            abort(403, 'You do not have permission to edit this water rate.');
        }

        $waterRate->is_active = ! $waterRate->is_active;
        $waterRate->save();

        $status = $waterRate->is_active ? 'activated' : 'deactivated';
        session()->flash('message', "Water rate {$status} successfully!");
    }

    // Utility methods
    public function isRateActive($waterRate)
    {
        $now = now();

        return $waterRate->is_active &&
               $waterRate->effective_from <= $now &&
               (! $waterRate->effective_to || $waterRate->effective_to >= $now);
    }

    public function getRateStatus($waterRate)
    {
        if (! $waterRate->is_active) {
            return 'inactive';
        }

        $now = now();

        if ($waterRate->effective_from > $now) {
            return 'future';
        }

        if ($waterRate->effective_to && $waterRate->effective_to < $now) {
            return 'expired';
        }

        return 'active';
    }

    // Permission-based scoping
    private function applyWaterRateScope($query)
    {
        $user = Auth::user();

        // Admin and management can see all water rates
        if ($this->hasPermission('rates.view_all')) {
            return $query;
        }

        // Assigned users can only see water rates for estates they're assigned to
        if ($this->hasPermission('rates.view_assigned')) {
            return $query->whereHas('estate', function ($q) use ($user): void {
                $q->whereHas('assignedUsers', function ($q) use ($user): void {
                    $q->where('user_id', $user->id);
                });
            });
        }

        // Default: no water rates visible
        return $query->whereRaw('1 = 0');
    }

    // Permission-based utility methods
    public function canCreateWaterRate()
    {
        return $this->hasPermission('rates.create');
    }

    public function canEditWaterRate($waterRate)
    {
        if (! $this->hasAnyPermission(['rates.edit_all', 'rates.edit_assigned'])) {
            return false;
        }

        if (! $this->hasPermission('rates.edit_all')) {
            return $this->isUserAssignedToEstate($waterRate->estate);
        }

        return true;
    }

    public function canDeleteWaterRate($waterRate)
    {
        if (! $this->hasPermission('rates.delete')) {
            return false;
        }

        if (! $this->hasPermission('rates.delete')) {
            return $this->isUserAssignedToEstate($waterRate->estate);
        }

        return true;
    }

    private function isUserAssignedToEstate($estate)
    {
        $user = Auth::user();

        if ($this->hasPermission('estates.view_assigned')) {
            return $estate->assignedUsers()->where('user_id', $user->id)->exists();
        }

        return false;
    }

    // Permission-based UI elements
    public function getShowCreateButtonProperty()
    {
        return $this->hasPermission('rates.create');
    }

    public function getShowEditActionsProperty()
    {
        return $this->hasAnyPermission(['rates.edit_all', 'rates.edit_assigned', 'rates.delete']);
    }

    public function getRequiredPermissionsHintProperty()
    {
        $hints = [];

        if (! $this->hasAnyPermission(['rates.view_all', 'rates.view_assigned'])) {
            $hints[] = 'View water rates permission';
        }

        if (! $this->hasPermission('rates.create') && $this->hasAnyPermission(['rates.view_all', 'rates.view_assigned'])) {
            $hints[] = 'Create water rates permission';
        }

        if (! $this->hasAnyPermission(['rates.edit_all', 'rates.edit_assigned']) && $this->hasAnyPermission(['rates.view_all', 'rates.view_assigned'])) {
            $hints[] = 'Edit water rates permission';
        }

        if (! $this->hasPermission('rates.delete') && $this->hasAnyPermission(['rates.view_all', 'rates.view_assigned'])) {
            $hints[] = 'Delete water rates permission';
        }

        return $hints;
    }

    public function render()
    {
        return view('livewire.water-rate-manager', [
            'waterRates' => $this->waterRates,
            'estates' => $this->estates,
            'mode' => $this->mode,
            'requiredPermissionsHint' => $this->requiredPermissionsHint,
            'showCreateButton' => $this->showCreateButton,
            'showEditActions' => $this->showEditActions,
            'canEditWaterRate' => fn ($rate) => $this->canEditWaterRate($rate),
            'canDeleteWaterRate' => fn ($rate) => $this->canDeleteWaterRate($rate),
        ]);
    }
}
