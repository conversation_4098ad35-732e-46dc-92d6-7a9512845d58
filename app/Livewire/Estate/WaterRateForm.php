<?php

namespace App\Livewire\Estate;

use App\Models\Estate;
use App\Models\WaterRate;
use App\Rules\WaterRateDateOverlap;
use Carbon\Carbon; // Import Log facade
use Illuminate\Support\Facades\Log; // Import Carbon
use Livewire\Component; // Import custom rule

class WaterRateForm extends Component
{
    public Estate $estate;

    public ?WaterRate $waterRate = null;

    public $name = '';

    public $rate_per_unit = 0.0;

    public $minimum_charge = 0.0;

    public $minimum_units = 0;

    public $fixed_charge = 0.0;

    public $effective_from;

    public $effective_to;

    public $is_active = true;

    public $description;

    public bool $showModal = false;

    protected $listeners = ['openWaterRateForm'];

    public function mount(Estate $estate, ?WaterRate $waterRate = null)
    {
        $this->estate = $estate;
        $this->waterRate = $waterRate;

        if ($this->waterRate instanceof \App\Models\WaterRate) {
            // Explicitly assign properties to avoid issues with toArray() and Carbon types
            $this->name = $this->waterRate->name;
            $this->rate_per_unit = $this->waterRate->rate_per_unit;
            $this->minimum_charge = $this->waterRate->minimum_charge;
            $this->minimum_units = $this->waterRate->minimum_units;
            $this->fixed_charge = $this->waterRate->fixed_charge;
            $this->effective_from = $this->waterRate->effective_from; // Already Carbon
            $this->effective_to = $this->waterRate->effective_to;     // Already Carbon
            $this->is_active = $this->waterRate->is_active;
            $this->description = $this->waterRate->description;
        } else {
            // Set default values for new rates when mounting
            $this->name = '';
            $this->rate_per_unit = 0.0;
            $this->minimum_charge = 0.0;
            $this->minimum_units = 0;
            $this->fixed_charge = 0.0;
            $this->effective_from = now();
            $this->effective_to = null;
            $this->is_active = true;
            $this->description = null;
        }
    }

    public function openWaterRateForm(?WaterRate $waterRate = null)
    {
        $this->resetValidation();
        $this->waterRate = $waterRate;

        if ($this->waterRate instanceof \App\Models\WaterRate) {
            // Explicitly assign properties to avoid issues with toArray() and Carbon types
            $this->name = $this->waterRate->name;
            $this->rate_per_unit = $this->waterRate->rate_per_unit;
            $this->minimum_charge = $this->waterRate->minimum_charge;
            $this->minimum_units = $this->waterRate->minimum_units;
            $this->fixed_charge = $this->waterRate->fixed_charge;
            $this->effective_from = $this->waterRate->effective_from; // Already Carbon
            $this->effective_to = $this->waterRate->effective_to;     // Already Carbon
            $this->is_active = $this->waterRate->is_active;
            $this->description = $this->waterRate->description;
        } else {
            // Set default values for new rates when opening form
            $this->name = '';
            $this->rate_per_unit = 0.0;
            $this->minimum_charge = 0.0;
            $this->minimum_units = 0;
            $this->fixed_charge = 0.0;
            $this->effective_from = now();
            $this->effective_to = null;
            $this->is_active = true;
            $this->description = null;
        }

        $this->showModal = true;
    }

    public function closeWaterRateForm()
    {
        $this->showModal = false;
    }

    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'rate_per_unit' => ['required', 'numeric', 'min:0'],
            'minimum_charge' => ['nullable', 'numeric', 'min:0'],
            'minimum_units' => ['nullable', 'integer', 'min:0'],
            'fixed_charge' => ['nullable', 'numeric', 'min:0'],
            'effective_from' => [
                'required',
                'date',
                new WaterRateDateOverlap(
                    $this->estate->id,
                    $this->waterRate?->id,
                    $this->effective_to
                ),
            ],
            'effective_to' => ['nullable', 'date', 'after_or_equal:effective_from'],
            'is_active' => ['nullable', 'boolean'],
            'description' => ['nullable', 'string', 'max:1000'],
        ];
    }

    public function save()
    {
        $this->validate();

        // Ensure dates are properly formatted
        $effectiveFrom = $this->effective_from;
        $effectiveTo = $this->effective_to;

        if (is_string($effectiveFrom)) {
            $effectiveFrom = Carbon::parse($effectiveFrom);
        }

        if (is_string($effectiveTo)) {
            $effectiveTo = Carbon::parse($effectiveTo);
        }

        $data = [
            'estate_id' => $this->estate->id,
            'name' => $this->name,
            'rate_per_unit' => (float) $this->rate_per_unit,
            'minimum_charge' => (float) ($this->minimum_charge ?: 0),
            'minimum_units' => (int) ($this->minimum_units ?: 0),
            'fixed_charge' => (float) ($this->fixed_charge ?: 0),
            'effective_from' => $effectiveFrom,
            'effective_to' => $effectiveTo,
            'is_active' => (bool) ($this->is_active ?? true),
            'description' => $this->description,
        ];

        if ($this->waterRate && $this->waterRate->exists) {
            $this->waterRate->update($data);
            $this->dispatch('waterRateUpdated');
        } else {
            WaterRate::create($data);
            $this->dispatch('waterRateCreated');
        }

        $this->closeWaterRateForm();
    }

    public function render()
    {
        return view('livewire.estate.water-rate-form');
    }
}
