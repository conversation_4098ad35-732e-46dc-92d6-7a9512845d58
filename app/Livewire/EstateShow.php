<?php

namespace App\Livewire;

use App\Models\Estate;
use App\Models\User;
use App\Services\EstateAssignmentService;
use App\Traits\HasPermissions;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class EstateShow extends Component
{
    use HasPermissions;

    public Estate $estate;

    public string $selectedTab = 'overview'; // Default tab

    // Modal states
    public $showAssignManagerModal = false;

    public $showAssignReviewerModal = false;

    public $showAssignCaretakerModal = false;

    // Available users for assignment
    public $availableManagers = [];

    public $availableReviewers = [];

    public $availableCaretakers = [];

    // Selected users for assignment
    public $selectedManagerIds = [];

    public $selectedReviewerIds = [];

    public $selectedCaretakerIds = [];

    protected $estateAssignmentService;

    protected $rules = [
        'selectedManagerIds' => 'required|array|min:1',
        'selectedManagerIds.*' => 'exists:users,id',
        'selectedReviewerIds' => 'required|array|min:1',
        'selectedReviewerIds.*' => 'exists:users,id',
        'selectedCaretakerIds' => 'required|array|min:1',
        'selectedCaretakerIds.*' => 'exists:users,id',
    ];

    public function boot(EstateAssignmentService $estateAssignmentService)
    {
        $this->estateAssignmentService = $estateAssignmentService;
    }

    public function mount(Estate $estate): void
    {
        $this->estate = $estate;
    }

    public function getUserRole()
    {
        if ($this->isAdmin()) {
            return 'admin';
        }
        if ($this->hasPermission('estate.view_all')) {
            return 'manager';
        }
        if ($this->hasPermission('billing.view_all')) {
            return 'reviewer';
        }
        if ($this->hasPermission('estate.view_assigned')) {
            return 'caretaker';
        }

        return 'guest';
    }

    public function getEstatesRoute()
    {
        $role = $this->getUserRole();

        return match ($role) {
            'manager' => route('management.estates'),
            'reviewer' => route('reviewer.estates'),
            'caretaker' => route('caretaker.estates'),
            default => route('estates'), // Fallback to generic route
        };
    }

    public function getEstateEditRoute($estate)
    {
        $role = $this->getUserRole();

        return match ($role) {
            'manager' => route('management.estates.edit', $estate),
            'reviewer' => route('management.estates.edit', $estate), // Reviewers use management routes for estates
            'caretaker' => route('caretaker.estates.edit', $estate),
            default => route('estates.edit', $estate), // Fallback to generic route
        };
    }

    public function getHouseCreateRoute($estateId = null)
    {
        $role = $this->getUserRole();
        $params = $estateId ? ['estate' => $estateId] : [];

        return match ($role) {
            'manager' => route('management.houses.create', $params),
            'reviewer' => route('reviewer.houses.create', $params),
            'caretaker' => route('caretaker.houses.create', $params),
            default => route('houses.create', $params), // Fallback to generic route
        };
    }

    public function getHousesRoute($estateId = null)
    {
        $role = $this->getUserRole();
        $params = $estateId ? ['estate' => $estateId] : [];

        return match ($role) {
            'manager' => route('management.houses', $params),
            'reviewer' => route('reviewer.houses', $params),
            'caretaker' => $estateId ? route('caretaker.houses', ['estateId' => $estateId]) : route('caretaker.houses'),
            default => route('houses', $params), // Fallback to generic route
        };
    }

    public function getHouseShowRoute($house)
    {
        $role = $this->getUserRole();

        return match ($role) {
            'manager' => route('management.houses.show', $house),
            'reviewer' => route('reviewer.houses.show', $house),
            'caretaker' => route('caretaker.houses.show', $house),
            default => route('houses.show', $house), // Fallback to generic route
        };
    }

    // Modal methods
    public function openAssignManagerModal()
    {
        $this->availableManagers = User::role('manager')
            ->whereDoesntHave('assignedEstates', function ($query): void {
                $query->where('estate_id', $this->estate->id);
            })
            ->get();
        $this->showAssignManagerModal = true;
    }

    public function openAssignReviewerModal()
    {
        $this->availableReviewers = User::role('reviewer')
            ->whereDoesntHave('assignedEstates', function ($query): void {
                $query->where('estate_id', $this->estate->id);
            })
            ->get();
        $this->showAssignReviewerModal = true;
    }

    public function openAssignCaretakerModal()
    {
        $this->availableCaretakers = User::role('caretaker')
            ->whereDoesntHave('assignedEstates', function ($query): void {
                $query->where('estate_id', $this->estate->id);
            })
            ->get();
        $this->showAssignCaretakerModal = true;
    }

    public function closeAssignManagerModal()
    {
        $this->showAssignManagerModal = false;
        $this->selectedManagerIds = [];
        $this->availableManagers = [];
    }

    public function closeAssignReviewerModal()
    {
        $this->showAssignReviewerModal = false;
        $this->selectedReviewerIds = [];
        $this->availableReviewers = [];
    }

    public function closeAssignCaretakerModal()
    {
        $this->showAssignCaretakerModal = false;
        $this->selectedCaretakerIds = [];
        $this->availableCaretakers = [];
    }

    public function assignManagers()
    {
        $this->validate([
            'selectedManagerIds' => 'required|array|min:1',
            'selectedManagerIds.*' => 'exists:users,id',
        ]);

        foreach ($this->selectedManagerIds as $selectedManagerId) {
            $manager = User::find($selectedManagerId);
            if ($manager) {
                $currentEstates = $manager->assignedEstates()->pluck('estates.id')->toArray();
                $newEstates = array_unique(array_merge($currentEstates, [$this->estate->id]));
                $this->estateAssignmentService->assignUserToEstates($manager, $newEstates, Auth::user());
            }
        }

        $this->closeAssignManagerModal();
        $this->dispatch('notify', [
            'type' => 'success',
            'message' => 'Managers assigned successfully.',
        ]);
    }

    public function assignReviewers()
    {
        $this->validate([
            'selectedReviewerIds' => 'required|array|min:1',
            'selectedReviewerIds.*' => 'exists:users,id',
        ]);

        foreach ($this->selectedReviewerIds as $selectedReviewerId) {
            $reviewer = User::find($selectedReviewerId);
            if ($reviewer) {
                $currentEstates = $reviewer->assignedEstates()->pluck('estates.id')->toArray();
                $newEstates = array_unique(array_merge($currentEstates, [$this->estate->id]));
                $this->estateAssignmentService->assignUserToEstates($reviewer, $newEstates, Auth::user());
            }
        }

        $this->closeAssignReviewerModal();
        $this->dispatch('notify', [
            'type' => 'success',
            'message' => 'Reviewers assigned successfully.',
        ]);
    }

    public function assignCaretakers()
    {
        $this->validate([
            'selectedCaretakerIds' => 'required|array|min:1',
            'selectedCaretakerIds.*' => 'exists:users,id',
        ]);

        foreach ($this->selectedCaretakerIds as $selectedCaretakerId) {
            $caretaker = User::find($selectedCaretakerId);
            if ($caretaker) {
                $currentEstates = $caretaker->assignedEstates()->pluck('estates.id')->toArray();
                $newEstates = array_unique(array_merge($currentEstates, [$this->estate->id]));
                $this->estateAssignmentService->assignUserToEstates($caretaker, $newEstates, Auth::user());
            }
        }

        $this->closeAssignCaretakerModal();
        $this->dispatch('notify', [
            'type' => 'success',
            'message' => 'Caretakers assigned successfully.',
        ]);
    }

    public function render()
    {
        $recentHouses = $this->estate->houses()
            ->with('contacts')
            ->latest()
            ->limit(5)
            ->get();

        // Get assigned staff
        $assignedManagers = User::role('manager')
            ->whereHas('assignedEstates', function ($query): void {
                $query->where('estate_id', $this->estate->id);
            })
            ->get();

        $assignedReviewers = User::role('reviewer')
            ->whereHas('assignedEstates', function ($query): void {
                $query->where('estate_id', $this->estate->id);
            })
            ->get();

        $assignedCaretakers = User::role('caretaker')
            ->whereHas('assignedEstates', function ($query): void {
                $query->where('estate_id', $this->estate->id);
            })
            ->get();

        $stats = [
            'total_houses' => $this->estate->total_houses ?? $this->estate->houses()->count(),
            'occupied_houses' => $this->estate->occupied_houses ?? $this->estate->houses()->whereHas('contacts')->count(),
            'vacant_houses' => ($this->estate->total_houses ?? $this->estate->houses()->count()) - ($this->estate->occupied_houses ?? $this->estate->houses()->whereHas('contacts')->count()),
            'occupancy_rate' => ($this->estate->total_houses ?? $this->estate->houses()->count()) > 0
                ? round((($this->estate->occupied_houses ?? $this->estate->houses()->whereHas('contacts')->count()) / ($this->estate->total_houses ?? $this->estate->houses()->count())) * 100, 1)
                : 0,
        ];

        return view('livewire.estate-show', [
            'recentHouses' => $recentHouses,
            'stats' => $stats,
            'assignedManagers' => $assignedManagers,
            'assignedReviewers' => $assignedReviewers,
            'assignedCaretakers' => $assignedCaretakers,
        ]);
    }
}
