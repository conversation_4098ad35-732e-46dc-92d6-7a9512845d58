<?php

namespace App\Livewire\Dashboard;

use App\Models\Invoice;
use App\Models\MeterReading;
use App\Traits\HasPermissions;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class PermissionActionButtons extends Component
{
    use HasPermissions;

    public $resourceId;

    public $resourceType; // reading, invoice, house

    public $resourceStatus;

    protected $listeners = ['refreshDashboard' => '$refresh'];

    public function getAvailableActionsProperty()
    {
        $actions = [];

        return match ($this->resourceType) {
            'reading' => $this->getReadingActions(),
            'invoice' => $this->getInvoiceActions(),
            'house' => $this->getHouseActions(),
            default => $actions,
        };
    }

    private function getReadingActions()
    {
        $actions = [];
        $reading = MeterReading::find($this->resourceId);

        if (! $reading) {
            return $actions;
        }

        // Caretaker actions for their own draft readings
        if ($this->canAccessDashboardView('caretaker') &&
            $reading->user_id === Auth::id() &&
            $reading->status === 'draft') {

            if ($this->hasPermission('meter_readings.submit')) {
                $actions[] = [
                    'label' => 'Submit for Review',
                    'action' => 'submitForReview',
                    'color' => 'blue',
                    'icon' => 'paper-airplane',
                ];
            }

            if ($this->hasPermission('meter_readings.delete')) {
                $actions[] = [
                    'label' => 'Delete',
                    'action' => 'deleteReading',
                    'color' => 'red',
                    'icon' => 'trash',
                ];
            }
        }

        // Reviewer actions for submitted readings
        if ($this->canAccessDashboardView('reviewer') &&
            $reading->status === 'submitted') {

            if ($this->hasPermission('meter_readings.approve')) {
                $actions[] = [
                    'label' => 'Approve',
                    'action' => 'approveReading',
                    'color' => 'green',
                    'icon' => 'check',
                ];
            }

            if ($this->hasPermission('meter_readings.reject')) {
                $actions[] = [
                    'label' => 'Reject',
                    'action' => 'rejectReading',
                    'color' => 'red',
                    'icon' => 'x-mark',
                ];
            }

            if ($this->hasPermission('meter_readings.validate')) {
                $actions[] = [
                    'label' => 'Validate',
                    'action' => 'validateReading',
                    'color' => 'purple',
                    'icon' => 'shield-check',
                ];
            }
        }

        // Management actions for approved readings
        if ($this->canAccessDashboardView('manager') && $reading->status === 'approved' && $this->hasPermission('readings.view_all')) {

            $actions[] = [
                'label' => 'Export',
                'action' => 'exportReading',
                'color' => 'indigo',
                'icon' => 'document-arrow-down',
            ];
        }

        return $actions;
    }

    private function getInvoiceActions()
    {
        $actions = [];
        $invoice = Invoice::find($this->resourceId);

        if (! $invoice) {
            return $actions;
        }

        // Management actions for invoices
        if ($this->canAccessDashboardView('manager')) {
            if ($this->hasPermission('invoices.edit') && $invoice->status === 'pending') {
                $actions[] = [
                    'label' => 'Edit',
                    'action' => 'editInvoice',
                    'color' => 'blue',
                    'icon' => 'pencil',
                ];
            }

            if ($this->hasPermission('invoices.delete')) {
                $actions[] = [
                    'label' => 'Delete',
                    'action' => 'deleteInvoice',
                    'color' => 'red',
                    'icon' => 'trash',
                ];
            }

            if ($this->hasPermission('invoices.send')) {
                $actions[] = [
                    'label' => 'Send Notification',
                    'action' => 'sendInvoiceNotification',
                    'color' => 'green',
                    'icon' => 'paper-airplane',
                ];
            }
        }

        // Resident actions for their invoices
        if ($this->canAccessDashboardView('resident')) {
            $user = Auth::user();
            $hasAccess = $user->contacts()->where('house_id', $invoice->house_id)->exists();

            if ($hasAccess) {
                if ($this->hasPermission('invoices.view') && $invoice->status !== 'paid') {
                    $actions[] = [
                        'label' => 'View Details',
                        'action' => 'viewInvoice',
                        'color' => 'blue',
                        'icon' => 'eye',
                    ];
                }

                if ($this->hasPermission('invoices.download')) {
                    $actions[] = [
                        'label' => 'Download PDF',
                        'action' => 'downloadInvoice',
                        'color' => 'indigo',
                        'icon' => 'document-arrow-down',
                    ];
                }
            }
        }

        return $actions;
    }

    private function getHouseActions()
    {
        $actions = [];

        // Management actions for houses
        if ($this->canAccessDashboardView('manager')) {
            if ($this->hasPermission('houses.edit')) {
                $actions[] = [
                    'label' => 'Edit',
                    'action' => 'editHouse',
                    'color' => 'blue',
                    'icon' => 'pencil',
                ];
            }

            if ($this->hasPermission('houses.manage_contacts')) {
                $actions[] = [
                    'label' => 'Manage Contacts',
                    'action' => 'manageHouseContacts',
                    'color' => 'green',
                    'icon' => 'users',
                ];
            }

            if ($this->hasPermission('houses.view_readings')) {
                $actions[] = [
                    'label' => 'View Readings',
                    'action' => 'viewHouseReadings',
                    'color' => 'purple',
                    'icon' => 'chart-bar',
                ];
            }

            if ($this->hasPermission('houses.view_invoices')) {
                $actions[] = [
                    'label' => 'View Invoices',
                    'action' => 'viewHouseInvoices',
                    'color' => 'yellow',
                    'icon' => 'document-text',
                ];
            }
        }

        return $actions;
    }

    public function performAction($action)
    {
        // This method will emit events to the parent component
        // The parent component will handle the actual action execution
        $this->dispatch('actionPerformed', [
            'action' => $action,
            'resourceId' => $this->resourceId,
            'resourceType' => $this->resourceType,
        ]);
    }

    public function render()
    {
        return view('livewire.dashboard.permission-action-buttons', [
            'availableActions' => $this->available_actions,
        ]);
    }
}
