<?php

namespace App\Livewire\Dashboard;

use App\Models\Estate;
use App\Traits\HasPermissions;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class BaseDashboard extends Component
{
    use HasPermissions;

    public $selectedEstate;

    public $search = '';

    public $dateRange = 'this_month';

    #[\Livewire\Attributes\Modelable]
    public $currentSection = 'overview';

    public function mount()
    {
        $this->authorizeAccess();
        $this->setDefaultValues();
    }

    private function authorizeAccess()
    {
        $user = Auth::user();

        $dashboardPermissions = [
            'view-manager-dashboard',
            'view-reviewer-dashboard',
            'view-caretaker-dashboard',
            'view-resident-dashboard',
        ];

        if (! $user || ! $user->hasAnyPermission($dashboardPermissions)) {
            abort(403, 'Unauthorized access');
        }
    }

    private function setDefaultValues()
    {
        $user = Auth::user();

        if ($this->canAccessCaretakerDashboard() && $user->assignedEstates()->count() === 1) {
            $this->selectedEstate = $user->assignedEstates()->first()->id;
        }
    }

    public function getEstatesProperty()
    {
        $user = Auth::user();

        if ($this->hasPermission('estates.view_all')) {
            return Estate::withCount(['houses', 'meterReadings'])
                ->orderBy('name')
                ->get();
        }

        return $user->assignedEstates()->orderBy('name')->get();
    }

    public function getCurrentUserRole()
    {
        $user = Auth::user();

        if ($user->hasPermissionTo('view-manager-dashboard')) {
            return 'manager';
        }
        if ($user->hasPermissionTo('view-reviewer-dashboard')) {
            return 'reviewer';
        }
        if ($user->hasPermissionTo('view-caretaker-dashboard')) {
            return 'caretaker';
        }
        if ($user->hasPermissionTo('view-resident-dashboard')) {
            return 'resident';
        }

        return 'unknown';
    }

    public function render()
    {
        $userRole = $this->getCurrentUserRole();

        return view('livewire.dashboard.base-dashboard', [
            'userRole' => $userRole,
            'estates' => $this->estates,
        ]);
    }
}
