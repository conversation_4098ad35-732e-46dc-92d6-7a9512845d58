<?php

namespace App\Livewire\Dashboard;

use App\Models\Estate;
use App\Models\Invoice;
use App\Models\MeterReading;
use App\Traits\HasPermissions;
use Carbon\Carbon;
use Livewire\Component;

class PermissionChartWidget extends Component
{
    use HasPermissions;

    public $selectedEstate;

    public $chartType = 'consumption';

    public $dateRange = '30'; // days

    protected $listeners = [
        'estateSelected' => 'updateEstate',
        'refreshDashboard' => '$refresh',
    ];

    public function updateEstate($estateId)
    {
        $this->selectedEstate = $estateId;
    }

    public function getChartDataProperty()
    {
        if (! $this->canAccessDashboardView('manager')) {
            return null;
        }

        return match ($this->chartType) {
            'consumption' => $this->getConsumptionChartData(),
            'revenue' => $this->getRevenueChartData(),
            'estate_comparison' => $this->getEstateComparisonData(),
            default => $this->getConsumptionChartData(),
        };
    }

    private function getConsumptionChartData()
    {
        $data = MeterReading::when($this->selectedEstate, function ($query): void {
            $query->whereHas('house', function ($q): void {
                $q->where('estate_id', $this->selectedEstate);
            });
        })
            ->where('created_at', '>=', now()->subDays($this->dateRange))
            ->selectRaw('DATE(created_at) as date, SUM(consumption) as total_consumption')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        return [
            'labels' => $data->pluck('date')->map(fn ($date) => Carbon::parse($date)->format('M d')),
            'datasets' => [
                [
                    'label' => 'Daily Consumption (Liters)',
                    'data' => $data->pluck('total_consumption'),
                    'borderColor' => 'rgb(59, 130, 246)',
                    'backgroundColor' => 'rgba(59, 130, 246, 0.1)',
                    'fill' => true,
                ],
            ],
        ];
    }

    private function getRevenueChartData()
    {
        $data = Invoice::when($this->selectedEstate, function ($query): void {
            $query->whereHas('house', function ($q): void {
                $q->where('estate_id', $this->selectedEstate);
            });
        })
            ->where('created_at', '>=', now()->subDays($this->dateRange))
            ->selectRaw('DATE(created_at) as date, SUM(amount) as total_revenue')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        return [
            'labels' => $data->pluck('date')->map(fn ($date) => Carbon::parse($date)->format('M d')),
            'datasets' => [
                [
                    'label' => 'Daily Revenue (KES)',
                    'data' => $data->pluck('total_revenue'),
                    'borderColor' => 'rgb(34, 197, 94)',
                    'backgroundColor' => 'rgba(34, 197, 94, 0.1)',
                    'fill' => true,
                ],
            ],
        ];
    }

    private function getEstateComparisonData()
    {
        $data = Estate::with(['houses.meterReadings' => function ($query): void {
            $query->where('created_at', '>=', now()->subDays($this->dateRange));
        }])
            ->when($this->selectedEstate, function ($query): void {
                $query->where('id', $this->selectedEstate);
            })
            ->get()
            ->map(fn ($estate) => [
                'name' => $estate->name,
                'consumption' => $estate->houses->flatMap->meterReadings->sum('consumption'),
                'houses' => $estate->houses->count(),
            ])
            ->sortByDesc('consumption')
            ->take(10);

        return [
            'labels' => $data->pluck('name'),
            'datasets' => [
                [
                    'label' => 'Total Consumption by Estate',
                    'data' => $data->pluck('consumption'),
                    'backgroundColor' => [
                        'rgba(59, 130, 246, 0.8)',
                        'rgba(34, 197, 94, 0.8)',
                        'rgba(251, 191, 36, 0.8)',
                        'rgba(239, 68, 68, 0.8)',
                        'rgba(168, 85, 247, 0.8)',
                    ],
                ],
            ],
        ];
    }

    public function getAvailableChartTypesProperty()
    {
        $types = [];

        if ($this->hasPermission('analytics.view_all') || $this->hasPermission('analytics.view_assigned')) {
            $types['consumption'] = 'Consumption Trends';
        }

        if ($this->hasPermission('reports.revenue_all') || $this->hasPermission('reports.revenue_assigned')) {
            $types['revenue'] = 'Revenue Trends';
        }

        if ($this->hasPermission('estates.analytics')) {
            $types['estate_comparison'] = 'Estate Comparison';
        }

        return $types;
    }

    public function render()
    {
        if (! $this->canAccessDashboardView('manager')) {
            return view('livewire.dashboard.permission-chart-widget', [
                'chartData' => null,
                'availableChartTypes' => [],
            ]);
        }

        return view('livewire.dashboard.permission-chart-widget', [
            'chartData' => $this->chart_data,
            'availableChartTypes' => $this->available_chart_types,
        ]);
    }
}
