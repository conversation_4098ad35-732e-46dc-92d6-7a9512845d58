<?php

namespace App\Livewire\Dashboard;

use App\Traits\HasPermissions;
use Livewire\Component;

class PermissionNavigation extends Component
{
    use HasPermissions;

    #[\Livewire\Attributes\Modelable]
    public $currentSection = 'overview';

    public function getNavigationItemsProperty()
    {
        $items = [];

        // Overview section (available to all dashboard users)
        if ($this->canAccessAnyDashboard()) {
            $items['overview'] = [
                'label' => 'Overview',
                'icon' => 'home',
                'permission' => null, // Available to all dashboard users
            ];
        }

        // Caretaker section
        if ($this->canAccessDashboardView('caretaker')) {
            $items['readings'] = [
                'label' => 'Meter Readings',
                'icon' => 'chart-bar',
                'permission' => 'view-caretaker-dashboard',
                'subItems' => [
                    'my-readings' => [
                        'label' => 'My Readings',
                        'permission' => 'readings.view_own',
                    ],
                    'submit-reading' => [
                        'label' => 'Submit Reading',
                        'permission' => 'readings.create_assigned',
                    ],
                    'pending-houses' => [
                        'label' => 'Pending Houses',
                        'permission' => 'houses.view_assigned',
                    ],
                ],
            ];
        }

        // Reviewer section
        if ($this->canAccessDashboardView('reviewer')) {
            $items['review'] = [
                'label' => 'Review',
                'icon' => 'M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z',
                'permission' => 'view-reviewer-dashboard',
                'subItems' => [
                    'pending-review' => [
                        'label' => 'Pending Review',
                        'permission' => 'readings.review_assigned',
                    ],
                    'anomalous-readings' => [
                        'label' => 'Anomalous Readings',
                        'permission' => 'readings.validate',
                    ],
                    'batch-review' => [
                        'label' => 'Batch Review',
                        'permission' => 'readings.approve_assigned',
                    ],
                ],
            ];
        }

        // Management section
        if ($this->canAccessDashboardView('manager')) {
            $items['management'] = [
                'label' => 'Management',
                'icon' => 'cog-6-tooth',
                'permission' => 'view-manager-dashboard',
                'subItems' => [
                    'analytics' => [
                        'label' => 'Analytics',
                        'permission' => 'analytics.view_assigned',
                    ],
                    'estates' => [
                        'label' => 'Estates',
                        'permission' => 'estates.view_all',
                    ],
                    'houses' => [
                        'label' => 'Houses',
                        'permission' => 'houses.view_all',
                    ],
                    'invoices' => [
                        'label' => 'Invoices',
                        'permission' => 'invoices.view_all',
                    ],
                    'reports' => [
                        'label' => 'Reports',
                        'permission' => 'reports.view_assigned',
                    ],
                ],
            ];
        }

        // Resident section
        if ($this->canAccessDashboardView('resident')) {
            $items['resident'] = [
                'label' => 'My Account',
                'icon' => 'user',
                'permission' => 'view-resident-dashboard',
                'subItems' => [
                    'billing' => [
                        'label' => 'Billing',
                        'permission' => 'invoices.view_own',
                    ],
                    'consumption' => [
                        'label' => 'Consumption',
                        'permission' => 'readings.view_own',
                    ],
                    'messages' => [
                        'label' => 'Messages',
                        'permission' => 'resident.messages.view',
                    ],
                    'profile' => [
                        'label' => 'Profile',
                        'permission' => 'resident.portal.access',
                    ],
                ],
            ];
        }

        // Admin section
        if ($this->hasPermission('users.manage_all')) {
            $items['admin'] = [
                'label' => 'Admin',
                'icon' => 'shield-check',
                'permission' => 'users.manage_all',
                'subItems' => [
                    'users' => [
                        'label' => 'Users',
                        'permission' => 'users.manage_all',
                    ],
                    'permissions' => [
                        'label' => 'Permissions',
                        'permission' => 'users.assign_roles',
                    ],
                    'settings' => [
                        'label' => 'Settings',
                        'permission' => 'system.settings.manage',
                    ],
                ],
            ];
        }

        return $this->filterNavigationItems($items);
    }

    public function isItemActive($itemKey, $itemData)
    {
        if ($this->currentSection === $itemKey) {
            return true;
        }

        if (isset($itemData['subItems'])) {
            foreach (array_keys($itemData['subItems']) as $subItemKey) {
                if ($this->currentSection === $subItemKey) {
                    return true;
                }
            }
        }

        return false;
    }

    public function activeSubItem($itemData)
    {
        if (isset($itemData['subItems'])) {
            foreach ($itemData['subItems'] as $subKey => $subItem) {
                if ($this->currentSection === $subKey) {
                    return $subKey;
                }
            }
        }

        return null;
    }

    private function filterNavigationItems($items)
    {
        $filteredItems = [];

        foreach ($items as $key => $item) {
            // Check if user has permission for this item
            if (! $item['permission'] || $this->hasPermission($item['permission'])) {
                $filteredItem = $item;

                // Filter sub-items if they exist
                if (isset($item['subItems'])) {
                    $filteredSubItems = [];
                    foreach ($item['subItems'] as $subKey => $subItem) {
                        if ($this->hasPermission($subItem['permission'])) {
                            $filteredSubItems[$subKey] = $subItem;
                        }
                    }

                    // Only include the item if it has accessible sub-items or no sub-items requirement
                    if (empty($item['subItems']) || $filteredSubItems !== []) {
                        $filteredItem['subItems'] = $filteredSubItems;
                        $filteredItems[$key] = $filteredItem;
                    }
                } else {
                    $filteredItems[$key] = $filteredItem;
                }
            }
        }

        return $filteredItems;
    }

    public function selectSection($section)
    {
        $this->currentSection = $section;
        $this->dispatch('sectionChanged', ['section' => $section]);
    }

    public function render()
    {
        // The navigation_items property already contains the raw items.
        // We need to process them to add isActive and activeSubItem flags.
        $processedNavigationItems = [];
        foreach ($this->navigation_items as $key => $item) {
            // Use the computed properties by calling them with the necessary arguments
            // isItemActive is a method that takes itemKey and itemData
            // activeSubItem is a method that takes itemData

            $processedItem = $item;
            $processedItem['isActive'] = $this->isItemActive($key, $item);
            $processedItem['activeSubItem'] = $this->activeSubItem($item);
            $processedNavigationItems[$key] = $processedItem;
        }

        return view('livewire.dashboard.permission-navigation', [
            'navigationItems' => $processedNavigationItems,
            'currentSection' => $this->currentSection,
        ]);
    }
}
