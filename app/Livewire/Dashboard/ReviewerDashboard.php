<?php

namespace App\Livewire\Dashboard;

use App\Models\MeterReading;
use App\Services\ReadingValidationService;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\WithPagination;

class ReviewerDashboard extends Component
{
    use WithPagination;

    public $selectedEstate;

    public $search = '';

    public $dateRange = 'this_month';

    public $reviewerStatus = 'submitted';

    public $riskFilter = '';

    public $showReviewModal = false;

    public $currentReading;

    public $reviewAction = 'approve';

    public $reviewNotes = '';

    public $anomalyThreshold = 50;

    public $selectAll = false;

    public $selectedReadings = [];

    public $showBatchModal = false;

    public $batchAction = 'approve';

    public $batchNotes = '';

    public $validationResults = [];

    public $showValidationModal = false;

    protected $listeners = [
        'estateSelected' => 'updateEstate',
        'refreshDashboard' => '$refresh',
    ];

    public function updateEstate($estateId)
    {
        $this->selectedEstate = $estateId;
        $this->resetPage();
    }

    public function getReviewerReadingsProperty()
    {
        MeterReading::with(['house.estate', 'user', 'house.contacts'])
            ->when($this->selectedEstate, function ($query): void {
                $query->whereHas('house', function ($q): void {
                    $q->where('estate_id', $this->selectedEstate);
                });
            })
            ->when($this->reviewerStatus !== 'all', function ($query): void {
                $query->where('status', $this->reviewerStatus);
            })
            ->when($this->search, function ($query): void {
                $query->whereHas('house', function ($q): void {
                    $q->where('house_number', 'like', '%'.$this->search.'%')
                        ->orWhere('address', 'like', '%'.$this->search.'%');
                });
            })
            ->when($this->dateRange, function ($query): void {
                $this->applyDateRange($query);
            })
            ->orderBy('submitted_at', 'desc');

        return $query->paginate(10);
    }

    public function getReviewerStatsProperty()
    {
        $baseQuery = MeterReading::query()
            ->when($this->selectedEstate, function ($query): void {
                $query->whereHas('house', function ($q): void {
                    $q->where('estate_id', $this->selectedEstate);
                });
            });

        return [
            'pending_review' => (clone $baseQuery)->where('status', 'submitted')->count(),
            'approved_today' => (clone $baseQuery)->where('status', 'approved')->whereDate('approved_at', today())->count(),
            'rejected_today' => (clone $baseQuery)->where('status', 'rejected')->whereDate('reviewed_at', today())->count(),
            'this_month' => (clone $baseQuery)->whereMonth('reading_date', now()->month)->count(),
        ];
    }

    public function getAnomalousReadings()
    {
        $readingValidationService = new ReadingValidationService;
        $readings = MeterReading::where('status', 'submitted')
            ->when($this->selectedEstate, function ($query): void {
                $query->whereHas('house', function ($q): void {
                    $q->where('estate_id', $this->selectedEstate);
                });
            })
            ->get();

        return $readings->filter(function ($reading) use ($readingValidationService) {
            $validation = $readingValidationService->validateReading($reading);

            return $validation['risk_level'] !== 'low';
        });
    }

    public function openReviewModal($readingId)
    {
        $this->currentReading = MeterReading::with(['house.estate', 'user'])->findOrFail($readingId);
        $this->showReviewModal = true;
    }

    public function closeReviewModal()
    {
        $this->showReviewModal = false;
        $this->currentReading = null;
        $this->reviewAction = 'approve';
        $this->reviewNotes = '';
    }

    public function submitReview()
    {
        if (! $this->currentReading) {
            return;
        }

        $this->validate([
            'reviewAction' => 'required|in:approve,reject',
            'reviewNotes' => 'required_if:reviewAction,reject|max:500',
        ]);

        $updateData = [
            'reviewed_by' => Auth::id(),
            'reviewed_at' => now(),
            'status' => $this->reviewAction === 'approve' ? 'approved' : 'rejected',
        ];

        if ($this->reviewAction === 'reject') {
            $updateData['rejection_reason'] = $this->reviewNotes;
        } else {
            $updateData['approved_at'] = now();
            $updateData['approved_by'] = Auth::id();
        }

        $this->currentReading->update($updateData);

        session()->flash('message', 'Reading '.$this->reviewAction.'d successfully!');
        $this->closeReviewModal();
    }

    public function getReadingHistory($houseId)
    {
        return MeterReading::where('house_id', $houseId)
            ->where('status', 'approved')
            ->orderBy('reading_date', 'desc')
            ->limit(6)
            ->get();
    }

    public function isAnomalous($reading)
    {
        $readingValidationService = new ReadingValidationService;
        $validation = $readingValidationService->validateReading($reading);

        return $validation['risk_level'] !== 'low';
    }

    public function getValidationResult($reading)
    {
        $readingValidationService = new ReadingValidationService;

        return $readingValidationService->validateReading($reading);
    }

    public function openValidationModal($readingId)
    {
        $this->currentReading = MeterReading::with(['house.estate', 'user'])->findOrFail($readingId);
        $this->validationResults = $this->getValidationResult($this->currentReading);
        $this->showValidationModal = true;
    }

    public function closeValidationModal()
    {
        $this->showValidationModal = false;
        $this->currentReading = null;
        $this->validationResults = [];
    }

    public function openBatchModal()
    {
        if (empty($this->selectedReadings)) {
            session()->flash('error', 'Please select readings to process in batch');

            return;
        }
        $this->showBatchModal = true;
    }

    public function closeBatchModal()
    {
        $this->showBatchModal = false;
        $this->batchAction = 'approve';
        $this->batchNotes = '';
    }

    public function submitBatchReview()
    {
        if (empty($this->selectedReadings)) {
            return;
        }

        $this->validate([
            'batchAction' => 'required|in:approve,reject',
            'batchNotes' => 'required_if:batchAction,reject|max:500',
        ]);

        $readings = MeterReading::whereIn('id', $this->selectedReadings)->get();

        $updateData = [
            'reviewed_by' => Auth::id(),
            'reviewed_at' => now(),
            'status' => $this->batchAction === 'approve' ? 'approved' : 'rejected',
        ];

        if ($this->batchAction === 'reject') {
            $updateData['rejection_reason'] = $this->batchNotes;
        } else {
            $updateData['approved_at'] = now();
            $updateData['approved_by'] = Auth::id();
        }

        foreach ($readings as $reading) {
            $reading->update($updateData);
        }

        session()->flash('message', count($this->selectedReadings).' readings '.$this->batchAction.'d successfully!');
        $this->selectedReadings = [];
        $this->closeBatchModal();
    }

    public function getRiskLevelBadgeClass($riskLevel)
    {
        return match ($riskLevel) {
            'critical' => 'bg-red-100 text-red-800',
            'high' => 'bg-orange-100 text-orange-800',
            'medium' => 'bg-yellow-100 text-yellow-800',
            'low' => 'bg-green-100 text-green-800',
            default => 'bg-gray-100 text-gray-800',
        };
    }

    private function applyDateRange($query)
    {
        switch ($this->dateRange) {
            case 'today':
                $query->whereDate('reading_date', today());
                break;
            case 'this_week':
                $query->whereBetween('reading_date', [now()->startOfWeek(), now()->endOfWeek()]);
                break;
            case 'this_month':
                $query->whereMonth('reading_date', now()->month);
                break;
            case 'last_month':
                $query->whereMonth('reading_date', now()->subMonth()->month);
                break;
        }
    }

    public function render()
    {
        return view('livewire.dashboard.reviewer-dashboard', [
            'reviewerReadings' => $this->reviewer_readings,
            'reviewerStats' => $this->reviewer_stats,
            'anomalousReadings' => $this->getAnomalousReadings(),
        ]);
    }
}
