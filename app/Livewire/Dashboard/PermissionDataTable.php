<?php

namespace App\Livewire\Dashboard;

use App\Models\House;
use App\Models\Invoice;
use App\Models\MeterReading;
use App\Traits\HasPermissions;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\WithPagination;

class PermissionDataTable extends Component
{
    use HasPermissions, WithPagination;

    public $selectedEstate;

    public $search = '';

    public $status = 'all';

    public $dateRange = 'this_month';

    public $tableType = 'readings'; // readings, invoices, houses

    protected $listeners = [
        'estateSelected' => 'updateEstate',
        'refreshDashboard' => '$refresh',
    ];

    public function updateEstate($estateId)
    {
        $this->selectedEstate = $estateId;
        $this->resetPage();
    }

    public function getTableDataProperty()
    {
        return match ($this->tableType) {
            'readings' => $this->getReadingsData(),
            'invoices' => $this->getInvoicesData(),
            'houses' => $this->getHousesData(),
            default => collect(),
        };
    }

    private function getReadingsData()
    {
        if (! $this->canAccessDashboardView('caretaker') && ! $this->canAccessDashboardView('reviewer')) {
            return MeterReading::query()->paginate(10);
        }

        $builder = MeterReading::with(['house.estate', 'house.contacts', 'user']);

        // Apply permission-based filtering
        if ($this->canAccessDashboardView('caretaker') && ! $this->canAccessDashboardView('reviewer')) {
            $builder->where('user_id', Auth::id());
        }

        $builder->when($this->selectedEstate, function ($query): void {
            $query->whereHas('house', function ($q): void {
                $q->where('estate_id', $this->selectedEstate);
            });
        })
            ->when($this->status !== 'all', function ($query): void {
                $query->where('status', $this->status);
            })
            ->when($this->search, function ($query): void {
                $query->whereHas('house', function ($q): void {
                    $q->where('house_number', 'like', '%'.$this->search.'%')
                        ->orWhere('address', 'like', '%'.$this->search.'%');
                });
            })
            ->when($this->dateRange, function ($query): void {
                $this->applyDateRange($query);
            })
            ->orderBy('created_at', 'desc');

        return $builder->paginate(10);
    }

    private function getInvoicesData()
    {
        if (! $this->canAccessDashboardView('manager') && ! $this->canAccessDashboardView('resident')) {
            return Invoice::query()->paginate(10);
        }

        $builder = Invoice::with(['house.estate', 'house.contacts']);

        // Apply permission-based filtering
        if ($this->canAccessDashboardView('resident') && ! $this->canAccessDashboardView('manager')) {
            $user = Auth::user();
            $houseIds = $user->contacts()->pluck('house_id');
            $builder->whereIn('house_id', $houseIds);
        }

        $builder->when($this->selectedEstate, function ($query): void {
            $query->whereHas('house', function ($q): void {
                $q->where('estate_id', $this->selectedEstate);
            });
        })
            ->when($this->status !== 'all', function ($query): void {
                $query->where('status', $this->status);
            })
            ->when($this->search, function ($query): void {
                $query->whereHas('house', function ($q): void {
                    $q->where('house_number', 'like', '%'.$this->search.'%')
                        ->orWhere('invoice_number', 'like', '%'.$this->search.'%');
                });
            })
            ->when($this->dateRange, function ($query): void {
                $this->applyInvoiceDateRange($query);
            })
            ->orderBy('created_at', 'desc');

        return $builder->paginate(10);
    }

    private function getHousesData()
    {
        if (! $this->canAccessDashboardView('manager')) {
            return House::query()->paginate(10);
        }

        $query = House::with(['estate', 'contacts', 'meterReadings' => function ($query): void {
            $query->where('created_at', '>=', now()->subDays(30));
        }]);

        $query->when($this->selectedEstate, function ($query): void {
            $query->where('estate_id', $this->selectedEstate);
        })
            ->when($this->search, function ($query): void {
                $query->where('house_number', 'like', '%'.$this->search.'%')
                    ->orWhere('address', 'like', '%'.$this->search.'%');
            })
            ->when($this->status !== 'all', function ($query): void {
                $query->where('status', $this->status);
            })
            ->orderBy('house_number');

        return $query->paginate(10);
    }

    private function applyDateRange($query)
    {
        switch ($this->dateRange) {
            case 'today':
                $query->whereDate('reading_date', today());
                break;
            case 'this_week':
                $query->whereBetween('reading_date', [now()->startOfWeek(), now()->endOfWeek()]);
                break;
            case 'this_month':
                $query->whereMonth('reading_date', now()->month);
                break;
            case 'last_month':
                $query->whereMonth('reading_date', now()->subMonth()->month);
                break;
        }
    }

    private function applyInvoiceDateRange($query)
    {
        switch ($this->dateRange) {
            case 'today':
                $query->whereDate('invoice_date', today());
                break;
            case 'this_week':
                $query->whereBetween('invoice_date', [now()->startOfWeek(), now()->endOfWeek()]);
                break;
            case 'this_month':
                $query->whereMonth('invoice_date', now()->month);
                break;
            case 'last_month':
                $query->whereMonth('invoice_date', now()->subMonth()->month);
                break;
        }
    }

    public function getAvailableTableTypesProperty()
    {
        $types = [];

        if ($this->canAccessDashboardView('caretaker') || $this->canAccessDashboardView('reviewer')) {
            $types['readings'] = 'Meter Readings';
        }

        if ($this->canAccessDashboardView('manager') || $this->canAccessDashboardView('resident')) {
            $types['invoices'] = 'Invoices';
        }

        if ($this->canAccessDashboardView('manager')) {
            $types['houses'] = 'Houses';
        }

        return $types;
    }

    public function getAvailableStatusesProperty()
    {
        return match ($this->tableType) {
            'readings' => ['all' => 'All', 'draft' => 'Draft', 'submitted' => 'Submitted', 'approved' => 'Approved', 'rejected' => 'Rejected'],
            'invoices' => ['all' => 'All', 'pending' => 'Pending', 'paid' => 'Paid', 'overdue' => 'Overdue'],
            'houses' => ['all' => 'All', 'occupied' => 'Occupied', 'vacant' => 'Vacant', 'maintenance' => 'Maintenance'],
            default => [],
        };
    }

    public function render()
    {
        return view('livewire.dashboard.permission-data-table', [
            'tableData' => $this->table_data,
            'availableTableTypes' => $this->available_table_types,
            'availableStatuses' => $this->available_statuses,
        ]);
    }
}
