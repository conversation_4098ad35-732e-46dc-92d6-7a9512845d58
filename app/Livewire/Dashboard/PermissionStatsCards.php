<?php

namespace App\Livewire\Dashboard;

use App\Models\Estate;
use App\Models\House;
use App\Models\Invoice;
use App\Models\MeterReading;
use App\Traits\HasPermissions;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class PermissionStatsCards extends Component
{
    use HasPermissions;

    public $selectedEstate;

    public $dateRange = '30'; // days

    protected $listeners = [
        'estateSelected' => 'updateEstate',
        'refreshDashboard' => '$refresh',
    ];

    public function updateEstate($estateId)
    {
        $this->selectedEstate = $estateId;
    }

    public function getStatsProperty()
    {
        $stats = [];

        // Caretaker Stats
        if ($this->canAccessDashboardView('caretaker')) {
            $baseQuery = MeterReading::where('user_id', Auth::id())
                ->when($this->selectedEstate, function ($query): void {
                    $query->whereHas('house', function ($q): void {
                        $q->where('estate_id', $this->selectedEstate);
                    });
                });

            $stats['caretaker'] = [
                'total_readings' => $baseQuery->count(),
                'draft_readings' => (clone $baseQuery)->where('status', 'draft')->count(),
                'submitted_readings' => (clone $baseQuery)->where('status', 'submitted')->count(),
                'approved_readings' => (clone $baseQuery)->where('status', 'approved')->count(),
                'rejected_readings' => (clone $baseQuery)->where('status', 'rejected')->count(),
                'today_readings' => (clone $baseQuery)->whereDate('reading_date', today())->count(),
            ];
        }

        // Reviewer Stats
        if ($this->canAccessDashboardView('reviewer')) {
            $baseQuery = MeterReading::query()
                ->when($this->selectedEstate, function ($query): void {
                    $query->whereHas('house', function ($q): void {
                        $q->where('estate_id', $this->selectedEstate);
                    });
                });

            $stats['reviewer'] = [
                'pending_review' => (clone $baseQuery)->where('status', 'submitted')->count(),
                'approved_today' => (clone $baseQuery)->where('status', 'approved')->whereDate('approved_at', today())->count(),
                'rejected_today' => (clone $baseQuery)->where('status', 'rejected')->whereDate('reviewed_at', today())->count(),
                'this_month' => (clone $baseQuery)->whereMonth('reading_date', now()->month)->count(),
            ];
        }

        // Management Stats
        if ($this->canAccessDashboardView('manager')) {
            $stats['management'] = [
                'total_houses' => $this->getTotalHouses(),
                'total_estates' => Estate::count(),
                'total_revenue' => $this->getTotalRevenue(),
                'avg_consumption' => $this->getAverageConsumption(),
                'pending_readings' => $this->getPendingReadingsCount(),
                'overdue_invoices' => $this->getOverdueInvoicesCount(),
            ];
        }

        // Resident Stats
        if ($this->canAccessDashboardView('resident')) {
            $user = Auth::user();
            $houseIds = $user->contacts()->pluck('house_id');

            $stats['resident'] = [
                'unpaid_invoices' => Invoice::whereIn('house_id', $houseIds)->where('status', '!=', 'paid')->count(),
                'total_unpaid' => Invoice::whereIn('house_id', $houseIds)->where('status', '!=', 'paid')->sum('total_amount'),
                'unread_messages' => $user->contacts()->join('whatsapp_messages', 'contacts.house_id', '=', 'whatsapp_messages.house_id')
                    ->where('whatsapp_messages.direction', 'incoming')
                    ->where('whatsapp_messages.is_read', false)
                    ->count(),
            ];
        }

        return $stats;
    }

    private function getTotalHouses()
    {
        return $this->selectedEstate
            ? House::where('estate_id', $this->selectedEstate)->count()
            : House::count();
    }

    private function getTotalRevenue()
    {
        return Invoice::when($this->selectedEstate, function ($query): void {
            $query->whereHas('house', function ($q): void {
                $q->where('estate_id', $this->selectedEstate);
            });
        })
            ->where('created_at', '>=', now()->subDays($this->dateRange))
            ->sum('amount');
    }

    private function getAverageConsumption()
    {
        return MeterReading::when($this->selectedEstate, function ($query): void {
            $query->whereHas('house', function ($q): void {
                $q->where('estate_id', $this->selectedEstate);
            });
        })
            ->where('created_at', '>=', now()->subDays($this->dateRange))
            ->avg('consumption') ?? 0;
    }

    private function getPendingReadingsCount()
    {
        return MeterReading::where('status', 'pending')
            ->when($this->selectedEstate, function ($query): void {
                $query->whereHas('house', function ($q): void {
                    $q->where('estate_id', $this->selectedEstate);
                });
            })
            ->count();
    }

    private function getOverdueInvoicesCount()
    {
        return Invoice::where('status', 'overdue')
            ->when($this->selectedEstate, function ($query): void {
                $query->whereHas('house', function ($q): void {
                    $q->where('estate_id', $this->selectedEstate);
                });
            })
            ->count();
    }

    public function render()
    {
        return view('livewire.dashboard.permission-stats-cards', [
            'stats' => $this->stats,
        ]);
    }
}
