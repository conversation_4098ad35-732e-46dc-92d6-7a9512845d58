<?php

namespace App\Livewire\Dashboard;

use App\Models\House;
use App\Models\Invoice;
use App\Models\MeterReading;
use App\Models\WhatsAppMessage;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class ResidentDashboard extends Component
{
    public $selectedHouseId;

    public $userHouses = [];

    public $currentHouse;

    public $latestInvoice;

    public $latestReading;

    public $unreadMessages;

    public $recentInvoices;

    public $recentReadings;

    public $consumptionStats;

    protected $listeners = ['refreshDashboard' => '$refresh'];

    public function mount()
    {
        $this->loadUserHouses();
        $this->selectDefaultHouse();
        $this->loadDashboardData();
    }

    private function loadUserHouses()
    {
        $user = Auth::user();
        $this->userHouses = $user->contacts()
            ->with('house.estate')
            ->get()
            ->map(fn ($contact) => [
                'id' => $contact->house_id,
                'house_number' => $contact->house->house_number,
                'estate_name' => $contact->house->estate->name,
                'address' => $contact->house->address,
            ])
            ->toArray();
    }

    private function selectDefaultHouse()
    {
        $sessionHouseId = session('resident_selected_house_id');

        if ($sessionHouseId && collect($this->userHouses)->contains('id', $sessionHouseId)) {
            $this->selectedHouseId = $sessionHouseId;
        } elseif (! empty($this->userHouses)) {
            $this->selectedHouseId = $this->userHouses[0]['id'];
        }

        if ($this->selectedHouseId) {
            $this->currentHouse = House::with('estate')->find($this->selectedHouseId);
        }
    }

    public function selectHouse($houseId)
    {
        if (collect($this->userHouses)->contains('id', $houseId)) {
            $this->selectedHouseId = $houseId;
            $this->currentHouse = House::with('estate')->find($houseId);
            session()->put('resident_selected_house_id', $houseId);
            $this->loadDashboardData();
        }
    }

    public function updatedSelectedHouseId($houseId)
    {
        $this->selectHouse($houseId);
    }

    private function loadDashboardData()
    {
        if (! $this->selectedHouseId) {
            return;
        }

        $this->latestInvoice = Invoice::where('house_id', $this->selectedHouseId)
            ->orderBy('invoice_date', 'desc')
            ->first();

        $this->latestReading = MeterReading::where('house_id', $this->selectedHouseId)
            ->where('status', 'approved')
            ->orderBy('reading_date', 'desc')
            ->first();

        $this->unreadMessages = WhatsAppMessage::where('house_id', $this->selectedHouseId)
            ->where('direction', 'incoming')
            ->where('is_read', false)
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        $this->recentInvoices = Invoice::where('house_id', $this->selectedHouseId)
            ->orderBy('invoice_date', 'desc')
            ->limit(5)
            ->get();

        $this->recentReadings = MeterReading::where('house_id', $this->selectedHouseId)
            ->where('status', 'approved')
            ->orderBy('reading_date', 'desc')
            ->limit(6)
            ->get();

        $this->calculateConsumptionStats();
    }

    private function calculateConsumptionStats()
    {
        $readings = MeterReading::where('house_id', $this->selectedHouseId)
            ->where('status', 'approved')
            ->orderBy('reading_date', 'desc')
            ->limit(12)
            ->get();

        if ($readings->count() < 2) {
            $this->consumptionStats = [
                'current_month' => 0,
                'previous_month' => 0,
                'average_consumption' => 0,
                'trend' => 'stable',
                'change_percentage' => 0,
            ];

            return;
        }

        $currentMonth = $readings->first()->consumption ?? 0;
        $previousMonth = $readings->skip(1)->first()->consumption ?? 0;
        $averageConsumption = $readings->avg('consumption');

        $trend = 'stable';
        if ($currentMonth > $previousMonth * 1.2) {
            $trend = 'increasing';
        } elseif ($currentMonth < $previousMonth * 0.8) {
            $trend = 'decreasing';
        }

        $this->consumptionStats = [
            'current_month' => $currentMonth,
            'previous_month' => $previousMonth,
            'average_consumption' => round($averageConsumption, 2),
            'trend' => $trend,
            'change_percentage' => $previousMonth > 0 ? round((($currentMonth - $previousMonth) / $previousMonth) * 100, 1) : 0,
        ];
    }

    public function getUnpaidInvoicesCountProperty()
    {
        return Invoice::where('house_id', $this->selectedHouseId)
            ->where('status', '!=', 'paid')
            ->count();
    }

    public function getTotalUnpaidAmountProperty()
    {
        return Invoice::where('house_id', $this->selectedHouseId)
            ->where('status', '!=', 'paid')
            ->sum('total_amount');
    }

    public function getUnreadMessagesCountProperty()
    {
        return WhatsAppMessage::where('house_id', $this->selectedHouseId)
            ->where('direction', 'incoming')
            ->where('is_read', false)
            ->count();
    }

    public function markMessagesAsRead()
    {
        WhatsAppMessage::where('house_id', $this->selectedHouseId)
            ->where('direction', 'incoming')
            ->where('is_read', false)
            ->update(['is_read' => true]);

        $this->loadDashboardData();
    }

    public function downloadInvoice($invoiceId)
    {
        $invoice = Invoice::where('id', $invoiceId)
            ->where('house_id', $this->selectedHouseId)
            ->first();

        if (! $invoice) {
            abort(404);
        }

        return redirect()->route('resident.invoices.pdf', $invoice->id);
    }

    public function render()
    {
        return view('livewire.dashboard.resident-dashboard', [
            'userHouses' => $this->userHouses,
            'currentHouse' => $this->currentHouse,
            'latestInvoice' => $this->latestInvoice,
            'latestReading' => $this->latestReading,
            'unreadMessages' => $this->unreadMessages,
            'recentInvoices' => $this->recentInvoices,
            'recentReadings' => $this->recentReadings,
            'consumptionStats' => $this->consumptionStats,
            'unpaidInvoicesCount' => $this->unpaid_invoices_count,
            'totalUnpaidAmount' => $this->total_unpaid_amount,
            'unreadMessagesCount' => $this->unread_messages_count,
        ]);
    }
}
