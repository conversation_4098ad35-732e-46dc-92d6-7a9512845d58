<?php

namespace App\Livewire\Dashboard;

use App\Models\Estate;
use App\Models\House;
use App\Models\Invoice;
use App\Models\MeterReading;
use Carbon\Carbon;
use Livewire\Component;

class ManagementDashboard extends Component
{
    public $selectedEstate;

    public $managementDateRange = '30'; // days

    public $chartType = 'consumption';

    public $exportFormat = 'pdf';

    protected $listeners = [
        'estateSelected' => 'updateEstate',
        'refreshDashboard' => '$refresh',
    ];

    public function updateEstate($estateId)
    {
        $this->selectedEstate = $estateId;
    }

    public function getManagementKpiDataProperty()
    {
        $query = $this->getManagementBaseQuery();

        return [
            'total_houses' => $this->getTotalHouses(),
            'total_estates' => $this->getTotalEstates(),
            'total_revenue' => $this->getTotalRevenue($query),
            'avg_consumption' => $this->getAverageConsumption($query),
            'pending_readings' => $this->getPendingReadingsCount(),
            'overdue_invoices' => $this->getOverdueInvoicesCount(),
        ];
    }

    public function getManagementChartDataProperty()
    {
        return match ($this->chartType) {
            'consumption' => $this->getConsumptionChartData(),
            'revenue' => $this->getRevenueChartData(),
            'estate_comparison' => $this->getEstateComparisonData(),
            default => $this->getConsumptionChartData(),
        };
    }

    public function getRecentReadingsProperty()
    {
        return MeterReading::with(['house.estate', 'house.contacts'])
            ->when($this->selectedEstate, function ($query): void {
                $query->whereHas('house', function ($q): void {
                    $q->where('estate_id', $this->selectedEstate);
                });
            })
            ->where('created_at', '>=', now()->subDays($this->managementDateRange))
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();
    }

    public function getTopConsumersProperty()
    {
        return House::with(['estate', 'meterReadings' => function ($query): void {
            $query->where('created_at', '>=', now()->subDays($this->managementDateRange));
        }])
            ->when($this->selectedEstate, function ($query): void {
                $query->where('estate_id', $this->selectedEstate);
            })
            ->get()
            ->map(function ($house) {
                $totalConsumption = $house->meterReadings->sum('consumption');

                return [
                    'house' => $house,
                    'total_consumption' => $totalConsumption,
                    'avg_daily' => $totalConsumption / max(1, $this->managementDateRange),
                ];
            })
            ->sortByDesc('total_consumption')
            ->take(10);
    }

    public function getEstateAnalyticsProperty()
    {
        return Estate::with(['houses.meterReadings' => function ($query): void {
            $query->where('created_at', '>=', now()->subDays($this->managementDateRange));
        }])
            ->when($this->selectedEstate, function ($query): void {
                $query->where('id', $this->selectedEstate);
            })
            ->get()
            ->map(function ($estate) {
                $totalHouses = $estate->houses->count();
                $totalConsumption = $estate->houses->flatMap->meterReadings->sum('consumption');
                $totalRevenue = $estate->houses->flatMap->invoices
                    ->where('created_at', '>=', now()->subDays($this->managementDateRange))
                    ->sum('amount');

                return [
                    'estate' => $estate,
                    'total_houses' => $totalHouses,
                    'total_consumption' => $totalConsumption,
                    'total_revenue' => $totalRevenue,
                    'avg_consumption_per_house' => $totalHouses > 0 ? $totalConsumption / $totalHouses : 0,
                    'occupancy_rate' => $totalHouses > 0 ? ($estate->houses->where('status', 'occupied')->count() / $totalHouses) * 100 : 0,
                ];
            });
    }

    private function getManagementBaseQuery()
    {
        $query = MeterReading::query();

        if ($this->selectedEstate) {
            $query->whereHas('house', function ($q): void {
                $q->where('estate_id', $this->selectedEstate);
            });
        }

        return $query->where('created_at', '>=', now()->subDays($this->managementDateRange));
    }

    private function getTotalHouses()
    {
        return $this->selectedEstate
            ? House::where('estate_id', $this->selectedEstate)->count()
            : House::count();
    }

    private function getTotalEstates()
    {
        return Estate::count();
    }

    private function getTotalRevenue($query)
    {
        return Invoice::when($this->selectedEstate, function ($query): void {
            $query->whereHas('house', function ($q): void {
                $q->where('estate_id', $this->selectedEstate);
            });
        })
            ->where('created_at', '>=', now()->subDays($this->managementDateRange))
            ->sum('amount');
    }

    private function getAverageConsumption($query)
    {
        return $query->avg('consumption') ?? 0;
    }

    private function getPendingReadingsCount()
    {
        return MeterReading::where('status', 'pending')
            ->when($this->selectedEstate, function ($query): void {
                $query->whereHas('house', function ($q): void {
                    $q->where('estate_id', $this->selectedEstate);
                });
            })
            ->count();
    }

    private function getOverdueInvoicesCount()
    {
        return Invoice::where('status', 'overdue')
            ->when($this->selectedEstate, function ($query): void {
                $query->whereHas('house', function ($q): void {
                    $q->where('estate_id', $this->selectedEstate);
                });
            })
            ->count();
    }

    private function getConsumptionChartData()
    {
        $data = $this->getManagementBaseQuery()
            ->selectRaw('DATE(created_at) as date, SUM(consumption) as total_consumption')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        return [
            'labels' => $data->pluck('date')->map(fn ($date) => Carbon::parse($date)->format('M d')),
            'datasets' => [
                [
                    'label' => 'Daily Consumption (Liters)',
                    'data' => $data->pluck('total_consumption'),
                    'borderColor' => 'rgb(59, 130, 246)',
                    'backgroundColor' => 'rgba(59, 130, 246, 0.1)',
                    'fill' => true,
                ],
            ],
        ];
    }

    private function getRevenueChartData()
    {
        $data = Invoice::when($this->selectedEstate, function ($query): void {
            $query->whereHas('house', function ($q): void {
                $q->where('estate_id', $this->selectedEstate);
            });
        })
            ->selectRaw('DATE(created_at) as date, SUM(amount) as total_revenue')
            ->where('created_at', '>=', now()->subDays($this->managementDateRange))
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        return [
            'labels' => $data->pluck('date')->map(fn ($date) => Carbon::parse($date)->format('M d')),
            'datasets' => [
                [
                    'label' => 'Daily Revenue (KES)',
                    'data' => $data->pluck('total_revenue'),
                    'borderColor' => 'rgb(34, 197, 94)',
                    'backgroundColor' => 'rgba(34, 197, 94, 0.1)',
                    'fill' => true,
                ],
            ],
        ];
    }

    private function getEstateComparisonData()
    {
        $data = Estate::with(['houses.meterReadings' => function ($query): void {
            $query->where('created_at', '>=', now()->subDays($this->managementDateRange));
        }])
            ->get()
            ->map(fn ($estate) => [
                'name' => $estate->name,
                'consumption' => $estate->houses->flatMap->meterReadings->sum('consumption'),
                'houses' => $estate->houses->count(),
            ])
            ->sortByDesc('consumption')
            ->take(10);

        return [
            'labels' => $data->pluck('name'),
            'datasets' => [
                [
                    'label' => 'Total Consumption by Estate',
                    'data' => $data->pluck('consumption'),
                    'backgroundColor' => [
                        'rgba(59, 130, 246, 0.8)',
                        'rgba(34, 197, 94, 0.8)',
                        'rgba(251, 191, 36, 0.8)',
                        'rgba(239, 68, 68, 0.8)',
                        'rgba(168, 85, 247, 0.8)',
                    ],
                ],
            ],
        ];
    }

    public function render()
    {
        return view('livewire.dashboard.management-dashboard', [
            'managementKpiData' => $this->management_kpi_data,
            'managementChartData' => $this->management_chart_data,
            'recentReadings' => $this->recent_readings,
            'topConsumers' => $this->top_consumers,
            'estateAnalytics' => $this->estate_analytics,
        ]);
    }
}
