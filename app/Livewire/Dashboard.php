<?php

namespace App\Livewire;

use App\Livewire\Dashboard\BaseDashboard;
use App\Traits\HasPermissions;
use Livewire\Component;

class Dashboard extends BaseDashboard
{
    use HasPermissions;

    // This component now extends BaseDashboard and serves as the main entry point
    // The actual functionality has been split into specialized components

    public function render()
    {
        // Call the parent render method to get base dashboard functionality
        $parentView = parent::render();

        // We can add any additional dashboard-wide logic here if needed

        return $parentView;
    }
}
