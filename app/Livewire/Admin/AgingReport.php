<?php

namespace App\Livewire\Admin;

use App\Exports\AgingReportExport;
use App\Services\AgingReportPdfService;
use App\Services\FinancialReportService;
use Livewire\Component;
use Maatwebsite\Excel\Facades\Excel;

class AgingReport extends Component
{
    public $estate_id;

    public $aging_report = [];

    public $show_report = false;

    public function mount()
    {
        $this->authorize('reports.view_all');
        $this->generateReport();
    }

    public function generateReport()
    {
        $financialReportService = new FinancialReportService;
        $this->aging_report = $financialReportService->generateAgingReport($this->estate_id);
        $this->show_report = true;
    }

    public function exportToExcel()
    {
        $financialReportService = new FinancialReportService;
        $agingReport = $financialReportService->generateAgingReport($this->estate_id);

        return Excel::download(new AgingReportExport($agingReport), 'aging-report-'.now()->format('Y-m-d').'.xlsx');
    }

    public function exportToPdf()
    {
        $financialReportService = new FinancialReportService;
        $agingReport = $financialReportService->generateAgingReport($this->estate_id);

        $agingReportPdfService = new AgingReportPdfService;
        $pdfPath = $agingReportPdfService->generateAgingReportPdf($agingReport);

        return response()->download(storage_path('app/'.$pdfPath), 'aging-report-'.now()->format('Y-m-d').'.pdf');
    }

    public function render()
    {
        return view('livewire.admin.aging-report')
            ->layout('components.layouts.app');
    }
}
