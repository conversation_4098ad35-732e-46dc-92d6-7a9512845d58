<?php

namespace App\Livewire\Admin;

use App\Models\Estate;
use App\Models\User;
use App\Services\EstateAssignmentService;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\WithPagination;

class EstateAssignmentManager extends Component
{
    use WithPagination;

    public $search = '';

    public $roleFilter = '';

    public $selectedUser;

    public $selectedEstates = [];

    public $allEstates = [];

    public $showAssignmentModal = false;

    public $showHistoryModal = false;

    public $assignmentHistory = [];

    protected $estateAssignmentService;

    protected $rules = [
        'selectedEstates' => 'required|array|min:1',
        'selectedEstates.*' => 'exists:estates,id',
    ];

    public function boot(EstateAssignmentService $estateAssignmentService): void
    {
        $this->estateAssignmentService = $estateAssignmentService;
    }

    public function mount(): void
    {
        $this->allEstates = Estate::active()->get();
    }

    public function render(): \Illuminate\View\View
    {
        $user = Auth::user();

        $users = User::query()
            ->when($this->search, function ($query): void {
                $query->where('name', 'like', '%'.$this->search.'%')
                    ->orWhere('email', 'like', '%'.$this->search.'%');
            })
            ->when($this->roleFilter, function ($query): void {
                $query->whereHas('roles', function ($q): void {
                    $q->where('name', $this->roleFilter);
                });
            })
            ->when(! $user->can('users.manage_all'), function ($query) use ($user): void {
                // If not admin, only show users from assigned estates
                $assignedEstateIds = $user->assignedEstates()->pluck('estates.id');
                if (! $assignedEstateIds->isEmpty()) {
                    $query->whereHas('assignedEstates', function ($q) use ($assignedEstateIds): void {
                        $q->whereIn('estates.id', $assignedEstateIds);
                    });
                }
            })
            ->whereNotIn('role', ['admin', 'resident'])
            ->with(['assignedEstates'])
            ->paginate(10);

        return view('livewire.admin.estate-assignment-manager', [
            'users' => $users,
            'roles' => ['manager', 'reviewer', 'caretaker'],
        ]);
    }

    public function openAssignmentModal($userId): void
    {
        $this->selectedUser = User::findOrFail($userId);
        $this->selectedEstates = $this->selectedUser->assignedEstates()->pluck('estates.id')->toArray();
        $this->showAssignmentModal = true;
    }

    public function closeAssignmentModal(): void
    {
        $this->showAssignmentModal = false;
        $this->selectedUser = null;
        $this->selectedEstates = [];
        $this->resetErrorBag();
    }

    public function saveAssignments(): void
    {
        $this->validate();

        $this->estateAssignmentService->assignUserToEstates(
            $this->selectedUser,
            $this->selectedEstates,
            Auth::user()
        );

        $this->closeAssignmentModal();
        $this->dispatch('notify', [
            'type' => 'success',
            'message' => 'Estate assignments updated successfully.',
        ]);
    }

    public function openHistoryModal($userId): void
    {
        $this->selectedUser = User::findOrFail($userId);
        $this->assignmentHistory = $this->estateAssignmentService->getUserAssignmentHistory($this->selectedUser);
        $this->showHistoryModal = true;
    }

    public function closeHistoryModal(): void
    {
        $this->showHistoryModal = false;
        $this->selectedUser = null;
        $this->assignmentHistory = [];
    }

    public function bulkAssignEstates(): void
    {
        $this->validate([
            'selectedEstates' => 'required|array|min:1',
            'selectedEstates.*' => 'exists:estates,id',
        ]);

        // This would be implemented with a bulk selection mechanism
        // For now, we'll just show a message
        $this->dispatch('notify', [
            'type' => 'info',
            'message' => 'Bulk assignment feature coming soon.',
        ]);
    }

    public function removeUserFromEstate($userId, $estateId): void
    {
        $user = User::findOrFail($userId);
        $estate = Estate::findOrFail($estateId);

        $this->estateAssignmentService->removeUserFromEstate($user, $estate, Auth::user());

        $this->dispatch('notify', [
            'type' => 'success',
            'message' => 'User removed from estate successfully.',
        ]);
    }

    public function getEstateName($estateId): string
    {
        $estate = $this->allEstates->firstWhere('id', $estateId);

        return $estate ? $estate->name : 'Unknown';
    }

    public function isUserAssignedToEstate($userId, $estateId): bool
    {
        $user = User::find($userId);

        return $user && $user->assignedEstates()->where('estates.id', $estateId)->exists();
    }
}
