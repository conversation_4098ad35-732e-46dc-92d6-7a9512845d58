<?php

namespace App\Livewire\Admin;

use App\Models\User;
use App\Services\ManagementHierarchyService;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\WithPagination;

class TeamManagement extends Component
{
    use WithPagination;

    public $search = '';

    public $roleFilter = '';

    public $selectedManager;

    public $selectedSubordinates = [];

    public $relationshipType = 'manages';

    public $allManagers = [];

    public $allSubordinates = [];

    public $showTeamModal = false;

    public $showHierarchyModal = false;

    public $teamHierarchy = [];

    protected $managementHierarchyService;

    protected $rules = [
        'selectedSubordinates' => 'required|array|min:1',
        'selectedSubordinates.*' => 'exists:users,id',
        'relationshipType' => 'required|in:manages,oversees',
    ];

    public function boot(ManagementHierarchyService $managementHierarchyService)
    {
        $this->managementHierarchyService = $managementHierarchyService;
    }

    public function mount()
    {
        $this->loadAvailableUsers();
    }

    public function render()
    {
        $user = Auth::user();

        $managers = User::query()
            ->when($this->search, function ($query): void {
                $query->where('name', 'like', '%'.$this->search.'%')
                    ->orWhere('email', 'like', '%'.$this->search.'%');
            })
            ->when($this->roleFilter, function ($query): void {
                $query->where('role', $this->roleFilter);
            })
            ->when(! $user->can('users.manage_all'), function ($query) use ($user): void {
                // If not admin, only show users from assigned estates
                $assignedEstateIds = $user->assignedEstates()->pluck('estates.id')->toArray();
                if (! empty($assignedEstateIds)) {
                    $query->whereHas('assignedEstates', function ($q) use ($assignedEstateIds): void {
                        $q->whereIn('estates.id', $assignedEstateIds);
                    });
                }
            })
            ->whereIn('role', ['admin', 'manager', 'reviewer'])
            ->with(['subordinates'])
            ->paginate(10);

        return view('livewire.admin.team-management', [
            'managers' => $managers,
            'roles' => ['admin', 'manager', 'reviewer', 'caretaker'],
        ]);
    }

    public function loadAvailableUsers()
    {
        $user = Auth::user();

        $this->allManagers = User::whereIn('role', ['admin', 'manager', 'reviewer'])
            ->when(! $user->can('users.manage_all'), function ($query) use ($user): void {
                $assignedEstateIds = $user->assignedEstates()->pluck('estates.id')->toArray();
                if (! empty($assignedEstateIds)) {
                    $query->whereHas('assignedEstates', function ($q) use ($assignedEstateIds): void {
                        $q->whereIn('estates.id', $assignedEstateIds);
                    });
                }
            })
            ->orderBy('name')
            ->get();

        $this->allSubordinates = User::whereIn('role', ['manager', 'reviewer', 'caretaker'])
            ->when(! $user->can('users.manage_all'), function ($query) use ($user): void {
                $assignedEstateIds = $user->assignedEstates()->pluck('estates.id')->toArray();
                if (! empty($assignedEstateIds)) {
                    $query->whereHas('assignedEstates', function ($q) use ($assignedEstateIds): void {
                        $q->whereIn('estates.id', $assignedEstateIds);
                    });
                }
            })
            ->orderBy('name')
            ->get();
    }

    public function openTeamModal($managerId)
    {
        $this->selectedManager = User::findOrFail($managerId);
        $this->selectedSubordinates = $this->selectedManager->subordinates()->pluck('subordinate_id')->toArray();
        $this->showTeamModal = true;
    }

    public function closeTeamModal()
    {
        $this->showTeamModal = false;
        $this->selectedManager = null;
        $this->selectedSubordinates = [];
        $this->relationshipType = 'manages';
        $this->resetErrorBag();
    }

    public function saveTeamAssignments()
    {
        $this->validate();

        $this->managementHierarchyService->assignSubordinates(
            $this->selectedManager,
            $this->selectedSubordinates,
            $this->relationshipType
        );

        $this->closeTeamModal();
        $this->dispatch('notify', [
            'type' => 'success',
            'message' => 'Team assignments updated successfully.',
        ]);
    }

    public function openHierarchyModal($managerId)
    {
        $this->selectedManager = User::findOrFail($managerId);
        $this->teamHierarchy = $this->managementHierarchyService->getAllSubordinates($this->selectedManager);
        $this->showHierarchyModal = true;
    }

    public function closeHierarchyModal()
    {
        $this->showHierarchyModal = false;
        $this->selectedManager = null;
        $this->teamHierarchy = [];
    }

    public function removeSubordinate($managerId, $subordinateId)
    {
        $manager = User::findOrFail($managerId);
        $subordinate = User::findOrFail($subordinateId);

        $this->managementHierarchyService->removeManagementRelationship($manager, $subordinate);

        $this->dispatch('notify', [
            'type' => 'success',
            'message' => 'Subordinate removed from team successfully.',
        ]);
    }

    public function getAvailableSubordinates()
    {
        if (! $this->selectedManager) {
            return $this->allSubordinates;
        }

        return $this->allSubordinates->filter(fn ($subordinate) => $this->managementHierarchyService->canManageUser($this->selectedManager, $subordinate));
    }

    public function getRelationshipLabel($relationship)
    {
        return match ($relationship) {
            'manages' => 'Manages',
            'oversees' => 'Oversees',
            default => ucfirst((string) $relationship),
        };
    }

    public function getSubordinateName($subordinateId)
    {
        $subordinate = $this->allSubordinates->firstWhere('id', $subordinateId);

        return $subordinate ? $subordinate->name : 'Unknown';
    }

    public function getHierarchyLevel($user)
    {
        return $this->managementHierarchyService->getHierarchyLevel($user);
    }

    public function canManageUser($manager, $subordinate)
    {
        return $this->managementHierarchyService->canManageUser($manager, $subordinate);
    }
}
