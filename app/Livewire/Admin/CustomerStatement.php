<?php

declare(strict_types=1);

namespace App\Livewire\Admin;

use App\Exports\CustomerStatementExport;
use App\Models\AccountTransaction;
use App\Models\House;
use App\Models\Invoice;
use App\Services\PdfGenerationService;
use Illuminate\Auth\Access\AuthorizationException;
use Livewire\Attributes\Computed;
use Livewire\Attributes\On;
use Livewire\Attributes\Title;
use Livewire\Component;
use Livewire\WithPagination;
use Maatwebsite\Excel\Facades\Excel;
use Symfony\Component\HttpFoundation\Response;

#[Title('Customer Statements')]
class CustomerStatement extends Component
{
    use WithPagination;

    public ?House $selectedHouse = null;

    public ?string $startDate = null;

    public ?string $endDate = null;

    public string $search = '';

    public int $perPage = 10;

    public string $sortField = 'created_at';

    public string $sortDirection = 'desc';

    protected $queryString = [
        'search' => ['except' => ''],
        'startDate' => ['except' => ''],
        'endDate' => ['except' => ''],
        'perPage' => ['except' => 10],
        'sortField' => ['except' => 'created_at'],
        'sortDirection' => ['except' => 'desc'],
    ];

    public function mount(): void
    {
        $this->authorizeAccess();
        $this->startDate = now()->subMonths(3)->format('Y-m-d');
        $this->endDate = now()->format('Y-m-d');
    }

    #[Computed]
    public function houses()
    {
        return House::with(['estate', 'contacts'])
            ->when($this->search, function ($query): void {
                $query->where(function ($q): void {
                    $q->where('house_number', 'like', "%{$this->search}%")
                        ->orWhereHas('contacts', function ($contactQuery): void {
                            $contactQuery->where('name', 'like', "%{$this->search}%")
                                ->orWhere('phone', 'like', "%{$this->search}%");
                        })
                        ->orWhereHas('estate', function ($estateQuery): void {
                            $estateQuery->where('name', 'like', "%{$this->search}%");
                        });
                });
            })
            ->orderBy('house_number')
            ->get();
    }

    #[Computed]
    public function transactions()
    {
        if (! $this->selectedHouse instanceof \App\Models\House) {
            return collect();
        }

        AccountTransaction::with(['user', 'houseAccount.house'])
            ->where('house_account_id', $this->selectedHouse->account->id)
            ->when($this->startDate, function ($query): void {
                $query->whereDate('created_at', '>=', $this->startDate);
            })
            ->when($this->endDate, function ($query): void {
                $query->whereDate('created_at', '<=', $this->endDate);
            })
            ->orderBy($this->sortField, $this->sortDirection);

        return $query->paginate($this->perPage);
    }

    #[Computed]
    public function invoices()
    {
        if (! $this->selectedHouse instanceof \App\Models\House) {
            return collect();
        }

        return Invoice::with(['waterRate'])
            ->where('house_id', $this->selectedHouse->id)
            ->when($this->startDate, function ($query): void {
                $query->whereDate('created_at', '>=', $this->startDate);
            })
            ->when($this->endDate, function ($query): void {
                $query->whereDate('created_at', '<=', $this->endDate);
            })
            ->orderBy('created_at', 'desc')
            ->get();
    }

    #[Computed]
    public function statementSummary()
    {
        if (! $this->selectedHouse instanceof \App\Models\House) {
            return null;
        }

        $transactions = $this->transactions;
        $invoices = $this->invoices;

        $openingBalance = $this->getOpeningBalance();
        $totalInvoices = $invoices->sum('total_due');
        $totalPayments = $transactions->where('transaction_type', 'payment')->sum('amount');
        $totalAdjustments = $transactions->where('transaction_type', 'adjustment')->sum('amount');
        $closingBalance = $openingBalance + $totalInvoices - $totalPayments + $totalAdjustments;

        return [
            'opening_balance' => $openingBalance,
            'total_invoices' => $totalInvoices,
            'total_payments' => $totalPayments,
            'total_adjustments' => $totalAdjustments,
            'closing_balance' => $closingBalance,
            'transaction_count' => $transactions->count(),
            'invoice_count' => $invoices->count(),
        ];
    }

    public function selectHouse(House $house): void
    {
        $this->selectedHouse = $house;
        $this->resetPage();
    }

    public function sortBy(string $field): void
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortField = $field;
            $this->sortDirection = 'asc';
        }
    }

    public function exportPdf(): Response
    {
        if (! $this->selectedHouse instanceof \App\Models\House) {
            $this->dispatch('notify', message: 'Please select a house first', type: 'error');

            // Return a proper response instead of empty string
            return response()->streamDownload(function (): void {
                echo 'No house selected';
            }, 'error.txt', [
                'Content-Type' => 'text/plain',
            ]);
        }

        $pdfGenerationService = new PdfGenerationService;
        $pdfPath = $pdfGenerationService->generateStatementPdf($this->selectedHouse, $this->invoices(), $this->transactions()->getCollection(), $this->startDate, $this->endDate);

        return response()->download($pdfPath, "statement-{$this->selectedHouse->house_number}-{$this->startDate}-to-{$this->endDate}.pdf");
    }

    public function exportExcel(): Response
    {
        if (! $this->selectedHouse instanceof \App\Models\House) {
            $this->dispatch('notify', message: 'Please select a house first', type: 'error');

            // Return a proper response instead of empty string
            return response()->streamDownload(function (): void {
                echo 'No house selected';
            }, 'error.txt', [
                'Content-Type' => 'text/plain',
            ]);
        }

        return Excel::download(
            new CustomerStatementExport($this->selectedHouse, $this->invoices(), $this->transactions()->getCollection()),
            "statement-{$this->selectedHouse->house_number}-{$this->startDate}-to-{$this->endDate}.xlsx"
        );
    }

    public function resetFilters(): void
    {
        $this->reset(['search', 'startDate', 'endDate', 'perPage']);
        $this->startDate = now()->subMonths(3)->format('Y-m-d');
        $this->endDate = now()->format('Y-m-d');
        $this->resetPage();
    }

    private function getOpeningBalance(): float
    {
        if (! $this->selectedHouse || ! $this->startDate) {
            return 0;
        }

        $transactionsBefore = AccountTransaction::where('house_account_id', $this->selectedHouse->account->id)
            ->whereDate('created_at', '<', $this->startDate)
            ->sum('amount');

        $invoicesBefore = Invoice::where('house_id', $this->selectedHouse->id)
            ->whereDate('created_at', '<', $this->startDate)
            ->sum('total_due');

        return $invoicesBefore - $transactionsBefore;
    }

    #[On('house-selected')]
    public function onHouseSelected(House $house): void
    {
        $this->selectHouse($house);
    }

    public function render()
    {
        $this->authorizeAccess();

        return view('livewire.admin.customer-statement', [
            'houses' => $this->houses,
            'transactions' => $this->transactions,
            'invoices' => $this->invoices,
            'statementSummary' => $this->statementSummary,
        ]);
    }

    private function authorizeAccess(): void
    {
        if (! auth()->user()->hasPermissionTo('export.data_all')) {
            throw new AuthorizationException('You do not have permission to access customer statements.');
        }
    }
}
