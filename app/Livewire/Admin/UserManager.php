<?php

namespace App\Livewire\Admin;

use App\Models\ActivityLog;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Livewire\Attributes\Url;
use Livewire\Component;
use Livewire\WithPagination;
use Spatie\Permission\Models\Role;

class UserManager extends Component
{
    use WithPagination;

    #[Url]
    public string $search = '';

    #[Url]
    public string $roleFilter = 'all';

    #[Url]
    public string $sortBy = 'name';

    #[Url]
    public string $sortDirection = 'asc';

    public bool $showCreateModal = false;

    public bool $showEditModal = false;

    public bool $showDeleteModal = false;

    public ?User $selectedUser = null;

    public string $name = '';

    public string $email = '';

    public string $password = '';

    public string $passwordConfirmation = '';

    public string $role = 'caretaker';

    protected $rules = [
        'name' => 'required|string|max:255',
        'email' => 'required|email|unique:users,email',
        'password' => 'required|min:8|confirmed',
        'role' => 'required|string',
    ];

    public function updatedSearch(): void
    {
        $this->resetPage();
    }

    public function updatedRoleFilter(): void
    {
        $this->resetPage();
    }

    public function sortBy(string $field): void
    {
        if ($this->sortBy === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortBy = $field;
            $this->sortDirection = 'asc';
        }
        $this->resetPage();
    }

    public function clearFilters(): void
    {
        $this->search = '';
        $this->roleFilter = 'all';
        $this->sortBy = 'name';
        $this->sortDirection = 'asc';
        $this->resetPage();
    }

    public function openCreateModal(): void
    {
        $this->resetForm();
        $this->showCreateModal = true;
    }

    public function openEditModal(User $user): void
    {
        $this->selectedUser = $user;
        $this->name = $user->name;
        $this->email = $user->email;

        $userRoles = $user->roles()->pluck('name')->toArray();
        $this->role = empty($userRoles) ? 'caretaker' : $userRoles[0];

        $this->password = '';
        $this->passwordConfirmation = '';

        $this->rules['email'] = 'required|email|unique:users,email,'.$user->id;
        $this->rules['password'] = 'nullable|min:8|confirmed';

        $this->showEditModal = true;
    }

    public function openDeleteModal(User $user): void
    {
        $this->selectedUser = $user;
        $this->showDeleteModal = true;
    }

    public function closeModals(): void
    {
        $this->showCreateModal = false;
        $this->showEditModal = false;
        $this->showDeleteModal = false;
        $this->selectedUser = null;
        $this->resetForm();
    }

    public function createUser(): void
    {
        $this->validate();

        $user = User::create([
            'name' => $this->name,
            'email' => $this->email,
            'password' => Hash::make($this->password),
        ]);

        $user->assignRole($this->role);

        ActivityLog::create([
            'user_id' => Auth::id(),
            'action' => 'created',
            'entity_type' => 'User',
            'entity_id' => $user->id,
            'description' => "Created user: {$user->name} ({$user->email})",
            'new_values' => $user->toArray(),
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ]);

        $this->closeModals();
        session()->flash('message', 'User created successfully.');
    }

    public function updateUser(): void
    {
        $this->validate();

        $oldValues = $this->selectedUser->toArray();

        $updateData = [
            'name' => $this->name,
            'email' => $this->email,
        ];

        if ($this->password !== '' && $this->password !== '0') {
            $updateData['password'] = Hash::make($this->password);
        }

        $this->selectedUser->update($updateData);
        $this->selectedUser->syncRoles([$this->role]);

        ActivityLog::create([
            'user_id' => Auth::id(),
            'action' => 'updated',
            'entity_type' => 'User',
            'entity_id' => $this->selectedUser->id,
            'description' => "Updated user: {$this->selectedUser->name}",
            'old_values' => $oldValues,
            'new_values' => $this->selectedUser->fresh()->toArray(),
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ]);

        $this->closeModals();
        session()->flash('message', 'User updated successfully.');
    }

    public function deleteUser(): void
    {
        if ($this->selectedUser->id === Auth::id()) {
            session()->flash('error', 'You cannot delete your own account.');
            $this->closeModals();

            return;
        }

        $oldValues = $this->selectedUser->toArray();
        $userName = $this->selectedUser->name;

        $this->selectedUser->delete();

        ActivityLog::create([
            'user_id' => Auth::id(),
            'action' => 'deleted',
            'entity_type' => 'User',
            'entity_id' => $this->selectedUser->id,
            'description' => "Deleted user: {$userName}",
            'old_values' => $oldValues,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ]);

        $this->closeModals();
        session()->flash('message', 'User deleted successfully.');
    }

    private function resetForm(): void
    {
        $this->name = '';
        $this->email = '';
        $this->password = '';
        $this->passwordConfirmation = '';
        $this->role = 'caretaker';
        $this->rules['email'] = 'required|email|unique:users,email';
        $this->rules['password'] = 'required|min:8|confirmed';
        $this->resetValidation();
    }

    public function render()
    {
        $query = User::query();

        if ($this->search !== '' && $this->search !== '0') {
            $query->where(function ($q): void {
                $q->where('name', 'like', '%'.$this->search.'%')
                    ->orWhere('email', 'like', '%'.$this->search.'%');
            });
        }

        if ($this->roleFilter !== 'all') {
            // This assumes 'role' column still exists in users table for filtering.
            // If not, this part needs to join with model_has_roles table.
            // For now, assuming it's kept for convenience or direct filtering.
            // If 'role' column is removed, this would need:
            // $query->whereHas('roles', function($q) use ($roleFilter) {
            //     $q->where('name', $roleFilter);
            // });
            $query->where('role', $this->roleFilter);
        }

        $query->orderBy($this->sortBy, $this->sortDirection);

        $lengthAwarePaginator = $query->paginate(10);

        return view('livewire.admin.user-manager', [
            'users' => $lengthAwarePaginator,
            'roles' => Role::pluck('name')->sort()->values(),
        ]);
    }
}
