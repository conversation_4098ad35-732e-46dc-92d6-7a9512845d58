<?php

declare(strict_types=1);

namespace App\Livewire\Admin;

use App\Models\Estate;
use App\Models\Invoice;
use App\Services\BillingOperationService;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\On;
use Livewire\Component;
use Livewire\WithPagination;

class BillingManager extends Component
{
    use WithPagination;

    public $selectedInvoices = [];

    public $statusFilter = '';

    public $estateFilter = '';

    public $bulkInvoiceAmount = 0;

    public $bulkInvoiceDescription = '';

    public $bulkReminderMessage = '';

    public $lastRefresh;

    public $showBulkInvoiceModal = false;

    public $showBulkReminderModal = false;

    protected $billingOperationService;

    protected $listeners = [
        'refresh-data' => 'refreshData',
        'invoice-generated' => 'handleInvoiceGenerated',
        'reminders-sent' => 'handleRemindersSent',
        'invoices-approved' => 'handleInvoicesApproved',
        'permission-denied' => 'handlePermissionDenied',
    ];

    public function boot(BillingOperationService $billingOperationService)
    {
        $this->billingOperationService = $billingOperationService;
    }

    public function mount()
    {
        $this->lastRefresh = now()->diffForHumans();
    }

    public function render()
    {
        $user = Auth::user();
        $estates = $user->can('estates.view_all')
            ? Estate::all()
            : Estate::where('id', $user->estate_id)->get();

        $invoices = $this->getFilteredInvoices();
        $billingSummary = $this->getBillingSummary();
        $agingAnalysis = $this->billingOperationService->getAgingAnalysis();
        $bulkStats = $this->billingOperationService->getBulkOperationStats();

        return view('livewire.admin.billing-manager', [
            'invoices' => $invoices,
            'estates' => $estates,
            'billingSummary' => $billingSummary,
            'agingAnalysis' => $agingAnalysis,
            'bulkStats' => $bulkStats,
        ]);
    }

    public function getFilteredInvoices()
    {
        $builder = Invoice::with(['house', 'house.estate', 'submittedBy', 'approvedBy']);

        if ($this->statusFilter) {
            $builder->where('status', $this->statusFilter);
        }

        if ($this->estateFilter) {
            $builder->whereHas('house', function ($q): void {
                $q->where('estate_id', $this->estateFilter);
            });
        }

        return $builder->latest()->paginate(20);
    }

    public function getBillingSummary()
    {
        $user = Auth::user();
        $estateId = $user->can('estates.view_all') ? null : $user->estate_id;

        if ($estateId) {
            return $this->billingOperationService->getBillingSummary($estateId);
        }

        // For management users, get summary across all estates
        $allEstates = Estate::all();
        $totalSummary = [
            'total_houses' => 0,
            'total_invoices' => 0,
            'paid_invoices' => 0,
            'overdue_invoices' => 0,
            'draft_invoices' => 0,
            'submitted_invoices' => 0,
            'approved_invoices' => 0,
            'total_amount' => 0,
            'paid_amount' => 0,
            'overdue_amount' => 0,
        ];

        foreach ($allEstates as $allEstate) {
            $summary = $this->billingOperationService->getBillingSummary($allEstate->id);
            foreach (array_keys($totalSummary) as $key) {
                $totalSummary[$key] += $summary[$key];
            }
        }

        return $totalSummary;
    }

    public function toggleInvoiceSelection($invoiceId)
    {
        if (in_array($invoiceId, $this->selectedInvoices)) {
            $this->selectedInvoices = array_diff($this->selectedInvoices, [$invoiceId]);
        } else {
            $this->selectedInvoices[] = $invoiceId;
        }
    }

    public function selectAllInvoices()
    {
        $invoices = $this->getFilteredInvoices();
        $this->selectedInvoices = $invoices->pluck('id')->toArray();
    }

    public function clearSelection()
    {
        $this->selectedInvoices = [];
    }

    public function generateBulkInvoices()
    {
        $user = Auth::user();

        if (! $user->can('invoices.generate_all')) {
            $this->dispatch('permission-denied');

            return;
        }

        $this->validate([
            'bulkInvoiceAmount' => 'required|numeric|min:0',
            'bulkInvoiceDescription' => 'required|string|min:3',
        ]);

        try {
            $estateId = $this->estateFilter ?: $user->estate_id;
            $houses = \App\Models\House::where('estate_id', $estateId)->get();

            $invoices = $this->billingOperationService->generateBulkInvoices(
                $houses->pluck('id')->toArray(),
                $this->bulkInvoiceAmount,
                $this->bulkInvoiceDescription,
                $user
            );

            $this->dispatch('invoice-generated', [
                'count' => count($invoices),
                'amount' => $this->bulkInvoiceAmount,
            ]);

            $this->reset(['bulkInvoiceAmount', 'bulkInvoiceDescription', 'showBulkInvoiceModal']);
            $this->refreshData();

        } catch (\Exception $e) {
            $this->dispatch('error', ['message' => 'Failed to generate bulk invoices: '.$e->getMessage()]);
        }
    }

    public function sendBulkReminders()
    {
        $user = Auth::user();

        if (! $user->can('invoices.send_all') && ! $user->can('invoices.send_assigned')) {
            $this->dispatch('permission-denied');

            return;
        }

        if (empty($this->selectedInvoices)) {
            $this->dispatch('error', ['message' => 'Please select invoices to send reminders']);

            return;
        }

        $this->validate([
            'bulkReminderMessage' => 'required|string|min:3',
        ]);

        try {
            $result = $this->billingOperationService->sendBulkReminders(
                $this->selectedInvoices,
                $this->bulkReminderMessage,
                $user
            );

            $this->dispatch('reminders-sent', [
                'sent_count' => $result['sent_count'],
                'failed_count' => $result['failed_count'],
            ]);

            $this->reset(['bulkReminderMessage', 'showBulkReminderModal', 'selectedInvoices']);
            $this->refreshData();

        } catch (\Exception $e) {
            $this->dispatch('error', ['message' => 'Failed to send bulk reminders: '.$e->getMessage()]);
        }
    }

    public function bulkApproveInvoices()
    {
        $user = Auth::user();

        if (! $user->can('invoices.approve_all') && ! $user->can('invoices.approve_assigned')) {
            $this->dispatch('permission-denied');

            return;
        }

        if (empty($this->selectedInvoices)) {
            $this->dispatch('error', ['message' => 'Please select invoices to approve']);

            return;
        }

        try {
            $result = $this->billingOperationService->bulkApproveInvoices(
                $this->selectedInvoices,
                $user
            );

            $this->dispatch('invoices-approved', [
                'approved_count' => $result['approved_count'],
                'skipped_count' => $result['skipped_count'],
            ]);

            $this->reset(['selectedInvoices']);
            $this->refreshData();

        } catch (\Exception $e) {
            $this->dispatch('error', ['message' => 'Failed to approve invoices: '.$e->getMessage()]);
        }
    }

    public function bulkCancelInvoices()
    {
        $user = Auth::user();

        if (! $user->can('invoices.delete')) {
            $this->dispatch('permission-denied');

            return;
        }

        if (empty($this->selectedInvoices)) {
            $this->dispatch('error', ['message' => 'Please select invoices to cancel']);

            return;
        }

        try {
            $result = $this->billingOperationService->bulkCancelInvoices(
                $this->selectedInvoices,
                $user
            );

            $this->dispatch('invoices-cancelled', [
                'cancelled_count' => $result['cancelled_count'],
                'skipped_count' => $result['skipped_count'],
            ]);

            $this->reset(['selectedInvoices']);
            $this->refreshData();

        } catch (\Exception $e) {
            $this->dispatch('error', ['message' => 'Failed to cancel invoices: '.$e->getMessage()]);
        }
    }

    public function exportBillingData()
    {
        try {
            // This would integrate with the export service
            $this->dispatch('billing-data-exported');
        } catch (\Exception $e) {
            $this->dispatch('error', ['message' => 'Failed to export billing data: '.$e->getMessage()]);
        }
    }

    public function refreshData()
    {
        $this->lastRefresh = now()->diffForHumans();
    }

    #[On('invoice-generated')]
    public function handleInvoiceGenerated($data)
    {
        $this->dispatch('success', [
            'message' => "Successfully generated {$data['count']} invoices totaling {$data['amount']}",
        ]);
    }

    #[On('reminders-sent')]
    public function handleRemindersSent($data)
    {
        $message = "Reminders sent to {$data['sent_count']} invoices";
        if ($data['failed_count'] > 0) {
            $message .= ", {$data['failed_count']} failed";
        }
        $this->dispatch('success', ['message' => $message]);
    }

    #[On('invoices-approved')]
    public function handleInvoicesApproved($data)
    {
        $message = "Approved {$data['approved_count']} invoices";
        if ($data['skipped_count'] > 0) {
            $message .= ", {$data['skipped_count']} skipped";
        }
        $this->dispatch('success', ['message' => $message]);
    }

    #[On('permission-denied')]
    public function handlePermissionDenied()
    {
        $this->dispatch('error', ['message' => 'You do not have permission to perform this action']);
    }

    public function getInvoiceStatusColor($status)
    {
        return match ($status) {
            'draft' => 'gray',
            'submitted' => 'yellow',
            'approved' => 'blue',
            'sent' => 'indigo',
            'paid' => 'green',
            'overdue' => 'red',
            'cancelled' => 'gray',
            default => 'gray',
        };
    }

    public function getAgingColor($days)
    {
        if ($days <= 30) {
            return 'yellow';
        }
        if ($days <= 60) {
            return 'orange';
        }

        return 'red';
    }
}
