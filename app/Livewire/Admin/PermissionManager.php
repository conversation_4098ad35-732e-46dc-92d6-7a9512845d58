<?php

namespace App\Livewire\Admin;

use App\Models\User;
use App\Services\PermissionService;
use Livewire\Component;
use Livewire\WithPagination;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class PermissionManager extends Component
{
    use WithPagination;

    // Tab management
    public $activeTab = 'users';

    // User management
    public $selectedUserId;

    public $userSearch = '';

    public $selectedUserPermissions = [];

    public $selectedUserRoles = [];

    // Role management
    public $selectedRoleId;

    public $roleSearch = '';

    public $selectedRolePermissions = [];

    public $newRoleName = '';

    public $newRoleDescription = '';

    // Permission management
    public $permissionSearch = '';

    public $newPermissionName = '';

    public $newPermissionDescription = '';

    // Permission groups
    public $permissionGroups = [];

    // Bulk operations
    public $selectedUsers = [];

    public $selectedRoles = [];

    public $selectedPermissions = [];

    public $bulkAction = '';

    public $bulkRoleName = '';

    public $bulkPermissionName = '';

    public $confirmingBulkAction = false;

    public $bulkActionDetails = '';

    protected $permissionService;

    protected $rules = [
        'newRoleName' => 'required|string|min:2|max:255|unique:roles,name',
        'newRoleDescription' => 'nullable|string|max:500',
        'newPermissionName' => 'required|string|min:2|max:255|unique:permissions,name',
        'newPermissionDescription' => 'nullable|string|max:500',
        'bulkRoleName' => 'required|string|min:2|max:255|unique:roles,name',
        'bulkPermissionName' => 'required|string|min:2|max:255|unique:permissions,name',
    ];

    public function boot()
    {
        $this->permissionService = app(PermissionService::class);
        $this->loadPermissionGroups();
    }

    private function loadPermissionGroups()
    {
        $this->permissionGroups = $this->permissionService->getAllPermissionsGrouped();
    }

    // User Management Methods
    public function selectUser($userId)
    {
        $this->selectedUserId = $userId;
        $user = User::find($userId);

        if ($user) {
            $this->selectedUserPermissions = $this->permissionService->getUserPermissions($user);
            $this->selectedUserRoles = $this->permissionService->getUserRoles($user);
        }
    }

    public function updateUserPermissions()
    {
        if (! $this->selectedUserId) {
            return;
        }

        $user = User::find($this->selectedUserId);
        if ($user) {
            $this->permissionService->assignPermissionsToUser($user, $this->selectedUserPermissions);
            session()->flash('message', 'User permissions updated successfully!');
        }
    }

    public function updateUserRoles()
    {
        if (! $this->selectedUserId) {
            return;
        }

        $user = User::find($this->selectedUserId);
        if ($user) {
            $this->permissionService->assignRolesToUser($user, $this->selectedUserRoles);
            // Refresh permissions as roles may have changed
            $this->selectedUserPermissions = $this->permissionService->getUserPermissions($user);
            session()->flash('message', 'User roles updated successfully!');
        }
    }

    // Role Management Methods
    public function selectRole($roleId)
    {
        $this->selectedRoleId = $roleId;
        $role = Role::find($roleId);

        if ($role) {
            $this->selectedRolePermissions = $role->permissions->pluck('name')->toArray();
        }
    }

    public function updateRolePermissions()
    {
        if (! $this->selectedRoleId) {
            return;
        }

        $role = Role::find($this->selectedRoleId);
        if ($role) {
            $role->syncPermissions($this->selectedRolePermissions);
            session()->flash('message', 'Role permissions updated successfully!');
        }
    }

    public function createRole()
    {
        $this->validate([
            'newRoleName' => 'required|string|min:2|max:255|unique:roles,name',
            'newRoleDescription' => 'nullable|string|max:500',
        ]);

        $this->permissionService->createRole($this->newRoleName, $this->newRoleDescription);

        $this->reset(['newRoleName', 'newRoleDescription']);
        session()->flash('message', 'Role created successfully!');
    }

    public function deleteRole($roleId)
    {
        $role = Role::find($roleId);
        if ($role && $role->name !== 'admin') {
            $this->permissionService->deleteRole($role->name);
            session()->flash('message', 'Role deleted successfully!');

            if ($this->selectedRoleId == $roleId) {
                $this->reset(['selectedRoleId', 'selectedRolePermissions']);
            }
        }
    }

    // Permission Management Methods
    public function createPermission()
    {
        $this->validate([
            'newPermissionName' => 'required|string|min:2|max:255|unique:permissions,name',
            'newPermissionDescription' => 'nullable|string|max:500',
        ]);

        $this->permissionService->createPermission($this->newPermissionName, $this->newPermissionDescription);

        $this->reset(['newPermissionName', 'newPermissionDescription']);
        $this->loadPermissionGroups();
        session()->flash('message', 'Permission created successfully!');
    }

    public function deletePermission($permissionName)
    {
        if ($this->permissionService->deletePermission($permissionName)) {
            session()->flash('message', 'Permission deleted successfully!');
            $this->loadPermissionGroups();
        }
    }

    // Bulk Operations Methods
    public function toggleUserSelection($userId)
    {
        if (in_array($userId, $this->selectedUsers)) {
            $this->selectedUsers = array_diff($this->selectedUsers, [$userId]);
        } else {
            $this->selectedUsers[] = $userId;
        }
    }

    public function toggleRoleSelection($roleId)
    {
        if (in_array($roleId, $this->selectedRoles)) {
            $this->selectedRoles = array_diff($this->selectedRoles, [$roleId]);
        } else {
            $this->selectedRoles[] = $roleId;
        }
    }

    public function togglePermissionSelection($permissionName)
    {
        if (in_array($permissionName, $this->selectedPermissions)) {
            $this->selectedPermissions = array_diff($this->selectedPermissions, [$permissionName]);
        } else {
            $this->selectedPermissions[] = $permissionName;
        }
    }

    public function selectAllUsers()
    {
        $this->selectedUsers = $this->users->pluck('id')->toArray();
    }

    public function deselectAllUsers()
    {
        $this->selectedUsers = [];
    }

    public function selectAllRoles()
    {
        $this->selectedRoles = $this->roles->pluck('id')->toArray();
    }

    public function deselectAllRoles()
    {
        $this->selectedRoles = [];
    }

    public function selectAllPermissions()
    {
        $this->selectedPermissions = [];
        foreach ($this->permissionGroups as $permissionGroup) {
            foreach ($permissionGroup as $permission) {
                $this->selectedPermissions[] = $permission['name'];
            }
        }
    }

    public function deselectAllPermissions()
    {
        $this->selectedPermissions = [];
    }

    public function prepareBulkAction($action)
    {
        if (empty($this->selectedUsers) && empty($this->selectedRoles) && empty($this->selectedPermissions)) {
            session()->flash('error', 'Please select items to perform bulk action on.');

            return;
        }

        $this->bulkAction = $action;
        $this->confirmingBulkAction = true;

        // Prepare action details for confirmation
        switch ($action) {
            case 'assign_role':
                $this->bulkActionDetails = 'Assign role to '.count($this->selectedUsers).' selected users';
                break;
            case 'remove_role':
                $this->bulkActionDetails = 'Remove role from '.count($this->selectedUsers).' selected users';
                break;
            case 'assign_permission':
                $this->bulkActionDetails = 'Assign permission to '.count($this->selectedRoles).' selected roles';
                break;
            case 'remove_permission':
                $this->bulkActionDetails = 'Remove permission from '.count($this->selectedRoles).' selected roles';
                break;
            case 'delete_permissions':
                $this->bulkActionDetails = 'Delete '.count($this->selectedPermissions).' selected permissions';
                break;
            case 'delete_roles':
                $this->bulkActionDetails = 'Delete '.count($this->selectedRoles).' selected roles';
                break;
        }
    }

    public function confirmBulkAction()
    {
        $this->validate([
            'bulkRoleName' => 'required|string|min:2|max:255|unique:roles,name',
            'bulkPermissionName' => 'required|string|min:2|max:255|unique:permissions,name',
        ]);

        switch ($this->bulkAction) {
            case 'assign_role':
                $this->bulkAssignRole();
                break;
            case 'remove_role':
                $this->bulkRemoveRole();
                break;
            case 'assign_permission':
                $this->bulkAssignPermission();
                break;
            case 'remove_permission':
                $this->bulkRemovePermission();
                break;
            case 'delete_permissions':
                $this->bulkDeletePermissions();
                break;
            case 'delete_roles':
                $this->bulkDeleteRoles();
                break;
        }

        $this->resetBulkOperation();
        session()->flash('message', 'Bulk action completed successfully!');
    }

    public function cancelBulkAction()
    {
        $this->resetBulkOperation();
    }

    private function bulkAssignRole()
    {
        $role = $this->permissionService->createRole($this->bulkRoleName, 'Bulk created role');

        foreach ($this->selectedUsers as $selectedUser) {
            $user = User::find($selectedUser);
            if ($user) {
                $user->assignRole($role->name);
                $this->permissionService->invalidateUserCache($user);
            }
        }
    }

    private function bulkRemoveRole()
    {
        $role = Role::where('name', $this->bulkRoleName)->first();
        if ($role) {
            foreach ($this->selectedUsers as $selectedUser) {
                $user = User::find($selectedUser);
                if ($user) {
                    $user->removeRole($role->name);
                    $this->permissionService->invalidateUserCache($user);
                }
            }
        }
    }

    private function bulkAssignPermission()
    {
        $permission = $this->permissionService->createPermission($this->bulkPermissionName, 'Bulk created permission');

        foreach ($this->selectedRoles as $selectedRole) {
            $role = Role::find($selectedRole);
            if ($role) {
                $role->givePermissionTo($permission->name);
            }
        }
    }

    private function bulkRemovePermission()
    {
        $permission = Permission::where('name', $this->bulkPermissionName)->first();
        if ($permission) {
            foreach ($this->selectedRoles as $selectedRole) {
                $role = Role::find($selectedRole);
                if ($role) {
                    $role->revokePermissionTo($permission->name);
                }
            }
        }
    }

    private function bulkDeletePermissions()
    {
        foreach ($this->selectedPermissions as $selectedPermission) {
            $this->permissionService->deletePermission($selectedPermission);
        }
        $this->loadPermissionGroups();
    }

    private function bulkDeleteRoles()
    {
        foreach ($this->selectedRoles as $selectedRole) {
            $role = Role::find($selectedRole);
            if ($role && $role->name !== 'admin') {
                $this->permissionService->deleteRole($role->name);
            }
        }
    }

    private function resetBulkOperation()
    {
        $this->bulkAction = '';
        $this->bulkRoleName = '';
        $this->bulkPermissionName = '';
        $this->confirmingBulkAction = false;
        $this->bulkActionDetails = '';
        $this->selectedUsers = [];
        $this->selectedRoles = [];
        $this->selectedPermissions = [];
    }

    // Computed Properties
    public function getUsersProperty()
    {
        return User::query()
            ->when($this->userSearch, function ($query): void {
                $query->where('name', 'like', '%'.$this->userSearch.'%')
                    ->orWhere('email', 'like', '%'.$this->userSearch.'%');
            })
            ->with('roles')
            ->orderBy('name')
            ->paginate(10);
    }

    public function getRolesProperty()
    {
        return Role::query()
            ->when($this->roleSearch, function ($query): void {
                $query->where('name', 'like', '%'.$this->roleSearch.'%');
            })
            ->with('permissions')
            ->orderBy('name')
            ->get();
    }

    public function getFilteredPermissionGroupsProperty()
    {
        if (empty($this->permissionSearch)) {
            return $this->permissionGroups;
        }

        $filtered = [];
        foreach ($this->permissionGroups as $category => $permissions) {
            $permissionSearch = $this->permissionSearch;
            $filteredPermissions = array_filter($permissions, fn ($permission) => str_contains(strtolower((string) $permission['name']), strtolower((string) $permissionSearch)) ||
                   str_contains(strtolower($permission['description'] ?? ''), strtolower((string) $permissionSearch)));

            if ($filteredPermissions !== []) {
                $filtered[$category] = $filteredPermissions;
            }
        }

        return $filtered;
    }

    public function getSelectedUserProperty()
    {
        return $this->selectedUserId ? User::find($this->selectedUserId) : null;
    }

    public function getSelectedRoleProperty()
    {
        return $this->selectedRoleId ? Role::find($this->selectedRoleId) : null;
    }

    // Helper Methods
    public function toggleUserPermission($permission)
    {
        if (in_array($permission, $this->selectedUserPermissions)) {
            $this->selectedUserPermissions = array_diff($this->selectedUserPermissions, [$permission]);
        } else {
            $this->selectedUserPermissions[] = $permission;
        }
    }

    public function toggleUserRole($role)
    {
        if (in_array($role, $this->selectedUserRoles)) {
            $this->selectedUserRoles = array_diff($this->selectedUserRoles, [$role]);
        } else {
            $this->selectedUserRoles[] = $role;
        }
    }

    public function toggleRolePermission($permission)
    {
        if (in_array($permission, $this->selectedRolePermissions)) {
            $this->selectedRolePermissions = array_diff($this->selectedRolePermissions, [$permission]);
        } else {
            $this->selectedRolePermissions[] = $permission;
        }
    }

    public function switchTab($tab)
    {
        $this->activeTab = $tab;
        $this->reset(['selectedUserId', 'selectedRoleId', 'userSearch', 'roleSearch', 'permissionSearch']);
    }

    public function render()
    {
        return view('livewire.admin.permission-manager', [
            'users' => $this->users,
            'roles' => $this->roles,
            'permissionGroups' => $this->filteredPermissionGroups,
            'selectedUser' => $this->selectedUser,
            'selectedRole' => $this->selectedRole,
        ]);
    }
}
