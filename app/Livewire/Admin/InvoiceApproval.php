<?php

declare(strict_types=1);

namespace App\Livewire\Admin;

use App\Models\Invoice;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Livewire\Component;
use Livewire\WithPagination;

class InvoiceApproval extends Component
{
    use WithPagination;

    public string $search = '';

    public string $filter = 'pending';

    public string $rejectionReason = '';

    public ?int $selectedInvoiceId = null;

    protected $queryString = [
        'search' => ['except' => ''],
        'filter' => ['except' => 'pending'],
    ];

    public function getInvoicesProperty()
    {
        $query = Invoice::with(['house.estate', 'submittedBy', 'approvedBy'])
            ->when($this->search, function ($query): void {
                $query->where('invoice_number', 'like', '%'.$this->search.'%');
            });

        match ($this->filter) {
            'draft' => $query->where('status', 'draft'),
            'submitted' => $query->where('status', 'submitted'),
            'approved' => $query->where('status', 'approved'),
            default => $query->whereIn('status', ['draft', 'submitted']),
        };

        return $query->latest()->paginate(10);
    }

    public function getStatisticsProperty()
    {
        return [
            'draft' => Invoice::where('status', 'draft')->count(),
            'submitted' => Invoice::where('status', 'submitted')->count(),
            'approved' => Invoice::where('status', 'approved')->count(),
            'pending' => Invoice::whereIn('status', ['draft', 'submitted'])->count(),
        ];
    }

    public function submitForApproval(int $invoiceId): void
    {
        try {
            $invoice = Invoice::findOrFail($invoiceId);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException) {
            $this->dispatch('error', 'Invoice not found');

            return;
        }

        $this->authorize('submitForApproval', $invoice);

        if (! $invoice->canBeSubmitted()) {
            $this->dispatch('error', 'Invoice cannot be submitted for approval');

            return;
        }

        $invoice->submitForApproval(Auth::user());
        $this->dispatch('invoice-submitted');
    }

    public function approve(int $invoiceId): void
    {
        try {
            $invoice = Invoice::findOrFail($invoiceId);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException) {
            $this->dispatch('error', 'Invoice not found');

            return;
        }

        $this->authorize('approve', $invoice);

        if (! $invoice->canBeApproved()) {
            $this->dispatch('error', 'Invoice cannot be approved');

            return;
        }

        $invoice->approve(Auth::user());
        $this->dispatch('invoice-approved');
    }

    public function reject(int $invoiceId): void
    {
        $this->validate([
            'rejectionReason' => 'required|string|min:3',
        ]);

        try {
            $invoice = Invoice::findOrFail($invoiceId);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException) {
            $this->dispatch('error', 'Invoice not found');

            return;
        }

        $this->authorize('reject', $invoice);

        if ($invoice->status !== 'submitted') {
            $this->dispatch('error', 'Only submitted invoices can be rejected');

            return;
        }

        $invoice->status = 'draft';
        $invoice->save();

        // Log the rejection
        Log::info('Invoice rejected', [
            'invoice_id' => $invoice->id,
            'user_id' => Auth::id(),
            'reason' => $this->rejectionReason,
        ]);

        $this->rejectionReason = '';
        $this->dispatch('invoice-rejected');
    }

    public function sendInvoice(int $invoiceId): void
    {
        try {
            $invoice = Invoice::findOrFail($invoiceId);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException) {
            $this->dispatch('error', 'Invoice not found');

            return;
        }

        $this->authorize('send', $invoice);

        if (! $invoice->canBeSent()) {
            $this->dispatch('error', 'Invoice cannot be sent');

            return;
        }

        $invoice->markAsSent();
        $this->dispatch('invoice-sent');
    }

    public function viewInvoice(int $invoiceId): void
    {
        try {
            $invoice = Invoice::findOrFail($invoiceId);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException) {
            $this->dispatch('error', 'Invoice not found');

            return;
        }

        $this->selectedInvoiceId = $invoiceId;
        $this->dispatch('show-invoice-details', invoiceId: $invoiceId);
    }

    public function render()
    {
        return view('livewire.admin.invoice-approval', [
            'invoices' => $this->invoices,
            'statistics' => $this->statistics,
        ]);
    }
}
