<?php

namespace App\Livewire\Admin;

use App\Models\ActivityLog;
use App\Models\User;
use App\Traits\HasPermissions;
use Carbon\Carbon;
use Livewire\Attributes\Url;
use Livewire\Component;
use Livewire\WithPagination;

class AuditLogs extends Component
{
    use HasPermissions, WithPagination;

    #[Url]
    public string $search = '';

    #[Url]
    public string $userFilter = 'all';

    #[Url]
    public string $actionFilter = 'all';

    #[Url]
    public string $entityFilter = 'all';

    #[Url]
    public string $dateFrom = '';

    #[Url]
    public string $dateTo = '';

    #[Url]
    public string $sortBy = 'created_at';

    #[Url]
    public string $sortDirection = 'desc';

    public bool $showDetailsModal = false;

    public ?ActivityLog $selectedLog = null;

    public function mount(): void
    {
        if ($this->dateFrom === '' || $this->dateFrom === '0') {
            $this->dateFrom = Carbon::now()->subDays(30)->format('Y-m-d');
        }
        if ($this->dateTo === '' || $this->dateTo === '0') {
            $this->dateTo = Carbon::now()->format('Y-m-d');
        }
    }

    public function updatedSearch(): void
    {
        $this->resetPage();
    }

    public function updatedUserFilter(): void
    {
        $this->resetPage();
    }

    public function updatedActionFilter(): void
    {
        $this->resetPage();
    }

    public function updatedEntityFilter(): void
    {
        $this->resetPage();
    }

    public function updatedDateFrom(): void
    {
        $this->resetPage();
    }

    public function updatedDateTo(): void
    {
        $this->resetPage();
    }

    public function sortBy(string $field): void
    {
        if ($this->sortBy === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortBy = $field;
            $this->sortDirection = 'desc';
        }
        $this->resetPage();
    }

    public function clearFilters(): void
    {
        $this->search = '';
        $this->userFilter = 'all';
        $this->actionFilter = 'all';
        $this->entityFilter = 'all';
        $this->dateFrom = Carbon::now()->subDays(30)->format('Y-m-d');
        $this->dateTo = Carbon::now()->format('Y-m-d');
        $this->sortBy = 'created_at';
        $this->sortDirection = 'desc';
        $this->resetPage();
    }

    public function showDetails(ActivityLog $activityLog): void
    {
        $this->selectedLog = $activityLog;
        $this->showDetailsModal = true;
    }

    public function closeModal(): void
    {
        $this->showDetailsModal = false;
        $this->selectedLog = null;
    }

    public function exportLogs(): void
    {
        if (! $this->canExportAuditLogs()) {
            session()->flash('error', 'You do not have permission to export audit logs.');

            return;
        }

        // Redirect to a dedicated export route with current filters
        $filters = [
            'search' => $this->search,
            'user_id' => $this->userFilter !== 'all' ? $this->userFilter : null,
            'action' => $this->actionFilter !== 'all' ? $this->actionFilter : null,
            'entity_type' => $this->entityFilter !== 'all' ? $this->entityFilter : null,
            'date_from' => $this->dateFrom,
            'date_to' => $this->dateTo,
            'sort_by' => $this->sortBy,
            'sort_direction' => $this->sortDirection,
        ];

        // Store filters in session for the export route
        session(['activity_logs_export_filters' => $filters]);

        // Redirect to export route
        $this->redirect(route('admin.audit.export'));
    }

    // Permission methods using PermissionAware trait
    public function canViewAuditLogs(): bool
    {
        return $this->canViewAllAuditLogs() || $this->canViewOwnAuditLogs();
    }

    public function canViewAllAuditLogs(): bool
    {
        return $this->hasPermission('audit.view_logs');
    }

    public function canViewOwnAuditLogs(): bool
    {
        return $this->hasPermission('audit.view_own_logs');
    }

    public function canExportAuditLogs(): bool
    {
        return $this->hasPermission('audit.export_logs');
    }

    public function canCleanupAuditLogs(): bool
    {
        return $this->hasPermission('audit.cleanup_logs');
    }

    public function canViewSystemAuditLogs(): bool
    {
        return $this->hasPermission('audit.system_logs');
    }

    public function render()
    {
        $builder = ActivityLog::with('user');

        // Apply search filter
        if ($this->search !== '' && $this->search !== '0') {
            $builder->where(function ($q): void {
                $q->where('description', 'like', '%'.$this->search.'%')
                    ->orWhere('action', 'like', '%'.$this->search.'%')
                    ->orWhere('entity_type', 'like', '%'.$this->search.'%');
            });
        }

        // Apply user filter
        if ($this->userFilter !== 'all') {
            $builder->where('user_id', $this->userFilter);
        }

        // Apply action filter
        if ($this->actionFilter !== 'all') {
            $builder->where('action', $this->actionFilter);
        }

        // Apply entity filter
        if ($this->entityFilter !== 'all') {
            $builder->where('entity_type', $this->entityFilter);
        }

        // Apply date filters
        if ($this->dateFrom !== '' && $this->dateFrom !== '0') {
            $builder->whereDate('created_at', '>=', $this->dateFrom);
        }
        if ($this->dateTo !== '' && $this->dateTo !== '0') {
            $builder->whereDate('created_at', '<=', $this->dateTo);
        }

        // Apply sorting
        $builder->orderBy($this->sortBy, $this->sortDirection);

        $lengthAwarePaginator = $builder->paginate(20);

        // Get filter options
        $users = User::orderBy('name')->get();
        $actions = ActivityLog::distinct()->pluck('action')->filter()->sort();
        $entityTypes = ActivityLog::distinct()->pluck('entity_type')->filter()->sort();

        return view('livewire.admin.audit-logs', [
            'logs' => $lengthAwarePaginator,
            'users' => $users,
            'actions' => $actions,
            'entityTypes' => $entityTypes,
        ]);
    }
}
