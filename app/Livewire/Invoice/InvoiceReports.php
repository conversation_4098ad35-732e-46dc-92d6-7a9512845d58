<?php

namespace App\Livewire\Invoice;

use App\Exports\InvoiceReportExport;
use App\Models\Invoice;
use Carbon\Carbon;
use Livewire\Component;
use Maatwebsite\Excel\Facades\Excel;

class InvoiceReports extends Component
{
    public $report_type = 'summary';

    public $date_from;

    public $date_to;

    public $status = 'all';

    public $estate_id;

    public function mount()
    {
        $this->date_from = now()->startOfMonth()->format('Y-m-d');
        $this->date_to = now()->endOfMonth()->format('Y-m-d');
    }

    public function generateReport()
    {
        $this->validate([
            'report_type' => 'required|in:summary,detailed,overdue,payments',
            'date_from' => 'required|date',
            'date_to' => 'required|date|after_or_equal:date_from',
            'status' => 'required|in:all,draft,sent,paid,overdue,cancelled',
            'estate_id' => 'nullable|exists:estates,id',
        ]);

        // Build the query based on filters
        $builder = Invoice::with(['house.estate', 'waterRate']);

        // Apply date filter
        $startDate = Carbon::parse($this->date_from);
        $endDate = Carbon::parse($this->date_to);
        $builder->whereBetween('created_at', [$startDate, $endDate]);

        // Apply status filter
        if ($this->status !== 'all') {
            if ($this->status === 'overdue') {
                $builder->where('status', 'sent')
                    ->where('due_date', '<', now());
            } else {
                $builder->where('status', $this->status);
            }
        }

        // Apply estate filter
        if ($this->estate_id) {
            $builder->whereHas('house.estate', function ($q): void {
                $q->where('id', $this->estate_id);
            });
        }

        // Apply report type specific filters
        switch ($this->report_type) {
            case 'overdue':
                $builder->where('status', 'sent')
                    ->where('due_date', '<', now());
                break;
            case 'payments':
                $builder->where('status', 'paid')
                    ->whereNotNull('paid_at');
                break;
        }

        $invoices = $builder->orderBy('created_at', 'desc')->get();

        // Generate and download Excel report
        return Excel::download(
            new InvoiceReportExport($invoices),
            'invoice-report-'.$this->report_type.'-'.now()->format('Y-m-d').'.xlsx'
        );
    }

    public function render()
    {
        return view('livewire.invoice.invoice-reports')
            ->layout('components.layouts.app');
    }
}
