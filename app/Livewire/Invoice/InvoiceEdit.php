<?php

namespace App\Livewire\Invoice;

use App\Models\Invoice;
use App\Models\WaterRate;
use App\Services\InvoiceGenerationService;
use Livewire\Component;

class InvoiceEdit extends Component
{
    public Invoice $invoice;

    public $current_reading;

    public $previous_reading;

    public $consumption;

    public $amount;

    public $notes;

    public $period_start;

    public $period_end;

    public $waterRates = [];

    protected function rules()
    {
        return [
            'current_reading' => 'required|numeric|min:0',
            'previous_reading' => 'required|numeric|min:0',
            'consumption' => 'required|numeric|min:0',
            'amount' => 'required|numeric|min:0',
            'notes' => 'nullable|string|max:1000',
            'period_start' => 'required|date',
            'period_end' => 'required|date|after:period_start',
        ];
    }

    public function mount(Invoice $invoice)
    {
        $this->invoice = $invoice;
        $this->current_reading = $invoice->current_reading;
        $this->previous_reading = $invoice->previous_reading;
        $this->consumption = $invoice->consumption;
        $this->amount = $invoice->amount;
        $this->notes = $invoice->notes;
        $this->period_start = $invoice->period_start->format('Y-m-d');
        $this->period_end = $invoice->period_end->format('Y-m-d');
        $this->waterRates = WaterRate::active()->get();
    }

    public function updated($propertyName)
    {
        if (in_array($propertyName, ['current_reading', 'previous_reading'])) {
            $this->calculateConsumption();
        }
    }

    public function calculateConsumption()
    {
        $this->consumption = max(0, $this->current_reading - $this->previous_reading);
        $this->calculateAmount();
    }

    public function calculateAmount()
    {
        if ($this->consumption > 0) {
            $invoiceGenerationService = new InvoiceGenerationService;
            $this->amount = $invoiceGenerationService->calculateAmount($this->consumption);
        }
    }

    public function update()
    {
        $this->validate();

        $this->invoice->update([
            'current_reading' => $this->current_reading,
            'previous_reading' => $this->previous_reading,
            'consumption' => $this->consumption,
            'amount' => $this->amount,
            'notes' => $this->notes,
            'period_start' => $this->period_start,
            'period_end' => $this->period_end,
        ]);

        session()->flash('message', 'Invoice updated successfully.');

        return redirect()->route('invoices.show', $this->invoice);
    }

    public function render()
    {
        return view('livewire.invoice.invoice-edit')
            ->layout('components.layouts.app');
    }
}
