<?php

namespace App\Livewire\Invoice;

use App\Models\Estate;
use App\Models\House;
use App\Models\Invoice;
use App\Traits\HasPermissions;
use Livewire\Component;
use Livewire\WithPagination;

class InvoiceList extends Component
{
    use HasPermissions, WithPagination;

    public string $status = '';

    public string $estate_id = '';

    public string $house_id = '';

    public string $date_from = '';

    public string $date_to = '';

    public string $search = '';

    public array $filters = [
        'status' => '',
        'estate_id' => '',
        'house_id' => '',
        'date_from' => '',
        'date_to' => '',
    ];

    protected $queryString = ['filters'];

    public function mount()
    {
        // Sync individual properties from filters array
        $this->status = $this->filters['status'] ?? '';
        $this->estate_id = $this->filters['estate_id'] ?? '';
        $this->house_id = $this->filters['house_id'] ?? '';
        $this->date_from = $this->filters['date_from'] ?? '';
        $this->date_to = $this->filters['date_to'] ?? '';
        $this->search = $this->filters['search'] ?? '';
    }

    public function render()
    {
        $lengthAwarePaginator = Invoice::with(['house.estate', 'meterReading'])
            ->when($this->status, fn ($query) => $query->where('status', $this->status))
            ->when($this->estate_id, fn ($query) => $query->whereHas('house', function ($q): void {
                $q->where('estate_id', $this->estate_id);
            }))
            ->when($this->house_id, fn ($query) => $query->where('house_id', $this->house_id))
            ->when($this->date_from, fn ($query) => $query->where('billing_period_start', '>=', $this->date_from))
            ->when($this->date_to, fn ($query) => $query->where('billing_period_end', '<=', $this->date_to))
            ->when($this->search, fn ($query) => $query->where(function ($q): void {
                $q->where('invoice_number', 'like', '%'.$this->search.'%')
                    ->orWhereHas('house', function ($q): void {
                        $q->where('house_number', 'like', '%'.$this->search.'%');
                    });
            }))
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        $estates = Estate::all();
        $houses = collect();

        if ($this->estate_id !== '' && $this->estate_id !== '0') {
            $houses = House::where('estate_id', $this->estate_id)->get();
        }

        return view('livewire.invoice.invoice-list', [
            'invoices' => $lengthAwarePaginator,
            'estates' => $estates,
            'houses' => $houses,
        ]);
    }

    public function updatedEstateId($value)
    {
        $this->house_id = '';
        $this->syncFilters();
    }

    public function updatedStatus($value)
    {
        $this->syncFilters();
    }

    public function updatedHouseId($value)
    {
        $this->syncFilters();
    }

    public function updatedDateFrom($value)
    {
        $this->syncFilters();
    }

    public function updatedDateTo($value)
    {
        $this->syncFilters();
    }

    public function updatedSearch($value)
    {
        $this->syncFilters();
    }

    private function syncFilters()
    {
        $this->filters = [
            'status' => $this->status,
            'estate_id' => $this->estate_id,
            'house_id' => $this->house_id,
            'date_from' => $this->date_from,
            'date_to' => $this->date_to,
            'search' => $this->search,
        ];
    }

    public function resetFilters()
    {
        $this->reset(['status', 'estate_id', 'house_id', 'date_from', 'date_to', 'search']);
        $this->syncFilters();
    }

    public function delete($invoiceId)
    {
        $invoice = Invoice::findOrFail($invoiceId);

        // Check if user has permission to delete this invoice using permission-based access
        if (! $this->hasAnyPermission(['invoices.delete_all', 'invoices.delete_assigned'])) {
            abort(403, 'You do not have permission to delete invoices.');
        }

        // If user only has delete_assigned permission, check if invoice is in assigned estate
        if (! $this->hasPermission('invoices.delete_all') && ! $this->isInvoiceInAssignedEstate($invoice)) {
            abort(403, 'You do not have permission to delete this invoice.');
        }

        // Check if invoice has payments
        if ($invoice->payments()->count() > 0) {
            session()->flash('error', 'Cannot delete invoice that has payments associated with it.');

            return;
        }

        // Check if invoice has been sent
        if ($invoice->sent_at) {
            session()->flash('error', 'Cannot delete invoice that has already been sent.');

            return;
        }

        $invoice->delete();
        session()->flash('message', 'Invoice deleted successfully!');
    }

    /**
     * Check if invoice belongs to user's assigned estates
     */
    private function isInvoiceInAssignedEstate($invoice)
    {
        if ($this->hasPermission('estates.view_all')) {
            return true;
        }

        if ($this->hasPermission('estates.view_assigned')) {
            return $invoice->house->estate_id === $this->currentUser()->estate_id;
        }

        return false;
    }
}
