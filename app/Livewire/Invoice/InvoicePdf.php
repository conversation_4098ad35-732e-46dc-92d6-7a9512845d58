<?php

namespace App\Livewire\Invoice;

use App\Models\Invoice;
use Barryvdh\DomPDF\Facade\Pdf;
use Livewire\Component;

class InvoicePdf extends Component
{
    public Invoice $invoice;

    public function mount(Invoice $invoice)
    {
        $this->invoice = $invoice;
    }

    public function download()
    {
        $pdf = PDF::loadView('pdf.invoice', [
            'invoice' => $this->invoice,
            'house' => $this->invoice->house,
            'estate' => $this->invoice->house->estate,
        ]);

        return response()->streamDownload(function () use ($pdf): void {
            echo $pdf->output();
        }, 'invoice-'.$this->invoice->invoice_number.'.pdf');
    }

    public function render()
    {
        return view('livewire.invoice.invoice-pdf', [
            'invoice' => $this->invoice,
        ]);
    }
}
