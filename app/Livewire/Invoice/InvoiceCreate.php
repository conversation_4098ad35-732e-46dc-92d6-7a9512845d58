<?php

namespace App\Livewire\Invoice;

use App\Models\House;
use App\Models\Invoice;
use App\Models\WaterRate;
use App\Services\InvoiceGenerationService;
use Livewire\Component;
use Livewire\WithFileUploads;

class InvoiceCreate extends Component
{
    use WithFileUploads;

    public $house_id;

    public $period_start;

    public $period_end;

    public $current_reading;

    public $previous_reading;

    public $consumption;

    public $amount;

    public $notes;

    public $selected_water_rate_id; // Add this for the dropdown

    public $houses = [];

    public $waterRates = [];

    protected function rules()
    {
        return [
            'house_id' => 'required|exists:houses,id',
            'period_start' => 'required|date',
            'period_end' => 'required|date|after:period_start',
            'current_reading' => 'required|numeric|min:0',
            'previous_reading' => 'required|numeric|min:0',
            'consumption' => 'required|numeric|min:0',
            'amount' => 'required|numeric|min:0',
            'notes' => 'nullable|string|max:1000',
        ];
    }

    public function mount()
    {
        $this->houses = House::with('estate')->get();
        $this->waterRates = WaterRate::active()->get();
        $this->period_start = now()->startOfMonth()->format('Y-m-d');
        $this->period_end = now()->endOfMonth()->format('Y-m-d');
    }

    public function updated($propertyName)
    {
        if (in_array($propertyName, ['current_reading', 'previous_reading'])) {
            $this->calculateConsumption();
        }
    }

    public function calculateConsumption()
    {
        if ($this->current_reading && $this->previous_reading) {
            $this->consumption = max(0, $this->current_reading - $this->previous_reading);
            $this->calculateAmount();
        }
    }

    public function calculateAmount()
    {
        if ($this->consumption > 0 && $this->selected_water_rate_id) {
            $invoiceGenerationService = new InvoiceGenerationService;
            // We need to pass the WaterRate object or its ID to calculateAmount
            // For simplicity, let's assume calculateAmount can take an ID or we refactor it
            // For now, let's get the rate and fixed charge directly
            $waterRate = WaterRate::find($this->selected_water_rate_id);
            if ($waterRate) {
                // This is a simplified calculation, adjust if InvoiceGenerationService has more logic
                $this->amount = $this->consumption * $waterRate->rate_per_unit;
            }
        }
    }

    public function save()
    {
        $this->validate();

        if (! $this->selected_water_rate_id) {
            $this->addError('selected_water_rate_id', 'Please select a water rate.');

            return null;
        }

        $waterRate = WaterRate::find($this->selected_water_rate_id);
        if (! $waterRate) {
            $this->addError('selected_water_rate_id', 'Selected water rate not found.');

            return null;
        }

        $totalAmount = $this->amount + $waterRate->fixed_charge;

        $invoice = Invoice::create([
            'house_id' => $this->house_id,
            'water_rate_id' => $this->selected_water_rate_id,
            'billing_period_start' => $this->period_start,
            'billing_period_end' => $this->period_end,
            'previous_reading' => $this->previous_reading,
            'current_reading' => $this->current_reading,
            'consumption' => $this->consumption,
            'rate_per_unit' => $waterRate->rate_per_unit,
            'fixed_charge' => $waterRate->fixed_charge,
            'amount' => $this->amount,
            'total_amount' => $totalAmount,
            'due_date' => now()->addDays(14), // Standard 14-day due date
            'notes' => $this->notes,
            'status' => 'draft', // Initial status should be draft
            'invoice_number' => 'INV-'.date('Ym').'-'.str_pad(Invoice::count() + 1, 4, '0', STR_PAD_LEFT), // Basic invoice number
        ]);

        session()->flash('message', 'Invoice created successfully.');

        return redirect()->route('invoices.show', $invoice);
    }

    public function render()
    {
        return view('livewire.invoice.invoice-create')
            ->layout('components.layouts.app');
    }
}
