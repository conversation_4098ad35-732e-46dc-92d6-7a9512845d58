<?php

namespace App\Livewire;

use App\Models\Estate;
use App\Traits\HasPermissions;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\WithPagination;

class UnifiedEstateManager extends Component
{
    use HasPermissions, WithPagination;

    // Mode management
    public $mode = 'list'; // list, create, edit

    public $estateId;

    // List view properties
    public $search = '';

    public $statusFilter = '';

    public $sortBy = 'created_at';

    public $sortDirection = 'desc';

    public array $filters = [];

    // Form properties
    public $name;

    public $code;

    public $address;

    public $city;

    public $state;

    public $postal_code;

    public $country = 'Kenya';

    public $contact_email;

    public $contact_phone;

    public $is_active = true;

    protected $queryString = ['filters'];

    protected function rules()
    {
        return [
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:50|unique:estates,code,'.$this->estateId,
            'address' => 'nullable|string|max:500',
            'city' => 'required|string|max:100',
            'state' => 'nullable|string|max:100',
            'postal_code' => 'nullable|string|max:20',
            'country' => 'nullable|string|max:100',
            'contact_email' => 'nullable|email|max:255',
            'contact_phone' => 'nullable|string|max:50',
            'is_active' => 'boolean',
        ];
    }

    protected $messages = [
        'name.required' => 'Estate name is required.',
        'code.required' => 'Estate code is required.',
        'code.unique' => 'This estate code is already in use.',
        'city.required' => 'City is required.',
        'contact_email.email' => 'Please enter a valid email address.',
    ];

    public function mount()
    {
        $this->authorizeAccess();
        $this->syncFiltersFromProperties();
    }

    private function authorizeAccess()
    {
        if (! $this->hasAnyPermission(['estates.view_all', 'estates.view_assigned'])) {
            abort(403, 'You do not have permission to view estates.');
        }
    }

    private function syncFiltersFromProperties()
    {
        $this->filters = [
            'search' => $this->search,
            'statusFilter' => $this->statusFilter,
            'sortBy' => $this->sortBy,
            'sortDirection' => $this->sortDirection,
        ];
    }

    // Mode management
    public function setMode($mode, $estateId = null)
    {
        // Check permissions for mode changes
        if ($mode === 'create' && ! $this->hasPermission('estates.create')) {
            abort(403, 'You do not have permission to create estates.');
        }

        if (($mode === 'edit' || $mode === 'delete') && ! $this->hasAnyPermission(['estates.edit_all', 'estates.edit_assigned'])) {
            abort(403, 'You do not have permission to edit estates.');
        }

        $this->mode = $mode;
        $this->estateId = $estateId;

        if ($mode === 'create') {
            $this->resetForm();
        } elseif ($mode === 'edit' && $estateId) {
            $this->loadEstate($estateId);
        }
    }

    public function resetToList()
    {
        $this->mode = 'list';
        $this->resetForm();
    }

    private function resetForm()
    {
        $this->reset([
            'name', 'code', 'address', 'city', 'state', 'postal_code',
            'contact_email', 'contact_phone', 'is_active',
        ]);
        $this->country = 'Kenya';
        $this->is_active = true;
    }

    private function loadEstate($estateId)
    {
        $estate = Estate::findOrFail($estateId);
        $this->name = $estate->name;
        $this->code = $estate->code;
        $this->address = $estate->address;
        $this->city = $estate->city;
        $this->state = $estate->state;
        $this->postal_code = $estate->postal_code;
        $this->country = $estate->country ?? 'Kenya';
        $this->contact_email = $estate->contact_email;
        $this->contact_phone = $estate->contact_phone;
        $this->is_active = $estate->is_active;
    }

    // Form actions
    public function save()
    {
        $this->validate();

        // Check permissions
        if ($this->mode === 'create' && ! $this->hasPermission('estates.create')) {
            abort(403, 'You do not have permission to create estates.');
        }

        if ($this->mode === 'edit' && ! $this->hasAnyPermission(['estates.edit_all', 'estates.edit_assigned'])) {
            abort(403, 'You do not have permission to edit estates.');
        }

        $estateData = [
            'name' => $this->name,
            'code' => $this->code,
            'address' => $this->address,
            'city' => $this->city,
            'state' => $this->state,
            'postal_code' => $this->postal_code,
            'country' => $this->country,
            'contact_email' => $this->contact_email,
            'contact_phone' => $this->contact_phone,
            'is_active' => $this->is_active,
        ];

        if ($this->mode === 'create') {
            Estate::create($estateData);
            $message = 'Estate created successfully.';
        } else {
            $estate = Estate::findOrFail($this->estateId);
            $estate->update($estateData);
            $message = 'Estate updated successfully.';
        }

        session()->flash('message', $message);
        $this->resetToList();
    }

    // List view data
    public function getEstatesProperty()
    {
        $query = Estate::withCount(['houses', 'houses as occupied_houses_count' => function ($query): void {
            $query->whereHas('contacts');
        }]);

        // Apply permission-based scoping
        $query = $this->applyEstateScope($query);

        // Apply search filter
        if ($this->search) {
            $query->where(function ($q): void {
                $q->where('name', 'like', '%'.$this->search.'%')
                    ->orWhere('code', 'like', '%'.$this->search.'%');
            });
        }

        // Apply status filter
        if ($this->statusFilter) {
            $query->where('is_active', $this->statusFilter === 'active');
        }

        // Apply sorting
        $query->orderBy($this->sortBy, $this->sortDirection);

        return $query->paginate(20);
    }

    public function getStatisticsProperty()
    {
        $query = Estate::query();
        $query = $this->applyEstateScope($query);

        return [
            'totalEstates' => $query->count(),
            'activeEstates' => (clone $query)->where('is_active', true)->count(),
            'inactiveEstates' => (clone $query)->where('is_active', false)->count(),
        ];
    }

    // Filter methods
    public function updatedSearch($value)
    {
        $this->syncFilters();
    }

    public function updatedStatusFilter($value)
    {
        $this->syncFilters();
    }

    public function updatedSortBy($value)
    {
        $this->syncFilters();
    }

    public function updatedSortDirection($value)
    {
        $this->syncFilters();
    }

    private function syncFilters()
    {
        $this->filters = [
            'search' => $this->search,
            'statusFilter' => $this->statusFilter,
            'sortBy' => $this->sortBy,
            'sortDirection' => $this->sortDirection,
        ];
    }

    public function resetFilters()
    {
        $this->reset(['search', 'statusFilter']);
        $this->sortBy = 'created_at';
        $this->sortDirection = 'desc';
        $this->syncFilters();
    }

    public function toggleSort($field)
    {
        if ($this->sortBy === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortBy = $field;
            $this->sortDirection = 'asc';
        }
        $this->syncFilters();
    }

    // Individual actions
    public function delete($estateId)
    {
        if (! $this->hasPermission('estates.delete')) {
            abort(403, 'You do not have permission to delete estates.');
        }

        $estate = Estate::findOrFail($estateId);

        // Check if estate has houses
        if ($estate->houses()->count() > 0) {
            session()->flash('error', 'Cannot delete estate that has houses associated with it.');

            return;
        }

        // Check if estate has water rates
        if ($estate->waterRates()->count() > 0) {
            session()->flash('error', 'Cannot delete estate that has water rates associated with it.');

            return;
        }

        $estate->delete();
        session()->flash('message', 'Estate deleted successfully!');
    }

    public function toggleActive($estateId)
    {
        if (! $this->hasAnyPermission(['estates.edit_all', 'estates.edit_assigned'])) {
            abort(403, 'You do not have permission to edit estates.');
        }

        $estate = Estate::findOrFail($estateId);
        $estate->is_active = ! $estate->is_active;
        $estate->save();

        $status = $estate->is_active ? 'activated' : 'deactivated';
        session()->flash('message', "Estate {$status} successfully!");
    }

    // Permission-based scoping
    private function applyEstateScope($query)
    {
        $user = Auth::user();

        // Admin and management can see all estates
        if ($this->hasPermission('estates.view_all')) {
            return $query;
        }

        // Assigned users can only see estates they're assigned to
        if ($this->hasPermission('estates.view_assigned')) {
            return $query->whereHas('assignedUsers', function ($q) use ($user): void {
                $q->where('user_id', $user->id);
            });
        }

        // Default: no estates visible
        return $query->whereRaw('1 = 0');
    }

    // Permission-based utility methods
    public function canCreateEstate()
    {
        return $this->hasPermission('estates.create');
    }

    public function canEditEstate($estateId = null)
    {
        if (! $this->hasAnyPermission(['estates.edit_all', 'estates.edit_assigned'])) {
            return false;
        }

        if ($estateId && ! $this->hasPermission('estates.edit_all')) {
            $estate = Estate::find($estateId);

            return $estate && $this->isUserAssignedToEstate($estate);
        }

        return true;
    }

    public function canDeleteEstate($estateId = null)
    {
        if (! $this->hasPermission('estates.delete')) {
            return false;
        }

        if ($estateId && ! $this->hasPermission('estates.delete')) {
            $estate = Estate::find($estateId);

            return $estate && $this->isUserAssignedToEstate($estate);
        }

        return true;
    }

    private function isUserAssignedToEstate($estate)
    {
        $user = Auth::user();

        if ($this->hasPermission('estates.view_assigned')) {
            return $estate->assignedUsers()->where('user_id', $user->id)->exists();
        }

        return false;
    }

    // Permission-based UI elements
    public function getShowCreateButtonProperty()
    {
        return $this->hasPermission('estates.create');
    }

    public function getShowEditActionsProperty()
    {
        return $this->hasAnyPermission(['estates.edit_all', 'estates.edit_assigned', 'estates.delete']);
    }

    public function getCanEditAllProperty()
    {
        return $this->hasPermission('estates.edit_all');
    }

    public function getCanDeleteAllProperty()
    {
        return $this->hasPermission('estates.delete');
    }

    public function getRequiredPermissionsHintProperty()
    {
        $hints = [];

        if (! $this->hasAnyPermission(['estates.view_all', 'estates.view_assigned'])) {
            $hints[] = 'View estates permission';
        }

        if (! $this->hasPermission('estates.create') && $this->hasAnyPermission(['estates.view_all', 'estates.view_assigned'])) {
            $hints[] = 'Create estates permission';
        }

        if (! $this->hasAnyPermission(['estates.edit_all', 'estates.edit_assigned']) && $this->hasAnyPermission(['estates.view_all', 'estates.view_assigned'])) {
            $hints[] = 'Edit estates permission';
        }

        if (! $this->hasPermission('estates.delete') && $this->hasAnyPermission(['estates.view_all', 'estates.view_assigned'])) {
            $hints[] = 'Delete estates permission';
        }

        return $hints;
    }

    // Route methods for view
    public function getEstateShowRoute($estate)
    {
        return route('estates.show', $estate->id);
    }

    public function getHousesRoute($estateId)
    {
        return route('houses.index', ['estate_id' => $estateId]);
    }

    public function render()
    {
        return view('livewire.estate-manager', [
            'estates' => $this->estates,
            'statistics' => $this->statistics,
            'mode' => $this->mode,
            'requiredPermissionsHint' => $this->requiredPermissionsHint,
            'showCreateButton' => $this->showCreateButton,
            'showEditActions' => $this->showEditActions,
            'canEditEstate' => fn ($id) => $this->canEditEstate($id),
            'canDeleteEstate' => fn ($id) => $this->canDeleteEstate($id),
        ]);
    }
}
