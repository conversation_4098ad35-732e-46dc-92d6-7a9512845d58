<?php

namespace App\Livewire;

use App\Models\Estate;
use App\Traits\HasPermissions;
use Livewire\Component;
use Livewire\WithPagination;

class EstateManager extends Component
{
    use HasPermissions, WithPagination;

    public $showCreateModal = false;

    public $showEditModal = false;

    public $showDeleteModal = false;

    public $showModal = false;

    public $estateIdToEdit;

    public $estateIdToDelete;

    public $estateIdToShow;

    public $search = '';

    public $statusFilter = '';

    public $sortBy = 'created_at';

    public $sortDirection = 'desc';

    public $form = [
        'name' => '',
        'code' => '',
        'address' => '',
        'city' => '',
        'state' => '',
        'postal_code' => '',
        'country' => 'Kenya',
        'contact_email' => '',
        'contact_phone' => '',
    ];

    protected function rules()
    {
        $rules = [
            'form.name' => 'required|string|max:255',
            'form.code' => 'required|string|max:50|unique:estates,code',
            'form.address' => 'nullable|string|max:500',
            'form.city' => 'required|string|max:100',
            'form.state' => 'nullable|string|max:100',
            'form.postal_code' => 'nullable|string|max:20',
            'form.country' => 'nullable|string|max:100',
            'form.contact_email' => 'nullable|email|max:255',
            'form.contact_phone' => 'nullable|string|max:50',
        ];

        if ($this->estateIdToEdit) {
            $rules['form.code'] .= ','.$this->estateIdToEdit;
        }

        return $rules;
    }

    public function openCreateModal()
    {
        $this->reset('form', 'estateIdToEdit');
        $this->form['country'] = 'Kenya';
        $this->showCreateModal = true;
    }

    public function openEditModal($estateId)
    {
        $this->reset('form');
        $estate = Estate::findOrFail($estateId);

        $this->form = [
            'name' => $estate->name,
            'code' => $estate->code,
            'address' => $estate->address ?? '',
            'city' => $estate->city ?? '',
            'state' => $estate->state ?? '',
            'postal_code' => $estate->postal_code ?? '',
            'country' => $estate->country ?? 'Kenya',
            'contact_email' => $estate->contact_email ?? '',
            'contact_phone' => $estate->contact_phone ?? '',
        ];

        $this->estateIdToEdit = $estateId;
        $this->showEditModal = true;
    }

    public function confirmDelete($estateId)
    {
        $this->estateIdToDelete = $estateId;
        $this->showDeleteModal = true;
    }

    public function showEstate($estateId)
    {
        $this->estateIdToShow = $estateId;
        $this->showModal = true;
    }

    public function save()
    {
        $this->validate();

        if ($this->estateIdToEdit) {
            $estate = Estate::findOrFail($this->estateIdToEdit);
            $estate->update($this->form);
            $this->dispatch('estate-saved', message: 'Estate updated successfully.');
        } else {
            Estate::create($this->form);
            $this->dispatch('estate-saved', message: 'Estate created successfully.');
        }

        $this->reset(['showCreateModal', 'showEditModal', 'form', 'estateIdToEdit']);
    }

    public function delete()
    {
        if ($this->estateIdToDelete) {
            $estate = Estate::findOrFail($this->estateIdToDelete);

            // Check if estate has houses
            if ($estate->houses()->count() > 0) {
                $this->addError('deletion', 'Cannot delete estate that has houses associated with it.');

                return;
            }

            // Check if estate has water rates
            if ($estate->waterRates()->count() > 0) {
                $this->addError('deletion', 'Cannot delete estate that has water rates associated with it.');

                return;
            }

            $estate->delete();
            $this->dispatch('estate-deleted', message: 'Estate deleted successfully.');
        }

        $this->reset(['showDeleteModal', 'estateIdToDelete']);
    }

    public function mount()
    {
        $this->authorizeAccess();
    }

    private function authorizeAccess()
    {
        // Check if user has any estate management permission
        $estatePermissions = [
            'estates.view_all',
            'estates.view_assigned',
            'estates.view_caretaker',
            'estates.create',
            'estates.edit_all',
            'estates.edit_assigned',
            'estates.delete',
        ];

        if (! auth()->user() || ! auth()->user()->hasAnyPermission($estatePermissions)) {
            abort(403, 'Unauthorized access to estate management');
        }
    }

    public function getEstateShowRoute($estate)
    {
        if (auth()->user()->hasPermissionTo('estates.view_all')) {
            return route('management.estates.show', $estate);
        }
        if (auth()->user()->hasPermissionTo('estates.view_assigned')) {
            return route('management.estates.show', $estate);
        }
        if (auth()->user()->hasPermissionTo('estates.view_caretaker')) {
            return route('caretaker.estates.show', $estate);
        }

        return route('estates.show', $estate);
    }

    public function getHousesRoute($estateId = null)
    {
        $params = $estateId ? ['estate' => $estateId] : [];

        if (auth()->user()->hasPermissionTo('houses.view_all')) {
            return route('management.houses', $params);
        }
        if (auth()->user()->hasPermissionTo('houses.view_reviewer')) {
            return route('reviewer.houses', $params);
        }
        if (auth()->user()->hasPermissionTo('houses.view_assigned') || auth()->user()->hasPermissionTo('houses.view_caretaker')) {
            return $estateId ? route('caretaker.houses', ['estateId' => $estateId]) : route('caretaker.houses');
        }

        return route('houses', $params);
    }

    public function getRequiredPermissionsHintProperty()
    {
        $user = auth()->user();
        $hints = [];

        if (! $user->hasAnyPermission(['estates.view_all', 'estates.view_assigned', 'estates.view_caretaker'])) {
            $hints[] = 'View estates permission required';
        }

        if (! $user->hasPermissionTo('estates.create') && $this->showCreateButton) {
            $hints[] = 'Create estate permission required';
        }

        return $hints;
    }

    public function getShowCreateButtonProperty()
    {
        return auth()->user()->hasPermissionTo('estates.create');
    }

    public function getShowEditActionsProperty()
    {
        return auth()->user()->hasAnyPermission(['estates.edit_all', 'estates.edit_assigned']);
    }

    public function getCanEditEstateProperty()
    {
        return function ($id) {
            $estate = Estate::findOrFail($id);

            return auth()->user()->hasPermissionTo('estates.edit_all') ||
                   (auth()->user()->hasPermissionTo('estates.edit_assigned') && $estate->isUserAssigned());
        };
    }

    public function getCanDeleteEstateProperty()
    {
        return fn ($id) => auth()->user()->hasPermissionTo('estates.delete');
    }

    public function getStatisticsProperty()
    {
        $user = auth()->user();
        $query = Estate::query();

        // Filter based on user permissions
        if (! $user->hasPermissionTo('estates.view_all')) {
            if ($user->hasPermissionTo('estates.view_assigned')) {
                $query->whereHas('userAssignments', function ($q) use ($user): void {
                    $q->where('user_id', $user->id);
                });
            } else {
                return []; // No estates visible
            }
        }

        return [
            'totalEstates' => $query->count(),
            'activeEstates' => $query->where('is_active', true)->count(),
            'inactiveEstates' => $query->where('is_active', false)->count(),
        ];
    }

    public function getModeProperty()
    {
        return 'list';
    }

    public function render()
    {
        $query = Estate::withCount(['houses', 'houses as occupied_houses_count' => function ($query): void {
            $query->whereHas('contacts');
        }]);

        // Apply permission-based filtering
        if (! auth()->user()->hasPermissionTo('estates.view_all')) {
            if (auth()->user()->hasPermissionTo('estates.view_assigned')) {
                $query->whereHas('userAssignments', function ($q): void {
                    $q->where('user_id', auth()->id())->where('is_active', true);
                });
            } elseif (auth()->user()->hasPermissionTo('estates.view_caretaker')) {
                $query->whereHas('userAssignments', function ($q): void {
                    $q->where('user_id', auth()->id())->where('is_active', true);
                });
            } else {
                $query->whereRaw('1=0'); // No estates visible
            }
        }

        // Apply search filter
        if ($this->search) {
            $query->where('name', 'like', '%'.$this->search.'%')
                ->orWhere('code', 'like', '%'.$this->search.'%');
        }

        // Apply status filter
        if ($this->statusFilter) {
            $query->where('is_active', $this->statusFilter === 'active');
        }

        // Apply sorting
        $query->orderBy($this->sortBy, $this->sortDirection);

        $estates = $query->paginate(10);

        return view('livewire.estate-manager', [
            'estates' => $estates,
            'statistics' => $this->statistics,
            'requiredPermissionsHint' => $this->requiredPermissionsHint,
            'showCreateButton' => $this->showCreateButton,
            'showEditActions' => $this->showEditActions,
            'canEditEstate' => $this->canEditEstate,
            'canDeleteEstate' => $this->canDeleteEstate,
            'mode' => $this->mode,
        ]);
    }
}
