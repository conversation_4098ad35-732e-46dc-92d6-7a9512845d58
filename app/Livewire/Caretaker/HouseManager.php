<?php

namespace App\Livewire\Caretaker;

use App\Models\Estate;
use App\Models\House;
use App\Models\MeterReading;
use App\Traits\HasPermissions;
use Livewire\Component;
use Livewire\WithPagination;

class HouseManager extends Component
{
    use HasPermissions, WithPagination;

    public $estateId;

    public $estateName;

    public $showAddReadingForm = false;

    public $selectedHouseForReading;

    public $currentReading = '';

    public $notes = '';

    protected $rules = [
        'currentReading' => 'required|numeric|min:0',
        'notes' => 'nullable|string|max:500',
    ];

    public function mount($estateId = null)
    {
        // Check if user has caretaker permissions using permission-based access
        if (! $this->hasAnyPermission([
            'readings.create_all',
            'readings.create_assigned',
            'readings.view_assigned',
        ])) {
            abort(403, 'Unauthorized access');
        }

        if ($estateId) {
            $estate = $this->currentUser()->assignedEstates()->findOrFail($estateId);
            $this->estateId = $estate->id;
            $this->estateName = $estate->name;
        } else {
            // If no estateId is provided, default to the first assigned estate
            $firstEstate = $this->currentUser()->assignedEstates()->first();
            if ($firstEstate) {
                $this->estateId = $firstEstate->id;
                $this->estateName = $firstEstate->name;
            } else {
                // Handle case where no estates are assigned
                $this->estateId = null;
                $this->estateName = 'No Estates Assigned';
            }
        }
    }

    public function updatedEstateId()
    {
        $estate = $this->currentUser()->assignedEstates()->find($this->estateId);
        $this->estateName = $estate ? $estate->name : 'No Estates Assigned';
        $this->resetPage();
        $this->showAddReadingForm = false; // Close form if estate changes
    }

    public function getEstatesProperty()
    {
        if ($this->hasPermission('estates.view_all')) {
            return Estate::orderBy('name')->get();
        } elseif ($this->hasPermission('estates.view_assigned')) {
            return $this->currentUser()->assignedEstates()->orderBy('name')->get();
        } else {
            return collect();
        }
    }

    public function getHousesProperty()
    {
        if (! $this->estateId) {
            return House::where('id', null)->paginate(10); // Return empty paginator
        }

        // Apply permission-based filtering
        $query = House::where('estate_id', $this->estateId)->with('meterReadings');

        if ($this->hasPermission('estates.view_all')) {
            // User can view all houses, no filtering needed
            return $query->paginate(10);
        } elseif ($this->hasPermission('estates.view_assigned')) {
            // User can only view houses in assigned estates
            if ($this->currentUser()->assignedEstates()->where('estates.id', $this->estateId)->exists()) {
                return $query->paginate(10);
            } else {
                // User is not assigned to this estate, return empty
                return $query->where('id', null)->paginate(10);
            }
        } else {
            // User has no estate viewing permissions, return empty
            return $query->where('id', null)->paginate(10);
        }
    }

    public function getLastReadingForHouse($houseId)
    {
        $query = MeterReading::where('house_id', $houseId)
            ->orderBy('reading_date', 'desc')
            ->orderBy('created_at', 'desc'); // In case same date

        // Apply permission-based filtering
        if ($this->hasPermission('readings.view_all')) {
            return $query->first();
        } elseif ($this->hasPermission('readings.view_assigned')) {
            $query->whereHas('house', function ($q): void {
                $q->whereIn('estate_id', $this->currentUser()->assignedEstates()->pluck('id'));
            });

            return $query->first();
        } else {
            // User can only view own readings
            return $query->where('user_id', $this->currentUser()->id)->first();
        }
    }

    public function addReading($houseId)
    {
        $this->selectedHouseForReading = $houseId;
        $this->currentReading = '';
        $this->notes = '';
        $this->showAddReadingForm = true;
        $this->resetErrorBag();
    }

    public function cancelAddReading()
    {
        $this->showAddReadingForm = false;
        $this->selectedHouseForReading = null;
        $this->resetErrorBag();
    }

    public function saveReading()
    {
        $this->validate();

        $house = House::findOrFail($this->selectedHouseForReading);

        // Check if user has permission to create readings for this house
        if (! $this->canCreateReadingForHouse($house)) {
            abort(403, 'You do not have permission to create readings for this house');
        }

        // Check if a reading for today already exists for this house
        $existingReadingToday = MeterReading::where('house_id', $this->selectedHouseForReading)
            ->whereDate('reading_date', now()->toDateString())
            ->first();

        if ($existingReadingToday) {
            session()->flash('error', 'A reading for this house has already been submitted today.');
            $this->cancelAddReading();

            return;
        }

        MeterReading::create([
            'house_id' => $this->selectedHouseForReading,
            'user_id' => $this->currentUser()->id,
            'current_reading' => $this->currentReading,
            'reading_date' => now()->toDateString(),
            'status' => 'draft', // Save as draft
            'notes' => $this->notes,
            'reading_method' => 'manual', // Assuming manual entry
        ]);

        session()->flash('message', 'Reading submitted as draft successfully!');
        $this->cancelAddReading();
    }

    /**
     * Check if user can create readings for a specific house
     */
    private function canCreateReadingForHouse($house)
    {
        if ($this->hasPermission('readings.create_all')) {
            return true;
        }

        if ($this->hasPermission('readings.create_assigned')) {
            return $house->estate_id === $this->currentUser()->estate_id;
        }

        return false;
    }

    public function submitAllDraftReadings()
    {
        if (! $this->estateId) {
            session()->flash('error', 'Please select an estate first.');

            return;
        }

        $query = MeterReading::where('status', 'draft')
            ->whereHas('house', function ($query): void {
                $query->where('estate_id', $this->estateId);
            });

        // Apply permission-based filtering
        if ($this->hasPermission('readings.submit_all')) {
            // No additional filtering needed
        } elseif ($this->hasPermission('readings.submit_assigned')) {
            $query->whereHas('house', function ($q): void {
                $q->whereIn('estate_id', $this->currentUser()->assignedEstates()->pluck('id'));
            });
        } else {
            // User can only submit own readings
            $query->where('user_id', $this->currentUser()->id);
        }

        $draftReadings = $query->get();

        if ($draftReadings->isEmpty()) {
            session()->flash('message', 'No draft readings found for this estate to submit.');

            return;
        }

        $draftReadings->each->submit(); // Uses the submit() model method

        session()->flash('message', count($draftReadings).' reading(s) submitted for review!');
    }

    public function render()
    {
        return view('livewire.caretaker.house-manager', [
            'houses' => $this->houses,
            'estates' => $this->estates,
        ]);
    }
}
