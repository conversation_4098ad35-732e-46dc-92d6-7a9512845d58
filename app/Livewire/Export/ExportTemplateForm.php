<?php

namespace App\Livewire\Export;

use App\Models\ExportTemplate;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class ExportTemplateForm extends Component
{
    public $templateId;

    public $name = '';

    public $entityType = 'estates';

    public $format = 'xlsx';

    public $columns = [];

    public $filters = [];

    public $isPublic = false;

    public $isScheduled = false;

    public $scheduleFrequency = 'monthly';

    public $showModal = false;

    protected $rules = [
        'name' => 'required|string|max:255',
        'entityType' => 'required|in:estates,houses,contacts,meter_readings,invoices,water_rates',
        'format' => 'required|in:xlsx,csv,pdf',
        'columns' => 'required|array|min:1',
        'isPublic' => 'boolean',
        'isScheduled' => 'boolean',
        'scheduleFrequency' => 'required_if:isScheduled,true|in:daily,weekly,monthly,quarterly',
    ];

    protected $messages = [
        'name.required' => 'Template name is required.',
        'entityType.required' => 'Entity type is required.',
        'format.required' => 'Export format is required.',
        'columns.required' => 'At least one column must be selected.',
        'scheduleFrequency.required_if' => 'Schedule frequency is required when scheduling is enabled.',
    ];

    public function mount($templateId = null)
    {
        if ($templateId) {
            $this->loadTemplate($templateId);
        } else {
            $this->setDefaultColumns();
        }
    }

    private function loadTemplate($templateId)
    {
        $template = ExportTemplate::findOrFail($templateId);

        if ($template->user_id !== Auth::id() && ! $template->is_public) {
            abort(403);
        }

        $this->templateId = $template->id;
        $this->name = $template->name;
        $this->entityType = $template->entity_type;
        $this->format = $template->format;
        $this->columns = $template->columns;
        $this->filters = $template->filters ?? [];
        $this->isPublic = $template->is_public;
        $this->isScheduled = $template->is_scheduled;
        $this->scheduleFrequency = $template->schedule_frequency ?? 'monthly';
    }

    private function setDefaultColumns()
    {
        $this->columns = $this->getAvailableColumnsForEntity($this->entityType);
    }

    public function updatedEntityType($value)
    {
        $this->setDefaultColumns();
    }

    public function save()
    {
        $this->validate();

        $data = [
            'user_id' => Auth::id(),
            'name' => $this->name,
            'entity_type' => $this->entityType,
            'format' => $this->format,
            'columns' => $this->columns,
            'filters' => $this->filters,
            'is_public' => $this->isPublic,
            'is_scheduled' => $this->isScheduled,
            'schedule_frequency' => $this->isScheduled ? $this->scheduleFrequency : null,
        ];

        if ($this->templateId) {
            $template = ExportTemplate::findOrFail($this->templateId);
            if ($template->user_id !== Auth::id()) {
                abort(403);
            }
            $template->update($data);
            $message = 'Export template updated successfully!';
        } else {
            ExportTemplate::create($data);
            $message = 'Export template created successfully!';
        }

        $this->resetForm();
        $this->dispatch('templateSaved');
        $this->showModal = false;

        session()->flash('message', $message);
    }

    public function resetForm()
    {
        $this->reset(['templateId', 'name', 'entityType', 'format', 'columns', 'filters', 'isPublic', 'isScheduled', 'scheduleFrequency']);
        $this->setDefaultColumns();
    }

    public function getAvailableEntitiesProperty()
    {
        return [
            'estates' => 'Estates',
            'houses' => 'Houses',
            'contacts' => 'Contacts',
            'meter_readings' => 'Meter Readings',
            'invoices' => 'Invoices',
            'water_rates' => 'Water Rates',
        ];
    }

    public function getAvailableFormatsProperty()
    {
        return [
            'xlsx' => 'Excel (.xlsx)',
            'csv' => 'CSV (.csv)',
            'pdf' => 'PDF (.pdf)',
        ];
    }

    public function getScheduleFrequenciesProperty()
    {
        return [
            'daily' => 'Daily',
            'weekly' => 'Weekly',
            'monthly' => 'Monthly',
            'quarterly' => 'Quarterly',
        ];
    }

    public function getAvailableColumnsForEntity($entityType)
    {
        return match ($entityType) {
            'estates' => [
                'ID', 'Name', 'Location', 'Status', 'Total Houses', 'Occupied Houses',
                'Occupancy Rate', 'Created At', 'Updated At',
            ],
            'houses' => [
                'ID', 'House Number', 'Estate', 'Address', 'Status', 'Meter Number',
                'Created At', 'Updated At',
            ],
            'contacts' => [
                'ID', 'Name', 'Email', 'Phone', 'Contact Type', 'House Number',
                'Estate', 'Status', 'Created At', 'Updated At',
            ],
            'meter_readings' => [
                'ID', 'House Number', 'Estate', 'Reading Date', 'Current Reading',
                'Previous Reading', 'Consumption', 'Status', 'Submitted By',
                'Reviewed By', 'Created At', 'Updated At',
            ],
            'invoices' => [
                'ID', 'Invoice Number', 'House Number', 'Estate', 'Billing Period',
                'Consumption', 'Rate per Unit', 'Amount', 'Status', 'Due Date',
                'Created At', 'Updated At',
            ],
            'water_rates' => [
                'ID', 'Name', 'Estate', 'Rate per Unit', 'Effective From',
                'Effective To', 'Description', 'Status', 'Created At', 'Updated At',
            ],
            default => [],
        };
    }

    public function render()
    {
        return view('livewire.export.export-template-form');
    }
}
