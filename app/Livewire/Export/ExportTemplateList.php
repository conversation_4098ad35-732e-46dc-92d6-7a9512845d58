<?php

namespace App\Livewire\Export;

use App\Models\ExportTemplate;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\WithPagination;

class ExportTemplateList extends Component
{
    use WithPagination;

    public $search = '';

    public $entityTypeFilter = 'all';

    public $showCreateModal = false;

    public $showEditModal = false;

    public $editingTemplateId;

    protected $listeners = ['templateSaved' => '$refresh'];

    public function deleteTemplate($templateId)
    {
        $template = ExportTemplate::findOrFail($templateId);

        if ($template->user_id !== Auth::id()) {
            abort(403);
        }

        $template->delete();

        session()->flash('message', 'Export template deleted successfully.');
    }

    public function editTemplate($templateId)
    {
        $this->editingTemplateId = $templateId;
        $this->showEditModal = true;
    }

    public function closeEditModal()
    {
        $this->showEditModal = false;
        $this->editingTemplateId = null;
    }

    public function getTemplatesProperty()
    {
        return ExportTemplate::forUser(Auth::id())
            ->when($this->entityTypeFilter !== 'all', function ($query): void {
                $query->where('entity_type', $this->entityTypeFilter);
            })
            ->when($this->search, function ($query): void {
                $query->where(function ($q): void {
                    $q->where('name', 'like', '%'.$this->search.'%')
                        ->orWhere('entity_type', 'like', '%'.$this->search.'%');
                });
            })
            ->withCount('exportJobs')
            ->latest()
            ->paginate(10);
    }

    public function getAvailableEntitiesProperty()
    {
        return [
            'all' => 'All Entities',
            'estates' => 'Estates',
            'houses' => 'Houses',
            'contacts' => 'Contacts',
            'meter_readings' => 'Meter Readings',
            'invoices' => 'Invoices',
            'water_rates' => 'Water Rates',
        ];
    }

    public function getEntityLabel($entityType)
    {
        return $this->availableEntities[$entityType] ?? $entityType;
    }

    public function getFormatLabel($format)
    {
        return match ($format) {
            'xlsx' => 'Excel',
            'csv' => 'CSV',
            'pdf' => 'PDF',
            default => strtoupper((string) $format),
        };
    }

    public function render()
    {
        return view('livewire.export.export-template-list');
    }
}
