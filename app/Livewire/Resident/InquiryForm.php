<?php

namespace App\Livewire\Resident;

use App\Mail\ContactFormSubmitted;
use App\Models\Contact;
use App\Models\House;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use Livewire\Component;

class InquiryForm extends Component
{
    public $selectedHouseId;

    public $userHouses = [];

    public $currentHouse;

    public $inquiryType = '';

    public $subject = '';

    public $message = '';

    public $priority = 'medium';

    public $contactMethod = 'email';

    public $contactPhone = '';

    public $attachment;

    public $isSubmitted = false;

    protected $rules = [
        'selectedHouseId' => 'required|exists:houses,id',
        'inquiryType' => 'required|string',
        'subject' => 'required|string|min:5|max:100',
        'message' => 'required|string|min:20|max:1000',
        'priority' => 'required|in:low,medium,high,urgent',
        'contactMethod' => 'required|in:email,phone,whatsapp',
        'contactPhone' => 'required_if:contactMethod,phone,whatsapp|string|max:20',
        'attachment' => 'nullable|file|max:5120|mimes:pdf,doc,docx,jpg,jpeg,png',
    ];

    protected $messages = [
        'selectedHouseId.required' => 'Please select a house for your inquiry.',
        'inquiryType.required' => 'Please select an inquiry type.',
        'subject.required' => 'Please enter a subject for your inquiry.',
        'subject.min' => 'Subject must be at least 5 characters long.',
        'subject.max' => 'Subject cannot exceed 100 characters.',
        'message.required' => 'Please enter your message.',
        'message.min' => 'Message must be at least 20 characters long.',
        'message.max' => 'Message cannot exceed 1000 characters.',
        'contactPhone.required_if' => 'Phone number is required when contact method is phone or WhatsApp.',
        'attachment.max' => 'Attachment size cannot exceed 5MB.',
        'attachment.mimes' => 'Attachment must be a PDF, DOC, DOCX, JPG, JPEG, or PNG file.',
    ];

    public function mount()
    {
        if (! Auth::check() || ! Auth::user()->isResident()) {
            return redirect()->route('resident.login');
        }

        $this->loadUserHouses();
        $this->selectDefaultHouse();

        return null;
    }

    private function loadUserHouses()
    {
        $user = Auth::user();
        $this->userHouses = $user->contacts()
            ->with('house.estate')
            ->get()
            ->map(fn ($contact) => [
                'id' => $contact->house_id,
                'house_number' => $contact->house->house_number,
                'estate_name' => $contact->house->estate->name,
                'address' => $contact->house->address,
            ])
            ->toArray();
    }

    private function selectDefaultHouse()
    {
        $sessionHouseId = session('resident_selected_house_id');

        if ($sessionHouseId && collect($this->userHouses)->contains('id', $sessionHouseId)) {
            $this->selectedHouseId = $sessionHouseId;
        } elseif (! empty($this->userHouses)) {
            $this->selectedHouseId = $this->userHouses[0]['id'];
        }

        if ($this->selectedHouseId) {
            $this->currentHouse = House::with('estate')->find($this->selectedHouseId);
        }
    }

    public function selectHouse($houseId)
    {
        if (collect($this->userHouses)->contains('id', $houseId)) {
            $this->selectedHouseId = $houseId;
            $this->currentHouse = House::with('estate')->find($houseId);
            session()->put('resident_selected_house_id', $houseId);
        }
    }

    public function updatedSelectedHouseId($houseId)
    {
        $this->selectHouse($houseId);
    }

    public function submitInquiry()
    {
        $this->validate();

        $user = Auth::user();
        $house = House::with('estate')->find($this->selectedHouseId);

        // Create contact record
        $contact = Contact::create([
            'house_id' => $this->selectedHouseId,
            'name' => $user->name,
            'email' => $user->email,
            'phone' => $this->contactPhone ?: $user->phone,
            'contact_type' => 'inquiry',
            'inquiry_type' => $this->inquiryType,
            'subject' => $this->subject,
            'message' => $this->message,
            'priority' => $this->priority,
            'preferred_contact_method' => $this->contactMethod,
            'status' => 'pending',
            'notes' => 'Submitted via resident portal',
        ]);

        // Handle attachment
        if ($this->attachment) {
            $path = $this->attachment->store('inquiry-attachments', 'public');
            $contact->attachment_path = $path;
            $contact->save();
        }

        // Send notification to estate managers
        $this->notifyEstateManagers($contact, $house);

        // Send confirmation to resident
        $this->sendConfirmationToResident($contact, $user);

        $this->isSubmitted = true;
        $this->resetForm();
    }

    private function notifyEstateManagers($contact, $house)
    {
        // Get estate managers and admins
        $managers = User::where(function ($query) use ($house): void {
            $query->where('role', 'admin')
                ->orWhereHas('assignedEstates', function ($query) use ($house): void {
                    $query->where('estate_id', $house->estate_id);
                });
        })->get();

        // Convert contact model to array format expected by email template
        $contactData = [
            'name' => $contact->name,
            'email' => $contact->email,
            'phone' => $contact->phone,
            'company' => $contact->company ?? null,
            'role' => $contact->contact_type ?? 'inquiry',
            'message_type' => $contact->inquiry_type ?? 'general',
            'message' => $contact->message,
        ];

        foreach ($managers as $manager) {
            // Send email notification
            Mail::to($manager->email)->send(new ContactFormSubmitted($contactData));

            // You can also add other notification methods here (like WhatsApp, SMS, etc.)
        }
    }

    private function sendConfirmationToResident($contact, $user)
    {
        // Convert contact model to array format expected by email template
        $contactData = [
            'name' => $contact->name,
            'email' => $contact->email,
            'phone' => $contact->phone,
            'company' => $contact->company ?? null,
            'role' => $contact->contact_type ?? 'inquiry',
            'message_type' => $contact->inquiry_type ?? 'general',
            'message' => $contact->message,
        ];

        // Send confirmation email to resident
        Mail::to($user->email)->send(new ContactFormSubmitted($contactData));
    }

    private function resetForm()
    {
        $this->reset(['inquiryType', 'subject', 'message', 'priority', 'contactMethod', 'contactPhone', 'attachment']);
    }

    public function getInquiryTypesProperty()
    {
        return [
            'billing' => 'Billing & Payment',
            'meter_reading' => 'Meter Reading Issue',
            'service_request' => 'Service Request',
            'maintenance' => 'Maintenance',
            'general' => 'General Inquiry',
            'complaint' => 'Complaint',
            'suggestion' => 'Suggestion',
            'other' => 'Other',
        ];
    }

    public function getPriorityLevelsProperty()
    {
        return [
            'low' => ['label' => 'Low', 'color' => 'text-green-600 bg-green-100'],
            'medium' => ['label' => 'Medium', 'color' => 'text-yellow-600 bg-yellow-100'],
            'high' => ['label' => 'High', 'color' => 'text-orange-600 bg-orange-100'],
            'urgent' => ['label' => 'Urgent', 'color' => 'text-red-600 bg-red-100'],
        ];
    }

    public function getContactMethodsProperty()
    {
        return [
            'email' => 'Email',
            'phone' => 'Phone Call',
            'whatsapp' => 'WhatsApp',
        ];
    }

    public function resetFormAndContinue()
    {
        $this->isSubmitted = false;
    }

    public function render()
    {
        return view('livewire.resident.inquiry-form')->layout('layouts.resident');
    }
}
