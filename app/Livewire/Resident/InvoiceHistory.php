<?php

namespace App\Livewire\Resident;

use App\Models\House;
use App\Models\Invoice;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\WithPagination;

class InvoiceHistory extends Component
{
    use WithPagination;

    public $selectedHouseId;

    public $userHouses = [];

    public $currentHouse;

    public $statusFilter = 'all';

    public $yearFilter = '';

    public $searchTerm = '';

    public $showFilters = false;

    protected $listeners = ['houseSelected' => 'selectHouse'];

    public function mount()
    {
        if (! Auth::check() || ! Auth::user()->isResident()) {
            return redirect()->route('resident.login');
        }

        $this->loadUserHouses();
        $this->selectDefaultHouse();
        $this->yearFilter = date('Y');

        return null;
    }

    private function loadUserHouses()
    {
        $user = Auth::user();
        $this->userHouses = $user->contacts()
            ->with('house.estate')
            ->get()
            ->map(fn ($contact) => [
                'id' => $contact->house_id,
                'house_number' => $contact->house->house_number,
                'estate_name' => $contact->house->estate->name,
                'address' => $contact->house->address,
            ])
            ->toArray();
    }

    private function selectDefaultHouse()
    {
        $sessionHouseId = session('resident_selected_house_id');

        if ($sessionHouseId && collect($this->userHouses)->contains('id', $sessionHouseId)) {
            $this->selectedHouseId = $sessionHouseId;
        } elseif (! empty($this->userHouses)) {
            $this->selectedHouseId = $this->userHouses[0]['id'];
        }

        if ($this->selectedHouseId) {
            $this->currentHouse = House::with('estate')->find($this->selectedHouseId);
        }
    }

    public function selectHouse($houseId)
    {
        if (collect($this->userHouses)->contains('id', $houseId)) {
            $this->selectedHouseId = $houseId;
            $this->currentHouse = House::with('estate')->find($houseId);
            session()->put('resident_selected_house_id', $houseId);
            $this->resetPage();
        }
    }

    public function updatedSelectedHouseId($houseId)
    {
        $this->selectHouse($houseId);
    }

    public function updatedStatusFilter($value)
    {
        $this->resetPage();
    }

    public function updatedYearFilter($value)
    {
        $this->resetPage();
    }

    public function updatedSearchTerm($value)
    {
        $this->resetPage();
    }

    public function getInvoicesProperty()
    {
        $query = Invoice::where('house_id', $this->selectedHouseId)
            ->with(['waterRate']);

        // Apply status filter
        if ($this->statusFilter !== 'all') {
            $query->where('status', $this->statusFilter);
        }

        // Apply year filter
        if ($this->yearFilter) {
            $query->whereYear('billing_period_start', $this->yearFilter);
        }

        // Apply search
        if ($this->searchTerm) {
            $query->where(function ($query): void {
                $query->where('invoice_number', 'like', '%'.$this->searchTerm.'%')
                    ->orWhere('billing_period_start', 'like', '%'.$this->searchTerm.'%');
            });
        }

        return $query->orderBy('billing_period_start', 'desc')
            ->paginate(10);
    }

    public function getInvoiceStatsProperty()
    {
        $query = Invoice::where('house_id', $this->selectedHouseId);

        $totalInvoices = $query->count();
        $paidInvoices = (clone $query)->where('status', 'paid')->count();
        $unpaidInvoices = (clone $query)->where('status', '!=', 'paid')->count();
        $totalAmount = $query->sum('total_amount');
        $paidAmount = (clone $query)->where('status', 'paid')->sum('total_amount');
        $unpaidAmount = $totalAmount - $paidAmount;

        return [
            'total_invoices' => $totalInvoices,
            'paid_invoices' => $paidInvoices,
            'unpaid_invoices' => $unpaidInvoices,
            'total_amount' => $totalAmount,
            'paid_amount' => $paidAmount,
            'unpaid_amount' => $unpaidAmount,
            'payment_rate' => $totalInvoices > 0 ? round(($paidInvoices / $totalInvoices) * 100, 1) : 0,
        ];
    }

    public function getAvailableYearsProperty()
    {
        return Invoice::where('house_id', $this->selectedHouseId)
            ->selectRaw('YEAR(billing_period_start) as year')
            ->distinct()
            ->orderBy('year', 'desc')
            ->pluck('year')
            ->toArray();
    }

    public function downloadInvoice($invoiceId)
    {
        $invoice = Invoice::where('id', $invoiceId)
            ->where('house_id', $this->selectedHouseId)
            ->first();

        if (! $invoice) {
            abort(404);
        }

        return redirect()->route('resident.invoices.pdf', $invoice->id);
    }

    public function viewInvoice($invoiceId)
    {
        $invoice = Invoice::where('id', $invoiceId)
            ->where('house_id', $this->selectedHouseId)
            ->first();

        if (! $invoice) {
            abort(404);
        }

        return redirect()->route('resident.invoices.show', $invoice->id);
    }

    public function toggleShowFilters()
    {
        $this->showFilters = ! $this->showFilters;
    }

    public function resetFilters()
    {
        $this->statusFilter = 'all';
        $this->yearFilter = date('Y');
        $this->searchTerm = '';
        $this->showFilters = false;
        $this->resetPage();
    }

    public function getStatusColor($status)
    {
        return [
            'paid' => 'text-green-600 bg-green-100',
            'unpaid' => 'text-red-600 bg-red-100',
            'overdue' => 'text-orange-600 bg-orange-100',
            'cancelled' => 'text-gray-600 bg-gray-100',
        ][$status] ?? 'text-gray-600 bg-gray-100';
    }

    public function render()
    {
        return view('livewire.resident.invoice-history')->layout('layouts.resident');
    }
}
