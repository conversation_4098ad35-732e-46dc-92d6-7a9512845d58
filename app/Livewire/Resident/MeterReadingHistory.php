<?php

namespace App\Livewire\Resident;

use App\Models\House;
use App\Models\MeterReading;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\WithPagination;

class MeterReadingHistory extends Component
{
    use WithPagination;

    public $selectedHouseId;

    public $userHouses = [];

    public $currentHouse;

    public $yearFilter = '';

    public $statusFilter = 'approved';

    public $searchTerm = '';

    public $showFilters = false;

    public $showChart = true;

    protected $listeners = ['houseSelected' => 'selectHouse'];

    public function mount()
    {
        if (! Auth::check() || ! Auth::user()->isResident()) {
            return redirect()->route('resident.login');
        }

        $this->loadUserHouses();
        $this->selectDefaultHouse();
        $this->yearFilter = date('Y');

        return null;
    }

    private function loadUserHouses()
    {
        $user = Auth::user();
        $this->userHouses = $user->contacts()
            ->with('house.estate')
            ->get()
            ->map(fn ($contact) => [
                'id' => $contact->house_id,
                'house_number' => $contact->house->house_number,
                'estate_name' => $contact->house->estate->name,
                'address' => $contact->house->address,
            ])
            ->toArray();
    }

    private function selectDefaultHouse()
    {
        $sessionHouseId = session('resident_selected_house_id');

        if ($sessionHouseId && collect($this->userHouses)->contains('id', $sessionHouseId)) {
            $this->selectedHouseId = $sessionHouseId;
        } elseif (! empty($this->userHouses)) {
            $this->selectedHouseId = $this->userHouses[0]['id'];
        }

        if ($this->selectedHouseId) {
            $this->currentHouse = House::with('estate')->find($this->selectedHouseId);
        }
    }

    public function selectHouse($houseId)
    {
        if (collect($this->userHouses)->contains('id', $houseId)) {
            $this->selectedHouseId = $houseId;
            $this->currentHouse = House::with('estate')->find($houseId);
            session()->put('resident_selected_house_id', $houseId);
            $this->resetPage();
        }
    }

    public function updatedSelectedHouseId($houseId)
    {
        $this->selectHouse($houseId);
    }

    public function updatedStatusFilter($value)
    {
        $this->resetPage();
    }

    public function updatedYearFilter($value)
    {
        $this->resetPage();
    }

    public function updatedSearchTerm($value)
    {
        $this->resetPage();
    }

    public function getReadingsProperty()
    {
        $query = MeterReading::where('house_id', $this->selectedHouseId)
            ->with(['user', 'reviewer']);

        // Apply status filter
        if ($this->statusFilter !== 'all') {
            $query->where('status', $this->statusFilter);
        }

        // Apply year filter
        if ($this->yearFilter) {
            $query->whereYear('reading_date', $this->yearFilter);
        }

        // Apply search
        if ($this->searchTerm) {
            $query->where(function ($query): void {
                $query->where('current_reading', 'like', '%'.$this->searchTerm.'%')
                    ->orWhere('consumption', 'like', '%'.$this->searchTerm.'%')
                    ->orWhere('reading_date', 'like', '%'.$this->searchTerm.'%');
            });
        }

        return $query->orderBy('reading_date', 'desc')
            ->paginate(15);
    }

    public function getReadingStatsProperty()
    {
        $query = MeterReading::where('house_id', $this->selectedHouseId);

        $totalReadings = $query->count();
        $approvedReadings = (clone $query)->where('status', 'approved')->count();
        $totalConsumption = (clone $query)->where('status', 'approved')->sum('consumption');
        $averageConsumption = $approvedReadings > 0 ? $totalConsumption / $approvedReadings : 0;

        // Get latest reading
        $latestReading = (clone $query)->latest('reading_date')->first();
        $latestConsumption = $latestReading ? $latestReading->consumption : 0;

        return [
            'total_readings' => $totalReadings,
            'approved_readings' => $approvedReadings,
            'total_consumption' => $totalConsumption,
            'average_consumption' => round($averageConsumption, 2),
            'latest_consumption' => $latestConsumption,
            'approval_rate' => $totalReadings > 0 ? round(($approvedReadings / $totalReadings) * 100, 1) : 0,
        ];
    }

    public function getChartDataProperty()
    {
        if (! $this->showChart || ! $this->selectedHouseId) {
            return [];
        }

        $readings = MeterReading::where('house_id', $this->selectedHouseId)
            ->where('status', 'approved')
            ->whereYear('reading_date', $this->yearFilter)
            ->orderBy('reading_date')
            ->get()
            ->groupBy(fn ($reading) => $reading->reading_date->format('Y-m'));

        return $readings->map(fn ($monthReadings, $month) => [
            'month' => $month,
            'consumption' => $monthReadings->sum('consumption'),
            'readings_count' => $monthReadings->count(),
            'average_reading' => $monthReadings->avg('current_reading'),
        ])->values()->toArray();
    }

    public function getAvailableYearsProperty()
    {
        return MeterReading::where('house_id', $this->selectedHouseId)
            ->selectRaw('YEAR(reading_date) as year')
            ->distinct()
            ->orderBy('year', 'desc')
            ->pluck('year')
            ->toArray();
    }

    public function getMonthlyAveragesProperty()
    {
        $readings = MeterReading::where('house_id', $this->selectedHouseId)
            ->where('status', 'approved')
            ->get()
            ->groupBy(fn ($reading) => $reading->reading_date->month);

        $averages = [];
        for ($month = 1; $month <= 12; $month++) {
            $monthReadings = $readings->get($month, collect());
            $averages[$month] = $monthReadings->isNotEmpty()
                ? round($monthReadings->avg('consumption'), 2)
                : null;
        }

        return $averages;
    }

    public function toggleShowFilters()
    {
        $this->showFilters = ! $this->showFilters;
    }

    public function resetFilters()
    {
        $this->statusFilter = 'approved';
        $this->yearFilter = date('Y');
        $this->searchTerm = '';
        $this->showFilters = false;
        $this->resetPage();
    }

    public function toggleChart()
    {
        $this->showChart = ! $this->showChart;
    }

    public function getStatusColor($status)
    {
        return [
            'draft' => 'text-gray-600 bg-gray-100',
            'submitted' => 'text-yellow-600 bg-yellow-100',
            'reviewed' => 'text-blue-600 bg-blue-100',
            'approved' => 'text-green-600 bg-green-100',
            'rejected' => 'text-red-600 bg-red-100',
        ][$status] ?? 'text-gray-600 bg-gray-100';
    }

    public function getRiskLevelColor($riskLevel)
    {
        return [
            'critical' => 'text-red-600 bg-red-100',
            'high' => 'text-orange-600 bg-orange-100',
            'medium' => 'text-yellow-600 bg-yellow-100',
            'low' => 'text-green-600 bg-green-100',
        ][$riskLevel] ?? 'text-gray-600 bg-gray-100';
    }

    public function render()
    {
        return view('livewire.resident.meter-reading-history')->layout('layouts.resident');
    }
}
