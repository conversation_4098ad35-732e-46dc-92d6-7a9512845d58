<?php

namespace App\Livewire\Traits;

use Illuminate\Support\Facades\Validator;

trait WithFormValidation
{
    /**
     * Validate form data with custom rules
     */
    public function validateForm(array $data, ?array $rules = null, ?array $messages = null)
    {
        $rules ??= $this->getValidationRules();
        $messages ??= $this->getValidationMessages();

        return Validator::make($data, $rules, $messages)->validate();
    }

    /**
     * Get validation rules for the form
     */
    protected function getValidationRules()
    {
        return [];
    }

    /**
     * Get validation messages for the form
     */
    protected function getValidationMessages()
    {
        return [];
    }

    /**
     * Validate a single field
     */
    public function validateField($field, $value, $rules = null)
    {
        $rules ??= $this->getValidationRules()[$field] ?? [];

        return Validator::make([$field => $value], [$field => $rules])->validate();
    }

    /**
     * Check if form has errors
     */
    public function hasFormErrors()
    {
        return $this->getErrorBag()->any();
    }

    /**
     * Get form errors for a specific field
     */
    public function getFieldErrors($field)
    {
        return $this->getErrorBag()->get($field);
    }

    /**
     * Clear form errors
     */
    public function clearFormErrors()
    {
        $this->resetErrorBag();
    }
}
