<?php

namespace App\Livewire\Traits;

use Livewire\WithPagination;

trait SearchablePaginationTrait
{
    use WithPagination;

    public $search = '';

    public $filters = [];

    public $sortBy = 'created_at';

    public $sortDirection = 'desc';

    public $perPage = 20;

    protected function applySearchFilters($query, $searchFields = ['name'])
    {
        return $query->when($this->search, function ($q) use ($searchFields): void {
            $q->where(function ($subQuery) use ($searchFields): void {
                foreach ($searchFields as $searchField) {
                    $subQuery->orWhere($searchField, 'like', '%'.$this->search.'%');
                }
            });
        });
    }

    protected function applyDateRangeFilters($query, $startDateField = 'date_from', $endDateField = 'date_to', $dateColumn = 'created_at')
    {
        return $query->when($this->filters[$startDateField] ?? null, function ($q) use ($startDateField, $dateColumn): void {
            $q->where($dateColumn, '>=', $this->filters[$startDateField]);
        })
            ->when($this->filters[$endDateField] ?? null, function ($q) use ($endDateField, $dateColumn): void {
                $q->where($dateColumn, '<=', $this->filters[$endDateField]);
            });
    }

    protected function applyStatusFilter($query, $statusField = 'status', $column = 'status')
    {
        return $query->when($this->filters[$statusField] ?? null, function ($q) use ($statusField, $column): void {
            if ($this->filters[$statusField] !== 'all') {
                $q->where($column, $this->filters[$statusField]);
            }
        });
    }

    protected function applyEstateFilter($query, $estateField = 'estate_id')
    {
        return $query->when($this->filters[$estateField] ?? null, function ($q) use ($estateField): void {
            $q->where('estate_id', $this->filters[$estateField]);
        });
    }

    protected function applySorting($query, $defaultSortBy = 'created_at', $defaultSortDirection = 'desc')
    {
        $sortBy = $this->sortBy ?: $defaultSortBy;
        $sortDirection = $this->sortDirection ?: $defaultSortDirection;

        return $query->orderBy($sortBy, $sortDirection);
    }

    protected function getBaseQuery($modelClass)
    {
        return $modelClass::query();
    }

    public function resetFilters()
    {
        $this->reset(['search', 'filters']);
        $this->resetPage();
    }

    public function updatedSearch()
    {
        $this->resetPage();
    }

    public function updatedFilters()
    {
        $this->resetPage();
    }

    public function getPaginationProperties()
    {
        return [
            'search' => $this->search,
            'filters' => $this->filters,
            'sortBy' => $this->sortBy,
            'sortDirection' => $this->sortDirection,
            'perPage' => $this->perPage,
        ];
    }
}
