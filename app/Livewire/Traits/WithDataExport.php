<?php

namespace App\Livewire\Traits;

use Maatwebsite\Excel\Facades\Excel;

trait WithDataExport
{
    // Export properties
    public $showExportModal = false;

    public $exportFormat = 'xlsx';

    public $exportFields = [];

    public $exportSelectedFields = [];

    public $exportFilename = '';

    /**
     * Initialize export functionality
     */
    public function initializeExport()
    {
        $this->exportSelectedFields = $this->getDefaultExportFields();
        $this->exportFilename = $this->getDefaultExportFilename();
    }

    /**
     * Get default export fields
     */
    protected function getDefaultExportFields()
    {
        return array_keys($this->exportFields);
    }

    /**
     * Get default export filename
     */
    protected function getDefaultExportFilename()
    {
        return class_basename(static::class).'-'.now()->format('Y-m-d');
    }

    /**
     * Get export data
     */
    protected function getExportData()
    {
        return [];
    }

    /**
     * Get export class
     */
    protected function getExportClass()
    {
        return null;
    }

    /**
     * Export data to specified format
     */
    public function exportData()
    {
        $this->validateExport();

        $data = $this->getExportData();
        $filename = $this->getExportFilename();

        return match ($this->exportFormat) {
            'csv' => $this->exportToCsv($data, $filename),
            'pdf' => $this->exportToPdf($data, $filename),
            'json' => $this->exportToJson($data, $filename),
            default => $this->exportToExcel($data, $filename),
        };
    }

    /**
     * Export to Excel
     */
    protected function exportToExcel($data, $filename)
    {
        $exportClass = $this->getExportClass();

        if ($exportClass) {
            return Excel::download(new $exportClass($data), $filename.'.xlsx');
        }

        // Fallback to simple array export
        return Excel::download(
            new \App\Exports\ArrayExport($data, $this->exportSelectedFields),
            $filename.'.xlsx'
        );
    }

    /**
     * Export to CSV
     */
    protected function exportToCsv($data, $filename)
    {
        $csv = '';

        // Add headers
        $headers = array_intersect_key($this->exportFields, array_flip($this->exportSelectedFields));
        $csv .= implode(',', array_values($headers))."\n";

        // Add data rows
        foreach ($data as $row) {
            $values = [];
            foreach ($this->exportSelectedFields as $exportSelectedField) {
                $values[] = $this->formatExportValue($row[$exportSelectedField] ?? '');
            }
            $csv .= implode(',', $values)."\n";
        }

        return response($csv)
            ->header('Content-Type', 'text/csv')
            ->header('Content-Disposition', 'attachment; filename="'.$filename.'.csv"');
    }

    /**
     * Export to PDF
     */
    protected function exportToPdf($data, $filename)
    {
        // This would typically use a PDF library like DomPDF or TCPDF
        // For now, we'll create a simple HTML-based PDF

        $html = $this->generatePdfHtml($data);

        // You would integrate with your preferred PDF library here
        // For example:
        // $pdf = \Barryvdh\DomPDF\Facade\Pdf::loadHTML($html);
        // return $pdf->download($filename . '.pdf');

        return response($html)
            ->header('Content-Type', 'text/html')
            ->header('Content-Disposition', 'attachment; filename="'.$filename.'.html"');
    }

    /**
     * Export to JSON
     */
    protected function exportToJson($data, $filename)
    {
        $filteredData = [];

        foreach ($data as $row) {
            $filteredRow = [];
            foreach ($this->exportSelectedFields as $exportSelectedField) {
                $filteredRow[$exportSelectedField] = $row[$exportSelectedField] ?? null;
            }
            $filteredData[] = $filteredRow;
        }

        return response()->json($filteredData)
            ->header('Content-Disposition', 'attachment; filename="'.$filename.'.json"');
    }

    /**
     * Generate PDF HTML
     */
    protected function generatePdfHtml($data)
    {
        $headers = array_intersect_key($this->exportFields, array_flip($this->exportSelectedFields));

        $html = '<!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>'.$this->exportFilename.'</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                table { width: 100%; border-collapse: collapse; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f2f2f2; }
                tr:nth-child(even) { background-color: #f9f9f9; }
            </style>
        </head>
        <body>
            <h1>'.$this->exportFilename.'</h1>
            <table>
                <thead>
                    <tr>';

        foreach ($headers as $header) {
            $html .= '<th>'.e($header).'</th>';
        }

        $html .= '</tr>
                </thead>
                <tbody>';

        foreach ($data as $row) {
            $html .= '<tr>';
            foreach ($this->exportSelectedFields as $exportSelectedField) {
                $html .= '<td>'.e($row[$exportSelectedField] ?? '').'</td>';
            }
            $html .= '</tr>';
        }

        return $html.'</tbody>
            </table>
        </body>
        </html>';
    }

    /**
     * Format export value
     */
    protected function formatExportValue($value)
    {
        if (is_null($value)) {
            return '';
        }

        if (is_bool($value)) {
            return $value ? 'Yes' : 'No';
        }

        if (is_array($value) || is_object($value)) {
            return json_encode($value);
        }

        // Escape commas and quotes for CSV
        return str_replace(['"', ','], ['""', '""'], (string) $value);
    }

    /**
     * Validate export settings
     */
    protected function validateExport()
    {
        $this->validate([
            'exportFormat' => 'required|in:xlsx,csv,pdf,json',
            'exportSelectedFields' => 'required|array|min:1',
            'exportFilename' => 'required|string|min:1',
        ]);
    }

    /**
     * Open export modal
     */
    public function openExportModal()
    {
        $this->initializeExport();
        $this->showExportModal = true;
    }

    /**
     * Close export modal
     */
    public function closeExportModal()
    {
        $this->showExportModal = false;
        $this->reset(['exportFormat', 'exportSelectedFields', 'exportFilename']);
    }

    /**
     * Get available export formats
     */
    public function getAvailableExportFormats()
    {
        return [
            'xlsx' => 'Excel (.xlsx)',
            'csv' => 'CSV (.csv)',
            'pdf' => 'PDF (.pdf)',
            'json' => 'JSON (.json)',
        ];
    }
}
