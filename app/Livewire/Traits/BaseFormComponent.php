<?php

namespace App\Livewire\Traits;

trait BaseFormComponent
{
    public $mode = 'create';

    public $showModal = false;

    public $editingId;

    abstract protected function getModelClass();

    abstract protected function getValidationRules();

    abstract protected function getRedirectRoute();

    abstract protected function loadEntityData($entity);

    abstract protected function prepareSaveData();

    public function openModal($entityId = null)
    {
        $this->resetForm();

        if ($entityId) {
            $this->mode = 'edit';
            $this->editingId = $entityId;
            $this->loadEntity($entityId);
        } else {
            $this->mode = 'create';
            $this->editingId = null;
        }

        $this->showModal = true;
    }

    public function save()
    {
        $this->validate();

        $data = $this->prepareSaveData();

        if ($this->editingId) {
            $entity = $this->getModelClass()::findOrFail($this->editingId);
            $entity->update($data);
            $message = $this->getSuccessMessage('updated');
        } else {
            $entity = $this->getModelClass()::create($data);
            $message = $this->getSuccessMessage('created');
        }

        $this->closeModal();
        session()->flash('message', $message);

        return redirect()->to($this->getRedirectRoute());
    }

    public function closeModal()
    {
        $this->showModal = false;
        $this->resetForm();
    }

    protected function loadEntity($entityId)
    {
        $entity = $this->getModelClass()::findOrFail($entityId);
        $this->loadEntityData($entity);
    }

    protected function resetForm()
    {
        $this->reset(['editingId', 'mode']);
        $this->resetValidation();
    }

    protected function getSuccessMessage($action)
    {
        $modelName = class_basename($this->getModelClass());

        return ucfirst($modelName)." {$action} successfully!";
    }
}
