<?php

namespace App\Livewire\Traits;

use Livewire\WithPagination;

trait WithSearchAndFilter
{
    use WithPagination;

    // Search properties
    public $search = '';

    public $searchFields = [];

    // Filter properties
    public $filters = [];

    public $activeFilters = [];

    // Sort properties
    public $sortField = 'created_at';

    public $sortDirection = 'desc';

    // Pagination properties
    public $perPage = 20;

    public $page = 1;

    /**
     * Initialize search and filter functionality
     */
    public function initializeSearchAndFilter()
    {
        $this->loadActiveFilters();
    }

    /**
     * Apply search to query
     */
    public function applySearch($query, $fields = null)
    {
        $searchFields = $fields ?? $this->searchFields;

        if (! $this->search || empty($searchFields)) {
            return $query;
        }

        return $query->where(function ($q) use ($searchFields): void {
            foreach ($searchFields as $searchField) {
                if (str_contains($searchField, '.')) {
                    // Handle relationship fields
                    $parts = explode('.', $searchField);
                    $relation = $parts[0];
                    $column = $parts[1];

                    $q->orWhereHas($relation, function ($query) use ($column): void {
                        $query->where($column, 'like', '%'.$this->search.'%');
                    });
                } else {
                    $q->orWhere($searchField, 'like', '%'.$this->search.'%');
                }
            }
        });
    }

    /**
     * Apply filters to query
     */
    public function applyFilters($query, $filterMappings = null)
    {
        $mappings = $filterMappings ?? $this->getFilterMappings();

        foreach ($this->activeFilters as $field => $value) {
            if (empty($value)) {
                continue;
            }

            $mapping = $mappings[$field] ?? null;

            if ($mapping) {
                if (is_callable($mapping)) {
                    $query = $mapping($query, $value);
                } elseif (is_array($mapping)) {
                    $this->applyArrayFilter($query, $field, $value, $mapping);
                } else {
                    $query->where($field, $value);
                }
            } else {
                $query->where($field, $value);
            }
        }

        return $query;
    }

    /**
     * Apply sorting to query
     */
    public function applySorting($query)
    {
        return $query->orderBy($this->sortField, $this->sortDirection);
    }

    /**
     * Apply pagination to query
     */
    public function applyPagination($query)
    {
        return $query->paginate($this->perPage);
    }

    /**
     * Get filtered and sorted results
     */
    public function getFilteredResults($query, $searchFields = null, $filterMappings = null)
    {
        $query = $this->applySearch($query, $searchFields);
        $query = $this->applyFilters($query, $filterMappings);
        $query = $this->applySorting($query);

        return $this->applyPagination($query);
    }

    /**
     * Update search
     */
    public function updatedSearch()
    {
        $this->resetPage();
        $this->saveActiveFilters();
    }

    /**
     * Update filter
     */
    public function updatedFilters()
    {
        $this->resetPage();
        $this->saveActiveFilters();
    }

    /**
     * Update sort
     */
    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortField = $field;
            $this->sortDirection = 'asc';
        }

        $this->saveActiveFilters();
    }

    /**
     * Reset all filters
     */
    public function resetFilters()
    {
        $this->reset(['search', 'filters', 'activeFilters']);
        $this->resetPage();
        $this->saveActiveFilters();
    }

    /**
     * Get filter mappings
     */
    protected function getFilterMappings()
    {
        return [];
    }

    /**
     * Apply array-based filter
     */
    protected function applyArrayFilter($query, $field, $value, $mapping)
    {
        $type = $mapping['type'] ?? 'equals';

        switch ($type) {
            case 'date_range':
                if (isset($value['from']) && $value['from']) {
                    $query->where($field, '>=', $value['from']);
                }
                if (isset($value['to']) && $value['to']) {
                    $query->where($field, '<=', $value['to']);
                }
                break;

            case 'relationship':
                $query->whereHas($mapping['relation'], function ($q) use ($mapping, $value): void {
                    $q->where($mapping['column'], $value);
                });
                break;

            case 'in':
                $query->whereIn($field, (array) $value);
                break;

            case 'like':
                $query->where($field, 'like', '%'.$value.'%');
                break;

            default:
                $query->where($field, $value);
        }
    }

    /**
     * Save active filters to session
     */
    protected function saveActiveFilters()
    {
        $this->activeFilters = array_filter($this->filters);

        if (method_exists($this, 'getSessionKey')) {
            session()->put($this->getSessionKey(), [
                'search' => $this->search,
                'filters' => $this->activeFilters,
                'sortField' => $this->sortField,
                'sortDirection' => $this->sortDirection,
                'perPage' => $this->perPage,
            ]);
        }
    }

    /**
     * Load active filters from session
     */
    protected function loadActiveFilters()
    {
        if (method_exists($this, 'getSessionKey')) {
            $sessionData = session()->get($this->getSessionKey(), []);

            $this->search = $sessionData['search'] ?? '';
            $this->filters = $sessionData['filters'] ?? [];
            $this->activeFilters = $this->filters;
            $this->sortField = $sessionData['sortField'] ?? 'created_at';
            $this->sortDirection = $sessionData['sortDirection'] ?? 'desc';
            $this->perPage = $sessionData['perPage'] ?? 20;
        }
    }

    /**
     * Get session key for filters
     */
    protected function getSessionKey()
    {
        return class_basename(static::class).'_filters';
    }
}
