<?php

namespace App\Livewire\Traits;

use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Livewire\WithFileUploads as LivewireWithFileUploads;

trait WithFileUpload
{
    use LivewireWithFileUploads;

    /**
     * Upload a file and return the path
     */
    public function uploadFile($file, $directory = 'uploads', $disk = 'public')
    {
        if (! $file) {
            return null;
        }

        $filename = $this->generateFileName($file);

        return $file->storeAs($directory, $filename, $disk);
    }

    /**
     * Upload multiple files
     */
    public function uploadFiles($files, $directory = 'uploads', $disk = 'public')
    {
        if (! $files) {
            return [];
        }

        $paths = [];

        foreach ($files as $file) {
            $paths[] = $this->uploadFile($file, $directory, $disk);
        }

        return $paths;
    }

    /**
     * Delete a file
     */
    public function deleteFile($path, $disk = 'public')
    {
        if (! $path) {
            return false;
        }

        return Storage::disk($disk)->delete($path);
    }

    /**
     * Generate a unique filename
     */
    protected function generateFileName($file)
    {
        $extension = $file->getClientOriginalExtension();
        $basename = Str::slug(pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME));
        $timestamp = now()->timestamp;
        $random = Str::random(6);

        return "{$basename}-{$timestamp}-{$random}.{$extension}";
    }

    /**
     * Validate file upload
     */
    public function validateFileUpload($file, $allowedTypes = [], $maxSize = null)
    {
        if (! $file) {
            return true;
        }

        $rules = ['file'];

        if ($allowedTypes) {
            $rules[] = 'mimes:'.implode(',', $allowedTypes);
        }

        if ($maxSize) {
            $rules[] = 'max:'.$maxSize;
        }

        return Validator::make(['file' => $file], ['file' => $rules])->validate();
    }

    /**
     * Get file URL
     */
    public function getFileUrl($path, $disk = 'public')
    {
        if (! $path) {
            return null;
        }

        return Storage::disk($disk)->url($path);
    }

    /**
     * Check if file exists
     */
    public function fileExists($path, $disk = 'public')
    {
        if (! $path) {
            return false;
        }

        return Storage::disk($disk)->exists($path);
    }
}
