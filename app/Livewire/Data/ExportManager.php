<?php

namespace App\Livewire\Data;

use App\Models\ExportJob;
use App\Models\ExportTemplate;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\WithPagination;

class ExportManager extends Component
{
    use WithPagination;

    public $activeTab = 'templates'; // templates or jobs

    // Template management properties
    public $showTemplateModal = false;

    public $editingTemplateId;

    public $deletingTemplateId;

    public $templateSearch = '';

    public $entityTypeFilter = 'all';

    // Template form properties
    public $templateName = '';

    public $entityType = 'estates';

    public $format = 'xlsx';

    public $columns = [];

    public $filters = [];

    public $isPublic = false;

    public $isScheduled = false;

    public $scheduleFrequency = 'monthly';

    // Job management properties
    public $jobSearch = '';

    public $jobStatusFilter = 'all';

    public $jobEntityTypeFilter = 'all';

    // Available options
    public $availableEntities = [];

    public $availableFormats = [];

    public $scheduleFrequencies = [];

    protected $rules = [
        'templateName' => 'required|string|max:255',
        'entityType' => 'required|in:estates,houses,contacts,meter_readings,invoices,water_rates',
        'format' => 'required|in:xlsx,csv,pdf',
        'columns' => 'required|array|min:1',
        'isPublic' => 'boolean',
        'isScheduled' => 'boolean',
        'scheduleFrequency' => 'required_if:isScheduled,true|in:daily,weekly,monthly,quarterly',
    ];

    protected $messages = [
        'templateName.required' => 'Template name is required.',
        'entityType.required' => 'Entity type is required.',
        'format.required' => 'Export format is required.',
        'columns.required' => 'At least one column must be selected.',
        'scheduleFrequency.required_if' => 'Schedule frequency is required when scheduling is enabled.',
    ];

    public function mount()
    {
        $this->initializeAvailableOptions();
        $this->setDefaultColumns();
    }

    private function initializeAvailableOptions()
    {
        $this->availableEntities = [
            'all' => 'All Entities',
            'estates' => 'Estates',
            'houses' => 'Houses',
            'contacts' => 'Contacts',
            'meter_readings' => 'Meter Readings',
            'invoices' => 'Invoices',
            'water_rates' => 'Water Rates',
        ];

        $this->availableFormats = [
            'xlsx' => 'Excel (.xlsx)',
            'csv' => 'CSV (.csv)',
            'pdf' => 'PDF (.pdf)',
        ];

        $this->scheduleFrequencies = [
            'daily' => 'Daily',
            'weekly' => 'Weekly',
            'monthly' => 'Monthly',
            'quarterly' => 'Quarterly',
        ];
    }

    // Template Management Methods
    public function openTemplateModal($templateId = null)
    {
        $this->resetTemplateForm();

        if ($templateId) {
            $this->loadTemplate($templateId);
        }

        $this->showTemplateModal = true;
    }

    public function confirmDeleteTemplate($templateId)
    {
        $this->deletingTemplateId = $templateId;
    }

    public function saveTemplate()
    {
        $this->validate();

        $data = [
            'user_id' => Auth::id(),
            'name' => $this->templateName,
            'entity_type' => $this->entityType,
            'format' => $this->format,
            'columns' => $this->columns,
            'filters' => $this->filters,
            'is_public' => $this->isPublic,
            'is_scheduled' => $this->isScheduled,
            'schedule_frequency' => $this->isScheduled ? $this->scheduleFrequency : null,
        ];

        if ($this->editingTemplateId) {
            $template = ExportTemplate::findOrFail($this->editingTemplateId);
            if ($template->user_id !== Auth::id()) {
                abort(403);
            }
            $template->update($data);
            $message = 'Export template updated successfully!';
        } else {
            ExportTemplate::create($data);
            $message = 'Export template created successfully!';
        }

        $this->closeTemplateModal();
        session()->flash('message', $message);
    }

    public function deleteTemplate()
    {
        if ($this->deletingTemplateId) {
            $template = ExportTemplate::findOrFail($this->deletingTemplateId);

            if ($template->user_id !== Auth::id()) {
                abort(403);
            }

            $template->delete();
            $this->deletingTemplateId = null;
            session()->flash('message', 'Export template deleted successfully!');
        }
    }

    public function closeTemplateModal()
    {
        $this->showTemplateModal = false;
        $this->resetTemplateForm();
    }

    private function resetTemplateForm()
    {
        $this->reset([
            'templateName', 'entityType', 'format', 'columns', 'filters',
            'isPublic', 'isScheduled', 'scheduleFrequency', 'editingTemplateId',
        ]);
        $this->setDefaultColumns();
    }

    private function loadTemplate($templateId)
    {
        $template = ExportTemplate::findOrFail($templateId);

        if ($template->user_id !== Auth::id() && ! $template->is_public) {
            abort(403);
        }

        $this->editingTemplateId = $template->id;
        $this->templateName = $template->name;
        $this->entityType = $template->entity_type;
        $this->format = $template->format;
        $this->columns = $template->columns;
        $this->filters = $template->filters ?? [];
        $this->isPublic = $template->is_public;
        $this->isScheduled = $template->is_scheduled;
        $this->scheduleFrequency = $template->schedule_frequency ?? 'monthly';
    }

    // Job Management Methods
    public function runExport($templateId)
    {
        $template = ExportTemplate::findOrFail($templateId);

        if ($template->user_id !== Auth::id() && ! $template->is_public) {
            abort(403);
        }

        // Create export job
        $job = ExportJob::create([
            'user_id' => Auth::id(),
            'export_template_id' => $template->id,
            'status' => 'pending',
            'filters' => $template->filters,
        ]);

        // Dispatch job to queue
        \App\Jobs\ProcessExportJob::dispatch($job);

        session()->flash('message', 'Export job started successfully!');
    }

    public function downloadExport($jobId)
    {
        $job = ExportJob::findOrFail($jobId);

        if ($job->user_id !== Auth::id()) {
            abort(403);
        }

        if ($job->status !== 'completed' || ! $job->file_path) {
            session()->flash('error', 'Export is not ready for download.');

            return null;
        }

        return response()->download(storage_path('app/'.$job->file_path));
    }

    public function deleteJob($jobId)
    {
        $job = ExportJob::findOrFail($jobId);

        if ($job->user_id !== Auth::id()) {
            abort(403);
        }

        // Delete file if exists
        if ($job->file_path && \Storage::exists($job->file_path)) {
            \Storage::delete($job->file_path);
        }

        $job->delete();
        session()->flash('message', 'Export job deleted successfully!');
    }

    // Data Properties
    public function getTemplatesProperty()
    {
        return ExportTemplate::forUser(Auth::id())
            ->when($this->entityTypeFilter !== 'all', function ($query): void {
                $query->where('entity_type', $this->entityTypeFilter);
            })
            ->when($this->templateSearch, function ($query): void {
                $query->where(function ($q): void {
                    $q->where('name', 'like', '%'.$this->templateSearch.'%')
                        ->orWhere('entity_type', 'like', '%'.$this->templateSearch.'%');
                });
            })
            ->withCount(['exportJobs' => function ($query): void {
                $query->where('created_at', '>=', now()->subDays(30));
            }])
            ->latest()
            ->paginate(10);
    }

    public function getJobsProperty()
    {
        return ExportJob::with('exportTemplate')
            ->where('user_id', Auth::id())
            ->when($this->jobStatusFilter !== 'all', function ($query): void {
                $query->where('status', $this->jobStatusFilter);
            })
            ->when($this->jobEntityTypeFilter !== 'all', function ($query): void {
                $query->whereHas('exportTemplate', function ($q): void {
                    $q->where('entity_type', $this->jobEntityTypeFilter);
                });
            })
            ->when($this->jobSearch, function ($query): void {
                $query->where(function ($q): void {
                    $q->where('id', 'like', '%'.$this->jobSearch.'%')
                        ->orWhereHas('exportTemplate', function ($q): void {
                            $q->where('name', 'like', '%'.$this->jobSearch.'%');
                        });
                });
            })
            ->latest()
            ->paginate(10);
    }

    public function getTemplateStatisticsProperty()
    {
        return [
            'total_templates' => ExportTemplate::forUser(Auth::id())->count(),
            'public_templates' => ExportTemplate::where('is_public', true)->count(),
            'scheduled_templates' => ExportTemplate::forUser(Auth::id())->where('is_scheduled', true)->count(),
            'recent_jobs' => ExportJob::where('user_id', Auth::id())
                ->where('created_at', '>=', now()->subDays(7))
                ->count(),
        ];
    }

    public function getJobStatisticsProperty()
    {
        $baseQuery = ExportJob::where('user_id', Auth::id());

        return [
            'total_jobs' => (clone $baseQuery)->count(),
            'pending_jobs' => (clone $baseQuery)->where('status', 'pending')->count(),
            'processing_jobs' => (clone $baseQuery)->where('status', 'processing')->count(),
            'completed_jobs' => (clone $baseQuery)->where('status', 'completed')->count(),
            'failed_jobs' => (clone $baseQuery)->where('status', 'failed')->count(),
            'recent_jobs' => (clone $baseQuery)->where('created_at', '>=', now()->subDays(7))->count(),
        ];
    }

    // Utility Methods
    public function updatedEntityType($value)
    {
        $this->setDefaultColumns();
    }

    private function setDefaultColumns()
    {
        $this->columns = $this->getAvailableColumnsForEntity($this->entityType);
    }

    public function getAvailableColumnsForEntity($entityType)
    {
        return match ($entityType) {
            'estates' => [
                'ID', 'Name', 'Location', 'Status', 'Total Houses', 'Occupied Houses',
                'Occupancy Rate', 'Created At', 'Updated At',
            ],
            'houses' => [
                'ID', 'House Number', 'Estate', 'Address', 'Status', 'Meter Number',
                'Created At', 'Updated At',
            ],
            'contacts' => [
                'ID', 'Name', 'Email', 'Phone', 'Contact Type', 'House Number',
                'Estate', 'Status', 'Created At', 'Updated At',
            ],
            'meter_readings' => [
                'ID', 'House Number', 'Estate', 'Reading Date', 'Current Reading',
                'Previous Reading', 'Consumption', 'Status', 'Submitted By',
                'Reviewed By', 'Created At', 'Updated At',
            ],
            'invoices' => [
                'ID', 'Invoice Number', 'House Number', 'Estate', 'Billing Period',
                'Consumption', 'Rate per Unit', 'Amount', 'Status', 'Due Date',
                'Created At', 'Updated At',
            ],
            'water_rates' => [
                'ID', 'Name', 'Estate', 'Rate per Unit', 'Effective From',
                'Effective To', 'Description', 'Status', 'Created At', 'Updated At',
            ],
            default => [],
        };
    }

    public function getEntityLabel($entityType)
    {
        return $this->availableEntities[$entityType] ?? $entityType;
    }

    public function getFormatLabel($format)
    {
        return $this->availableFormats[$format] ?? strtoupper((string) $format);
    }

    public function getStatusLabel($status)
    {
        return match ($status) {
            'pending' => 'Pending',
            'processing' => 'Processing',
            'completed' => 'Completed',
            'failed' => 'Failed',
            default => ucfirst((string) $status),
        };
    }

    public function getStatusColor($status)
    {
        return match ($status) {
            'pending' => 'yellow',
            'processing' => 'blue',
            'completed' => 'green',
            'failed' => 'red',
            default => 'gray',
        };
    }

    public function render()
    {
        return view('livewire.data.export-manager', [
            'templates' => $this->templates,
            'jobs' => $this->jobs,
            'templateStatistics' => $this->templateStatistics,
            'jobStatistics' => $this->jobStatistics,
            'availableEntities' => $this->availableEntities,
            'availableFormats' => $this->availableFormats,
            'scheduleFrequencies' => $this->scheduleFrequencies,
            'activeTab' => $this->activeTab,
        ]);
    }
}
