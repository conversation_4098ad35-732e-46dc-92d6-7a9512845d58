<?php

namespace App\Livewire;

use App\Models\MeterReading;
use Livewire\Component;
use Livewire\WithPagination;

class MeterReadingReview extends Component
{
    use WithPagination;

    public $selectedReading;

    public $reviewNotes = '';

    public $filterStatus = 'pending';

    public $filterEstate = '';

    public $filterDate = '';

    protected $rules = [
        'reviewNotes' => 'required|string|max:500',
    ];

    public function mount()
    {
        $this->authorize('review-readings');
    }

    public function getReadingsProperty()
    {
        return MeterReading::with(['house.estate', 'user'])
            ->when($this->filterStatus === 'pending', fn ($query) => $query->whereIn('status', ['submitted', 'reviewed']))
            ->when($this->filterStatus !== 'pending', fn ($query) => $query->where('status', $this->filterStatus))
            ->when($this->filterEstate, fn ($query) => $query->whereHas('house', function ($q): void {
                $q->where('estate_id', $this->filterEstate);
            }))
            ->when($this->filterDate, fn ($query) => $query->whereDate('reading_date', $this->filterDate))
            ->orderBy('reading_date', 'desc')
            ->paginate(10);
    }

    public function getStatsProperty()
    {
        return [
            'pending' => MeterReading::whereIn('status', ['submitted', 'reviewed'])->count(),
            'approved' => MeterReading::where('status', 'approved')->count(),
            'rejected' => MeterReading::where('status', 'rejected')->count(),
            'today' => MeterReading::whereDate('reading_date', today())->count(),
        ];
    }

    public function selectReading($readingId)
    {
        $this->selectedReading = MeterReading::with(['house.estate', 'user'])->findOrFail($readingId);
        $this->reviewNotes = '';
    }

    public function approveReading()
    {
        $this->validate();

        $this->selectedReading->approve(Auth::id(), $this->reviewNotes);
        $this->selectedReading = null;
        $this->reviewNotes = '';

        session()->flash('message', 'Reading approved and invoice generated successfully!');
    }

    public function rejectReading()
    {
        $this->validate();

        $this->selectedReading->reject(Auth::id(), $this->reviewNotes);
        $this->selectedReading = null;
        $this->reviewNotes = '';

        session()->flash('message', 'Reading rejected successfully!');
    }

    public function render()
    {
        return view('livewire.meter-reading-review', [
            'readings' => $this->readings,
            'stats' => $this->stats,
        ]);
    }
}
