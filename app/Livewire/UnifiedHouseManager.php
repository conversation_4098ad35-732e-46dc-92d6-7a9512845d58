<?php

namespace App\Livewire;

use App\Models\Estate;
use App\Models\House;
use App\Traits\HasPermissions;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\WithFileUploads;
use Livewire\WithPagination;

class UnifiedHouseManager extends Component
{
    use HasPermissions, WithFileUploads, WithPagination;

    // Mode management
    public $mode = 'list'; // list, create, edit, import

    public $houseId;

    // List view properties
    public $estate_id = '';

    public $search = '';

    public array $filters = [];

    // Form properties
    public $house_number;

    public $estate_id_form;

    public $meter_number;

    public $occupancy_status = 'vacant';

    public $notes;

    // Import properties
    public $file;

    public $importPreview = [];

    public $showImportPreview = false;

    // Collections
    public $estates = [];

    protected $queryString = ['filters'];

    protected function rules()
    {
        return [
            'house_number' => 'required|string|min:1|max:20',
            'estate_id_form' => 'required|exists:estates,id',
            'meter_number' => 'required|string|min:1|max:50|unique:houses,meter_number,'.$this->houseId,
            'occupancy_status' => 'required|in:vacant,occupied,maintenance',
            'notes' => 'nullable|string|max:1000',
        ];
    }

    protected $messages = [
        'house_number.required' => 'House number is required.',
        'house_number.min' => 'House number must be at least 1 character.',
        'estate_id_form.required' => 'Please select an estate.',
        'estate_id_form.exists' => 'The selected estate does not exist.',
        'meter_number.required' => 'Meter number is required.',
        'meter_number.unique' => 'This meter number is already in use.',
        'occupancy_status.required' => 'Occupancy status is required.',
        'occupancy_status.in' => 'Invalid occupancy status selected.',
    ];

    public function mount()
    {
        $this->authorizeAccess();
        $this->syncFiltersFromProperties();
        $this->loadCollections();
    }

    private function authorizeAccess()
    {
        // Check if user has any house management permission
        $housePermissions = [
            'houses.view_all',
            'houses.view_assigned',
            'houses.view_own',
            'houses.create',
            'houses.edit_all',
            'houses.edit_assigned',
            'houses.delete',
        ];

        if (! Auth::user() || ! Auth::user()->hasAnyPermission($housePermissions)) {
            abort(403, 'Unauthorized access to house management');
        }
    }

    private function syncFiltersFromProperties()
    {
        $this->filters = [
            'estate_id' => $this->estate_id,
            'search' => $this->search,
        ];
    }

    private function loadCollections()
    {
        // Load estates based on user permissions
        if ($this->hasPermission('estates.view_all')) {
            $this->estates = Estate::orderBy('name')->get();
        } elseif ($this->hasPermission('estates.view_assigned')) {
            $this->estates = Auth::user()->assignedEstates()->orderBy('name')->get();
        } else {
            $this->estates = collect();
        }
    }

    // Mode management
    public function setMode($mode, $houseId = null)
    {
        // Check permissions for mode changes
        if ($mode === 'create' && ! $this->hasPermission('houses.create')) {
            session()->flash('error', 'You do not have permission to create houses.');

            return;
        }

        if ($mode === 'edit' && ! $this->hasAnyPermission(['houses.edit_all', 'houses.edit_assigned'])) {
            session()->flash('error', 'You do not have permission to edit houses.');

            return;
        }

        if ($mode === 'import' && ! $this->hasPermission('houses.create')) {
            session()->flash('error', 'You do not have permission to import houses.');

            return;
        }

        $this->mode = $mode;
        $this->houseId = $houseId;

        if ($mode === 'create') {
            $this->resetForm();
        } elseif ($mode === 'edit' && $houseId) {
            $this->loadHouse($houseId);
        } elseif ($mode === 'import') {
            $this->resetImport();
        }
    }

    public function resetToList()
    {
        $this->mode = 'list';
        $this->resetForm();
        $this->resetImport();
    }

    private function resetForm()
    {
        $this->reset([
            'house_number', 'estate_id_form', 'meter_number',
            'occupancy_status', 'notes',
        ]);
    }

    private function resetImport()
    {
        $this->reset(['file', 'importPreview', 'showImportPreview']);
    }

    private function loadHouse($houseId)
    {
        $house = House::findOrFail($houseId);
        $this->house_number = $house->house_number;
        $this->estate_id_form = $house->estate_id;
        $this->meter_number = $house->meter_number;
        $this->occupancy_status = $house->occupancy_status;
        $this->notes = $house->notes;
    }

    // Form actions
    public function save()
    {
        $this->validate();

        $houseData = [
            'house_number' => $this->house_number,
            'estate_id' => $this->estate_id_form,
            'meter_number' => $this->meter_number,
            'occupancy_status' => $this->occupancy_status,
            'notes' => $this->notes,
        ];

        if ($this->mode === 'create') {
            House::create($houseData);
            $message = 'House created successfully.';
        } else {
            $house = House::findOrFail($this->houseId);
            $house->update($houseData);
            $message = 'House updated successfully.';
        }

        session()->flash('message', $message);
        $this->resetToList();
    }

    // Import functionality
    public function import()
    {
        $this->validate([
            'file' => 'required|mimes:csv,txt|max:10240', // Max 10MB
        ]);

        $path = $this->file->getRealPath();
        $csvData = array_map('str_getcsv', file($path));

        // Remove header if exists
        if (count($csvData) > 0 && strtolower((string) $csvData[0][0]) === 'house_number') {
            array_shift($csvData);
        }

        $this->importPreview = [];
        $errors = [];

        foreach ($csvData as $index => $row) {
            if (count($row) < 3) {
                $errors[] = 'Row '.($index + 1).': Insufficient columns';

                continue;
            }

            $previewRow = [
                'row' => $index + 1,
                'house_number' => $row[0] ?? '',
                'estate_name' => $row[1] ?? '',
                'meter_number' => $row[2] ?? '',
                'occupancy_status' => $row[3] ?? 'vacant',
                'notes' => $row[4] ?? '',
                'errors' => [],
            ];

            // Validation
            if (empty($previewRow['house_number'])) {
                $previewRow['errors'][] = 'House number is required';
            }

            if (empty($previewRow['estate_name'])) {
                $previewRow['errors'][] = 'Estate name is required';
            } else {
                $estate = Estate::where('name', $previewRow['estate_name'])->first();
                if (! $estate) {
                    $previewRow['errors'][] = 'Estate not found';
                }
            }

            if (empty($previewRow['meter_number'])) {
                $previewRow['errors'][] = 'Meter number is required';
            } else {
                $existingHouse = House::where('meter_number', $previewRow['meter_number'])->first();
                if ($existingHouse && (! $this->houseId || $existingHouse->id != $this->houseId)) {
                    $previewRow['errors'][] = 'Meter number already exists';
                }
            }

            $this->importPreview[] = $previewRow;
        }

        $this->showImportPreview = true;

        if ($errors === [] && array_filter(array_column($this->importPreview, 'errors')) === []) {
            $this->processImport();
        }
    }

    public function processImport()
    {
        $successCount = 0;
        $errorCount = 0;

        foreach ($this->importPreview as $row) {
            if (! empty($row['errors'])) {
                $errorCount++;

                continue;
            }

            try {
                $estate = Estate::where('name', $row['estate_name'])->first();

                House::create([
                    'house_number' => $row['house_number'],
                    'estate_id' => $estate->id,
                    'meter_number' => $row['meter_number'],
                    'occupancy_status' => $row['occupancy_status'] ?? 'vacant',
                    'notes' => $row['notes'] ?? '',
                ]);

                $successCount++;
            } catch (\Exception) {
                $errorCount++;
            }
        }

        session()->flash('message', "Import completed: {$successCount} houses imported successfully, {$errorCount} failed.");
        $this->resetToList();
    }

    public function cancelImport()
    {
        $this->resetImport();
        $this->mode = 'list';
    }

    // List view data
    public function getHousesProperty()
    {
        $query = House::with('estate');

        // Apply permission-based filtering
        $query = $this->applyHouseScope($query);

        // Apply estate filter
        if ($this->estate_id) {
            $query->where('estate_id', $this->estate_id);
        }

        // Apply search filter
        if ($this->search) {
            $query->where(function ($q): void {
                $q->where('house_number', 'like', '%'.$this->search.'%')
                    ->orWhere('meter_number', 'like', '%'.$this->search.'%')
                    ->orWhereHas('estate', function ($q): void {
                        $q->where('name', 'like', '%'.$this->search.'%');
                    });
            });
        }

        return $query->orderBy('house_number')->paginate(20);
    }

    public function getEstatesForFilterProperty()
    {
        if ($this->hasPermission('estates.view_all')) {
            return Estate::orderBy('name')->get();
        } elseif ($this->hasPermission('estates.view_assigned')) {
            return Auth::user()->assignedEstates()->orderBy('name')->get();
        } else {
            return collect();
        }
    }

    // Filter methods
    public function updatedEstateId($value)
    {
        $this->syncFilters();
    }

    public function updatedSearch($value)
    {
        $this->syncFilters();
    }

    /**
     * Apply permission-based scope to house query
     */
    private function applyHouseScope($query)
    {
        // If user can view all houses, no additional filtering needed
        if ($this->hasPermission('houses.view_all')) {
            return $query;
        }

        // If user can view assigned houses, filter by assigned estates
        if ($this->hasPermission('houses.view_assigned')) {
            $assignedEstateIds = Auth::user()->assignedEstates()->pluck('id');

            return $query->whereIn('estate_id', $assignedEstateIds);
        }

        // If user can view own houses, filter by user's houses
        if ($this->hasPermission('houses.view_own')) {
            return $query->whereHas('contacts', function ($q): void {
                $q->where('user_id', Auth::user()->id);
            });
        }

        // If no specific permission, return empty query
        return $query->whereRaw('1 = 0');
    }

    private function syncFilters()
    {
        $this->filters = [
            'estate_id' => $this->estate_id,
            'search' => $this->search,
        ];
    }

    public function resetFilters()
    {
        $this->reset(['estate_id', 'search']);
        $this->syncFilters();
    }

    // Individual actions
    public function delete($houseId)
    {
        if (! $this->hasPermission('houses.delete')) {
            abort(403, 'You do not have permission to delete houses.');
        }

        $house = House::findOrFail($houseId);

        // Check if user can delete this specific house
        if (! $this->canDeleteHouse($houseId)) {
            session()->flash('error', 'You do not have permission to delete this house.');

            return;
        }

        $house->delete();
        session()->flash('message', 'House deleted successfully!');
    }

    // Permission-based utility methods
    public function canCreateHouse()
    {
        return $this->hasPermission('houses.create');
    }

    public function canEditHouse($houseId = null)
    {
        if (! $this->hasAnyPermission(['houses.edit_all', 'houses.edit_assigned'])) {
            return false;
        }

        if ($houseId && ! $this->hasPermission('houses.edit_all')) {
            $house = House::find($houseId);

            return $house && $this->isUserAssignedToEstate($house->estate);
        }

        return true;
    }

    public function canDeleteHouse($houseId = null)
    {
        if (! $this->hasPermission('houses.delete')) {
            return false;
        }

        if ($houseId && ! $this->hasPermission('houses.delete')) {
            $house = House::find($houseId);

            return $house && $this->isUserAssignedToEstate($house->estate);
        }

        return true;
    }

    private function isUserAssignedToEstate($estate)
    {
        $user = Auth::user();

        if ($this->hasPermission('estates.view_assigned')) {
            return $estate->assignedUsers()->where('user_id', $user->id)->exists();
        }

        return false;
    }

    // Permission-based UI elements
    public function getShowCreateButtonProperty()
    {
        return $this->hasPermission('houses.create');
    }

    public function getShowImportButtonProperty()
    {
        return $this->hasPermission('houses.create');
    }

    public function getShowEditActionsProperty()
    {
        return $this->hasAnyPermission(['houses.edit_all', 'houses.edit_assigned', 'houses.delete']);
    }

    public function getRequiredPermissionsHintProperty()
    {
        $hints = [];

        if (! $this->hasAnyPermission(['houses.view_all', 'houses.view_assigned', 'houses.view_own'])) {
            $hints[] = 'View houses permission';
        }

        if (! $this->hasPermission('houses.create') && $this->hasAnyPermission(['houses.view_all', 'houses.view_assigned', 'houses.view_own'])) {
            $hints[] = 'Create houses permission';
        }

        if (! $this->hasAnyPermission(['houses.edit_all', 'houses.edit_assigned']) && $this->hasAnyPermission(['houses.view_all', 'houses.view_assigned', 'houses.view_own'])) {
            $hints[] = 'Edit houses permission';
        }

        if (! $this->hasPermission('houses.delete') && $this->hasAnyPermission(['houses.view_all', 'houses.view_assigned', 'houses.view_own'])) {
            $hints[] = 'Delete houses permission';
        }

        return $hints;
    }

    public function render()
    {
        return view('livewire.house-manager', [
            'houses' => $this->houses,
            'estates' => $this->estates,
            'estatesForFilter' => $this->estatesForFilter,
            'mode' => $this->mode,
            'importPreview' => $this->importPreview,
            'showImportPreview' => $this->showImportPreview,
            'requiredPermissionsHint' => $this->requiredPermissionsHint,
            'showCreateButton' => $this->showCreateButton,
            'showImportButton' => $this->showImportButton,
            'showEditActions' => $this->showEditActions,
            'canEditHouse' => fn ($id) => $this->canEditHouse($id),
            'canDeleteHouse' => fn ($id) => $this->canDeleteHouse($id),
        ]);
    }
}
