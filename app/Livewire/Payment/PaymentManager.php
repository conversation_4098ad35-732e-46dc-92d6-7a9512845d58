<?php

namespace App\Livewire\Payment;

use App\Models\Invoice;
use App\Models\InvoicePayment;
use App\Traits\HasPermissions;
use Livewire\Component;

class PaymentManager extends Component
{
    use HasPermissions;

    public Invoice $invoice;

    public $payments;

    public float $totalPaid = 0.0;

    public float $balanceDue = 0.0;

    public string $sortDirection = 'desc';

    public string $sortColumn = 'payment_date';

    public array $selectedPayments = [];

    public bool $showRefundForm = false;

    public bool $showAdjustmentForm = false;

    public bool $showPaymentForm = false;

    public float $refundAmount = 0.0;

    public string $refundReason = '';

    public float $adjustmentAmount = 0.0;

    public string $adjustmentReason = '';

    protected function rules(): array
    {
        return [
            'refundAmount' => ['required', 'numeric', 'min:0.01', 'max:'.$this->totalPaid],
            'refundReason' => ['required', 'string', 'max:255'],
            'adjustmentAmount' => ['required', 'numeric', 'min:-999999.99', 'max:999999.99'],
            'adjustmentReason' => ['required', 'string', 'max:255'],
        ];
    }

    public function mount(Invoice $invoice)
    {
        $this->invoice = $invoice;
        $this->loadPayments();

        // Check if user has permission to view payments for this invoice
        if (! $this->canViewPayments()) {
            abort(403, 'You do not have permission to view payments for this invoice.');
        }
    }

    public function loadPayments(): void
    {
        $builder = $this->invoice->payments()->with(['user']);

        if (! $this->canViewAllPayments()) {
            // Filter payments based on user permissions
            $builder->where(function ($q): void {
                if ($this->canViewOwnPayments()) {
                    $q->where('user_id', auth()->id());
                }
                if ($this->canViewAssignedPayments()) {
                    // This would need additional logic to filter by assigned estates/houses
                    // For now, we'll leave it as is since the PermissionService handles this
                }
            });
        }

        $this->payments = $builder->orderBy($this->sortColumn, $this->sortDirection)->get();
        $this->totalPaid = $this->payments->sum('amount');
        $this->balanceDue = $this->invoice->balance_due;
    }

    public function sortPayments(string $column): void
    {
        $this->sortColumn = $column;
        $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        $this->loadPayments();
    }

    public function togglePaymentSelection($paymentId): void
    {
        if (in_array($paymentId, $this->selectedPayments)) {
            $this->selectedPayments = array_diff($this->selectedPayments, [$paymentId]);
        } else {
            $this->selectedPayments[] = $paymentId;
        }
    }

    public function selectAllPayments(): void
    {
        if ($this->canViewAllPayments()) {
            $this->selectedPayments = $this->payments->pluck('id')->toArray();
        } else {
            // Only select own payments
            $this->selectedPayments = $this->payments->where('user_id', auth()->id())->pluck('id')->toArray();
        }
    }

    public function clearSelection(): void
    {
        $this->selectedPayments = [];
    }

    public function showRefundModal(): void
    {
        if (! $this->canRefundPayments()) {
            session()->flash('error', 'You do not have permission to process refunds.');

            return;
        }

        $this->showRefundForm = true;
        $this->refundAmount = 0.0;
        $this->refundReason = '';
        $this->resetValidation();
    }

    public function processRefund(): void
    {
        if (! $this->canRefundPayments()) {
            session()->flash('error', 'You do not have permission to process refunds.');

            return;
        }

        $this->validate();

        if ($this->selectedPayments === []) {
            session()->flash('error', 'Please select at least one payment to refund.');

            return;
        }

        $totalRefundAmount = 0;
        $paymentsToRefund = [];

        foreach ($this->selectedPayments as $selectedPayment) {
            $payment = InvoicePayment::find($selectedPayment);
            if ($payment && $payment->invoice_id === $this->invoice->id) {
                $paymentsToRefund[] = $payment;
                $totalRefundAmount += $payment->amount;
            }
        }

        if ($totalRefundAmount > $this->totalPaid) {
            session()->flash('error', 'Refund amount cannot exceed total paid amount.');

            return;
        }

        try {
            foreach ($paymentsToRefund as $paymentToRefund) {
                $paymentToRefund->update([
                    'payment_status' => 'refunded',
                    'notes' => $paymentToRefund->notes."\nRefund processed: ".$this->refundReason,
                ]);
            }

            session()->flash('success', 'Refund processed successfully.');
            $this->showRefundForm = false;
            $this->selectedPayments = [];
            $this->loadPayments();
            $this->invoice->refresh();

        } catch (\Exception $e) {
            session()->flash('error', 'Failed to process refund: '.$e->getMessage());
        }
    }

    public function showAdjustmentModal(): void
    {
        if (! $this->canAdjustPayments()) {
            session()->flash('error', 'You do not have permission to process adjustments.');

            return;
        }

        $this->showAdjustmentForm = true;
        $this->adjustmentAmount = 0.0;
        $this->adjustmentReason = '';
        $this->resetValidation();
    }

    public function processAdjustment(): void
    {
        if (! $this->canAdjustPayments()) {
            session()->flash('error', 'You do not have permission to process adjustments.');

            return;
        }

        $this->validate();

        if ($this->selectedPayments === []) {
            session()->flash('error', 'Please select at least one payment to adjust.');

            return;
        }

        try {
            foreach ($this->selectedPayments as $selectedPayment) {
                $payment = InvoicePayment::find($selectedPayment);
                if ($payment && $payment->invoice_id === $this->invoice->id) {
                    $payment->update([
                        'amount' => $payment->amount + $this->adjustmentAmount,
                        'notes' => $payment->notes."\nAdjustment processed: ".$this->adjustmentReason,
                    ]);
                }
            }

            session()->flash('success', 'Payment adjustment processed successfully.');
            $this->showAdjustmentForm = false;
            $this->selectedPayments = [];
            $this->loadPayments();
            $this->invoice->refresh();

        } catch (\Exception $e) {
            session()->flash('error', 'Failed to process adjustment: '.$e->getMessage());
        }
    }

    public function exportPayments(): void
    {
        if (! $this->canExportPayments()) {
            session()->flash('error', 'You do not have permission to export payments.');

            return;
        }

        // Trigger payment export
        $this->dispatch('export-payments', invoiceId: $this->invoice->id);
    }

    // Permission methods using PermissionAware trait
    public function canViewPayments(): bool
    {
        return $this->canViewAllPayments() || $this->canViewAssignedPayments() || $this->canViewOwnPayments();
    }

    public function canViewAllPayments(): bool
    {
        return auth()->user()->can('payments.view_all');
    }

    public function canViewAssignedPayments(): bool
    {
        return auth()->user()->can('payments.view_assigned');
    }

    public function canViewOwnPayments(): bool
    {
        return auth()->user()->can('payments.view_own');
    }

    public function canRefundPayments(): bool
    {
        return auth()->user()->can('payments.refund');
    }

    public function canAdjustPayments(): bool
    {
        return auth()->user()->can('payments.adjust');
    }

    public function canExportPayments(): bool
    {
        return auth()->user()->can('payments.export_all') ||
               auth()->user()->can('payments.export_assigned') ||
               auth()->user()->can('payments.export_own');
    }

    public function canProcessPayments(): bool
    {
        return auth()->user()->can('payments.process');
    }

    public function render()
    {
        return view('livewire.payment.payment-manager');
    }
}
