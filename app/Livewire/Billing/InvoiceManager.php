<?php

namespace App\Livewire\Billing;

use App\Exports\InvoiceReportExport;
use App\Models\Estate;
use App\Models\House;
use App\Models\Invoice;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\WithPagination;
use Maatwebsite\Excel\Facades\Excel;

class InvoiceManager extends Component
{
    use WithPagination;

    // List view properties
    public string $status = '';

    public string $estate_id = '';

    public string $house_id = '';

    public string $date_from = '';

    public string $date_to = '';

    public string $search = '';

    public array $filters = [];

    // Report generation properties
    public $showReportModal = false;

    public $report_type = 'summary';

    public $report_date_from;

    public $report_date_to;

    public $report_status = 'all';

    public $report_estate_id;

    // Bulk actions
    public $selectedInvoices = [];

    public $selectAll = false;

    public $bulkAction = '';

    protected $queryString = ['filters'];

    public function mount()
    {
        $this->syncFiltersFromProperties();
        $this->setReportDefaults();
    }

    private function syncFiltersFromProperties()
    {
        $this->filters = [
            'status' => $this->status,
            'estate_id' => $this->estate_id,
            'house_id' => $this->house_id,
            'date_from' => $this->date_from,
            'date_to' => $this->date_to,
            'search' => $this->search,
        ];
    }

    private function setReportDefaults()
    {
        $this->report_date_from = now()->startOfMonth()->format('Y-m-d');
        $this->report_date_to = now()->endOfMonth()->format('Y-m-d');
    }

    public function getInvoicesProperty()
    {
        return Invoice::with(['house.estate', 'meterReading'])
            ->when($this->status, fn ($query) => $query->where('status', $this->status))
            ->when($this->estate_id, fn ($query) => $query->whereHas('house', function ($q): void {
                $q->where('estate_id', $this->estate_id);
            }))
            ->when($this->house_id, fn ($query) => $query->where('house_id', $this->house_id))
            ->when($this->date_from, fn ($query) => $query->where('billing_period_start', '>=', $this->date_from))
            ->when($this->date_to, fn ($query) => $query->where('billing_period_end', '<=', $this->date_to))
            ->when($this->search, fn ($query) => $query->where(function ($q): void {
                $q->where('invoice_number', 'like', '%'.$this->search.'%')
                    ->orWhereHas('house', function ($q): void {
                        $q->where('house_number', 'like', '%'.$this->search.'%');
                    });
            }))
            ->orderBy('created_at', 'desc')
            ->paginate(20);
    }

    public function getEstatesProperty()
    {
        return Estate::orderBy('name')->get();
    }

    public function getHousesProperty()
    {
        if ($this->estate_id !== '' && $this->estate_id !== '0') {
            return House::where('estate_id', $this->estate_id)->orderBy('house_number')->get();
        }

        return collect();
    }

    public function getStatisticsProperty()
    {
        $baseQuery = Invoice::query();

        if ($this->estate_id !== '' && $this->estate_id !== '0') {
            $baseQuery->whereHas('house', function ($q): void {
                $q->where('estate_id', $this->estate_id);
            });
        }

        if ($this->date_from && $this->date_to) {
            $baseQuery->whereBetween('created_at', [$this->date_from, $this->date_to]);
        }

        return [
            'total_invoices' => (clone $baseQuery)->count(),
            'total_amount' => (clone $baseQuery)->sum('amount'),
            'paid_invoices' => (clone $baseQuery)->where('status', 'paid')->count(),
            'paid_amount' => (clone $baseQuery)->where('status', 'paid')->sum('amount'),
            'pending_invoices' => (clone $baseQuery)->where('status', 'pending')->count(),
            'overdue_invoices' => (clone $baseQuery)->where('status', 'overdue')->count(),
            'overdue_amount' => (clone $baseQuery)->where('status', 'overdue')->sum('amount'),
        ];
    }

    // Filter methods
    public function updatedEstateId($value)
    {
        $this->house_id = '';
        $this->syncFilters();
    }

    public function updatedStatus($value)
    {
        $this->syncFilters();
    }

    public function updatedHouseId($value)
    {
        $this->syncFilters();
    }

    public function updatedDateFrom($value)
    {
        $this->syncFilters();
    }

    public function updatedDateTo($value)
    {
        $this->syncFilters();
    }

    public function updatedSearch($value)
    {
        $this->syncFilters();
    }

    private function syncFilters()
    {
        $this->filters = [
            'status' => $this->status,
            'estate_id' => $this->estate_id,
            'house_id' => $this->house_id,
            'date_from' => $this->date_from,
            'date_to' => $this->date_to,
            'search' => $this->search,
        ];
    }

    public function resetFilters()
    {
        $this->reset(['status', 'estate_id', 'house_id', 'date_from', 'date_to', 'search']);
        $this->syncFilters();
    }

    // Bulk actions
    public function updatedSelectAll($value)
    {
        $this->selectedInvoices = $value ? $this->invoices->pluck('id')->toArray() : [];
    }

    public function performBulkAction()
    {
        if (empty($this->selectedInvoices) || empty($this->bulkAction)) {
            return;
        }

        $invoices = Invoice::whereIn('id', $this->selectedInvoices)->get();

        switch ($this->bulkAction) {
            case 'delete':
                $this->bulkDelete($invoices);
                break;
            case 'mark_sent':
                $this->bulkMarkSent($invoices);
                break;
            case 'mark_paid':
                $this->bulkMarkPaid($invoices);
                break;
            case 'mark_overdue':
                $this->bulkMarkOverdue($invoices);
                break;
        }

        $this->reset(['selectedInvoices', 'selectAll', 'bulkAction']);
        session()->flash('message', 'Bulk action completed successfully!');
    }

    private function bulkDelete($invoices)
    {
        foreach ($invoices as $invoice) {
            if ($this->canDeleteInvoice($invoice)) {
                $invoice->delete();
            }
        }
    }

    private function bulkMarkSent($invoices)
    {
        $invoices->each->update([
            'status' => 'sent',
            'sent_at' => now(),
        ]);
    }

    private function bulkMarkPaid($invoices)
    {
        $invoices->each->update([
            'status' => 'paid',
            'paid_at' => now(),
        ]);
    }

    private function bulkMarkOverdue($invoices)
    {
        $invoices->where('status', 'sent')->each->update([
            'status' => 'overdue',
        ]);
    }

    // Report generation
    public function generateReport()
    {
        $this->validate([
            'report_type' => 'required|in:summary,detailed,overdue,payments',
            'report_date_from' => 'required|date',
            'report_date_to' => 'required|date|after_or_equal:report_date_from',
            'report_status' => 'required|in:all,draft,pending,sent,paid,overdue,cancelled',
            'report_estate_id' => 'nullable|exists:estates,id',
        ]);

        $builder = Invoice::with(['house.estate', 'waterRate']);

        // Apply date filter
        $startDate = Carbon::parse($this->report_date_from);
        $endDate = Carbon::parse($this->report_date_to);
        $builder->whereBetween('created_at', [$startDate, $endDate]);

        // Apply status filter
        if ($this->report_status !== 'all') {
            if ($this->report_status === 'overdue') {
                $builder->where('status', 'sent')
                    ->where('due_date', '<', now());
            } else {
                $builder->where('status', $this->report_status);
            }
        }

        // Apply estate filter
        if ($this->report_estate_id) {
            $builder->whereHas('house.estate', function ($q): void {
                $q->where('id', $this->report_estate_id);
            });
        }

        // Apply report type specific filters
        switch ($this->report_type) {
            case 'overdue':
                $builder->where('status', 'sent')
                    ->where('due_date', '<', now());
                break;
            case 'payments':
                $builder->where('status', 'paid')
                    ->whereNotNull('paid_at');
                break;
        }

        $invoices = $builder->orderBy('created_at', 'desc')->get();

        $this->showReportModal = false;

        return Excel::download(
            new InvoiceReportExport($invoices),
            'invoice-report-'.$this->report_type.'-'.now()->format('Y-m-d').'.xlsx'
        );
    }

    // Individual actions
    public function delete($invoiceId)
    {
        $invoice = Invoice::findOrFail($invoiceId);

        if (! $this->canDeleteInvoice($invoice)) {
            session()->flash('error', 'Cannot delete this invoice.');

            return;
        }

        $invoice->delete();
        session()->flash('message', 'Invoice deleted successfully!');
    }

    private function canDeleteInvoice($invoice)
    {
        // Check if user has permission to delete this invoice
        if (! Auth::user()->can('invoices.delete_all') &&
            (! Auth::user()->can('invoices.delete_assigned') || $invoice->house->estate_id !== Auth::user()->estate_id)) {
            return false;
        }

        // Check if invoice has payments
        if ($invoice->payments()->count() > 0) {
            return false;
        }

        // Check if invoice has been sent
        return ! $invoice->sent_at;
    }

    public function render()
    {
        return view('livewire.billing.invoice-manager', [
            'invoices' => $this->invoices,
            'estates' => $this->estates,
            'houses' => $this->houses,
            'statistics' => $this->statistics,
        ]);
    }
}
