<?php

namespace App\Livewire\Billing;

use App\Models\House;
use App\Models\Invoice;
use App\Models\WaterRate;
use App\Services\InvoiceGenerationService;
use Livewire\Component;
use Livewire\WithFileUploads;

class InvoiceForm extends Component
{
    use WithFileUploads;

    public $invoice;

    public $mode = 'create'; // create or edit

    // Invoice fields
    public $house_id;

    public $period_start;

    public $period_end;

    public $current_reading;

    public $previous_reading;

    public $consumption;

    public $amount;

    public $notes;

    public $status = 'pending';

    // Collections
    public $houses = [];

    public $waterRates = [];

    protected function rules()
    {
        return [
            'house_id' => 'required|exists:houses,id',
            'period_start' => 'required|date',
            'period_end' => 'required|date|after:period_start',
            'current_reading' => 'required|numeric|min:0',
            'previous_reading' => 'required|numeric|min:0',
            'consumption' => 'required|numeric|min:0',
            'amount' => 'required|numeric|min:0',
            'notes' => 'nullable|string|max:1000',
            'status' => 'required|in:draft,pending,sent,paid,overdue,cancelled',
        ];
    }

    protected $messages = [
        'house_id.required' => 'Please select a house.',
        'house_id.exists' => 'The selected house does not exist.',
        'period_start.required' => 'Period start date is required.',
        'period_end.required' => 'Period end date is required.',
        'period_end.after' => 'Period end must be after period start.',
        'current_reading.required' => 'Current reading is required.',
        'current_reading.numeric' => 'Current reading must be a number.',
        'current_reading.min' => 'Current reading cannot be negative.',
        'previous_reading.required' => 'Previous reading is required.',
        'previous_reading.numeric' => 'Previous reading must be a number.',
        'previous_reading.min' => 'Previous reading cannot be negative.',
        'consumption.required' => 'Consumption is required.',
        'consumption.numeric' => 'Consumption must be a number.',
        'consumption.min' => 'Consumption cannot be negative.',
        'amount.required' => 'Amount is required.',
        'amount.numeric' => 'Amount must be a number.',
        'amount.min' => 'Amount cannot be negative.',
        'status.required' => 'Status is required.',
        'status.in' => 'Invalid status selected.',
    ];

    public function mount($invoiceId = null)
    {
        if ($invoiceId) {
            $this->mode = 'edit';
            $this->loadInvoice($invoiceId);
        } else {
            $this->mode = 'create';
            $this->setDefaultValues();
        }

        $this->loadCollections();
    }

    private function loadInvoice($invoiceId)
    {
        $this->invoice = Invoice::findOrFail($invoiceId);
        $this->house_id = $this->invoice->house_id;
        $this->period_start = $this->invoice->period_start->format('Y-m-d');
        $this->period_end = $this->invoice->period_end->format('Y-m-d');
        $this->current_reading = $this->invoice->current_reading;
        $this->previous_reading = $this->invoice->previous_reading;
        $this->consumption = $this->invoice->consumption;
        $this->amount = $this->invoice->amount;
        $this->notes = $this->invoice->notes;
        $this->status = $this->invoice->status;
    }

    private function setDefaultValues()
    {
        $this->period_start = now()->startOfMonth()->format('Y-m-d');
        $this->period_end = now()->endOfMonth()->format('Y-m-d');
        $this->status = 'pending';
    }

    private function loadCollections()
    {
        $this->houses = House::with('estate')->orderBy('house_number')->get();
        $this->waterRates = WaterRate::active()->get();
    }

    public function updated($propertyName)
    {
        if (in_array($propertyName, ['current_reading', 'previous_reading'])) {
            $this->calculateConsumption();
        }
    }

    public function calculateConsumption()
    {
        if ($this->current_reading && $this->previous_reading) {
            $this->consumption = max(0, $this->current_reading - $this->previous_reading);
            $this->calculateAmount();
        }
    }

    public function calculateAmount()
    {
        if ($this->consumption > 0) {
            $invoiceGenerationService = new InvoiceGenerationService;
            $this->amount = $invoiceGenerationService->calculateAmount($this->consumption);
        }
    }

    public function autoCalculateFromHouse()
    {
        if ($this->house_id) {
            $house = House::find($this->house_id);

            // Get the last reading for this house
            $lastReading = $house->meterReadings()
                ->orderBy('reading_date', 'desc')
                ->first();

            if ($lastReading) {
                $this->previous_reading = $lastReading->reading;
                $this->period_start = $lastReading->reading_date->format('Y-m-d');
            }
        }
    }

    public function save()
    {
        $this->validate();

        $invoiceData = [
            'house_id' => $this->house_id,
            'period_start' => $this->period_start,
            'period_end' => $this->period_end,
            'current_reading' => $this->current_reading,
            'previous_reading' => $this->previous_reading,
            'consumption' => $this->consumption,
            'amount' => $this->amount,
            'notes' => $this->notes,
            'status' => $this->status,
        ];

        if ($this->mode === 'create') {
            $invoice = Invoice::create($invoiceData);
            $message = 'Invoice created successfully.';
        } else {
            $this->invoice->update($invoiceData);
            $invoice = $this->invoice;
            $message = 'Invoice updated successfully.';
        }

        session()->flash('message', $message);

        return redirect()->route('invoices.show', $invoice);
    }

    public function saveAsDraft()
    {
        $this->status = 'draft';

        return $this->save();
    }

    public function submitForApproval()
    {
        $this->status = 'pending';

        return $this->save();
    }

    public function render()
    {
        return view('livewire.billing.invoice-form', [
            'mode' => $this->mode,
            'invoice' => $this->invoice,
        ]);
    }
}
