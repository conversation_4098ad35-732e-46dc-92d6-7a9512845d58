<?php

namespace App\Livewire\Management;

use App\Models\Estate;
use App\Models\House;
use App\Traits\HasPermissions;
use Livewire\Component;
use Livewire\WithPagination;

class PropertyManager extends Component
{
    use HasPermissions;
    use WithPagination;

    public $activeTab = 'estates'; // estates or houses

    // Estate management properties
    public $showEstateModal = false;

    public $editingEstateId;

    public $deletingEstateId;

    public $estateSearch = '';

    public $estateStatusFilter = '';

    public $estateSortBy = 'created_at';

    public $estateSortDirection = 'desc';

    // House management properties
    public $showHouseModal = false;

    public $editingHouseId;

    public $deletingHouseId;

    public $houseSearch = '';

    public $houseStatusFilter = '';

    public $houseEstateFilter = '';

    public $houseSortBy = 'created_at';

    public $houseSortDirection = 'desc';

    // Form data
    public $estateForm = [
        'name' => '',
        'code' => '',
        'address' => '',
        'city' => '',
        'state' => '',
        'postal_code' => '',
        'country' => 'Kenya',
        'contact_email' => '',
        'contact_phone' => '',
        'is_active' => true,
    ];

    public $houseForm = [
        'house_number' => '',
        'address' => '',
        'estate_id' => '',
        'status' => 'active',
        'meter_number' => '',
        'meter_type' => 'analog',
        'is_active' => true,
    ];

    protected function rules()
    {
        $estateRules = [
            'estateForm.name' => 'required|string|max:255',
            'estateForm.code' => 'required|string|max:50|unique:estates,code',
            'estateForm.address' => 'nullable|string|max:500',
            'estateForm.city' => 'required|string|max:100',
            'estateForm.state' => 'nullable|string|max:100',
            'estateForm.postal_code' => 'nullable|string|max:20',
            'estateForm.country' => 'nullable|string|max:100',
            'estateForm.contact_email' => 'nullable|email|max:255',
            'estateForm.contact_phone' => 'nullable|string|max:50',
            'estateForm.is_active' => 'boolean',
        ];

        $houseRules = [
            'houseForm.house_number' => 'required|string|max:50',
            'houseForm.address' => 'nullable|string|max:500',
            'houseForm.estate_id' => 'required|exists:estates,id',
            'houseForm.status' => 'required|in:active,inactive,maintenance',
            'houseForm.meter_number' => 'nullable|string|max:50',
            'houseForm.meter_type' => 'required|in:analog,digital,smart',
            'houseForm.is_active' => 'boolean',
        ];

        if ($this->editingEstateId) {
            $estateRules['estateForm.code'] .= ','.$this->editingEstateId;
        }

        if ($this->editingHouseId) {
            $houseRules['houseForm.house_number'] .= ',house_number,'.$this->editingHouseId;
        }

        return array_merge($estateRules, $houseRules);
    }

    public function mount()
    {
        // Authorization is handled by route middleware
    }

    // Estate Management Methods
    public function openEstateModal($estateId = null)
    {
        $this->reset('estateForm');
        $this->estateForm['country'] = 'Kenya';
        $this->estateForm['is_active'] = true;

        if ($estateId) {
            $estate = Estate::findOrFail($estateId);
            $this->estateForm = [
                'name' => $estate->name,
                'code' => $estate->code,
                'address' => $estate->address ?? '',
                'city' => $estate->city ?? '',
                'state' => $estate->state ?? '',
                'postal_code' => $estate->postal_code ?? '',
                'country' => $estate->country ?? 'Kenya',
                'contact_email' => $estate->contact_email ?? '',
                'contact_phone' => $estate->contact_phone ?? '',
                'is_active' => $estate->is_active,
            ];
            $this->editingEstateId = $estateId;
        } else {
            $this->editingEstateId = null;
        }

        $this->showEstateModal = true;
    }

    public function confirmDeleteEstate($estateId)
    {
        $this->deletingEstateId = $estateId;
    }

    public function saveEstate()
    {
        $this->validate();

        if ($this->editingEstateId) {
            $estate = Estate::findOrFail($this->editingEstateId);
            $estate->update($this->estateForm);
            $message = 'Estate updated successfully!';
        } else {
            Estate::create($this->estateForm);
            $message = 'Estate created successfully!';
        }

        $this->closeEstateModal();
        session()->flash('message', $message);
    }

    public function deleteEstate()
    {
        if ($this->deletingEstateId) {
            $estate = Estate::findOrFail($this->deletingEstateId);

            // Check if estate has houses
            if ($estate->houses()->count() > 0) {
                session()->flash('error', 'Cannot delete estate that has houses associated with it.');

                return;
            }

            // Check if estate has water rates
            if ($estate->waterRates()->count() > 0) {
                session()->flash('error', 'Cannot delete estate that has water rates associated with it.');

                return;
            }

            $estate->delete();
            $this->deletingEstateId = null;
            session()->flash('message', 'Estate deleted successfully!');
        }
    }

    public function closeEstateModal()
    {
        $this->showEstateModal = false;
        $this->editingEstateId = null;
        $this->reset('estateForm');
    }

    // House Management Methods
    public function openHouseModal($houseId = null)
    {
        $this->reset('houseForm');
        $this->houseForm['status'] = 'active';
        $this->houseForm['meter_type'] = 'analog';
        $this->houseForm['is_active'] = true;

        if ($houseId) {
            $house = House::findOrFail($houseId);
            $this->houseForm = [
                'house_number' => $house->house_number,
                'address' => $house->address ?? '',
                'estate_id' => $house->estate_id,
                'status' => $house->status ?? 'active',
                'meter_number' => $house->meter_number ?? '',
                'meter_type' => $house->meter_type ?? 'analog',
                'is_active' => $house->is_active,
            ];
            $this->editingHouseId = $houseId;
        } else {
            $this->editingHouseId = null;
        }

        $this->showHouseModal = true;
    }

    public function confirmDeleteHouse($houseId)
    {
        $this->deletingHouseId = $houseId;
    }

    public function saveHouse()
    {
        $this->validate();

        if ($this->editingHouseId) {
            $house = House::findOrFail($this->editingHouseId);
            $house->update($this->houseForm);
            $message = 'House updated successfully!';
        } else {
            House::create($this->houseForm);
            $message = 'House created successfully!';
        }

        $this->closeHouseModal();
        session()->flash('message', $message);
    }

    public function deleteHouse()
    {
        if ($this->deletingHouseId) {
            $house = House::findOrFail($this->deletingHouseId);

            // Check if house has contacts
            if ($house->contacts()->count() > 0) {
                session()->flash('error', 'Cannot delete house that has contacts associated with it.');

                return;
            }

            // Check if house has meter readings
            if ($house->meterReadings()->count() > 0) {
                session()->flash('error', 'Cannot delete house that has meter readings associated with it.');

                return;
            }

            // Check if house has invoices
            if ($house->invoices()->count() > 0) {
                session()->flash('error', 'Cannot delete house that has invoices associated with it.');

                return;
            }

            $house->delete();
            $this->deletingHouseId = null;
            session()->flash('message', 'House deleted successfully!');
        }
    }

    public function closeHouseModal()
    {
        $this->showHouseModal = false;
        $this->editingHouseId = null;
        $this->reset('houseForm');
    }

    // Data Properties
    public function getEstatesProperty()
    {
        $query = Estate::withCount(['houses', 'houses as occupied_houses_count' => function ($query): void {
            $query->whereHas('contacts');
        }]);

        if ($this->estateSearch) {
            $query->where('name', 'like', '%'.$this->estateSearch.'%')
                ->orWhere('code', 'like', '%'.$this->estateSearch.'%');
        }

        if ($this->estateStatusFilter) {
            $query->where('is_active', $this->estateStatusFilter === 'active');
        }

        $query->orderBy($this->estateSortBy, $this->estateSortDirection);

        return $query->paginate(10);
    }

    public function getHousesProperty()
    {
        $builder = House::with(['estate', 'contacts']);

        if ($this->houseSearch) {
            $builder->where('house_number', 'like', '%'.$this->houseSearch.'%')
                ->orWhere('address', 'like', '%'.$this->houseSearch.'%');
        }

        if ($this->houseStatusFilter) {
            $builder->where('status', $this->houseStatusFilter);
        }

        if ($this->houseEstateFilter) {
            $builder->where('estate_id', $this->houseEstateFilter);
        }

        $builder->orderBy($this->houseSortBy, $this->houseSortDirection);

        return $builder->paginate(10);
    }

    public function getEstatesForFilterProperty()
    {
        return Estate::orderBy('name')->get();
    }

    public function getEstateStatisticsProperty()
    {
        return [
            'total_estates' => Estate::count(),
            'active_estates' => Estate::where('is_active', true)->count(),
            'inactive_estates' => Estate::where('is_active', false)->count(),
            'total_houses' => House::count(),
            'occupied_houses' => House::whereHas('contacts')->count(),
        ];
    }

    public function getHouseStatisticsProperty()
    {
        $query = House::query();

        if ($this->houseEstateFilter) {
            $query->where('estate_id', $this->houseEstateFilter);
        }

        return [
            'total_houses' => (clone $query)->count(),
            'active_houses' => (clone $query)->where('status', 'active')->count(),
            'inactive_houses' => (clone $query)->where('status', 'inactive')->count(),
            'maintenance_houses' => (clone $query)->where('status', 'maintenance')->count(),
            'occupied_houses' => (clone $query)->whereHas('contacts')->count(),
        ];
    }

    // Utility Methods
    public function getUserRole()
    {
        if ($this->isAdmin()) {
            return 'admin';
        }
        if ($this->hasPermission('estate.view_all')) {
            return 'manager';
        }
        if ($this->hasPermission('billing.view_all')) {
            return 'reviewer';
        }
        if ($this->hasPermission('estate.view_assigned')) {
            return 'caretaker';
        }

        return 'guest';
    }

    public function getEstateShowRoute($estate)
    {
        $role = $this->getUserRole();

        return match ($role) {
            'manager' => route('management.estates.show', $estate),
            'reviewer' => route('management.estates.show', $estate),
            'caretaker' => route('caretaker.estates.show', $estate),
            default => route('estates.show', $estate),
        };
    }

    public function getHouseShowRoute($house)
    {
        $role = $this->getUserRole();

        return match ($role) {
            'manager' => route('management.houses.show', $house),
            'reviewer' => route('reviewer.houses.show', $house),
            'caretaker' => route('caretaker.houses.show', $house),
            default => route('houses.show', $house),
        };
    }

    public function render()
    {
        return view('livewire.management.property-manager', [
            'estates' => $this->estates,
            'houses' => $this->houses,
            'estatesForFilter' => $this->estatesForFilter,
            'estateStatistics' => $this->estateStatistics,
            'houseStatistics' => $this->houseStatistics,
            'activeTab' => $this->activeTab,
        ]);
    }
}
