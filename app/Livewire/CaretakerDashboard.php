<?php

namespace App\Livewire;

use App\Models\Estate;
use App\Models\House;
use App\Models\MeterReading;
use App\Traits\HasPermissions;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Livewire\Component;
use Livewire\WithPagination;

class CaretakerDashboard extends Component
{
    use HasPermissions, WithPagination;

    public $selectedEstate;

    public $selectedStatus = 'all';

    public $search = '';

    public $dateRange = 'this_month';

    public $showQuickAddReadingForm = false;

    public $selectedHouseForQuickReading;

    public $quickCurrentReading = '';

    public $quickNotes = '';

    public function mount()
    {
        // Check if user has caretaker permissions using permission-based access
        if (! $this->hasAnyPermission(['readings.create_all', 'readings.create_assigned', 'readings.view_assigned'])) {
            abort(403, 'Unauthorized access to caretaker dashboard');
        }

        // Default to first estate if only one
        if ($this->getEstateAccessScope() === 'assigned' && $this->currentUser()->assignedEstates()->count() === 1) {
            $this->selectedEstate = $this->currentUser()->assignedEstates()->first()->id;
        }
    }

    public function getEstatesProperty()
    {
        if ($this->hasPermission('estates.view_all')) {
            return Estate::orderBy('name')->get();
        } elseif ($this->hasPermission('estates.view_assigned')) {
            return $this->currentUser()->assignedEstates()->orderBy('name')->get();
        } else {
            return collect();
        }
    }

    public function getReadingsProperty()
    {
        $builder = MeterReading::with(['house.estate', 'house.contacts'])
            ->when($this->selectedEstate, function ($query): void {
                $query->whereHas('house', function ($q): void {
                    $q->where('estate_id', $this->selectedEstate);
                });
            })
            ->when($this->selectedStatus !== 'all', function ($query): void {
                $query->where('status', $this->selectedStatus);
            })
            ->when($this->search, function ($query): void {
                $query->whereHas('house', function ($q): void {
                    $q->where('house_number', 'like', '%'.$this->search.'%')
                        ->orWhere('address', 'like', '%'.$this->search.'%');
                });
            })
            ->when($this->dateRange, function ($query): void {
                switch ($this->dateRange) {
                    case 'today':
                        $query->whereDate('reading_date', today());
                        break;
                    case 'this_week':
                        $query->whereBetween('reading_date', [now()->startOfWeek(), now()->endOfWeek()]);
                        break;
                    case 'this_month':
                        $query->whereMonth('reading_date', now()->month);
                        break;
                    case 'last_month':
                        $query->whereMonth('reading_date', now()->subMonth()->month);
                        break;
                }
            })
            ->orderBy('reading_date', 'desc');

        // Apply permission-based scope filtering
        if ($this->hasPermission('readings.view_all')) {
            // No additional filtering needed
        } elseif ($this->hasPermission('readings.view_assigned')) {
            $query->whereHas('house', function ($q): void {
                $q->whereIn('estate_id', $this->currentUser()->assignedEstates()->pluck('id'));
            });
        } else {
            // If user can only view own readings
            $builder->where('user_id', $this->currentUser()->id);
        }

        return $builder->paginate(10);
    }

    public function getStatsProperty()
    {
        $baseQuery = MeterReading::when($this->selectedEstate, function ($query): void {
            $query->whereHas('house', function ($q): void {
                $q->where('estate_id', $this->selectedEstate);
            });
        });

        // Apply permission-based scope filtering
        if ($this->hasPermission('readings.view_all')) {
            // No additional filtering needed
        } elseif ($this->hasPermission('readings.view_assigned')) {
            $baseQuery->whereHas('house', function ($q): void {
                $q->whereIn('estate_id', $this->currentUser()->assignedEstates()->pluck('id'));
            });
        } else {
            // If user can only view own readings
            $baseQuery->where('user_id', $this->currentUser()->id);
        }

        return [
            'total_readings' => $baseQuery->count(),
            'draft_readings' => (clone $baseQuery)->where('status', 'draft')->count(),
            'submitted_readings' => (clone $baseQuery)->where('status', 'submitted')->count(),
            'approved_readings' => (clone $baseQuery)->where('status', 'approved')->count(),
            'rejected_readings' => (clone $baseQuery)->where('status', 'rejected')->count(),
            'today_readings' => (clone $baseQuery)->whereDate('reading_date', today())->count(),
        ];
    }

    public function getPendingHousesProperty()
    {
        if (! $this->selectedEstate) {
            return collect();
        }

        return House::where('estate_id', $this->selectedEstate)
            ->where('is_active', true)
            ->whereDoesntHave('meterReadings', function ($query): void {
                $query->where('user_id', Auth::id())
                    ->whereMonth('reading_date', now()->month);
            })
            ->orderBy('house_number')
            ->get();
    }

    public function deleteReading($readingId)
    {
        $reading = MeterReading::findOrFail($readingId);

        // Check permissions using permission-based access
        if ($reading->status !== 'draft') {
            abort(403, 'Can only delete draft readings');
        }

        if (! $this->hasPermission('readings.delete_all') && (! $this->hasPermission('readings.delete_assigned') || ! $this->isReadingInAssignedEstate($reading))) {
            abort(403, 'You do not have permission to delete this reading');
        }

        if ($reading->photo_path) {
            Storage::disk('public')->delete($reading->photo_path);
        }

        $reading->delete();

        session()->flash('message', 'Reading deleted successfully!');
    }

    /**
     * Check if reading belongs to user's assigned estates
     */
    private function isReadingInAssignedEstate($reading)
    {
        if ($this->hasPermission('estates.view_all')) {
            return true;
        }

        if ($this->hasPermission('estates.view_assigned')) {
            return $reading->house->estate_id === $this->currentUser()->estate_id;
        }

        return false;
    }

    public function submitForReview($readingId)
    {
        $reading = MeterReading::findOrFail($readingId);

        if ($reading->status !== 'draft') {
            abort(403, 'Can only submit draft readings for review');
        }

        if (! $this->hasPermission('readings.submit_all') && (! $this->hasPermission('readings.submit_assigned') || ! $this->isReadingInAssignedEstate($reading))) {
            abort(403, 'You do not have permission to submit this reading for review');
        }

        $reading->update([
            'status' => 'submitted',
            'submitted_at' => now(),
        ]);

        session()->flash('message', 'Reading submitted for review successfully!');
    }

    public function addQuickReading($houseId)
    {
        $this->selectedHouseForQuickReading = $houseId;
        $this->quickCurrentReading = '';
        $this->quickNotes = '';
        $this->showQuickAddReadingForm = true;
        $this->resetErrorBag();
    }

    public function cancelQuickAddReading()
    {
        $this->showQuickAddReadingForm = false;
        $this->selectedHouseForQuickReading = null;
        $this->resetErrorBag();
    }

    public function saveQuickReading()
    {
        $this->validate([
            'quickCurrentReading' => 'required|numeric|min:0',
            'quickNotes' => 'nullable|string|max:500',
        ]);

        $house = House::findOrFail($this->selectedHouseForQuickReading);

        // Check if user has permission to create readings for this house
        if (! $this->canCreateReadingForHouse($house)) {
            abort(403, 'You do not have permission to create readings for this house');
        }

        // Check if a reading for today already exists for this house and user
        $existingReadingToday = MeterReading::where('house_id', $this->selectedHouseForQuickReading)
            ->whereDate('reading_date', now()->toDateString())
            ->first();

        if ($existingReadingToday) {
            session()->flash('error', 'A reading for this house has already been submitted today.');
            $this->cancelQuickAddReading();

            return;
        }

        MeterReading::create([
            'house_id' => $this->selectedHouseForQuickReading,
            'user_id' => $this->currentUser()->id,
            'current_reading' => $this->quickCurrentReading,
            'reading_date' => now()->toDateString(),
            'status' => 'draft', // Save as draft
            'notes' => $this->quickNotes,
            'reading_method' => 'manual', // Assuming manual entry
        ]);

        session()->flash('message', 'Reading submitted as draft successfully!');
        $this->cancelQuickAddReading();
    }

    /**
     * Check if user can create readings for a specific house
     */
    private function canCreateReadingForHouse($house)
    {
        if ($this->hasPermission('readings.create_all')) {
            return true;
        }

        if ($this->hasPermission('readings.create_assigned')) {
            return $house->estate_id === $this->currentUser()->estate_id;
        }

        return false;
    }

    public function render()
    {
        return view('livewire.caretaker-dashboard', [
            'readings' => $this->readings,
            'stats' => $this->stats,
            'pendingHouses' => $this->pendingHouses,
            'estates' => $this->estates,
        ]);
    }
}
