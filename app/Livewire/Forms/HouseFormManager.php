<?php

namespace App\Livewire\Forms;

use App\Models\Estate;
use App\Models\House;
use Illuminate\Support\Facades\Auth;

class HouseFormManager extends UnifiedFormManager
{
    public $estateId;

    public $estates = [];

    public $estateFilter = '';

    public $formDefaults = [
        'estate_id' => '',
        'house_number' => '',
        'address' => '',
        'unit_type' => 'apartment',
        'occupancy_status' => 'vacant',
        'is_active' => true,
        'notes' => '',
    ];

    protected function getModelClass()
    {
        return House::class;
    }

    protected function getValidationRules()
    {
        $rules = [
            'form.estate_id' => 'required|exists:estates,id',
            'form.house_number' => 'required|string|max:50',
            'form.address' => 'nullable|string|max:500',
            'form.unit_type' => 'required|in:apartment,house,studio,office,other',
            'form.occupancy_status' => 'required|in:vacant,occupied,maintenance',
            'form.is_active' => 'boolean',
            'form.notes' => 'nullable|string|max:1000',
        ];

        if ($this->editingId) {
            $rules['form.house_number'] = 'required|string|max:50|unique:houses,house_number,'.$this->editingId.',id,estate_id,'.$this->form['estate_id'];
        } else {
            $rules['form.house_number'] = 'required|string|max:50|unique:houses,house_number,NULL,id,estate_id,'.$this->form['estate_id'];
        }

        return $rules;
    }

    protected function getFormFields()
    {
        return [
            'estate_id' => ['type' => 'select', 'label' => 'Estate', 'required' => true, 'options' => $this->estates],
            'house_number' => ['type' => 'text', 'label' => 'House Number', 'required' => true],
            'address' => ['type' => 'textarea', 'label' => 'Address'],
            'unit_type' => ['type' => 'select', 'label' => 'Unit Type', 'required' => true, 'options' => [
                'apartment' => 'Apartment',
                'house' => 'House',
                'studio' => 'Studio',
                'office' => 'Office',
                'other' => 'Other',
            ]],
            'occupancy_status' => ['type' => 'select', 'label' => 'Occupancy Status', 'required' => true, 'options' => [
                'vacant' => 'Vacant',
                'occupied' => 'Occupied',
                'maintenance' => 'Under Maintenance',
            ]],
            'is_active' => ['type' => 'checkbox', 'label' => 'Active'],
            'notes' => ['type' => 'textarea', 'label' => 'Notes'],
        ];
    }

    protected function prepareSaveData()
    {
        return $this->form;
    }

    protected function loadEntityData($entity)
    {
        $this->form = [
            'estate_id' => $entity->estate_id,
            'house_number' => $entity->house_number,
            'address' => $entity->address ?? '',
            'unit_type' => $entity->unit_type,
            'occupancy_status' => $entity->occupancy_status,
            'is_active' => $entity->is_active,
            'notes' => $entity->notes ?? '',
        ];
    }

    protected function getListView()
    {
        return 'livewire.forms.house-form-manager';
    }

    protected function getFormView()
    {
        return 'livewire.forms.house-form';
    }

    protected function getSearchableFields()
    {
        return ['house_number', 'address'];
    }

    protected function applyFilter($query, $field, $value)
    {
        return match ($field) {
            'estate_id' => $query->where('estate_id', $value),
            'occupancy_status' => $query->where('occupancy_status', $value),
            'is_active' => $query->where('is_active', $value),
            default => parent::applyFilter($query, $field, $value),
        };
    }

    protected function canDeleteItem($item)
    {
        // Check if house has meter readings
        if ($item->meterReadings()->count() > 0) {
            session()->flash('error', 'Cannot delete house that has meter readings associated with it.');

            return false;
        }

        // Check if house has invoices
        if ($item->invoices()->count() > 0) {
            session()->flash('error', 'Cannot delete house that has invoices associated with it.');

            return false;
        }

        // Check if house has contacts
        if ($item->contacts()->count() > 0) {
            session()->flash('error', 'Cannot delete house that has contacts associated with it.');

            return false;
        }

        return true;
    }

    protected function getStatistics()
    {
        $query = House::query();

        if ($this->estateFilter) {
            $query->where('estate_id', $this->estateFilter);
        }

        return [
            'total_houses' => $query->count(),
            'occupied_houses' => (clone $query)->where('occupancy_status', 'occupied')->count(),
            'vacant_houses' => (clone $query)->where('occupancy_status', 'vacant')->count(),
            'active_houses' => (clone $query)->where('is_active', true)->count(),
        ];
    }

    protected function checkPermissions()
    {
        if (! Auth::user()->can('houses.view')) {
            abort(403, 'Unauthorized action.');
        }
    }

    public function mount()
    {
        parent::mount();
        $this->loadEstates();

        // Set estate filter from route parameter if available
        if (request()->has('estateId')) {
            $this->estateFilter = request('estateId');
            $this->filters['estate_id'] = $this->estateFilter;
        }
    }

    protected function loadEstates()
    {
        $user = Auth::user();

        if ($user->can('estates.manage_all')) {
            $this->estates = Estate::orderBy('name')->get();
        } else {
            $this->estates = Estate::where('id', $user->estate_id)->orderBy('name')->get();
        }
    }

    public function updatedEstateFilter($value)
    {
        $this->filters['estate_id'] = $value;
        $this->resetPage();
    }

    public function getFilteredEstatesProperty()
    {
        return $this->estates;
    }

    public function getHouseShowRoute($house)
    {
        $role = Auth::user()->role->value;

        return match ($role) {
            'manager' => route('management.houses.show', $house),
            'reviewer' => route('reviewer.houses.show', $house),
            'caretaker' => route('caretaker.houses.show', $house),
            default => route('houses.show', $house),
        };
    }

    public function getMeterReadingsRoute($houseId)
    {
        $role = Auth::user()->role->value;

        return match ($role) {
            'manager' => route('management.houses.meter-readings', $houseId),
            'reviewer' => route('reviewer.houses.meter-readings', $houseId),
            'caretaker' => route('caretaker.houses.meter-readings', $houseId),
            default => route('houses.meter-readings', $houseId),
        };
    }

    public function getInvoicesRoute($houseId)
    {
        $role = Auth::user()->role->value;

        return match ($role) {
            'manager' => route('management.houses.invoices', $houseId),
            'reviewer' => route('reviewer.houses.invoices', $houseId),
            'caretaker' => route('caretaker.houses.invoices', $houseId),
            default => route('houses.invoices', $houseId),
        };
    }
}
