<?php

namespace App\Livewire\Forms;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Session;
use Illuminate\Validation\Rules\Password as PasswordRule;
use Illuminate\Validation\ValidationException;
use Livewire\Component;

class SettingsManager extends Component
{
    // Profile fields
    public string $name = '';

    public string $email = '';

    // Password fields
    public string $current_password = '';

    public string $password = '';

    public string $password_confirmation = '';

    // Appearance fields
    public string $theme = 'light';

    public string $language = 'en';

    public int $pagination_limit = 10;

    // Active tab
    public string $activeTab = 'profile';

    protected function rules()
    {
        return [
            // Profile validation rules
            'name' => ['required', 'string', 'max:255'],
            'email' => [
                'required',
                'string',
                'lowercase',
                'email',
                'max:255',
                'unique:users,email,'.Auth::id(),
            ],

            // Password validation rules
            'current_password' => ['required_if:activeTab,password', 'string', 'current_password'],
            'password' => ['required_if:activeTab,password', 'string', PasswordRule::defaults(), 'confirmed'],

            // Appearance validation rules
            'theme' => ['required', 'in:light,dark,auto'],
            'language' => ['required', 'in:en,sw,fr'],
            'pagination_limit' => ['required', 'integer', 'min:5', 'max:100'],
        ];
    }

    protected $messages = [
        'name.required' => 'Your name is required.',
        'email.required' => 'Your email address is required.',
        'email.email' => 'Please enter a valid email address.',
        'email.unique' => 'This email address is already in use.',
        'current_password.required_if' => 'Current password is required to change password.',
        'current_password.current_password' => 'Current password is incorrect.',
        'password.required_if' => 'New password is required.',
        'password.confirmed' => 'Password confirmation does not match.',
        'theme.required' => 'Please select a theme.',
        'language.required' => 'Please select a language.',
        'pagination_limit.required' => 'Pagination limit is required.',
    ];

    public function mount()
    {
        $user = Auth::user();

        // Load profile data
        $this->name = $user->name;
        $this->email = $user->email;

        // Load appearance settings
        $this->theme = $user->settings['theme'] ?? 'light';
        $this->language = $user->settings['language'] ?? 'en';
        $this->pagination_limit = $user->settings['pagination_limit'] ?? 10;
    }

    public function updateProfile()
    {
        $this->activeTab = 'profile';
        $this->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => [
                'required',
                'string',
                'lowercase',
                'email',
                'max:255',
                'unique:users,email,'.Auth::id(),
            ],
        ]);

        $user = Auth::user();
        $user->fill([
            'name' => $this->name,
            'email' => $this->email,
        ]);

        if ($user->isDirty('email')) {
            $user->email_verified_at = null;
        }

        $user->save();

        $this->dispatch('profile-updated', name: $user->name);
        session()->flash('message', 'Profile updated successfully!');
    }

    public function updatePassword()
    {
        $this->activeTab = 'password';

        try {
            $this->validate([
                'current_password' => ['required', 'string', 'current_password'],
                'password' => ['required', 'string', PasswordRule::defaults(), 'confirmed'],
            ]);
        } catch (ValidationException $e) {
            $this->reset('current_password', 'password', 'password_confirmation');
            throw $e;
        }

        Auth::user()->update([
            'password' => Hash::make($this->password),
        ]);

        $this->reset('current_password', 'password', 'password_confirmation');

        $this->dispatch('password-updated');
        session()->flash('message', 'Password updated successfully!');
    }

    public function updateAppearance()
    {
        $this->activeTab = 'appearance';
        $this->validate([
            'theme' => ['required', 'in:light,dark,auto'],
            'language' => ['required', 'in:en,sw,fr'],
            'pagination_limit' => ['required', 'integer', 'min:5', 'max:100'],
        ]);

        $user = Auth::user();
        $settings = $user->settings ?? [];

        $settings['theme'] = $this->theme;
        $settings['language'] = $this->language;
        $settings['pagination_limit'] = $this->pagination_limit;

        $user->settings = $settings;
        $user->save();

        $this->dispatch('appearance-updated');
        session()->flash('message', 'Appearance settings updated successfully!');
    }

    public function resendVerificationNotification()
    {
        $user = Auth::user();

        if ($user->hasVerifiedEmail()) {
            $this->redirectIntended(default: route('dashboard', absolute: false));

            return;
        }

        $user->sendEmailVerificationNotification();
        Session::flash('status', 'verification-link-sent');
    }

    public function deleteAccount()
    {
        $this->validate([
            'current_password' => ['required', 'string', 'current_password'],
        ]);

        $user = Auth::user();

        Auth::logout();

        $user->delete();

        session()->invalidate();
        session()->regenerateToken();

        return redirect('/');
    }

    public function render()
    {
        return view('livewire.forms.settings-manager', [
            'user' => Auth::user(),
        ]);
    }
}
