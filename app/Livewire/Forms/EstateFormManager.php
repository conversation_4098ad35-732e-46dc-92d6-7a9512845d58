<?php

namespace App\Livewire\Forms;

use App\Models\Estate;
use App\Traits\HasPermissions;

class EstateFormManager extends UnifiedFormManager
{
    use HasPermissions;

    public $formDefaults = [
        'name' => '',
        'code' => '',
        'address' => '',
        'city' => '',
        'state' => '',
        'postal_code' => '',
        'country' => 'Kenya',
        'contact_email' => '',
        'contact_phone' => '',
        'is_active' => true,
    ];

    protected function getModelClass()
    {
        return Estate::class;
    }

    protected function getValidationRules()
    {
        $rules = [
            'form.name' => 'required|string|max:255',
            'form.code' => 'required|string|max:50|unique:estates,code',
            'form.address' => 'nullable|string|max:500',
            'form.city' => 'required|string|max:100',
            'form.state' => 'nullable|string|max:100',
            'form.postal_code' => 'nullable|string|max:20',
            'form.country' => 'nullable|string|max:100',
            'form.contact_email' => 'nullable|email|max:255',
            'form.contact_phone' => 'nullable|string|max:50',
            'form.is_active' => 'boolean',
        ];

        if ($this->editingId) {
            $rules['form.code'] = 'required|string|max:50|unique:estates,code,'.$this->editingId;
        }

        return $rules;
    }

    protected function getFormFields()
    {
        return [
            'name' => ['type' => 'text', 'label' => 'Estate Name', 'required' => true],
            'code' => ['type' => 'text', 'label' => 'Estate Code', 'required' => true],
            'address' => ['type' => 'textarea', 'label' => 'Address'],
            'city' => ['type' => 'text', 'label' => 'City', 'required' => true],
            'state' => ['type' => 'text', 'label' => 'State'],
            'postal_code' => ['type' => 'text', 'label' => 'Postal Code'],
            'country' => ['type' => 'text', 'label' => 'Country'],
            'contact_email' => ['type' => 'email', 'label' => 'Contact Email'],
            'contact_phone' => ['type' => 'tel', 'label' => 'Contact Phone'],
            'is_active' => ['type' => 'checkbox', 'label' => 'Active'],
        ];
    }

    protected function prepareSaveData()
    {
        return $this->form;
    }

    protected function loadEntityData($entity)
    {
        $this->form = [
            'name' => $entity->name,
            'code' => $entity->code,
            'address' => $entity->address ?? '',
            'city' => $entity->city ?? '',
            'state' => $entity->state ?? '',
            'postal_code' => $entity->postal_code ?? '',
            'country' => $entity->country ?? 'Kenya',
            'contact_email' => $entity->contact_email ?? '',
            'contact_phone' => $entity->contact_phone ?? '',
            'is_active' => $entity->is_active,
        ];
    }

    protected function getListView()
    {
        return 'livewire.forms.estate-form-manager';
    }

    protected function getFormView()
    {
        return 'livewire.forms.estate-form';
    }

    protected function getSearchableFields()
    {
        return ['name', 'code', 'city'];
    }

    protected function canDeleteItem($item)
    {
        // Check if estate has houses
        if ($item->houses()->count() > 0) {
            session()->flash('error', 'Cannot delete estate that has houses associated with it.');

            return false;
        }

        // Check if estate has water rates
        if ($item->waterRates()->count() > 0) {
            session()->flash('error', 'Cannot delete estate that has water rates associated with it.');

            return false;
        }

        return true;
    }

    protected function getStatistics()
    {
        return [
            'total_estates' => Estate::count(),
            'active_estates' => Estate::where('is_active', true)->count(),
            'inactive_estates' => Estate::where('is_active', false)->count(),
        ];
    }

    protected function checkPermissions()
    {
        if (! Auth::user()->can('estates.view_all') && ! Auth::user()->can('estates.view_assigned')) {
            abort(403, 'Unauthorized action.');
        }
    }

    public function getUserRole()
    {
        if ($this->isAdmin()) {
            return 'admin';
        }
        if ($this->hasPermission('estate.view_all')) {
            return 'manager';
        }
        if ($this->hasPermission('billing.view_all')) {
            return 'reviewer';
        }
        if ($this->hasPermission('estate.view_assigned')) {
            return 'caretaker';
        }

        return 'guest';
    }

    public function getEstateShowRoute($estate)
    {
        $role = $this->getUserRole();

        return match ($role) {
            'manager' => route('management.estates.show', $estate),
            'reviewer' => route('management.estates.show', $estate),
            'caretaker' => route('caretaker.estates.show', $estate),
            default => route('estates.show', $estate),
        };
    }

    public function getHousesRoute($estateId = null)
    {
        $role = $this->getUserRole();
        $params = $estateId ? ['estate' => $estateId] : [];

        return match ($role) {
            'manager' => route('management.houses', $params),
            'reviewer' => route('reviewer.houses', $params),
            'caretaker' => $estateId ? route('caretaker.houses', ['estateId' => $estateId]) : route('caretaker.houses'),
            default => route('houses', $params),
        };
    }
}
