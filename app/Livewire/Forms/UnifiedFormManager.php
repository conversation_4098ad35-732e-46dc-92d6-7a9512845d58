<?php

namespace App\Livewire\Forms;

use App\Livewire\Traits\WithFormValidation;
// Removed WithPermissionChecks - functionality moved to HasPermissions
use App\Livewire\Traits\WithSearchAndFilter;
use Livewire\Component;
use Livewire\WithPagination;

abstract class UnifiedFormManager extends Component
{
    use WithFormValidation, WithPagination, WithPermissionChecks, WithSearchAndFilter;

    // Form properties
    public $showModal = false;

    public $mode = 'create';

    public $editingId;

    public $form = [];

    public $formDefaults = [];

    // List properties
    public $search = '';

    public $sortBy = 'created_at';

    public $sortDirection = 'desc';

    public $filters = [];

    // Bulk actions
    public $selectedItems = [];

    public $selectAll = false;

    public $bulkAction = '';

    // Abstract methods that must be implemented by child classes
    abstract protected function getModelClass();

    abstract protected function getValidationRules();

    abstract protected function getFormFields();

    abstract protected function prepareSaveData();

    abstract protected function loadEntityData($entity);

    abstract protected function getListView();

    abstract protected function getFormView();

    public function mount()
    {
        $this->initializeForm();
        $this->checkPermissions();
    }

    protected function initializeForm()
    {
        $this->form = $this->formDefaults;
        $this->setFilterDefaults();
    }

    protected function setFilterDefaults()
    {
        // Override in child classes if needed
    }

    // Modal management
    public function openCreateModal()
    {
        $this->resetForm();
        $this->mode = 'create';
        $this->showModal = true;
    }

    public function openEditModal($entityId)
    {
        $this->resetForm();
        $this->mode = 'edit';
        $this->editingId = $entityId;
        $this->loadEntity($entityId);
        $this->showModal = true;
    }

    public function closeModal()
    {
        $this->showModal = false;
        $this->resetForm();
    }

    // Form operations
    public function save()
    {
        $this->validate($this->getValidationRules());

        $data = $this->prepareSaveData();

        if ($this->editingId) {
            $entity = $this->getModelClass()::findOrFail($this->editingId);
            $entity->update($data);
            $message = $this->getSuccessMessage('updated');
        } else {
            $entity = $this->getModelClass()::create($data);
            $message = $this->getSuccessMessage('created');
        }

        $this->closeModal();
        session()->flash('message', $message);
    }

    protected function loadEntity($entityId)
    {
        $entity = $this->getModelClass()::findOrFail($entityId);
        $this->loadEntityData($entity);
    }

    protected function resetForm()
    {
        $this->reset(['editingId', 'mode']);
        $this->form = $this->formDefaults;
        $this->resetValidation();
    }

    // List operations
    public function getItemsProperty()
    {
        $query = $this->getModelClass()::query();

        // Apply search
        if ($this->search) {
            $query = $this->applySearch($query);
        }

        // Apply filters
        foreach ($this->filters as $field => $value) {
            if (! empty($value)) {
                $query = $this->applyFilter($query, $field, $value);
            }
        }

        // Apply sorting
        $query->orderBy($this->sortBy, $this->sortDirection);

        return $query->paginate(15);
    }

    protected function applySearch($query)
    {
        $searchableFields = $this->getSearchableFields();

        return $query->where(function ($q) use ($searchableFields): void {
            foreach ($searchableFields as $searchableField) {
                $q->orWhere($searchableField, 'like', '%'.$this->search.'%');
            }
        });
    }

    protected function applyFilter($query, $field, $value)
    {
        // Override in child classes for custom filtering
        return $query->where($field, $value);
    }

    protected function getSearchableFields()
    {
        // Override in child classes
        return ['name'];
    }

    // Bulk actions
    public function updatedSelectAll($value)
    {
        $this->selectedItems = $value ? $this->items->pluck('id')->toArray() : [];
    }

    public function performBulkAction()
    {
        if (empty($this->selectedItems) || empty($this->bulkAction)) {
            return;
        }

        $items = $this->getModelClass()::whereIn('id', $this->selectedItems)->get();

        match ($this->bulkAction) {
            'delete' => $this->bulkDelete($items),
            default => $this->performCustomBulkAction($items, $this->bulkAction),
        };

        $this->reset(['selectedItems', 'selectAll', 'bulkAction']);
        session()->flash('message', 'Bulk action completed successfully!');
    }

    protected function bulkDelete($items)
    {
        foreach ($items as $item) {
            if ($this->canDeleteItem($item)) {
                $item->delete();
            }
        }
    }

    protected function canDeleteItem($item)
    {
        // Override in child classes for custom deletion logic
        return true;
    }

    protected function performCustomBulkAction($items, $action)
    {
        // Override in child classes for custom bulk actions
    }

    // Individual actions
    public function delete($itemId)
    {
        $item = $this->getModelClass()::findOrFail($itemId);

        if (! $this->canDeleteItem($item)) {
            session()->flash('error', 'Cannot delete this item.');

            return;
        }

        $item->delete();
        session()->flash('message', $this->getSuccessMessage('deleted'));
    }

    // Utility methods
    protected function getSuccessMessage($action)
    {
        $modelName = class_basename($this->getModelClass());

        return ucfirst($modelName)." {$action} successfully!";
    }

    protected function checkPermissions()
    {
        // Override in child classes for permission checks
    }

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function resetFilters()
    {
        $this->reset(['search', 'filters']);
        $this->setFilterDefaults();
    }

    public function render()
    {
        return view($this->getListView(), [
            'items' => $this->items,
            'statistics' => $this->getStatistics(),
        ]);
    }

    protected function getStatistics()
    {
        // Override in child classes to provide statistics
        return [];
    }
}
