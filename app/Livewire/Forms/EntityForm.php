<?php

namespace App\Livewire\Forms;

use App\Models\Estate;
use App\Models\House;
use Livewire\Component;

class EntityForm extends Component
{
    public $entity;

    public $entityType;

    public $mode = 'create'; // create or edit

    // Estate fields
    public $estateName = '';

    public $estateDescription = '';

    public $estateLocation = '';

    public $estateContactEmail = '';

    public $estateContactPhone = '';

    // House fields
    public $houseNumber = '';

    public $houseAddress = '';

    public $houseEstateId = '';

    public $houseStatus = 'active';

    public $houseMeterNumber = '';

    public $houseMeterType = 'analog';

    protected $rules = [
        // Estate validation rules
        'estateName' => 'required|string|min:3|max:255',
        'estateDescription' => 'nullable|string|max:1000',
        'estateLocation' => 'nullable|string|max:500',
        'estateContactEmail' => 'nullable|email|max:255',
        'estateContactPhone' => 'nullable|string|max:20',

        // House validation rules
        'houseNumber' => 'required|string|min:1|max:50',
        'houseAddress' => 'nullable|string|max:500',
        'houseEstateId' => 'required|exists:estates,id',
        'houseStatus' => 'required|in:active,inactive,maintenance',
        'houseMeterNumber' => 'nullable|string|max:50',
        'houseMeterType' => 'required|in:analog,digital,smart',
    ];

    protected $messages = [
        'estateName.required' => 'The estate name is required.',
        'estateName.min' => 'The estate name must be at least 3 characters.',
        'houseNumber.required' => 'The house number is required.',
        'houseEstateId.required' => 'Please select an estate.',
        'houseEstateId.exists' => 'The selected estate does not exist.',
    ];

    public function mount($entityType, $entityId = null)
    {
        $this->entityType = $entityType;

        if ($entityId) {
            $this->mode = 'edit';
            $this->loadEntity($entityId);
        }
    }

    private function loadEntity($entityId)
    {
        if ($this->entityType === 'estate') {
            $this->entity = Estate::findOrFail($entityId);
            $this->estateName = $this->entity->name;
            $this->estateDescription = $this->entity->description ?? '';
            $this->estateLocation = $this->entity->location ?? '';
            $this->estateContactEmail = $this->entity->contact_email ?? '';
            $this->estateContactPhone = $this->entity->contact_phone ?? '';
        } elseif ($this->entityType === 'house') {
            $this->entity = House::findOrFail($entityId);
            $this->houseNumber = $this->entity->house_number;
            $this->houseAddress = $this->entity->address ?? '';
            $this->houseEstateId = $this->entity->estate_id;
            $this->houseStatus = $this->entity->status ?? 'active';
            $this->houseMeterNumber = $this->entity->meter_number ?? '';
            $this->houseMeterType = $this->entity->meter_type ?? 'analog';
        }
    }

    public function save()
    {
        $this->validate();

        if ($this->entityType === 'estate') {
            $this->saveEstate();
        } elseif ($this->entityType === 'house') {
            $this->saveHouse();
        }

        session()->flash('message', $this->mode === 'create' ?
            ucfirst((string) $this->entityType).' created successfully!' :
            ucfirst((string) $this->entityType).' updated successfully!');

        return redirect()->to('/'.$this->entityType.'s');
    }

    private function saveEstate()
    {
        $data = [
            'name' => $this->estateName,
            'description' => $this->estateDescription,
            'location' => $this->estateLocation,
            'contact_email' => $this->estateContactEmail,
            'contact_phone' => $this->estateContactPhone,
        ];

        if ($this->mode === 'create') {
            $this->entity = Estate::create($data);
        } else {
            $this->entity->update($data);
        }
    }

    private function saveHouse()
    {
        $data = [
            'house_number' => $this->houseNumber,
            'address' => $this->houseAddress,
            'estate_id' => $this->houseEstateId,
            'status' => $this->houseStatus,
            'meter_number' => $this->houseMeterNumber,
            'meter_type' => $this->houseMeterType,
        ];

        if ($this->mode === 'create') {
            $this->entity = House::create($data);
        } else {
            $this->entity->update($data);
        }
    }

    public function getEstatesProperty()
    {
        return Estate::orderBy('name')->get();
    }

    public function render()
    {
        return view('livewire.forms.entity-form', [
            'entityType' => $this->entityType,
            'mode' => $this->mode,
            'estates' => $this->estates,
        ]);
    }
}
