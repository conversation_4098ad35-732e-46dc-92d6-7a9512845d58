<?php

namespace App\Livewire\Forms;

use App\Models\Contact;
use App\Models\Estate;
use App\Models\House;
use Illuminate\Support\Facades\Auth;

class ContactFormManager extends UnifiedFormManager
{
    public $estates = [];

    public $houses = [];

    public $estateFilter = '';

    public $houseFilter = '';

    public $formDefaults = [
        'house_id' => '',
        'first_name' => '',
        'last_name' => '',
        'email' => '',
        'phone' => '',
        'alternative_phone' => '',
        'address' => '',
        'relationship' => 'owner',
        'is_primary' => true,
        'is_active' => true,
        'notes' => '',
    ];

    protected function getModelClass()
    {
        return Contact::class;
    }

    protected function getValidationRules()
    {
        return [
            'form.house_id' => 'required|exists:houses,id',
            'form.first_name' => 'required|string|max:100',
            'form.last_name' => 'required|string|max:100',
            'form.email' => 'nullable|email|max:255',
            'form.phone' => 'required|string|max:50',
            'form.alternative_phone' => 'nullable|string|max:50',
            'form.address' => 'nullable|string|max:500',
            'form.relationship' => 'required|in:owner,tenant,relative,other',
            'form.is_primary' => 'boolean',
            'form.is_active' => 'boolean',
            'form.notes' => 'nullable|string|max:1000',
        ];
    }

    protected function getFormFields()
    {
        return [
            'house_id' => ['type' => 'select', 'label' => 'House', 'required' => true, 'options' => $this->houses],
            'first_name' => ['type' => 'text', 'label' => 'First Name', 'required' => true],
            'last_name' => ['type' => 'text', 'label' => 'Last Name', 'required' => true],
            'email' => ['type' => 'email', 'label' => 'Email'],
            'phone' => ['type' => 'tel', 'label' => 'Phone', 'required' => true],
            'alternative_phone' => ['type' => 'tel', 'label' => 'Alternative Phone'],
            'address' => ['type' => 'textarea', 'label' => 'Address'],
            'relationship' => ['type' => 'select', 'label' => 'Relationship', 'required' => true, 'options' => [
                'owner' => 'Owner',
                'tenant' => 'Tenant',
                'relative' => 'Relative',
                'other' => 'Other',
            ]],
            'is_primary' => ['type' => 'checkbox', 'label' => 'Primary Contact'],
            'is_active' => ['type' => 'checkbox', 'label' => 'Active'],
            'notes' => ['type' => 'textarea', 'label' => 'Notes'],
        ];
    }

    protected function prepareSaveData()
    {
        return $this->form;
    }

    protected function loadEntityData($entity)
    {
        $this->form = [
            'house_id' => $entity->house_id,
            'first_name' => $entity->first_name,
            'last_name' => $entity->last_name,
            'email' => $entity->email ?? '',
            'phone' => $entity->phone,
            'alternative_phone' => $entity->alternative_phone ?? '',
            'address' => $entity->address ?? '',
            'relationship' => $entity->relationship,
            'is_primary' => $entity->is_primary,
            'is_active' => $entity->is_active,
            'notes' => $entity->notes ?? '',
        ];
    }

    protected function getListView()
    {
        return 'livewire.forms.contact-form-manager';
    }

    protected function getFormView()
    {
        return 'livewire.forms.contact-form';
    }

    protected function getSearchableFields()
    {
        return ['first_name', 'last_name', 'email', 'phone'];
    }

    protected function applySearch($query)
    {
        return $query->where(function ($q): void {
            $q->where('first_name', 'like', '%'.$this->search.'%')
                ->orWhere('last_name', 'like', '%'.$this->search.'%')
                ->orWhere('email', 'like', '%'.$this->search.'%')
                ->orWhere('phone', 'like', '%'.$this->search.'%')
                ->orWhereHas('house', function ($q): void {
                    $q->where('house_number', 'like', '%'.$this->search.'%');
                });
        });
    }

    protected function applyFilter($query, $field, $value)
    {
        return match ($field) {
            'estate_id' => $query->whereHas('house', function ($q) use ($value): void {
                $q->where('estate_id', $value);
            }),
            'house_id' => $query->where('house_id', $value),
            'relationship' => $query->where('relationship', $value),
            'is_primary' => $query->where('is_primary', $value),
            'is_active' => $query->where('is_active', $value),
            default => parent::applyFilter($query, $field, $value),
        };
    }

    public function getItemsProperty()
    {
        $query = Contact::with(['house.estate']);

        // Apply search
        if ($this->search) {
            $query = $this->applySearch($query);
        }

        // Apply filters
        foreach ($this->filters as $field => $value) {
            if (! empty($value)) {
                $query = $this->applyFilter($query, $field, $value);
            }
        }

        // Apply sorting
        $query->orderBy($this->sortBy, $this->sortDirection);

        return $query->paginate(20);
    }

    protected function canDeleteItem($item)
    {
        // Check if contact has associated invoices
        if ($item->invoices()->count() > 0) {
            session()->flash('error', 'Cannot delete contact that has invoices associated with it.');

            return false;
        }

        return true;
    }

    protected function getStatistics()
    {
        $baseQuery = Contact::query();

        // Apply estate filter if set
        if ($this->estateFilter) {
            $baseQuery->whereHas('house', function ($q): void {
                $q->where('estate_id', $this->estateFilter);
            });
        }

        // Apply house filter if set
        if ($this->houseFilter) {
            $baseQuery->where('house_id', $this->houseFilter);
        }

        return [
            'total_contacts' => (clone $baseQuery)->count(),
            'primary_contacts' => (clone $baseQuery)->where('is_primary', true)->count(),
            'active_contacts' => (clone $baseQuery)->where('is_active', true)->count(),
            'owner_contacts' => (clone $baseQuery)->where('relationship', 'owner')->count(),
            'tenant_contacts' => (clone $baseQuery)->where('relationship', 'tenant')->count(),
        ];
    }

    protected function checkPermissions()
    {
        if (! Auth::user()->can('contacts.view_all') && ! Auth::user()->can('contacts.view_assigned') && ! Auth::user()->can('contacts.view_own')) {
            abort(403, 'Unauthorized action.');
        }
    }

    public function mount()
    {
        parent::mount();
        $this->loadEstates();
    }

    protected function loadEstates()
    {
        $user = Auth::user();

        if ($user->can('estates.manage_all')) {
            $this->estates = Estate::orderBy('name')->get();
        } else {
            $this->estates = Estate::where('id', $user->estate_id)->orderBy('name')->get();
        }
    }

    public function updatedEstateFilter($value)
    {
        $this->filters['estate_id'] = $value;
        $this->houseFilter = '';
        $this->filters['house_id'] = '';
        $this->resetPage();
    }

    public function updatedHouseFilter($value)
    {
        $this->filters['house_id'] = $value;
        $this->resetPage();
    }

    public function getFilteredHousesProperty()
    {
        $query = House::query();

        if ($this->estateFilter) {
            $query->where('estate_id', $this->estateFilter);
        }

        return $query->orderBy('house_number')->get();
    }

    public function getFullNameAttribute()
    {
        return trim($this->form['first_name'].' '.$this->form['last_name']);
    }

    protected function performCustomBulkAction($items, $action)
    {
        switch ($action) {
            case 'mark_primary':
                $items->each->update(['is_primary' => true]);
                break;
            case 'mark_secondary':
                $items->each->update(['is_primary' => false]);
                break;
            case 'activate':
                $items->each->update(['is_active' => true]);
                break;
            case 'deactivate':
                $items->each->update(['is_active' => false]);
                break;
        }
    }

    public function save()
    {
        // If this is being set as primary, unmark other primary contacts for the same house
        if ($this->form['is_primary']) {
            Contact::where('house_id', $this->form['house_id'])
                ->where('id', '!=', $this->editingId)
                ->update(['is_primary' => false]);
        }

        parent::save();
    }
}
