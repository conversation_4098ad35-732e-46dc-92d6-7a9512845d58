<?php

use App\Jobs\ProcessExportJob;
use App\Models\Contact;
use App\Models\Estate;
use App\Models\ExportJob;
use App\Models\ExportTemplate;
use App\Models\House;
use App\Models\Invoice;
use App\Models\MeterReading;
use App\Models\User;
use App\Models\WaterRate;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Queue;

beforeEach(function (): void {
    // Create a user with export permissions
    $this->user = User::factory()->admin()->create();

    // Give the user export permission
    $this->user->givePermissionTo('export.data_all');

    Auth::login($this->user);

    // Create test data
    $this->estate = Estate::factory()->create();
    $this->house = House::factory()->create(['estate_id' => $this->estate->id]);
    $this->contact = Contact::factory()->create(['house_id' => $this->house->id]);
    $this->meterReading = MeterReading::factory()->create(['house_id' => $this->house->id]);
    $this->invoice = Invoice::factory()->create(['house_id' => $this->house->id]);
    $this->waterRate = WaterRate::factory()->create(['estate_id' => $this->estate->id]);
});

test('user can access export management page', function (): void {
    $response = $this->get(route('exports.index'));

    $response->assertOk();
    $response->assertSee('Data Export Manager');
});

test('user can create export job', function (): void {
    Queue::fake();

    $response = $this->post(route('api.exports.initiate'), [
        'entity_type' => 'estates',
        'format' => 'xlsx',
        'export_name' => 'Test Export',
    ]);

    $response->assertStatus(201);
    $response->assertJson(['message' => 'Export job created successfully']);

    $this->assertDatabaseHas('export_jobs', [
        'user_id' => $this->user->id,
        'entity_type' => 'estates',
        'format' => 'xlsx',
        'status' => 'pending',
    ]);

    Queue::assertPushed(ProcessExportJob::class);
});

test('user can view their export jobs', function (): void {
    ExportJob::factory()->create([
        'user_id' => $this->user->id,
        'entity_type' => 'houses',
        'status' => 'completed',
    ]);

    ExportJob::factory()->create([
        'user_id' => $this->user->id,
        'entity_type' => 'invoices',
        'status' => 'pending',
    ]);

    // Create export job for another user (should not be visible)
    ExportJob::factory()->create([
        'user_id' => User::factory()->create()->id,
        'entity_type' => 'contacts',
        'status' => 'completed',
    ]);

    $response = $this->get(route('api.exports.index'));

    $response->assertOk();
    $response->assertJsonCount(2, 'data'); // Only user's own exports
});

test('user can delete their export jobs', function (): void {
    $exportJob = ExportJob::factory()->create([
        'user_id' => $this->user->id,
        'file_path' => 'exports/test.xlsx',
    ]);

    // Mock storage
    \Storage::shouldReceive('exists')->with('exports/test.xlsx')->andReturn(true);
    \Storage::shouldReceive('delete')->with('exports/test.xlsx')->andReturn(true);

    $response = $this->delete(route('api.exports.destroy', $exportJob->id));

    $response->assertOk();
    $response->assertJson(['message' => 'Export deleted successfully']);

    $this->assertDatabaseMissing('export_jobs', ['id' => $exportJob->id]);
});

test('user can retry failed export jobs', function (): void {
    Queue::fake();

    $exportJob = ExportJob::factory()->create([
        'user_id' => $this->user->id,
        'status' => 'failed',
        'error' => 'Test error',
    ]);

    $response = $this->post(route('api.exports.retry', $exportJob->id));

    $response->assertOk();
    $response->assertJson(['message' => 'Export job retried successfully']);

    $this->assertDatabaseHas('export_jobs', [
        'id' => $exportJob->id,
        'status' => 'pending',
        'error' => null,
    ]);

    Queue::assertPushed(ProcessExportJob::class);
});

test('user cannot retry non-failed export jobs', function (): void {
    $exportJob = ExportJob::factory()->create([
        'user_id' => $this->user->id,
        'status' => 'completed',
    ]);

    $response = $this->post(route('api.exports.retry', $exportJob->id));

    $response->assertStatus(400);
    $response->assertJson(['error' => 'Only failed exports can be retried']);
});

test('user can create export template', function (): void {
    $response = $this->post(route('api.exports.templates.store'), [
        'name' => 'Test Template',
        'entity_type' => 'estates',
        'format' => 'xlsx',
        'columns' => ['ID', 'Name', 'Location', 'Status'],
        'is_public' => false,
        'is_scheduled' => false,
    ]);

    $response->assertStatus(201);
    $response->assertJson(['name' => 'Test Template']);

    $this->assertDatabaseHas('export_templates', [
        'user_id' => $this->user->id,
        'name' => 'Test Template',
        'entity_type' => 'estates',
        'format' => 'xlsx',
    ]);
});

test('user can view their export templates', function (): void {
    ExportTemplate::factory()->create([
        'user_id' => $this->user->id,
        'name' => 'User Template',
        'entity_type' => 'houses',
    ]);

    ExportTemplate::factory()->create([
        'user_id' => $this->user->id,
        'name' => 'Public Template',
        'entity_type' => 'invoices',
        'is_public' => true,
    ]);

    // Create template for another user (should not be visible unless public)
    ExportTemplate::factory()->create([
        'user_id' => User::factory()->create()->id,
        'name' => 'Other User Template',
        'entity_type' => 'contacts',
        'is_public' => false,
    ]);

    $response = $this->get(route('api.exports.templates'));

    $response->assertOk();
    $response->assertJsonCount(2, 'data'); // User's templates + public templates
});

test('user can update their export templates', function (): void {
    $template = ExportTemplate::factory()->create([
        'user_id' => $this->user->id,
        'name' => 'Original Name',
    ]);

    $response = $this->put(route('api.exports.templates.update', $template->id), [
        'name' => 'Updated Name',
        'entity_type' => 'estates',
        'format' => 'csv',
        'columns' => ['ID', 'Name'],
    ]);

    $response->assertOk();
    $response->assertJson(['name' => 'Updated Name']);

    $this->assertDatabaseHas('export_templates', [
        'id' => $template->id,
        'name' => 'Updated Name',
        'format' => 'csv',
    ]);
});

test('user can delete their export templates', function (): void {
    $template = ExportTemplate::factory()->create([
        'user_id' => $this->user->id,
    ]);

    $response = $this->delete(route('api.exports.templates.destroy', $template->id));

    $response->assertOk();
    $response->assertJson(['message' => 'Export template deleted successfully']);

    $this->assertDatabaseMissing('export_templates', ['id' => $template->id]);
});

test('user can preview export data', function (): void {
    $response = $this->post(route('api.exports.preview'), [
        'entity_type' => 'estates',
        'limit' => 5,
    ]);

    $response->assertOk();
    $response->assertJsonStructure([
        'data',
        'total_count',
        'sample_columns',
    ]);

    // Should contain the estate we created
    $response->assertJsonFragment(['Name' => $this->estate->name]);
});

test('export preview validates entity type', function (): void {
    $response = $this->post(route('api.exports.preview'), [
        'entity_type' => 'invalid_type',
    ]);

    $response->assertStatus(422);
    $response->assertJsonValidationErrors(['entity_type']);
});

test('export job creation validates required fields', function (): void {
    $response = $this->post(route('api.exports.initiate'), []);

    $response->assertStatus(422);
    $response->assertJsonValidationErrors(['entity_type', 'format', 'export_name']);
});

test('user cannot access other users export jobs', function (): void {
    $otherUser = User::factory()->create();
    $otherUser->givePermissionTo('export.data_all');

    $exportJob = ExportJob::factory()->create([
        'user_id' => $otherUser->id,
    ]);

    $response = $this->get(route('api.exports.show', $exportJob->id));

    $response->assertStatus(404);
});

test('user cannot modify other users export templates', function (): void {
    $otherUser = User::factory()->create();
    $otherUser->givePermissionTo('export.data_all');

    $template = ExportTemplate::factory()->create([
        'user_id' => $otherUser->id,
        'is_public' => false,
    ]);

    $response = $this->put(route('api.exports.templates.update', $template->id), [
        'name' => 'Hacked Name',
    ]);

    $response->assertStatus(404);
});

test('public templates are accessible to all users', function (): void {
    $otherUser = User::factory()->create();
    $otherUser->givePermissionTo('export.data_all');

    $publicTemplate = ExportTemplate::factory()->create([
        'user_id' => $otherUser->id,
        'is_public' => true,
    ]);

    $response = $this->get(route('api.exports.templates.show', $publicTemplate->id));

    $response->assertOk();
    $response->assertJson(['id' => $publicTemplate->id]);
});
