<?php

use Tests\Traits\CreatesTestUsers;
use Tests\Traits\HasSpatiePermissions;

uses(\Illuminate\Foundation\Testing\RefreshDatabase::class);
uses(CreatesTestUsers::class);
uses(HasSpatiePermissions::class);

beforeEach(function () {
    $this->setUpPermissions();
});

test('guests are redirected to the login page', function (): void {
    $this->get('/dashboard')->assertRedirect('/login');
});

test('authenticated users can visit the dashboard', function (): void {
    $user = $this->createManagerUser();
    $this->actingAs($user);

    $this->get('/dashboard')->assertRedirect('/management/dashboard');
    $this->get('/management/dashboard')->assertStatus(200);
});
