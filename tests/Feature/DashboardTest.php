<?php

use App\Models\User;

uses(\Illuminate\Foundation\Testing\RefreshDatabase::class);

test('guests are redirected to the login page', function (): void {
    $this->get('/dashboard')->assertRedirect('/login');
});

test('authenticated users can visit the dashboard', function (): void {
    $this->actingAs($user = User::factory()->manager());

    $this->get('/dashboard')->assertRedirect('/management/dashboard');
    $this->get('/management/dashboard')->assertStatus(200);
});
