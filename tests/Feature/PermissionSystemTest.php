<?php

use App\Models\PermissionAuditLog;
use App\Models\User;
use App\Services\PermissionService;
use Spatie\Permission\Models\Permission;

uses(\Tests\Traits\CreatesTestUsers::class);
uses(\Illuminate\Foundation\Testing\RefreshDatabase::class);
uses(\Tests\Traits\HasSpatiePermissions::class);

beforeEach(function (): void {
    // Ensure migrations are run
    $this->artisan('migrate:fresh');

    $this->permissionService = app(PermissionService::class);
    $this->setUpPermissions();
});

test('admin user has all permissions', function (): void {
    $user = $this->createAdminUser();

    expect($user->hasPermissionTo('estates.create'))->toBeTrue();
    expect($user->hasPermissionTo('invoices.delete'))->toBeTrue();
    expect($user->hasPermissionTo('users.manage'))->toBeTrue();
    expect($user->hasPermissionTo('system.settings.edit'))->toBeTrue();

    // Test using <PERSON><PERSON>'s direct methods (preferred approach)
    expect($user->can('estates.create'))->toBeTrue();
    expect($user->can('invoices.delete'))->toBeTrue();
});

test('manager user has assigned permissions', function (): void {
    $user = $this->createManagerUser();

    expect($user->hasPermissionTo('estates.view_assigned'))->toBeTrue();
    expect($user->hasPermissionTo('invoices.view_assigned'))->toBeTrue();
    expect($user->hasPermissionTo('reports.view_assigned'))->toBeTrue();

    // Test using Spatie's direct methods (preferred approach)
    expect($user->can('estates.view_assigned'))->toBeTrue();
    expect($user->can('invoices.view_assigned'))->toBeTrue();

    // Test manager does NOT have admin permissions
    expect($user->hasPermissionTo('estates.create'))->toBeFalse();
    expect($user->hasPermissionTo('invoices.delete'))->toBeFalse();
    expect($user->can('estates.create'))->toBeFalse();
});

test('reviewer user has assigned permissions', function (): void {
    $user = $this->createReviewerUser();

    expect($user->hasPermissionTo('readings.approve_assigned'))->toBeTrue();
    expect($user->hasPermissionTo('invoices.approve_assigned'))->toBeTrue();
    expect($user->hasPermissionTo('reports.view_assigned'))->toBeTrue();

    // Test using Spatie's direct methods (preferred approach)
    expect($user->can('readings.approve_assigned'))->toBeTrue();
    expect($user->can('invoices.approve_assigned'))->toBeTrue();

    // Test reviewer does NOT have admin permissions
    expect($user->hasPermissionTo('estates.create'))->toBeFalse();
    expect($user->can('estates.create'))->toBeFalse();
});

test('caretaker user has assigned permissions', function (): void {
    $user = $this->createCaretakerUser();

    expect($user->hasPermissionTo('readings.create_assigned'))->toBeTrue();
    expect($user->hasPermissionTo('contacts.manage_assigned'))->toBeTrue();
    expect($user->hasPermissionTo('estates.view_assigned'))->toBeTrue();

    // Test using Spatie's direct methods (preferred approach)
    expect($user->can('readings.create_assigned'))->toBeTrue();
    expect($user->can('contacts.manage_assigned'))->toBeTrue();

    // Test caretaker does NOT have admin permissions
    expect($user->hasPermissionTo('invoices.delete'))->toBeFalse();
    expect($user->can('invoices.delete'))->toBeFalse();
});

test('resident user has assigned permissions', function (): void {
    $user = $this->createResidentUser();

    expect($user->hasPermissionTo('invoices.view_own'))->toBeTrue();
    expect($user->hasPermissionTo('accounts.view_own'))->toBeTrue();
    expect($user->hasPermissionTo('resident.portal.access'))->toBeTrue();

    // Test using Spatie's direct methods (preferred approach)
    expect($user->can('invoices.view_own'))->toBeTrue();
    expect($user->can('accounts.view_own'))->toBeTrue();

    // Test resident does NOT have admin permissions
    expect($user->hasPermissionTo('estates.create'))->toBeFalse();
    expect($user->can('estates.create'))->toBeFalse();
});

test('permission service user has permission method', function (): void {
    $user = $this->createAdminUser();
    $manager = $this->createManagerUser();

    // Test admin has all permissions
    expect($user->can('any.permission.name'))->toBeTrue();

    // Test manager has specific permissions
    expect($manager->can('estates.view_assigned'))->toBeTrue();
    expect($manager->can('estates.create'))->toBeFalse();
});

test('permission service user has any permission method', function (): void {
    $user = $this->createAdminUser();
    $manager = $this->createManagerUser();
    $resident = $this->createResidentUser();

    // Test admin has any of the permissions
    expect($this->permissionService->userHasAnyPermission($user, ['estates.create', 'invoices.delete']))->toBeTrue();

    // Test manager has any of the permissions
    expect($this->permissionService->userHasAnyPermission($manager, ['estates.view_assigned', 'invoices.delete']))->toBeTrue();
    expect($this->permissionService->userHasAnyPermission($manager, ['estates.create', 'invoices.delete']))->toBeFalse();

    // Test resident has any of the permissions
    expect($this->permissionService->userHasAnyPermission($resident, ['invoices.view_own', 'accounts.view_own']))->toBeTrue();
    expect($this->permissionService->userHasAnyPermission($resident, ['estates.create', 'invoices.delete']))->toBeFalse();
});

test('permission service user has all permissions method', function (): void {
    $user = $this->createAdminUser();
    $manager = $this->createManagerUser();

    // Test admin has all permissions
    expect($this->permissionService->userHasAllPermissions($user, ['estates.create', 'invoices.delete']))->toBeTrue();

    // Test manager does not have all permissions
    expect($this->permissionService->userHasAllPermissions($manager, ['estates.create', 'invoices.delete']))->toBeFalse();
    expect($this->permissionService->userHasAllPermissions($manager, ['estates.view_assigned', 'invoices.view_assigned']))->toBeTrue();
});

test('can create new permissions', function (): void {
    // Debug: Check if permissions table exists
    $tableExists = \Illuminate\Support\Facades\Schema::hasTable('permissions');
    expect($tableExists)->toBeTrue('Permissions table should exist');

    $permission = Permission::create([
        'name' => 'test.custom.permission',
        'guard_name' => 'web',
    ]);

    $this->assertDatabaseHas('permissions', ['name' => 'test.custom.permission']);

    $user = $this->createAdminUser();
    $user->givePermissionTo($permission);

    expect($user->hasPermissionTo('test.custom.permission'))->toBeTrue();
});

test('unauthorized users are denied access', function (): void {
    $user = $this->createResidentUser();

    // Test that resident cannot access admin routes
    $response = $this->actingAs($user)->get('/admin/permissions');
    $response->assertStatus(403);

    // Test that resident cannot access manager routes
    $response = $this->actingAs($user)->get('/management/dashboard');
    $response->assertStatus(403);
});

test('authorized users can access routes', function (): void {
    $user = $this->createAdminUser();
    $manager = $this->createManagerUser();

    // Test admin can access admin routes
    $response = $this->actingAs($user)->get('/admin/permissions');
    $response->assertStatus(200);

    // Test manager can access manager routes
    $response = $this->actingAs($manager)->get('/management/dashboard');
    $response->assertStatus(200);
});

test('middleware protects routes based on permissions', function (): void {
    $user = $this->createResidentUser();

    // Test that permission middleware blocks unauthorized access
    $response = $this->actingAs($user)->get('/admin/permissions');
    $response->assertStatus(403);

    $admin = $this->createAdminUser();
    $response = $this->actingAs($admin)->get('/admin/permissions');
    $response->assertStatus(200);
});

test('permission audit logging works', function (): void {
    $admin = $this->createAdminUser();
    $user = User::factory()->create();

    // Manually create audit log (since automatic logging is not implemented)
    $permission = Permission::create(['name' => 'audit.test.permission', 'guard_name' => 'web']);

    // Simulate permission assignment with audit logging
    $user->givePermissionTo($permission);

    // Manually create the audit log that would be created by an observer/listener
    PermissionAuditLog::create([
        'user_id' => $admin->id,
        'action' => 'assigned',
        'permission_name' => 'audit.test.permission',
        'target_user_id' => $user->id,
        'target_type' => 'permission',
        'target_id' => (string) $permission->id,
        'ip_address' => '127.0.0.1',
        'user_agent' => 'Test Agent',
    ]);

    // Check if audit log was created
    $this->assertDatabaseHas('permission_audit_logs', [
        'user_id' => $admin->id,
        'action' => 'assigned',
        'permission_name' => 'audit.test.permission',
        'target_user_id' => $user->id,
    ]);

    // Simulate permission removal with audit logging
    $user->revokePermissionTo($permission);

    // Manually create the audit log for removal
    PermissionAuditLog::create([
        'user_id' => $admin->id,
        'action' => 'revoked',
        'permission_name' => 'audit.test.permission',
        'target_user_id' => $user->id,
        'target_type' => 'permission',
        'target_id' => (string) $permission->id,
        'ip_address' => '127.0.0.1',
        'user_agent' => 'Test Agent',
    ]);

    // Check if audit log was created for removal
    $this->assertDatabaseHas('permission_audit_logs', [
        'user_id' => $admin->id,
        'action' => 'revoked',
        'permission_name' => 'audit.test.permission',
        'target_user_id' => $user->id,
    ]);
});

test('whatsapp permissions work', function (): void {
    $user = $this->createAdminUser();
    $manager = $this->createManagerUser();

    // Test admin has all WhatsApp permissions
    expect($user->hasPermissionTo('whatsapp.settings'))->toBeTrue();
    expect($user->hasPermissionTo('whatsapp.send_all'))->toBeTrue();
    expect($user->hasPermissionTo('whatsapp.logs.view'))->toBeTrue();

    // Test manager has limited WhatsApp permissions
    expect($manager->hasPermissionTo('whatsapp.send_assigned'))->toBeTrue();
    expect($manager->hasPermissionTo('whatsapp.logs.view'))->toBeTrue();
    expect($manager->hasPermissionTo('whatsapp.send_invoices_assigned'))->toBeTrue();

    // Test manager does NOT have admin WhatsApp permissions
    expect($manager->hasPermissionTo('whatsapp.settings'))->toBeFalse();
    expect($manager->hasPermissionTo('whatsapp.send_all'))->toBeFalse();
});

test('payment permissions work', function (): void {
    $user = $this->createAdminUser();
    $manager = $this->createManagerUser();

    // Test admin has all payment permissions
    expect($user->hasPermissionTo('payments.view_all'))->toBeTrue();
    expect($user->hasPermissionTo('payments.process'))->toBeTrue();
    expect($user->hasPermissionTo('payments.refund'))->toBeTrue();

    // Test manager has limited payment permissions
    expect($manager->hasPermissionTo('payments.view_assigned'))->toBeTrue();
    expect($manager->hasPermissionTo('payments.view_history_assigned'))->toBeTrue();

    // Test manager does NOT have admin payment permissions
    expect($manager->hasPermissionTo('payments.view_all'))->toBeFalse();
    expect($manager->hasPermissionTo('payments.process'))->toBeFalse();
});

test('audit permissions work', function (): void {
    $user = $this->createAdminUser();
    $manager = $this->createManagerUser();

    // Test admin has all audit permissions
    expect($user->hasPermissionTo('audit.view_logs'))->toBeTrue();
    expect($user->hasPermissionTo('audit.export_logs'))->toBeTrue();
    expect($user->hasPermissionTo('audit.cleanup_logs'))->toBeTrue();

    // Test manager has limited audit permissions
    expect($manager->hasPermissionTo('audit.view_logs'))->toBeTrue();

    // Test manager does NOT have admin audit permissions
    expect($manager->hasPermissionTo('audit.export_logs'))->toBeFalse();
    expect($manager->hasPermissionTo('audit.cleanup_logs'))->toBeFalse();
});

test('settings permissions work', function (): void {
    $user = $this->createAdminUser();
    $manager = $this->createManagerUser();

    // Test admin has all settings permissions
    expect($user->hasPermissionTo('system.settings.view'))->toBeTrue();
    expect($user->hasPermissionTo('system.settings.edit'))->toBeTrue();
    expect($user->hasPermissionTo('system.settings.manage'))->toBeTrue();

    // Test manager has no settings permissions
    expect($manager->hasPermissionTo('system.settings.view'))->toBeFalse();
    expect($manager->hasPermissionTo('system.settings.edit'))->toBeFalse();
});

test('validation permissions work', function (): void {
    $user = $this->createAdminUser();
    $this->createManagerUser();
    $reviewer = $this->createReviewerUser();

    // Test admin has all validation permissions
    expect($user->hasPermissionTo('validation.view_rules'))->toBeTrue();
    expect($user->hasPermissionTo('validation.create_rules'))->toBeTrue();
    expect($user->hasPermissionTo('validation.edit_rules'))->toBeTrue();
    expect($user->hasPermissionTo('validation.delete_rules'))->toBeTrue();
    expect($user->hasPermissionTo('validation.run_validation'))->toBeTrue();

    // Test reviewer has limited validation permissions
    expect($reviewer->hasPermissionTo('validation.view_rules'))->toBeTrue();
    expect($reviewer->hasPermissionTo('validation.run_validation'))->toBeTrue();

    // Test reviewer does NOT have admin validation permissions
    expect($reviewer->hasPermissionTo('validation.create_rules'))->toBeFalse();
    expect($reviewer->hasPermissionTo('validation.delete_rules'))->toBeFalse();
});
