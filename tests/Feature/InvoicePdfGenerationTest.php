<?php

use App\Models\Contact;
use App\Models\Estate;
use App\Models\House;
use App\Models\Invoice;
use App\Models\MeterReading;
use App\Models\WaterRate;
use App\Services\PdfGenerationService;
use Illuminate\Support\Facades\Storage;

test('pdf generation service generates invoice pdf', function (): void {
    // Create test data
    $estate = Estate::factory()->create();
    $house = House::factory()->create(['estate_id' => $estate->id]);
    $contact = Contact::factory()->create(['house_id' => $house->id]);
    $waterRate = WaterRate::factory()->create(['estate_id' => $estate->id]);
    $meterReading = MeterReading::factory()->create(['house_id' => $house->id]);

    $invoice = Invoice::factory()->create([
        'house_id' => $house->id,
        'meter_reading_id' => $meterReading->id,
        'water_rate_id' => $waterRate->id,
    ]);

    // Test PDF generation
    $pdfService = new PdfGenerationService;
    $path = $pdfService->generateInvoicePdf($invoice);

    // Assert PDF was created
    expect($path)->toBeString();
    expect($invoice->fresh()->pdf_path)->toBe($path);
    expect(Storage::exists($path))->toBeTrue();

    // Clean up
    Storage::delete($path);
});

test('pdf generation service downloads existing pdf', function (): void {
    // Create test data
    $estate = Estate::factory()->create();
    $house = House::factory()->create(['estate_id' => $estate->id]);
    $contact = Contact::factory()->create(['house_id' => $house->id]);
    $waterRate = WaterRate::factory()->create(['estate_id' => $estate->id]);
    $meterReading = MeterReading::factory()->create(['house_id' => $house->id]);

    $invoice = Invoice::factory()->create([
        'house_id' => $house->id,
        'meter_reading_id' => $meterReading->id,
        'water_rate_id' => $waterRate->id,
    ]);

    // Generate PDF first
    $pdfService = new PdfGenerationService;
    $path = $pdfService->generateInvoicePdf($invoice);

    // Test PDF download
    $response = $pdfService->downloadInvoicePdf($invoice);

    // Assert response is a download
    expect($response)->toBeInstanceOf(\Symfony\Component\HttpFoundation\StreamedResponse::class);

    // Clean up
    Storage::delete($path);
});

test('pdf generation service regenerates pdf', function (): void {
    // Create test data
    $estate = Estate::factory()->create();
    $house = House::factory()->create(['estate_id' => $estate->id]);
    $contact = Contact::factory()->create(['house_id' => $house->id]);
    $waterRate = WaterRate::factory()->create(['estate_id' => $estate->id]);
    $meterReading = MeterReading::factory()->create(['house_id' => $house->id]);

    $invoice = Invoice::factory()->create([
        'house_id' => $house->id,
        'meter_reading_id' => $meterReading->id,
        'water_rate_id' => $waterRate->id,
    ]);

    // Generate initial PDF
    $pdfService = new PdfGenerationService;
    $originalPath = $pdfService->generateInvoicePdf($invoice);

    // Regenerate PDF
    $newPath = $pdfService->regenerateInvoicePdf($invoice);

    // Assert new PDF was created
    expect($newPath)->toBeString();
    expect($invoice->fresh()->pdf_path)->toBe($newPath);
    expect(Storage::exists($newPath))->toBeTrue();

    // Clean up
    Storage::delete($newPath);
});

test('pdf generation throws exception for missing pdf', function (): void {
    $invoice = Invoice::factory()->create(['pdf_path' => null]);

    $pdfService = new PdfGenerationService;

    expect(function () use ($pdfService, $invoice): void {
        $pdfService->downloadInvoicePdf($invoice);
    })->toThrow(\Exception::class, 'PDF not available for this invoice');
});

test('pdf generation throws exception for missing file', function (): void {
    $invoice = Invoice::factory()->create(['pdf_path' => 'invoices/nonexistent.pdf']);

    $pdfService = new PdfGenerationService;

    expect(function () use ($pdfService, $invoice): void {
        $pdfService->downloadInvoicePdf($invoice);
    })->toThrow(\Exception::class, 'PDF file not found');
});

test('pdf generation only includes primary contact', function (): void {
    // Create test data
    $estate = Estate::factory()->create();
    $house = House::factory()->create(['estate_id' => $estate->id]);

    // Create secondary contact first
    $secondaryContact = Contact::factory()->secondary()->create([
        'house_id' => $house->id,
        'name' => 'Secondary Contact',
        'phone' => '+0987654321',
    ]);

    // Create primary contact (default is primary)
    $primaryContact = Contact::factory()->create([
        'house_id' => $house->id,
        'name' => 'Primary Contact',
        'phone' => '+1234567890',
    ]);

    // Refresh house to ensure relationships are loaded correctly
    $house->refresh();

    // Verify database state
    expect(Contact::where('house_id', $house->id)->count())->toBe(2);
    expect(Contact::where('house_id', $house->id)->where('is_primary', true)->count())->toBe(1);
    expect(Contact::where('house_id', $house->id)->where('is_primary', false)->count())->toBe(1);

    // Verify the primaryContact relationship returns the correct contact
    expect($house->primaryContact->name)->toBe('Primary Contact');
    expect($house->primaryContact->phone)->toBe('+1234567890');

    $waterRate = WaterRate::factory()->create(['estate_id' => $estate->id]);
    $meterReading = MeterReading::factory()->create(['house_id' => $house->id]);

    $invoice = Invoice::factory()->create([
        'house_id' => $house->id,
        'meter_reading_id' => $meterReading->id,
        'water_rate_id' => $waterRate->id,
    ]);

    // Test PDF generation
    $pdfService = new PdfGenerationService;
    $path = $pdfService->generateInvoicePdf($invoice);

    // Assert PDF was created
    expect($path)->toBeString();
    expect(Storage::exists($path))->toBeTrue();

    // Test that the invoice loads the correct relationships
    $invoice->load(['house.estate', 'house.primaryContact', 'meterReading', 'waterRate']);

    // Assert that the house has the correct primary contact
    expect($invoice->house->primaryContact)->not->toBeNull();
    expect($invoice->house->primaryContact->name)->toBe('Primary Contact');
    expect($invoice->house->primaryContact->phone)->toBe('+1234567890');

    // Assert that the house has multiple contacts but only one primary
    expect($invoice->house->contacts()->count())->toBe(2);
    expect($invoice->house->primaryContact()->count())->toBe(1);

    // Clean up
    Storage::delete($path);
});
