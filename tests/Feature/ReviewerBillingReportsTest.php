<?php

use App\Models\Estate;
use App\Models\User;
use Tests\Traits\SetsUpSpatiePermissions;

uses(\Illuminate\Foundation\Testing\RefreshDatabase::class, SetsUpSpatiePermissions::class);

beforeEach(function (): void {
    $this->setUpSpatiePermissions();
});

test('reviewer can access billing reports', function (): void {
    // Create admin user to assign estates
    $admin = User::factory()->create();
    $admin->assignRole('admin');

    // Create reviewer and estate
    $reviewer = User::factory()->create();
    $reviewer->assignRole('reviewer');
    $estate = Estate::factory()->create();

    // Assign estate to reviewer
    $reviewer->assignedEstates()->attach($estate->id, ['assigned_by' => $admin->id]);

    $reportsResponse = $this->actingAs($reviewer)->get('/reviewer/billing/reports');
    $reportsResponse->assertStatus(200);
});

test('unauthenticated user cannot access billing reports', function (): void {
    $response = $this->get('/reviewer/billing/reports');

    $response->assertRedirect('/login');
});

test('user without permission cannot access billing reports', function (): void {
    // Create a manager user who doesn't have invoices.generate_assigned permission
    $manager = User::factory()->create();
    $manager->assignRole('manager');

    $response = $this->actingAs($manager)->get('/reviewer/billing/reports');

    $response->assertStatus(403);
});
