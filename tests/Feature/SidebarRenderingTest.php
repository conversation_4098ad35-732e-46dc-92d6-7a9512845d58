<?php

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

describe('Sidebar Rendering', function (): void {
    beforeEach(function (): void {
        // Create test users for each role
        $this->admin = User::factory()->admin();
        $this->manager = User::factory()->manager();
        $this->reviewer = User::factory()->reviewer();
        $this->caretaker = User::factory()->caretaker();
        $this->resident = User::factory()->resident();
    });

    describe('Sidebar HTML Structure', function (): void {
        it('renders correct dashboard link for each role', function (): void {
            $roles = [
                'admin' => [
                    'user' => $this->admin,
                    'expected_route' => 'management.dashboard',
                    'expected_text' => 'Management Dashboard',
                ],
                'manager' => [
                    'user' => $this->manager,
                    'expected_route' => 'management.dashboard',
                    'expected_text' => 'Management Dashboard',
                ],
                'reviewer' => [
                    'user' => $this->reviewer,
                    'expected_route' => 'reviewer.dashboard',
                    'expected_text' => 'Reviewer Dashboard',
                ],
                'caretaker' => [
                    'user' => $this->caretaker,
                    'expected_route' => 'caretaker.dashboard',
                    'expected_text' => 'Caretaker Dashboard',
                ],
                'resident' => [
                    'user' => $this->resident,
                    'expected_route' => 'dashboard',
                    'expected_text' => 'Dashboard',
                ],
            ];

            foreach ($roles as $role) {
                $response = $this->actingAs($role['user'])
                    ->get('/dashboard')
                    ->assertRedirect(route($role['expected_route']));

                $response = $this->actingAs($role['user'])
                    ->get(route($role['expected_route']));

                $response->assertSee($role['expected_text']);
                $response->assertSee('href="'.route($role['expected_route']).'"');
            }
        });

        it('renders correct active states for navigation items', function (): void {
            // Test admin dashboard active state
            $response = $this->actingAs($this->admin)
                ->get('/admin/dashboard');

            $response->assertSee('bg-brand-100 text-brand-700');
            $response->assertSee('Management Dashboard');
        });

        it('renders navigation sections with proper headings', function (): void {
            $response = $this->actingAs($this->admin)
                ->get('/admin/dashboard');

            $response->assertSee('Water Management');
            $response->assertSee('Water Operations');
            $response->assertSee('Billing & Reports');
            $response->assertSee('Administration');
        });

        it('renders user information in sidebar footer', function (): void {
            $response = $this->actingAs($this->admin)
                ->get('/admin/dashboard');

            $response->assertSee($this->admin->name);
            $response->assertSee($this->admin->email);
            $response->assertSee($this->admin->initials());
        });

        it('renders logout functionality', function (): void {
            $response = $this->actingAs($this->admin)
                ->get('/admin/dashboard');

            $response->assertSee('logout-form');
            $response->assertSee('method="POST"');
            $response->assertSee('csrf-token');
        });
    });

    describe('Sidebar Navigation Groups', function (): void {
        it('shows water management section to appropriate users', function (): void {
            $usersWithWaterManagement = [$this->admin, $this->manager, $this->reviewer, $this->caretaker];

            foreach ($usersWithWaterManagement as $userWithWaterManagement) {
                $route = match ($userWithWaterManagement->role) {
                    'admin' => '/admin/dashboard',
                    'manager' => '/management/dashboard',
                    'reviewer' => '/reviewer/dashboard',
                    'caretaker' => '/caretaker/dashboard',
                };

                $response = $this->actingAs($userWithWaterManagement)->get($route);
                $response->assertSee('Water Management');
            }

            // Resident should not see water management
            $response = $this->actingAs($this->resident)->get('/resident/dashboard');
            $response->assertDontSee('Water Management');
        });

        it('shows water operations section to appropriate users', function (): void {
            $usersWithWaterOperations = [$this->admin, $this->manager, $this->reviewer, $this->caretaker];

            foreach ($usersWithWaterOperations as $userWithWaterOperation) {
                $route = match ($userWithWaterOperation->role) {
                    'admin' => '/admin/dashboard',
                    'manager' => '/management/dashboard',
                    'reviewer' => '/reviewer/dashboard',
                    'caretaker' => '/caretaker/dashboard',
                };

                $response = $this->actingAs($userWithWaterOperation)->get($route);
                $response->assertSee('Water Operations');
            }

            // Resident should not see water operations
            $response = $this->actingAs($this->resident)->get('/resident/dashboard');
            $response->assertDontSee('Water Operations');
        });

        it('shows billing & reports section to appropriate users', function (): void {
            $usersWithBilling = [$this->admin, $this->manager, $this->reviewer, $this->resident];

            foreach ($usersWithBilling as $userWithBilling) {
                $route = match ($userWithBilling->role) {
                    'admin' => '/admin/dashboard',
                    'manager' => '/management/dashboard',
                    'reviewer' => '/reviewer/dashboard',
                    'resident' => '/resident/dashboard',
                };

                $response = $this->actingAs($userWithBilling)->get($route);
                $response->assertSee('Billing & Reports');
            }

            // Caretaker should not see billing & reports
            $response = $this->actingAs($this->caretaker)->get('/caretaker/dashboard');
            $response->assertDontSee('Billing & Reports');
        });

        it('shows administration section only to admin', function (): void {
            $response = $this->actingAs($this->admin)->get('/admin/dashboard');
            $response->assertSee('Administration');

            $nonAdminUsers = [$this->manager, $this->reviewer, $this->caretaker, $this->resident];
            foreach ($nonAdminUsers as $nonAdminUser) {
                $route = match ($nonAdminUser->role) {
                    'manager' => '/management/dashboard',
                    'reviewer' => '/reviewer/dashboard',
                    'caretaker' => '/caretaker/dashboard',
                    'resident' => '/resident/dashboard',
                };

                $response = $this->actingAs($nonAdminUser)->get($route);
                $response->assertDontSee('Administration');
            }
        });
    });

    describe('Sidebar Icons and Styling', function (): void {
        it('renders SVG icons for navigation items', function (): void {
            $response = $this->actingAs($this->admin)
                ->get('/admin/dashboard');

            // Check for SVG icons
            $response->assertSee('<svg');
            $response->assertSee('fill="none"');
            $response->assertSee('stroke="currentColor"');
        });

        it('applies correct CSS classes for navigation items', function (): void {
            $response = $this->actingAs($this->admin)
                ->get('/admin/dashboard');

            $response->assertSee('group flex items-center rounded-lg px-3 py-2 text-sm font-medium transition-colors duration-200');
            $response->assertSee('text-gray-700 hover:bg-gray-100');
        });

        it('applies active state styling correctly', function (): void {
            $response = $this->actingAs($this->admin)
                ->get('/admin/dashboard');

            $response->assertSee('bg-brand-100 text-brand-700 dark:bg-brand-900/50 dark:text-brand-400');
        });

        it('renders user avatar with initials', function (): void {
            $response = $this->actingAs($this->admin)
                ->get('/admin/dashboard');

            $response->assertSee('flex h-10 w-10 items-center justify-center rounded-full bg-gray-100 text-gray-800');
            $response->assertSee($this->admin->initials());
        });
    });

    describe('Sidebar Responsive Behavior', function (): void {
        it('includes responsive classes for mobile', function (): void {
            $response = $this->actingAs($this->admin)
                ->get('/admin/dashboard');

            $response->assertSee('fixed inset-y-0 left-0 z-30 w-64');
            $response->assertSee('lg:static lg:block lg:translate-x-0');
            $response->assertSee('-translate-x-full');
        });

        it('includes Alpine.js data for sidebar toggle', function (): void {
            $response = $this->actingAs($this->admin)
                ->get('/admin/dashboard');

            $response->assertSee('x-data="{ open: false }"');
            $response->assertSee('x-show="open"');
            $response->assertSee('@sidebar-toggle.window="open = !open"');
        });

        it('includes mobile close button', function (): void {
            $response = $this->actingAs($this->admin)
                ->get('/admin/dashboard');

            $response->assertSee('lg:hidden');
            $response->assertSee('@click="open = false"');
        });
    });

    describe('Sidebar Accessibility', function (): void {
        it('includes proper ARIA labels', function (): void {
            $response = $this->actingAs($this->admin)
                ->get('/admin/dashboard');

            $response->assertSee('aria-label');
        });

        it('includes proper focus states', function (): void {
            $response = $this->actingAs($this->admin)
                ->get('/admin/dashboard');

            $response->assertSee('hover:bg-gray-100');
            $response->assertSee('focus:outline-none');
        });

        it('includes proper color contrast classes for dark mode', function (): void {
            $response = $this->actingAs($this->admin)
                ->get('/admin/dashboard');

            $response->assertSee('dark:text-gray-300');
            $response->assertSee('dark:hover:bg-gray-800');
            $response->assertSee('dark:bg-gray-900');
        });
    });
});
