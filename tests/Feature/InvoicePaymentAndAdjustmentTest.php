<?php

use App\Models\Contact;
use App\Models\Estate;
use App\Models\House;
use App\Models\Invoice;
use App\Models\InvoiceAdjustment;
use App\Models\InvoicePayment;
use App\Models\MeterReading;
use App\Models\User;
use App\Models\WaterRate;

test('invoice can record payment', function (): void {
    // Create test data
    $user = User::factory()->create();
    $estate = Estate::factory()->create();
    $house = House::factory()->create(['estate_id' => $estate->id]);
    $contact = Contact::factory()->create(['house_id' => $house->id]);
    $waterRate = WaterRate::factory()->create(['estate_id' => $estate->id]);
    $meterReading = MeterReading::factory()->create(['house_id' => $house->id]);

    $invoice = Invoice::factory()->create([
        'house_id' => $house->id,
        'meter_reading_id' => $meterReading->id,
        'water_rate_id' => $waterRate->id,
        'total_amount' => 1000,
        'status' => 'sent',
    ]);

    // Test payment recording
    $payment = $invoice->recordPayment(500, 'cash', now(), 'REF123', 'Partial payment');

    // Assert payment was created
    expect($payment)->toBeInstanceOf(InvoicePayment::class);
    expect((float) $payment->amount)->toBe(500.0);
    expect($payment->payment_method)->toBe('cash');
    expect($payment->reference_number)->toBe('REF123');
    expect($payment->notes)->toBe('Partial payment');
    expect($payment->invoice_id)->toBe($invoice->id);

    // Assert invoice calculations
    $invoice->refresh();
    expect((float) $invoice->getTotalPaidAttribute())->toBe(500.0);
    expect((float) $invoice->getBalanceDueAttribute())->toBe(500.0);
    expect($invoice->getIsFullyPaidAttribute())->toBeFalse();
});

test('invoice is marked as paid when fully paid', function (): void {
    // Create test data
    $user = User::factory()->create();
    $estate = Estate::factory()->create();
    $house = House::factory()->create(['estate_id' => $estate->id]);
    $contact = Contact::factory()->create(['house_id' => $house->id]);
    $waterRate = WaterRate::factory()->create(['estate_id' => $estate->id]);
    $meterReading = MeterReading::factory()->create(['house_id' => $house->id]);

    $invoice = Invoice::factory()->create([
        'house_id' => $house->id,
        'meter_reading_id' => $meterReading->id,
        'water_rate_id' => $waterRate->id,
        'total_amount' => 1000,
        'status' => 'sent',
    ]);

    // Test full payment
    $payment = $invoice->recordPayment(1000, 'mpesa', now(), 'MPESA123', 'Full payment');

    // Assert invoice is marked as paid
    $invoice->refresh();
    expect($invoice->status)->toBe('paid');
    expect($invoice->paid_at)->not->toBeNull();
    expect($invoice->payment_reference)->toBe('MPESA123');
    expect($invoice->getIsFullyPaidAttribute())->toBeTrue();
    expect($invoice->getBalanceDueAttribute())->toBe(0.0);
});

test('invoice can add adjustment', function (): void {
    // Create test data
    $user = User::factory()->create();
    $estate = Estate::factory()->create();
    $house = House::factory()->create(['estate_id' => $estate->id]);
    $contact = Contact::factory()->create(['house_id' => $house->id]);
    $waterRate = WaterRate::factory()->create(['estate_id' => $estate->id]);
    $meterReading = MeterReading::factory()->create(['house_id' => $house->id]);

    $invoice = Invoice::factory()->create([
        'house_id' => $house->id,
        'meter_reading_id' => $meterReading->id,
        'water_rate_id' => $waterRate->id,
        'total_amount' => 1000,
        'status' => 'sent',
    ]);

    // Test adjustment addition
    $adjustment = $invoice->addAdjustment(100, 'credit', 'Customer discount', now(), 'discount');

    // Assert adjustment was created
    expect($adjustment)->toBeInstanceOf(InvoiceAdjustment::class);
    expect((float) $adjustment->amount)->toBe(-100.0);
    expect($adjustment->type)->toBe('credit');
    expect($adjustment->description)->toBe('Customer discount');
    expect($adjustment->reason)->toBe('discount');
    expect($adjustment->invoice_id)->toBe($invoice->id);

    // Assert invoice calculations
    $invoice->refresh();
    expect((float) $invoice->getTotalAdjustmentsAttribute())->toBe(-100.0);
    expect((float) $invoice->getBalanceDueAttribute())->toBe(900.0);
});

test('invoice balance calculation includes payments and adjustments', function (): void {
    // Create test data
    $user = User::factory()->create();
    $estate = Estate::factory()->create();
    $house = House::factory()->create(['estate_id' => $estate->id]);
    $contact = Contact::factory()->create(['house_id' => $house->id]);
    $waterRate = WaterRate::factory()->create(['estate_id' => $estate->id]);
    $meterReading = MeterReading::factory()->create(['house_id' => $house->id]);

    $invoice = Invoice::factory()->create([
        'house_id' => $house->id,
        'meter_reading_id' => $meterReading->id,
        'water_rate_id' => $waterRate->id,
        'total_amount' => 1000,
        'status' => 'sent',
    ]);

    // Add payment
    $invoice->recordPayment(300, 'cash', now(), 'PAY123', 'Partial payment');

    // Add credit adjustment
    $invoice->addAdjustment(50, 'credit', 'Small discount', now(), 'discount');

    // Add debit adjustment (penalty)
    $invoice->addAdjustment(25, 'debit', 'Late fee', now(), 'penalty');

    // Assert balance calculation
    $invoice->refresh();
    expect((float) $invoice->getTotalPaidAttribute())->toBe(300.0);
    expect((float) $invoice->getTotalAdjustmentsAttribute())->toBe(-25.0); // -50 + 25
    expect((float) $invoice->getBalanceDueAttribute())->toBe(675.0); // 1000 - 300 - 25
});

test('invoice payment generates reference number automatically', function (): void {
    // Create test data
    $user = User::factory()->create();
    $estate = Estate::factory()->create();
    $house = House::factory()->create(['estate_id' => $estate->id]);
    $contact = Contact::factory()->create(['house_id' => $house->id]);
    $waterRate = WaterRate::factory()->create(['estate_id' => $estate->id]);
    $meterReading = MeterReading::factory()->create(['house_id' => $house->id]);

    $invoice = Invoice::factory()->create([
        'house_id' => $house->id,
        'meter_reading_id' => $meterReading->id,
        'water_rate_id' => $waterRate->id,
        'total_amount' => 1000,
        'status' => 'sent',
    ]);

    // Test payment without reference
    $payment = $invoice->recordPayment(500, 'cash');

    // Assert reference was generated
    expect($payment->reference_number)->not->toBeNull();
    expect($payment->reference_number)->toMatch('/^PAY-\d{8}-[A-Z0-9]{6}$/');
});

test('invoice adjustment generates reference number automatically', function (): void {
    // Create test data
    $user = User::factory()->create();
    $estate = Estate::factory()->create();
    $house = House::factory()->create(['estate_id' => $estate->id]);
    $contact = Contact::factory()->create(['house_id' => $house->id]);
    $waterRate = WaterRate::factory()->create(['estate_id' => $estate->id]);
    $meterReading = MeterReading::factory()->create(['house_id' => $house->id]);

    $invoice = Invoice::factory()->create([
        'house_id' => $house->id,
        'meter_reading_id' => $meterReading->id,
        'water_rate_id' => $waterRate->id,
        'total_amount' => 1000,
        'status' => 'sent',
    ]);

    // Test adjustment
    $adjustment = $invoice->addAdjustment(100, 'credit', 'Discount', now(), 'discount');

    // Assert reference was generated
    expect($adjustment->reference_number)->not->toBeNull();
    expect($adjustment->reference_number)->toMatch('/^ADJ-\d{8}-[A-Z0-9]{6}$/');
});
