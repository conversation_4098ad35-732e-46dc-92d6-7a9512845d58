<?php

use App\Models\Contact;
use App\Models\Estate;
use App\Models\House;
use App\Models\Invoice;
use App\Models\User;
use App\Models\WhatsAppMessage;

uses(\Illuminate\Foundation\Testing\RefreshDatabase::class);

test('can create whatsapp message', function (): void {
    $user = User::factory()->create();
    $contact = Contact::factory()->create();
    $house = House::factory()->create();
    $invoice = Invoice::factory()->create(['house_id' => $house->id]);

    $message = WhatsAppMessage::create([
        'sender_id' => $user->id,
        'recipient_contact_id' => $contact->id,
        'house_id' => $house->id,
        'estate_id' => $house->estate_id,
        'recipient' => $contact->whatsapp_number,
        'message_type' => 'template',
        'content' => 'Test message content',
        'template_name' => 'invoice_delivery',
        'parameters' => ['amount' => 100.00],
        'messageable_type' => Invoice::class,
        'messageable_id' => $invoice->id,
    ]);

    $this->assertDatabaseHas('whatsapp_messages', [
        'id' => $message->id,
        'sender_id' => $user->id,
        'recipient_contact_id' => $contact->id,
        'house_id' => $house->id,
        'estate_id' => $house->estate_id,
        'content' => 'Test message content',
        'status' => 'pending',
    ]);
});

test('message status updates', function (): void {
    $message = WhatsAppMessage::factory()->create(['status' => 'pending']);

    // Test marking as sent
    $message->markAsSent('msg_12345');
    expect($message->fresh()->status)->toEqual('sent');
    expect($message->fresh()->message_id)->toEqual('msg_12345');

    // Test marking as delivered
    $message->markAsDelivered();
    expect($message->fresh()->status)->toEqual('delivered');
    expect($message->fresh()->delivered_at)->not->toBeNull();

    // Test marking as read
    $message->markAsRead();
    expect($message->fresh()->status)->toEqual('read');
    expect($message->fresh()->read_at)->not->toBeNull();

    // Test marking as failed
    $message->markAsFailed('API timeout');
    expect($message->fresh()->status)->toEqual('failed');
    expect($message->fresh()->failed_reason)->toEqual('API timeout');
});

test('polymorphic relationship with invoice', function (): void {
    $invoice = Invoice::factory()->create();
    $message = WhatsAppMessage::factory()
        ->forInvoice($invoice)
        ->create();

    expect($message->messageable_type)->toEqual(Invoice::class);
    expect($message->messageable_id)->toEqual($invoice->id);
    expect($message->messageable)->toBeInstanceOf(Invoice::class);
});

test('house relationship', function (): void {
    $house = House::factory()->create();
    $message = WhatsAppMessage::factory()
        ->forHouse($house)
        ->create();

    expect($message->house)->toBeInstanceOf(House::class);
    expect($message->house->id)->toEqual($house->id);
});

test('estate relationship', function (): void {
    $estate = Estate::factory()->create();
    $message = WhatsAppMessage::factory()
        ->forEstate($estate)
        ->create();

    expect($message->estate)->toBeInstanceOf(Estate::class);
    expect($message->estate->id)->toEqual($estate->id);
});

test('contact relationship', function (): void {
    $contact = Contact::factory()->create();
    $message = WhatsAppMessage::factory()
        ->create(['recipient_contact_id' => $contact->id]);

    expect($message->recipientContact)->toBeInstanceOf(Contact::class);
    expect($message->recipientContact->id)->toEqual($contact->id);
});

test('sender relationship', function (): void {
    $user = User::factory()->create();
    $message = WhatsAppMessage::factory()
        ->create(['sender_id' => $user->id]);

    expect($message->sender)->toBeInstanceOf(User::class);
    expect($message->sender->id)->toEqual($user->id);
});

test('scope filters', function (): void {
    WhatsAppMessage::factory()->count(3)->create(['status' => 'sent']);
    WhatsAppMessage::factory()->count(2)->create(['status' => 'delivered']);
    WhatsAppMessage::factory()->count(1)->create(['status' => 'failed']);

    expect(WhatsAppMessage::byStatus('sent')->get())->toHaveCount(3);
    expect(WhatsAppMessage::byStatus('delivered')->get())->toHaveCount(2);
    expect(WhatsAppMessage::byStatus('failed')->get())->toHaveCount(1);
});

test('estate scope filter', function (): void {
    $estate1 = Estate::factory()->create();
    $estate2 = Estate::factory()->create();

    WhatsAppMessage::factory()->count(3)->forEstate($estate1)->create();
    WhatsAppMessage::factory()->count(2)->forEstate($estate2)->create();

    expect(WhatsAppMessage::forEstate($estate1->id)->get())->toHaveCount(3);
    expect(WhatsAppMessage::forEstate($estate2->id)->get())->toHaveCount(2);
});

test('house scope filter', function (): void {
    $house1 = House::factory()->create();
    $house2 = House::factory()->create();

    WhatsAppMessage::factory()->count(3)->forHouse($house1)->create();
    WhatsAppMessage::factory()->count(2)->forHouse($house2)->create();

    expect(WhatsAppMessage::forHouse($house1->id)->get())->toHaveCount(3);
    expect(WhatsAppMessage::forHouse($house2->id)->get())->toHaveCount(2);
});

test('for invoice scope', function (): void {
    $invoice1 = Invoice::factory()->create();
    $invoice2 = Invoice::factory()->create();

    WhatsAppMessage::factory()->count(3)->forInvoice($invoice1)->create();
    WhatsAppMessage::factory()->count(2)->forInvoice($invoice2)->create();

    expect(WhatsAppMessage::forInvoice($invoice1->id)->get())->toHaveCount(3);
    expect(WhatsAppMessage::forInvoice($invoice2->id)->get())->toHaveCount(2);
});

test('status color and icon methods', function (): void {
    $message = WhatsAppMessage::factory()->create(['status' => 'pending']);
    $this->assertStringContainsString('text-yellow-600', $message->getStatusColor());
    $this->assertStringContainsString('clock', $message->getStatusIcon());

    $message->markAsSent('msg_12345');
    expect($message->fresh()->status)->toEqual('sent');
    $this->assertStringContainsString('text-blue-600', $message->fresh()->getStatusColor());
    $this->assertStringContainsString('paper-airplane', $message->fresh()->getStatusIcon());

    $message->markAsDelivered();
    expect($message->fresh()->status)->toEqual('delivered');
    $this->assertStringContainsString('text-green-600', $message->fresh()->getStatusColor());
    $this->assertStringContainsString('check-circle', $message->fresh()->getStatusIcon());

    $message->markAsRead();
    expect($message->fresh()->status)->toEqual('read');
    $this->assertStringContainsString('text-green-800', $message->fresh()->getStatusColor());
    $this->assertStringContainsString('eye', $message->fresh()->getStatusIcon());

    $message->markAsFailed('API timeout');
    expect($message->fresh()->status)->toEqual('failed');
    $this->assertStringContainsString('text-red-600', $message->fresh()->getStatusColor());
    $this->assertStringContainsString('x-circle', $message->fresh()->getStatusIcon());
});

test('retry count functionality', function (): void {
    $message = WhatsAppMessage::factory()->create(['retry_count' => 0]);
    expect($message->retry_count)->toEqual(0);

    $message->update(['retry_count' => 3]);
    expect($message->fresh()->retry_count)->toEqual(3);
});

test('interactive data storage', function (): void {
    $interactiveData = [
        'type' => 'button',
        'body' => ['text' => 'Please select an option:'],
        'action' => [
            'buttons' => [
                ['type' => 'reply', 'reply' => ['id' => 'pay_now', 'title' => 'Pay Now']],
            ],
        ],
    ];

    $message = WhatsAppMessage::factory()->create([
        'interactive_data' => $interactiveData,
        'message_type' => 'interactive',
    ]);

    expect($message->interactive_data)->toEqual($interactiveData);
    expect($message->message_type)->toEqual('interactive');
});

test('media url storage', function (): void {
    $mediaUrl = 'https://example.com/media/invoice.pdf';

    $message = WhatsAppMessage::factory()->create([
        'media_url' => $mediaUrl,
        'message_type' => 'media',
    ]);

    expect($message->media_url)->toEqual($mediaUrl);
    expect($message->message_type)->toEqual('media');
});

test('message creation fails for inactive contact', function (): void {
    $user = User::factory()->create();
    $contact = Contact::factory()->inactive()->create(['whatsapp_number' => '1234567890']);
    $house = House::factory()->create();

    $message = WhatsAppMessage::create([
        'sender_id' => $user->id,
        'recipient_contact_id' => $contact->id,
        'house_id' => $house->id,
        'estate_id' => $house->estate_id,
        'recipient' => $contact->whatsapp_number,
        'message_type' => 'template',
        'content' => 'Test message',
        'template_name' => 'invoice_delivery',
    ]);

    $this->assertDatabaseHas('whatsapp_messages', [
        'id' => $message->id,
        'status' => 'failed',
        'failed_reason' => 'Contact is inactive or has disabled notifications',
    ]);
});

test('message creation fails for contact with disabled notifications', function (): void {
    $user = User::factory()->create();
    $contact = Contact::factory()->create([
        'whatsapp_number' => '1234567890',
        'receive_notifications' => false,
    ]);
    $house = House::factory()->create();

    $message = WhatsAppMessage::create([
        'sender_id' => $user->id,
        'recipient_contact_id' => $contact->id,
        'house_id' => $house->id,
        'estate_id' => $house->estate_id,
        'recipient' => $contact->whatsapp_number,
        'message_type' => 'template',
        'content' => 'Test message',
        'template_name' => 'invoice_delivery',
    ]);

    $this->assertDatabaseHas('whatsapp_messages', [
        'id' => $message->id,
        'status' => 'failed',
        'failed_reason' => 'Contact is inactive or has disabled notifications',
    ]);
});

test('message creation succeeds for active contact with notifications', function (): void {
    $user = User::factory()->create();
    $contact = Contact::factory()->create([
        'whatsapp_number' => '1234567890',
        'is_active' => true,
        'receive_notifications' => true,
    ]);
    $house = House::factory()->create();

    $message = WhatsAppMessage::create([
        'sender_id' => $user->id,
        'recipient_contact_id' => $contact->id,
        'house_id' => $house->id,
        'estate_id' => $house->estate_id,
        'recipient' => $contact->whatsapp_number,
        'message_type' => 'template',
        'content' => 'Test message',
        'template_name' => 'invoice_delivery',
    ]);

    $this->assertDatabaseHas('whatsapp_messages', [
        'id' => $message->id,
        'status' => 'pending',
        'failed_reason' => null,
    ]);
});

test('contact can receive messages method', function (): void {
    $activeContact = Contact::factory()->create([
        'is_active' => true,
        'receive_notifications' => true,
        'whatsapp_number' => '1234567890', // Add a WhatsApp number for the active contact
    ]);

    $inactiveContact = Contact::factory()->inactive()->create();

    $noNotificationsContact = Contact::factory()->create([
        'receive_notifications' => false,
    ]);

    expect($activeContact->canReceiveMessages())->toBeTrue();
    expect($inactiveContact->canReceiveMessages())->toBeFalse();
    expect($noNotificationsContact->canReceiveMessages())->toBeFalse();
});
