<?php

use App\Models\Estate;
use Illuminate\Support\Facades\Gate;
use Tests\Traits\CreatesTestUsers;
use Tests\Traits\SetsUpSpatiePermissions;

uses(\Illuminate\Foundation\Testing\RefreshDatabase::class, CreatesTestUsers::class, SetsUpSpatiePermissions::class);

test('residents have access to their accounts and invoices', function (): void {
    $resident = $this->createResidentUser();

    // Check resident permissions
    expect($resident->hasPermissionTo('accounts.view_own'))->toBeTrue();
    expect($resident->hasPermissionTo('accounts.view_balance_own'))->toBeTrue();
    expect($resident->hasPermissionTo('accounts.view_transactions_own'))->toBeTrue();
    expect($resident->hasPermissionTo('accounts.view_statement_own'))->toBeTrue();
    expect($resident->hasPermissionTo('accounts.export_statement_own'))->toBeTrue();
    expect($resident->hasPermissionTo('invoices.view_own'))->toBeTrue();
    expect($resident->hasPermissionTo('invoices.download_own'))->toBeTrue();
    expect($resident->hasPermissionTo('invoices.view_payments_own'))->toBeTrue();
    expect($resident->hasPermissionTo('invoices.view_adjustments_own'))->toBeTrue();
    expect($resident->hasPermissionTo('payments.view_own'))->toBeTrue();
    expect($resident->hasPermissionTo('payments.view_history_own'))->toBeTrue();
    expect($resident->hasPermissionTo('resident.payments.create'))->toBeTrue();
    expect($resident->hasPermissionTo('resident.invoices.download'))->toBeTrue();

    // Check resident doesn't have access to other permissions
    expect($resident->hasPermissionTo('accounts.view_all'))->toBeFalse();
    expect($resident->hasPermissionTo('invoices.view_all'))->toBeFalse();
    expect($resident->hasPermissionTo('export.data_all'))->toBeFalse();
});

test('managers have access to reports for assigned estates', function (): void {
    $estate = Estate::factory()->create();
    $manager = $this->createManagerUser($estate->id);

    // Check manager permissions for assigned estates
    expect($manager->hasPermissionTo('reports.view_assigned'))->toBeTrue();
    expect($manager->hasPermissionTo('reports.generate_assigned'))->toBeTrue();
    expect($manager->hasPermissionTo('reports.aging_assigned'))->toBeTrue();
    expect($manager->hasPermissionTo('reports.revenue_assigned'))->toBeTrue();
    expect($manager->hasPermissionTo('reports.billing_assigned'))->toBeTrue();
    expect($manager->hasPermissionTo('reports.customer_statements_assigned'))->toBeTrue();
    expect($manager->hasPermissionTo('analytics.view_assigned'))->toBeTrue();
    expect($manager->hasPermissionTo('export.data_assigned'))->toBeTrue();

    // Check manager has invoice management permissions
    expect($manager->hasPermissionTo('invoices.view_assigned'))->toBeTrue();
    expect($manager->hasPermissionTo('invoices.adjust_assigned'))->toBeTrue();
    expect($manager->hasPermissionTo('invoices.export_assigned'))->toBeTrue();
    expect($manager->hasPermissionTo('invoices.approve_assigned'))->toBeTrue();
    expect($manager->hasPermissionTo('invoices.send_assigned'))->toBeTrue();
    expect($manager->hasPermissionTo('invoices.generate_assigned'))->toBeTrue();

    // Check manager has account access permissions
    expect($manager->hasPermissionTo('accounts.view_assigned'))->toBeTrue();
    expect($manager->hasPermissionTo('accounts.view_balance_assigned'))->toBeTrue();
    expect($manager->hasPermissionTo('accounts.view_transactions_assigned'))->toBeTrue();
    expect($manager->hasPermissionTo('accounts.view_statement_assigned'))->toBeTrue();
    expect($manager->hasPermissionTo('accounts.export_statement_assigned'))->toBeTrue();

    // Check manager doesn't have system-level permissions
    expect($manager->hasPermissionTo('export.data_all'))->toBeFalse();
    expect($manager->hasPermissionTo('invoices.view_all'))->toBeFalse();
});

test('reviewers have full access to accounts invoices and statements for assigned estates', function (): void {
    $estate = Estate::factory()->create();
    $reviewer = $this->createReviewerUser($estate->id);

    // Check reviewer has full account access
    expect($reviewer->hasPermissionTo('accounts.view_assigned'))->toBeTrue();
    expect($reviewer->hasPermissionTo('accounts.manage_assigned'))->toBeTrue();
    expect($reviewer->hasPermissionTo('accounts.view_balance_assigned'))->toBeTrue();
    expect($reviewer->hasPermissionTo('accounts.view_transactions_assigned'))->toBeTrue();
    expect($reviewer->hasPermissionTo('accounts.create_transaction_assigned'))->toBeTrue();
    expect($reviewer->hasPermissionTo('accounts.edit_transaction_assigned'))->toBeTrue();
    expect($reviewer->hasPermissionTo('accounts.view_statement_assigned'))->toBeTrue();
    expect($reviewer->hasPermissionTo('accounts.export_statement_assigned'))->toBeTrue();
    expect($reviewer->hasPermissionTo('accounts.adjust_balance_assigned'))->toBeTrue();

    // Check reviewer has full invoice access
    expect($reviewer->hasPermissionTo('invoices.view_assigned'))->toBeTrue();
    expect($reviewer->hasPermissionTo('invoices.generate_assigned'))->toBeTrue();
    expect($reviewer->hasPermissionTo('invoices.edit_assigned'))->toBeTrue();
    expect($reviewer->hasPermissionTo('invoices.send_assigned'))->toBeTrue();
    expect($reviewer->hasPermissionTo('invoices.export_assigned'))->toBeTrue();
    expect($reviewer->hasPermissionTo('invoices.approve_assigned'))->toBeTrue();
    expect($reviewer->hasPermissionTo('invoices.adjust_assigned'))->toBeTrue();
    expect($reviewer->hasPermissionTo('invoices.create_manual'))->toBeTrue();
    expect($reviewer->hasPermissionTo('invoices.delete_assigned'))->toBeTrue();

    // Check reviewer has full payment access
    expect($reviewer->hasPermissionTo('payments.view_assigned'))->toBeTrue();
    expect($reviewer->hasPermissionTo('payments.approve_assigned'))->toBeTrue();
    expect($reviewer->hasPermissionTo('payments.create_assigned'))->toBeTrue();
    expect($reviewer->hasPermissionTo('payments.edit_assigned'))->toBeTrue();
    expect($reviewer->hasPermissionTo('payments.export_assigned'))->toBeTrue();
    expect($reviewer->hasPermissionTo('payments.reconcile_assigned'))->toBeTrue();

    // Check reviewer has full report access
    expect($reviewer->hasPermissionTo('reports.view_assigned'))->toBeTrue();
    expect($reviewer->hasPermissionTo('reports.generate_assigned'))->toBeTrue();
    expect($reviewer->hasPermissionTo('reports.aging_assigned'))->toBeTrue();
    expect($reviewer->hasPermissionTo('reports.revenue_assigned'))->toBeTrue();
    expect($reviewer->hasPermissionTo('reports.billing_assigned'))->toBeTrue();
    expect($reviewer->hasPermissionTo('reports.customer_statements_assigned'))->toBeTrue();
    expect($reviewer->hasPermissionTo('reports.financial_assigned'))->toBeTrue();

    // Check reviewer doesn't have system-level permissions
    expect($reviewer->hasPermissionTo('export.data_all'))->toBeFalse();
    expect($reviewer->hasPermissionTo('invoices.view_all'))->toBeFalse();
});

test('caretakers have access to balance list for assigned estates', function (): void {
    $estate = Estate::factory()->create();
    $caretaker = $this->createCaretakerUser($estate->id);

    // Check caretaker has balance viewing permissions
    expect($caretaker->hasPermissionTo('accounts.view_balance_assigned'))->toBeTrue();
    expect($caretaker->hasPermissionTo('accounts.view_balance_list_assigned'))->toBeTrue();

    // Check caretaker has basic invoice viewing permissions
    expect($caretaker->hasPermissionTo('invoices.view_assigned'))->toBeTrue();
    expect($caretaker->hasPermissionTo('invoices.view_status_assigned'))->toBeTrue();

    // Check caretaker has basic report permissions
    expect($caretaker->hasPermissionTo('reports.view_assigned'))->toBeTrue();
    expect($caretaker->hasPermissionTo('reports.balance_list_assigned'))->toBeTrue();

    // Check caretaker has contact management permissions
    expect($caretaker->hasPermissionTo('contacts.view_assigned'))->toBeTrue();
    expect($caretaker->hasPermissionTo('contacts.manage_assigned'))->toBeTrue();
    expect($caretaker->hasPermissionTo('contacts.create_assigned'))->toBeTrue();
    expect($caretaker->hasPermissionTo('contacts.edit_assigned'))->toBeTrue();

    // Check caretaker doesn't have financial management permissions
    expect($caretaker->hasPermissionTo('accounts.manage_assigned'))->toBeFalse();
    expect($caretaker->hasPermissionTo('invoices.edit_assigned'))->toBeFalse();
    expect($caretaker->hasPermissionTo('payments.create_assigned'))->toBeFalse();
    expect($caretaker->hasPermissionTo('export.data_assigned'))->toBeFalse();
});

test('admin has full access to all billing system features', function (): void {
    $admin = $this->createAdminUser();

    // Check admin has all system-level permissions
    expect($admin->hasPermissionTo('export.data_all'))->toBeTrue();
    expect($admin->hasPermissionTo('invoices.view_all'))->toBeTrue();
    expect($admin->hasPermissionTo('invoices.generate_all'))->toBeTrue();
    expect($admin->hasPermissionTo('invoices.edit_all'))->toBeTrue();
    expect($admin->hasPermissionTo('invoices.delete'))->toBeTrue();
    expect($admin->hasPermissionTo('invoices.send_all'))->toBeTrue();
    expect($admin->hasPermissionTo('invoices.adjust_all'))->toBeTrue();
    expect($admin->hasPermissionTo('invoices.export_all'))->toBeTrue();

    // Check admin has full account access
    expect($admin->hasPermissionTo('accounts.view_all'))->toBeTrue();
    expect($admin->hasPermissionTo('accounts.manage_all'))->toBeTrue();
    expect($admin->hasPermissionTo('accounts.view_balance_all'))->toBeTrue();
    expect($admin->hasPermissionTo('accounts.view_transactions_all'))->toBeTrue();
    expect($admin->hasPermissionTo('accounts.view_statement_all'))->toBeTrue();
    expect($admin->hasPermissionTo('accounts.export_statement_all'))->toBeTrue();

    // Check admin has full report access
    expect($admin->hasPermissionTo('reports.view_all'))->toBeTrue();
    expect($admin->hasPermissionTo('reports.generate_all'))->toBeTrue();
    expect($admin->hasPermissionTo('reports.aging_all'))->toBeTrue();
    expect($admin->hasPermissionTo('reports.revenue_all'))->toBeTrue();
    expect($admin->hasPermissionTo('reports.billing_all'))->toBeTrue();
    expect($admin->hasPermissionTo('reports.customer_statements_all'))->toBeTrue();
    expect($admin->hasPermissionTo('reports.financial_all'))->toBeTrue();
    expect($admin->hasPermissionTo('analytics.view_all'))->toBeTrue();

    // Check admin has full payment access
    expect($admin->hasPermissionTo('payments.view_all'))->toBeTrue();
    expect($admin->hasPermissionTo('payments.approve_all'))->toBeTrue();
    expect($admin->hasPermissionTo('payments.export_all'))->toBeTrue();
});

test('gates are properly registered for new permissions', function (): void {
    // Test that gates are registered for new permissions
    expect(Gate::has('accounts.view_own'))->toBeTrue();
    expect(Gate::has('accounts.view_assigned'))->toBeTrue();
    expect(Gate::has('accounts.view_all'))->toBeTrue();
    expect(Gate::has('invoices.view_own'))->toBeTrue();
    expect(Gate::has('invoices.view_assigned'))->toBeTrue();
    expect(Gate::has('invoices.view_all'))->toBeTrue();
    expect(Gate::has('payments.view_own'))->toBeTrue();
    expect(Gate::has('payments.view_assigned'))->toBeTrue();
    expect(Gate::has('payments.view_all'))->toBeTrue();
    expect(Gate::has('reports.aging_assigned'))->toBeTrue();
    expect(Gate::has('reports.revenue_assigned'))->toBeTrue();
    expect(Gate::has('reports.billing_assigned'))->toBeTrue();
    expect(Gate::has('reports.customer_statements_assigned'))->toBeTrue();
    expect(Gate::has('reports.balance_list_assigned'))->toBeTrue();
});
