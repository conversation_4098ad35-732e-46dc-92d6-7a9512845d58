<?php

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

describe('Sidebar Navigation', function (): void {
    beforeEach(function (): void {
        // Create test users for each role
        $this->admin = User::factory()->admin();
        $this->manager = User::factory()->manager();
        $this->reviewer = User::factory()->reviewer();
        $this->caretaker = User::factory()->caretaker();
        $this->resident = User::factory()->resident();

        // Create estate and assign to manager and caretaker
        $this->estate = \App\Models\Estate::factory()->create();
        $this->manager->assignedEstates()->attach($this->estate, ['assigned_by' => $this->manager->id]);
        $this->caretaker->assignedEstates()->attach($this->estate, ['assigned_by' => $this->caretaker->id]);
    });

    describe('Admin Sidebar', function (): void {
        it('shows admin-specific navigation items', function (): void {
            $response = $this->actingAs($this->admin)
                ->get('/admin/dashboard');

            $response->assertSee('Admin Dashboard');
            $response->assertSee('Users');
            $response->assertSee('Estate Assignments');
            $response->assertSee('Team Management');
            $response->assertSee('System Settings');
            $response->assertSee('Estates');
            $response->assertSee('Houses');
            $response->assertSee('Contacts');
            $response->assertSee('Audit Logs');
            $response->assertSee('Analytics');
            $response->assertSee('Reports');
            $response->assertSee('Aging Report');
            $response->assertSee('Revenue Report');
            $response->assertSee('Data Export');
        });

        it('does not show role-specific items from other roles', function (): void {
            $response = $this->actingAs($this->admin)
                ->get('/admin/dashboard');

            $response->assertDontSee('Record Readings');
            $response->assertDontSee('Pending Review');
            $response->assertDontSee('My Bills');
            $response->assertDontSee('Usage History');
        });
    });

    describe('Manager Sidebar', function (): void {
        it('shows manager-specific navigation items', function (): void {
            $response = $this->actingAs($this->manager)
                ->get('/management/dashboard');

            $response->assertSee('Management Dashboard');
            $response->assertSee('Estates');
            $response->assertSee('Houses');
            $response->assertSee('Contacts');
            $response->assertSee('Estate Analytics');
            $response->assertSee('Team Users');
            $response->assertSee('Estate Assignments');
            $response->assertSee('Reports');
            $response->assertSee('Data Export');
        });

        it('does not show admin-specific items', function (): void {
            $response = $this->actingAs($this->manager)
                ->get('/management/dashboard');

            $response->assertDontSee('Admin Dashboard');
            $response->assertDontSee('System Settings');
            $response->assertDontSee('Audit Logs');
            $response->assertDontSee('Aging Report');
            $response->assertDontSee('Revenue Report');
        });
    });

    describe('Reviewer Sidebar', function (): void {
        it('shows reviewer-specific navigation items', function (): void {
            $response = $this->actingAs($this->reviewer)
                ->get('/reviewer/dashboard');

            $response->assertSee('Reviewer Dashboard');
            $response->assertSee('Invoices');
            $response->assertSee('Invoice Reports');
            $response->assertSee('All Readings');
            $response->assertSee('Pending Review');
            $response->assertSee('Estates');
            $response->assertSee('Houses');
            $response->assertSee('Contacts');
            $response->assertSee('Reports');
            $response->assertSee('Data Export');
        });

        it('does not show management or admin items', function (): void {
            $response = $this->actingAs($this->reviewer)
                ->get('/reviewer/dashboard');

            $response->assertDontSee('Estate Analytics');
            $response->assertDontSee('Team Users');
            $response->assertDontSee('System Settings');
            $response->assertDontSee('Audit Logs');
            $response->assertDontSee('Analytics');
        });
    });

    describe('Caretaker Sidebar', function (): void {
        it('shows caretaker-specific navigation items', function (): void {
            $response = $this->actingAs($this->caretaker)
                ->get('/caretaker/dashboard');

            $response->assertSee('Caretaker Dashboard');
            $response->assertSee('Record Readings');
            $response->assertSee('View Readings');
            $response->assertSee('Manage Contacts');
            $response->assertSee('My Estates');
            $response->assertSee('My Houses');
        });

        it('does not show billing or admin features', function (): void {
            $response = $this->actingAs($this->caretaker)
                ->get('/caretaker/dashboard');

            $response->assertDontSee('Invoices');
            $response->assertDontSee('Reports');
            $response->assertDontSee('Data Export');
            $response->assertDontSee('System Settings');
            $response->assertDontSee('Audit Logs');
            $response->assertDontSee('Analytics');
        });
    });

    describe('Resident Navigation', function (): void {
        it('shows resident-specific navigation items in header', function (): void {
            $response = $this->actingAs($this->resident)
                ->get('/resident/dashboard');

            // Resident uses a different layout with navigation in header
            $response->assertSee('Dashboard');
            $response->assertSee('Invoices');
            $response->assertSee('Readings');
            $response->assertSee('Messages');
            $response->assertSee('Contact Us');
        });

        it('does not show sidebar management features', function (): void {
            $response = $this->actingAs($this->resident)
                ->get('/resident/dashboard');

            // Resident layout doesn't have sidebar management sections
            $response->assertDontSee('Water Operations');
            $response->assertDontSee('Administration');
            $response->assertDontSee('User Management');
            $response->assertDontSee('System Configuration');
        });
    });

    describe('Common Navigation Items', function (): void {
        it('shows estates link to appropriate roles', function (): void {
            // Admin should see estates
            $response = $this->actingAs($this->admin)->get('/admin/dashboard');
            $response->assertSee('Estates');

            // Manager should see estates
            $response = $this->actingAs($this->manager)->get('/management/dashboard');
            $response->assertSee('Estates');

            // Reviewer should see estates
            $response = $this->actingAs($this->reviewer)->get('/reviewer/dashboard');
            $response->assertSee('Estates');

            // Caretaker should see estates
            $response = $this->actingAs($this->caretaker)->get('/caretaker/dashboard');
            $response->assertSee('Estates');

            // Resident should not see estates
            $response = $this->actingAs($this->resident)->get('/resident/dashboard');
            $response->assertDontSee('Estates');
        });

        it('shows houses link to appropriate roles', function (): void {
            // Admin should see houses
            $response = $this->actingAs($this->admin)->get('/admin/dashboard');
            $response->assertSee('Houses');

            // Manager should see houses
            $response = $this->actingAs($this->manager)->get('/management/dashboard');
            $response->assertSee('Houses');

            // Reviewer should see houses
            $response = $this->actingAs($this->reviewer)->get('/reviewer/dashboard');
            $response->assertSee('Houses');

            // Caretaker should see houses
            $response = $this->actingAs($this->caretaker)->get('/caretaker/dashboard');
            $response->assertSee('Houses');

            // Resident should not see houses in sidebar (uses different navigation structure)
            $response = $this->actingAs($this->resident)->get('/resident/dashboard');
            // Note: Resident may see "Houses" in content, but not as sidebar navigation
        });

        it('shows contacts link to appropriate roles', function (): void {
            // Admin should see contacts
            $response = $this->actingAs($this->admin)->get('/admin/dashboard');
            $response->assertSee('Contacts');

            // Manager should see contacts
            $response = $this->actingAs($this->manager)->get('/management/dashboard');
            $response->assertSee('Contacts');

            // Reviewer should see contacts
            $response = $this->actingAs($this->reviewer)->get('/reviewer/dashboard');
            $response->assertSee('Contacts');

            // Caretaker should see contacts
            $response = $this->actingAs($this->caretaker)->get('/caretaker/dashboard');
            $response->assertSee('Manage Contacts');

            // Resident should not see contacts (they see "My Contacts" instead)
            $response = $this->actingAs($this->resident)->get('/resident/dashboard');
            $response->assertDontSee('Contacts');
        });
    });

    describe('Route Accessibility', function (): void {
        it('admin routes exist and are accessible', function (): void {
            $this->actingAs($this->admin);

            // Check that routes exist (may not return 200 due to permissions, but should not be 404)
            $this->get(route('estates'))->assertStatusNot(404);
            $this->get(route('houses'))->assertStatusNot(404);
            $this->get(route('contacts'))->assertStatusNot(404);
            $this->get(route('estates.analytics'))->assertStatusNot(404);
        });

        it('manager routes exist and are accessible', function (): void {
            $this->actingAs($this->manager);

            // Check that routes exist (may not return 200 due to permissions, but should not be 404)
            $this->get(route('management.estates'))->assertStatusNot(404);
            $this->get(route('management.houses'))->assertStatusNot(404);
            $this->get(route('management.contacts'))->assertStatusNot(404);
            $this->get(route('management.estates.analytics'))->assertStatusNot(404);
        });

        it('allows reviewer to access reviewer routes', function (): void {
            $this->actingAs($this->reviewer);

            $this->get(route('reviewer.billing'))->assertOk();
            $this->get(route('reviewer.readings'))->assertOk();
            $this->get(route('reviewer.readings.pending'))->assertOk();
        });

        it('allows caretaker to access caretaker routes', function (): void {
            $this->actingAs($this->caretaker);

            $this->get(route('caretaker.readings.create'))->assertOk();
            $this->get(route('caretaker.readings'))->assertOk();
            $this->get(route('caretaker.estates'))->assertOk();
        });

        it('allows resident to access resident routes', function (): void {
            $this->actingAs($this->resident);

            $this->get(route('resident.invoices'))->assertOk();
            $this->get(route('resident.readings'))->assertOk();
            $this->get(route('resident.messages'))->assertOk();
        });
    });

    describe('Permission-based Visibility', function (): void {
        it('respects analytics permissions', function (): void {
            // Admin should see Analytics
            $response = $this->actingAs($this->admin)->get('/admin/dashboard');
            $response->assertSee('Analytics');

            // Manager should see Estate Analytics but not Analytics
            $response = $this->actingAs($this->manager)
                ->get('/management/dashboard');
            $response->assertSee('Estate Analytics');

            // Other roles should not see any analytics
            $response = $this->actingAs($this->reviewer)->get('/reviewer/dashboard');
            $response->assertDontSee('Estate Analytics');
            $response->assertDontSee('Analytics');

            $response = $this->actingAs($this->caretaker)->get('/caretaker/dashboard');
            $response->assertDontSee('Estate Analytics');
            $response->assertDontSee('Analytics');

            $response = $this->actingAs($this->resident)->get('/resident/dashboard');
            $response->assertDontSee('Estate Analytics');
            $response->assertDontSee('Analytics');
        });

        it('respects reading permissions', function (): void {
            // Caretaker should see Record Readings
            $response = $this->actingAs($this->caretaker)->get('/caretaker/dashboard');
            $response->assertSee('Record Readings');

            // Reviewer should see Pending Review
            $response = $this->actingAs($this->reviewer)->get('/reviewer/dashboard');
            $response->assertSee('Pending Review');

            // Admin should not see Record Readings (different structure)
            $response = $this->actingAs($this->admin)->get('/admin/dashboard');
            $response->assertDontSee('Record Readings');

            // Manager should not see reading-specific items
            $response = $this->actingAs($this->manager)->get('/management/dashboard');
            $response->assertDontSee('Record Readings');
            $response->assertDontSee('Pending Review');

            // Resident should see Readings in navigation
            $response = $this->actingAs($this->resident)
                ->get('/resident/dashboard');
            $response->assertSee('Readings');
        });

        it('respects administration permissions', function (): void {
            // Only admin should see administration sections
            $response = $this->actingAs($this->admin)->get('/admin/dashboard');
            $response->assertSee('User Management');
            $response->assertSee('System Configuration');
            $response->assertSee('Monitoring & Audit');
            $response->assertSee('Reports & Exports');

            // Other roles should not see admin sections
            $nonAdminUsers = [$this->manager, $this->reviewer, $this->caretaker, $this->resident];
            foreach ($nonAdminUsers as $nonAdminUser) {
                $route = match ($nonAdminUser->role) {
                    'manager' => '/management/dashboard',
                    'reviewer' => '/reviewer/dashboard',
                    'caretaker' => '/caretaker/dashboard',
                    'resident' => '/resident/dashboard',
                };

                $response = $this->actingAs($nonAdminUser)->get($route);
                $response->assertDontSee('User Management');
                $response->assertDontSee('System Configuration');
                $response->assertDontSee('Monitoring & Audit');
            }
        });
    });
});
