<?php

use App\Livewire\Auth\Register;
use Livewire\Livewire;

uses(\Illuminate\Foundation\Testing\RefreshDatabase::class);

test('registration screen can be rendered', function (): void {
    $response = $this->get('/register');

    $response->assertStatus(200);
});

test('new users can register', function (): void {
    $testable = Livewire::test(Register::class)
        ->set('name', 'Test User')
        ->set('email', '<EMAIL>')
        ->set('password', 'password')
        ->set('password_confirmation', 'password')
        ->call('register');

    $testable
        ->assertHasNoErrors()
        ->assertRedirect(route('dashboard', absolute: false));

    $this->assertAuthenticated();
});
