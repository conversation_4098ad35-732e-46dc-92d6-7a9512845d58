<?php

use App\Models\Estate;
use App\Models\House;
use App\Models\Invoice;
use App\Models\InvoiceAdjustment;
use App\Models\InvoiceLineItem;
use App\Models\InvoicePayment;
use App\Models\User;
use App\Models\WaterRate;

uses(\Illuminate\Foundation\Testing\RefreshDatabase::class);

beforeEach(function (): void {
    $this->user = User::factory()->admin();
    $this->estate = Estate::factory()->create();
    $this->house = House::factory()->create([
        'estate_id' => $this->estate->id,
        'house_number' => 'A101',
        'meter_number' => 'MTR001',
    ]);
    $this->waterRate = WaterRate::factory()->create([
        'estate_id' => $this->estate->id,
        'rate_per_unit' => 50.00,
        'effective_from' => now()->subMonth(),
    ]);
});

test('can create invoice with line items', function (): void {
    $invoice = Invoice::create([
        'invoice_number' => 'INV-2024-001',
        'house_id' => $this->house->id,
        'meter_reading_id' => \App\Models\MeterReading::factory()->create(['house_id' => $this->house->id])->id,
        'water_rate_id' => $this->waterRate->id,
        'billing_period_start' => now()->subMonth(),
        'billing_period_end' => now(),
        'previous_reading' => 1000,
        'current_reading' => 1050,
        'consumption' => 50,
        'rate_per_unit' => 50.00,
        'amount' => 2500.00,
        'fixed_charge' => 0,
        'total_amount' => 2500.00,
        'due_date' => now()->addDays(7),
        'status' => 'draft',
    ]);

    InvoiceLineItem::create([
        'invoice_id' => $invoice->id,
        'type' => 'consumption',
        'description' => 'Water consumption - 50 units',
        'quantity' => 50,
        'rate' => 50.00,
        'amount' => 2500.00,
    ]);

    $this->assertDatabaseHas('invoices', [
        'invoice_number' => 'INV-2024-001',
        'total_amount' => 2500.00,
        'status' => 'draft',
    ]);

    $this->assertDatabaseHas('invoice_line_items', [
        'invoice_id' => $invoice->id,
        'quantity' => 50,
        'rate' => 50.00,
    ]);
});

test('can view invoice detail with proper formatting', function (): void {
    $invoice = Invoice::factory()->create([
        'house_id' => $this->house->id,
        'billing_period_start' => now()->subMonth(),
        'billing_period_end' => now(),
        'due_date' => now()->addDays(7),
        'total_amount' => 2500.00,
        'status' => 'draft',
    ]);

    InvoiceLineItem::create([
        'invoice_id' => $invoice->id,
        'type' => 'consumption',
        'description' => 'Water consumption',
        'quantity' => 50,
        'rate' => 50.00,
        'amount' => 2500.00,
    ]);

    $response = $this->actingAs($this->user)->get("/invoices/{$invoice->id}");

    $response->assertStatus(200)
        ->assertSee('Water consumption')
        ->assertSee('50.00')
        ->assertSee('2,500.00');
});

test('can record payment against invoice', function (): void {
    $invoice = Invoice::factory()->create([
        'house_id' => $this->house->id,
        'total_amount' => 2500.00,
        'status' => 'sent',
    ]);

    $payment = InvoicePayment::create([
        'invoice_id' => $invoice->id,
        'amount' => 1500.00,
        'payment_date' => now(),
        'payment_method' => 'cash',
        'reference_number' => 'PAY-TEST-001',
        'notes' => 'First payment',
    ]);

    $invoice->refresh();
    expect($invoice->payments->sum('amount'))->toEqual(1500.00);
    expect($invoice->balance_due)->toEqual(1000.00);
});

test('can add adjustment to invoice', function (): void {
    $invoice = Invoice::factory()->create([
        'house_id' => $this->house->id,
        'total_amount' => 2500.00,
        'status' => 'sent',
    ]);

    $adjustment = InvoiceAdjustment::create([
        'invoice_id' => $invoice->id,
        'amount' => -200.00,
        'type' => 'discount',
        'reason' => 'Early payment discount',
        'adjustment_date' => now(),
        'reference_number' => 'ADJ-TEST-002',
    ]);

    $invoice->refresh();
    expect($invoice->adjustments->sum('amount'))->toEqual(-200.00);
    expect($invoice->total_amount + $invoice->adjustments->sum('amount'))->toEqual(2300.00);
});

test('complete invoice workflow test', function (): void {
    // 1. Create invoice
    $invoice = Invoice::factory()->create([
        'house_id' => $this->house->id,
        'billing_period_start' => now()->subMonth(),
        'billing_period_end' => now(),
        'due_date' => now()->addDays(7),
        'total_amount' => 3000.00,
        'status' => 'sent',
    ]);

    // 2. Verify initial state
    expect($invoice->total_amount)->toEqual(3000.00);
    expect($invoice->balance_due)->toEqual(3000.00);
    expect($invoice->status)->toEqual('sent');

    // 3. Record partial payment
    InvoicePayment::create([
        'invoice_id' => $invoice->id,
        'amount' => 1500.00,
        'payment_date' => now(),
        'payment_method' => 'mpesa',
        'reference_number' => 'PAY-TEST-002',
    ]);

    $invoice->refresh();
    expect($invoice->payments->sum('amount'))->toEqual(1500.00);
    expect($invoice->balance_due)->toEqual(1500.00);

    // 4. Add discount adjustment
    InvoiceAdjustment::create([
        'invoice_id' => $invoice->id,
        'amount' => -300.00,
        'type' => 'discount',
        'reason' => 'Early payment discount',
        'adjustment_date' => now(),
        'reference_number' => 'ADJ-TEST-002',
    ]);

    $invoice->refresh();
    expect($invoice->adjustments->sum('amount'))->toEqual(-300.00);
    expect($invoice->balance_due)->toEqual(1200.00);

    // 5. Record final payment
    InvoicePayment::create([
        'invoice_id' => $invoice->id,
        'amount' => 1200.00,
        'payment_date' => now()->addDays(3),
        'payment_method' => 'cash',
        'reference_number' => 'PAY-TEST-003',
    ]);

    $invoice->refresh();
    expect($invoice->payments->sum('amount'))->toEqual(2700.00);
    expect($invoice->balance_due)->toEqual(0.00);

    // 6. Mark as paid
    $invoice->update(['status' => 'paid']);
    expect($invoice->fresh()->status)->toEqual('paid');
});

test('invoice detail view shows correct data', function (): void {
    $invoice = Invoice::factory()->create([
        'house_id' => $this->house->id,
        'invoice_number' => 'INV-TEST-001',
        'billing_period_start' => now()->subMonth(),
        'billing_period_end' => now(),
        'due_date' => now()->addDays(7),
        'total_amount' => 1500.00,
        'status' => 'draft',
    ]);

    InvoiceLineItem::create([
        'invoice_id' => $invoice->id,
        'type' => 'consumption',
        'description' => 'Water consumption - 30 units',
        'quantity' => 30,
        'rate' => 50.00,
        'amount' => 1500.00,
    ]);

    $response = $this->actingAs($this->user)->get("/invoices/{$invoice->id}");

    $response->assertStatus(200)
        ->assertSee('Water consumption - 30 units')
        ->assertSee('30.00') // Quantity
        ->assertSee('1,500.00');
    // Total
});
