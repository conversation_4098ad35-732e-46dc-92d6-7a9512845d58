<?php

use App\Models\User;
use Tests\Traits\SetsUpSpatiePermissions;

uses(\Illuminate\Foundation\Testing\RefreshDatabase::class, SetsUpSpatiePermissions::class);

beforeEach(function (): void {
    // Set up Spatie permissions for tests
    $this->setUpSpatiePermissions();
});

test('nav link component renders correctly with permission', function (): void {
    $userFactory = User::factory()->manager();
    $this->actingAs($userFactory);

    $response = $this->get(route('dashboard'));

    $response->assertStatus(302);
    // Redirects to role-specific dashboard
});

test('admin user sees admin sidebar', function (): void {
    $userFactory = User::factory()->admin();
    $this->actingAs($userFactory);

    $response = $this->get(route('admin.dashboard'));

    $response->assertStatus(200);
    $response->assertSee('Admin Dashboard');
    $response->assertSee('Users');
    $response->assertSee('System Settings');
});

test('manager user sees manager sidebar', function (): void {
    $estate = \App\Models\Estate::factory()->create();
    $userFactory = User::factory()->manager();
    $userFactory->assignedEstates()->attach($estate, ['assigned_by' => $userFactory->id]);
    $this->actingAs($userFactory);

    $response = $this->get(route('management.dashboard'));

    $response->assertStatus(200);
    $response->assertSee('Management Dashboard');
    $response->assertSee('Estates');
    $response->assertSee('Houses');
    $response->assertSee('Contacts');
    $response->assertSee('Estate Analytics');
    $response->assertSee('Team Users');
    $response->assertSee('Estate Assignments');
    $response->assertSee('Reports');
    $response->assertSee('Data Export');
});

test('reviewer user sees reviewer sidebar', function (): void {
    $userFactory = User::factory()->reviewer();
    $this->actingAs($userFactory);

    $response = $this->get(route('reviewer.dashboard'));

    $response->assertStatus(200);
    $response->assertSee('Reviewer Dashboard');
    $response->assertSee('Invoices');
    $response->assertSee('Invoice Reports');
    $response->assertSee('All Readings');
    $response->assertSee('Pending Review');
    $response->assertSee('Estates');
    $response->assertSee('Houses');
    $response->assertSee('Contacts');
    $response->assertSee('Reports');
    $response->assertSee('Data Export');
});

test('caretaker user sees caretaker sidebar', function (): void {
    $estate = \App\Models\Estate::factory()->create();
    $userFactory = User::factory()->caretaker();
    $userFactory->assignedEstates()->attach($estate, ['assigned_by' => $userFactory->id]);
    $this->actingAs($userFactory);

    $response = $this->get(route('caretaker.dashboard'));

    $response->assertStatus(200);
    $response->assertSee('Caretaker Dashboard');
    $response->assertSee('Record Readings');
    $response->assertSee('View Readings');
    $response->assertSee('Manage Contacts');
    $response->assertSee('My Estates');
    $response->assertSee('My Houses');
});

test('resident user sees resident sidebar', function (): void {
    $userFactory = User::factory()->resident();

    // Create a house and contact for the resident
    $estate = \App\Models\Estate::factory()->create();
    $house = \App\Models\House::factory()->create(['estate_id' => $estate->id]);
    \App\Models\Contact::factory()->create([
        'user_id' => $userFactory->id,
        'house_id' => $house->id,
        'type' => 'owner',
        'is_active' => true,
    ]);

    $this->actingAs($userFactory);

    $response = $this->get(route('resident.dashboard'));

    if ($response->status() === 500) {
        $content = $response->getContent();
        $this->fail('Test failed with 500 error. Response content: '.$content);
    }

    $response->assertStatus(200);
    $response->assertSee('Resident Portal');
    // Check that resident portal loads
});

test('admin user cannot access manager dashboard', function (): void {
    $userFactory = User::factory()->admin();
    $this->actingAs($userFactory);

    $response = $this->get(route('management.dashboard'));

    $response->assertStatus(403);
});

test('manager user cannot access admin dashboard', function (): void {
    $estate = \App\Models\Estate::factory()->create();
    $userFactory = User::factory()->manager();
    $userFactory->assignedEstates()->attach($estate, ['assigned_by' => $userFactory->id]);
    $this->actingAs($userFactory);

    $response = $this->get(route('admin.dashboard'));

    $response->assertStatus(403);
});

test('reviewer user cannot access caretaker dashboard', function (): void {
    $userFactory = User::factory()->reviewer();
    $this->actingAs($userFactory);

    $response = $this->get(route('caretaker.dashboard'));

    $response->assertStatus(403);
});

test('caretaker user cannot access reviewer dashboard', function (): void {
    $estate = \App\Models\Estate::factory()->create();
    $userFactory = User::factory()->caretaker();
    $userFactory->assignedEstates()->attach($estate, ['assigned_by' => $userFactory->id]);
    $this->actingAs($userFactory);

    $response = $this->get(route('reviewer.dashboard'));

    $response->assertStatus(403);
});

test('navigation links have correct active states', function (): void {
    $estate = \App\Models\Estate::factory()->create();
    $userFactory = User::factory()->manager();
    $userFactory->assignedEstates()->attach($estate, ['assigned_by' => $userFactory->id]);
    $this->actingAs($userFactory);

    $response = $this->get(route('management.dashboard'));

    $response->assertStatus(200);

    // Check that the dashboard link is marked as active
    $response->assertSee('bg-blue-50 text-blue-600');
});

test('admin dashboard shows system statistics', function (): void {
    $userFactory = User::factory()->admin();
    $this->actingAs($userFactory);

    $response = $this->get(route('admin.dashboard'));

    $response->assertStatus(200);
    $response->assertSee('Total Users');
    $response->assertSee('Total Estates');
    $response->assertSee('System Status');
});

test('unauthenticated user cannot access any dashboard', function (): void {
    $response = $this->get(route('dashboard'));
    $response->assertRedirect('/login');
});

test('main dashboard redirects to role specific dashboard', function (): void {
    $estate = \App\Models\Estate::factory()->create();
    $manager = User::factory()->manager();
    $manager->assignedEstates()->attach($estate, ['assigned_by' => $manager->id]);

    $this->actingAs($manager);
    $response = $this->get(route('dashboard'));
    $response->assertRedirect(route('management.dashboard'));

    $reviewer = User::factory()->reviewer();
    $this->actingAs($reviewer);
    $response = $this->get(route('dashboard'));
    $response->assertRedirect(route('reviewer.dashboard'));

    $caretaker = User::factory()->caretaker();
    $caretaker->assignedEstates()->attach($estate, ['assigned_by' => $caretaker->id]);
    $this->actingAs($caretaker);
    $response = $this->get(route('dashboard'));
    $response->assertRedirect(route('caretaker.dashboard'));
});
