<?php

use App\Models\User;

uses(\Illuminate\Foundation\Testing\RefreshDatabase::class);

test('management user can access management dashboard', function (): void {
    $estate = \App\Models\Estate::factory()->create();
    $userFactory = User::factory()->manager();
    $userFactory->assignedEstates()->attach($estate, ['assigned_by' => $userFactory->id]);
    $this->actingAs($userFactory);

    $response = $this->get(route('management.dashboard'));

    $response->assertStatus(200);
});

test('reviewer user can access reviewer dashboard', function (): void {
    $estate = \App\Models\Estate::factory()->create();
    $userFactory = User::factory()->reviewer();
    $userFactory->assignedEstates()->attach($estate, ['assigned_by' => $userFactory->id]);
    $this->actingAs($userFactory);

    $response = $this->get(route('reviewer.dashboard'));

    $response->assertStatus(200);
});

test('caretaker user can access caretaker dashboard', function (): void {
    $estate = \App\Models\Estate::factory()->create();
    $userFactory = User::factory()->caretaker();
    $userFactory->assignedEstates()->attach($estate, ['assigned_by' => $userFactory->id]);

    $this->actingAs($userFactory);

    $response = $this->get(route('caretaker.dashboard'));

    if ($response->status() !== 200) {
        echo 'Response status: '.$response->status()."\n";
        echo 'Response content: '.$response->content()."\n";
    }

    $response->assertStatus(200);
});

test('management user cannot access reviewer dashboard', function (): void {
    $userFactory = User::factory()->manager();
    $this->actingAs($userFactory);

    $response = $this->get(route('reviewer.dashboard'));

    $response->assertStatus(403);
});

test('reviewer user cannot access management dashboard',
    function (): void {
        $userFactory = User::factory()->reviewer();
        $this->actingAs($userFactory);

        $response = $this->get(route('management.dashboard'));

        $response->assertStatus(403);
    });
