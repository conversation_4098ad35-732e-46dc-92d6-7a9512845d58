<?php

use App\Models\Estate;
use Tests\Traits\CreatesTestUsers;
use Tests\Traits\HasSpatiePermissions;

uses(\Illuminate\Foundation\Testing\RefreshDatabase::class);
uses(CreatesTestUsers::class);
uses(HasSpatiePermissions::class);

beforeEach(function () {
    $this->setUpPermissions();
});

test('management user can access management dashboard', function (): void {
    $estate = Estate::factory()->create();
    $user = $this->createManagerUser();

    // Assign estate to user
    $user->assignedEstates()->attach($estate->id, [
        'assigned_from' => now()->toDateString(),
        'is_active' => true,
    ]);

    $this->actingAs($user);

    $response = $this->get(route('management.dashboard'));

    $response->assertStatus(200);
});

test('reviewer user can access reviewer dashboard', function (): void {
    $estate = Estate::factory()->create();
    $user = $this->createReviewerUser();

    // Assign estate to user
    $user->assignedEstates()->attach($estate->id, [
        'assigned_from' => now()->toDateString(),
        'is_active' => true,
    ]);

    $this->actingAs($user);

    $response = $this->get(route('reviewer.dashboard'));

    $response->assertStatus(200);
});

test('caretaker user can access caretaker dashboard', function (): void {
    $estate = Estate::factory()->create();
    $user = $this->createCaretakerUser();

    // Assign estate to user
    $user->assignedEstates()->attach($estate->id, [
        'assigned_from' => now()->toDateString(),
        'is_active' => true,
    ]);

    $this->actingAs($user);

    $response = $this->get(route('caretaker.dashboard'));

    $response->assertStatus(200);
});

test('management user cannot access reviewer dashboard', function (): void {
    $user = $this->createManagerUser();
    $this->actingAs($user);

    $response = $this->get(route('reviewer.dashboard'));

    $response->assertStatus(403);
});

test('reviewer user cannot access management dashboard', function (): void {
    $user = $this->createReviewerUser();
    $this->actingAs($user);

    $response = $this->get(route('management.dashboard'));

    $response->assertStatus(403);
});
