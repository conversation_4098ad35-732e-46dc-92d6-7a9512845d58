<?php

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

describe('Sidebar Route Generation', function (): void {
    beforeEach(function (): void {
        // Create test users for each role
        $this->admin = User::factory()->admin()->create();
        $this->manager = User::factory()->manager()->create();
        $this->reviewer = User::factory()->reviewer()->create();
        $this->caretaker = User::factory()->caretaker()->create();
        $this->resident = User::factory()->resident()->create();
    });

    describe('Estate Routes', function (): void {
        it('generates correct estate route for admin', function (): void {
            $response = $this->actingAs($this->admin)
                ->get('/admin/dashboard');

            $response->assertSee('href="'.route('estates').'"');
            $response->assertDontSee(route('management.estates'));
            $response->assertDontSee(route('reviewer.estates'));
            $response->assertDontSee(route('caretaker.estates'));
        });

        it('generates correct estate route for manager', function (): void {
            $response = $this->actingAs($this->manager)
                ->get('/management/dashboard');

            $response->assertSee('href="'.route('management.estates').'"');
            $response->assertDontSee(route('estates'));
            $response->assertDontSee(route('reviewer.estates'));
            $response->assertDontSee(route('caretaker.estates'));
        });

        it('generates correct estate route for reviewer', function (): void {
            $response = $this->actingAs($this->reviewer)
                ->get('/reviewer/dashboard');

            $response->assertSee('href="'.route('reviewer.estates').'"');
            $response->assertDontSee(route('estates'));
            $response->assertDontSee(route('management.estates'));
            $response->assertDontSee(route('caretaker.estates'));
        });

        it('generates correct estate route for caretaker', function (): void {
            $response = $this->actingAs($this->caretaker)
                ->get('/caretaker/dashboard');

            $response->assertSee('href="'.route('caretaker.estates').'"');
            $response->assertDontSee(route('estates'));
            $response->assertDontSee(route('management.estates'));
            $response->assertDontSee(route('reviewer.estates'));
        });

        it('does not show estate route for resident', function (): void {
            $response = $this->actingAs($this->resident)
                ->get('/resident/dashboard');

            $response->assertDontSee(route('estates'));
            $response->assertDontSee(route('management.estates'));
            $response->assertDontSee(route('reviewer.estates'));
            $response->assertDontSee(route('caretaker.estates'));
        });
    });

    describe('Estate Analytics Routes', function (): void {
        it('generates correct estate analytics route for admin', function (): void {
            $response = $this->actingAs($this->admin)
                ->get('/admin/dashboard');

            $response->assertSee('href="'.route('estates.analytics').'"');
            $response->assertDontSee(route('management.estates.analytics'));
        });

        it('generates correct estate analytics route for manager', function (): void {
            $response = $this->actingAs($this->manager)
                ->get('/management/dashboard');

            $response->assertSee('href="'.route('management.estates.analytics').'"');
            $response->assertDontSee(route('estates.analytics'));
        });

        it('does not show estate analytics route for non-admin/manager users', function (): void {
            $users = [$this->reviewer, $this->caretaker, $this->resident];

            foreach ($users as $user) {
                $route = match (true) {
                    $user->hasRole('reviewer') => '/reviewer/dashboard',
                    $user->hasRole('caretaker') => '/caretaker/dashboard',
                    $user->hasRole('resident') => '/resident/dashboard',
                    default => '/dashboard',
                };

                $response = $this->actingAs($user)->get($route);
                $response->assertDontSee(route('estates.analytics'));
                $response->assertDontSee(route('management.estates.analytics'));
            }
        });
    });

    describe('House Routes', function (): void {
        it('generates correct house route for admin', function (): void {
            $response = $this->actingAs($this->admin)
                ->get('/admin/dashboard');

            $response->assertSee('href="'.route('houses').'"');
            $response->assertDontSee(route('management.houses'));
            $response->assertDontSee(route('reviewer.houses'));
            $response->assertDontSee(route('caretaker.houses'));
        });

        it('generates correct house route for manager', function (): void {
            $response = $this->actingAs($this->manager)
                ->get('/management/dashboard');

            $response->assertSee('href="'.route('management.houses').'"');
            $response->assertDontSee(route('houses'));
            $response->assertDontSee(route('reviewer.houses'));
            $response->assertDontSee(route('caretaker.houses'));
        });

        it('generates correct house route for reviewer', function (): void {
            $response = $this->actingAs($this->reviewer)
                ->get('/reviewer/dashboard');

            $response->assertSee('href="'.route('reviewer.houses').'"');
            $response->assertDontSee(route('houses'));
            $response->assertDontSee(route('management.houses'));
            $response->assertDontSee(route('caretaker.houses'));
        });

        it('generates correct house route for caretaker with estate parameter', function (): void {
            $response = $this->actingAs($this->caretaker)
                ->get('/caretaker/dashboard');

            // Should contain the base route
            $response->assertSee(route('caretaker.houses'));

            // Should contain the route generation logic with estateId parameter
            $response->assertSee("auth()->user()->isAdmin() ? route('houses') : (auth()->user()->isManager() ? route('management.houses') : (auth()->user()->isReviewer() ? route('reviewer.houses') : route('caretaker.houses', ['estateId' => auth()->user()->assignedEstates()->first()->id ?? ''])))");
        });

        it('generates correct house route for resident', function (): void {
            $response = $this->actingAs($this->resident)
                ->get('/resident/dashboard');

            // Resident should see houses route (their own houses)
            $response->assertSee('href="'.route('houses').'"');
        });
    });

    describe('Contact Routes', function (): void {
        it('generates correct contact route for admin', function (): void {
            $response = $this->actingAs($this->admin)
                ->get('/admin/dashboard');

            $response->assertSee('href="'.route('contacts').'"');
            $response->assertDontSee(route('management.contacts'));
            $response->assertDontSee(route('reviewer.contacts'));
            $response->assertDontSee(route('caretaker.contacts'));
        });

        it('generates correct contact route for manager', function (): void {
            $response = $this->actingAs($this->manager)
                ->get('/management/dashboard');

            $response->assertSee('href="'.route('management.contacts').'"');
            $response->assertDontSee(route('contacts'));
            $response->assertDontSee(route('reviewer.contacts'));
            $response->assertDontSee(route('caretaker.contacts'));
        });

        it('generates correct contact route for reviewer', function (): void {
            $response = $this->actingAs($this->reviewer)
                ->get('/reviewer/dashboard');

            $response->assertSee('href="'.route('reviewer.contacts').'"');
            $response->assertDontSee(route('contacts'));
            $response->assertDontSee(route('management.contacts'));
            $response->assertDontSee(route('caretaker.contacts'));
        });

        it('generates correct contact route for caretaker', function (): void {
            $response = $this->actingAs($this->caretaker)
                ->get('/caretaker/dashboard');

            $response->assertSee('href="'.route('caretaker.contacts').'"');
            $response->assertDontSee(route('contacts'));
            $response->assertDontSee(route('management.contacts'));
            $response->assertDontSee(route('reviewer.contacts'));
        });

        it('does not show contact route for resident', function (): void {
            $response = $this->actingAs($this->resident)
                ->get('/resident/dashboard');

            $response->assertDontSee(route('contacts'));
            $response->assertDontSee(route('management.contacts'));
            $response->assertDontSee(route('reviewer.contacts'));
            $response->assertDontSee(route('caretaker.contacts'));
        });
    });

    describe('Reading Routes', function (): void {
        it('generates correct record readings route for admin', function (): void {
            $response = $this->actingAs($this->admin)
                ->get('/admin/dashboard');

            $response->assertSee('href="'.route('readings.create').'"');
            $response->assertDontSee(route('caretaker.readings.create'));
        });

        it('generates correct record readings route for caretaker', function (): void {
            $response = $this->actingAs($this->caretaker)
                ->get('/caretaker/dashboard');

            $response->assertSee('href="'.route('caretaker.readings.create').'"');
            $response->assertDontSee(route('readings.create'));
        });

        it('generates correct review readings route for admin', function (): void {
            $response = $this->actingAs($this->admin)
                ->get('/admin/dashboard');

            $response->assertSee('href="'.route('readings.review').'"');
            $response->assertDontSee(route('reviewer.readings.pending'));
        });

        it('generates correct review readings route for reviewer', function (): void {
            $response = $this->actingAs($this->reviewer)
                ->get('/reviewer/dashboard');

            $response->assertSee('href="'.route('reviewer.readings.pending').'"');
            $response->assertDontSee(route('readings.review'));
        });

        it('generates correct all readings route for each role', function (): void {
            // Admin
            $response = $this->actingAs($this->admin)
                ->get('/admin/dashboard');
            $response->assertSee('href="'.route('readings.index').'"');

            // Caretaker
            $response = $this->actingAs($this->caretaker)
                ->get('/caretaker/dashboard');
            $response->assertSee('href="'.route('caretaker.readings').'"');

            // Reviewer
            $response = $this->actingAs($this->reviewer)
                ->get('/reviewer.dashboard');
            $response->assertSee('href="'.route('reviewer.readings').'"');

            // Resident
            $response = $this->actingAs($this->resident)
                ->get('/resident.dashboard');
            $response->assertSee('href="'.route('resident.readings').'"');
        });
    });

    describe('Billing Routes', function (): void {
        it('generates correct billing route for admin', function (): void {
            $response = $this->actingAs($this->admin)
                ->get('/admin/dashboard');

            $response->assertSee('href="'.route('billing.index').'"');
            $response->assertDontSee(route('reviewer.billing'));
            $response->assertDontSee(route('resident.invoices'));
        });

        it('generates correct billing route for reviewer', function (): void {
            $response = $this->actingAs($this->reviewer)
                ->get('/reviewer.dashboard');

            $response->assertSee('href="'.route('reviewer.billing').'"');
            $response->assertDontSee(route('billing.index'));
            $response->assertDontSee(route('resident.invoices'));
        });

        it('generates correct billing route for resident', function (): void {
            $response = $this->actingAs($this->resident)
                ->get('/resident.dashboard');

            $response->assertSee('href="'.route('resident.invoices').'"');
            $response->assertDontSee(route('billing.index'));
            $response->assertDontSee(route('reviewer.billing'));
        });

        it('does not show billing route for manager and caretaker', function (): void {
            // Manager
            $response = $this->actingAs($this->manager)
                ->get('/management.dashboard');
            $response->assertDontSee(route('billing.index'));
            $response->assertDontSee(route('reviewer.billing'));
            $response->assertDontSee(route('resident.invoices'));

            // Caretaker
            $response = $this->actingAs($this->caretaker)
                ->get('/caretaker.dashboard');
            $response->assertDontSee(route('billing.index'));
            $response->assertDontSee(route('reviewer.billing'));
            $response->assertDontSee(route('resident.invoices'));
        });
    });

    describe('Report Routes', function (): void {
        it('generates correct reports route for admin with dropdown', function (): void {
            $response = $this->actingAs($this->admin)
                ->get('/admin.dashboard');

            $response->assertSee(route('admin.reports.aging'));
            $response->assertSee(route('admin.reports.revenue'));
            $response->assertSee(route('admin.reports.customer-statement'));
        });

        it('generates correct reports route for manager', function (): void {
            $response = $this->actingAs($this->manager)
                ->get('/management.dashboard');

            $response->assertSee('href="'.route('management.reports').'"');
            $response->assertDontSee(route('admin.reports.aging'));
        });

        it('generates correct reports route for reviewer', function (): void {
            $response = $this->actingAs($this->reviewer)
                ->get('/reviewer.dashboard');

            $response->assertSee('href="'.route('reviewer.billing.reports').'"');
            $response->assertDontSee(route('admin.reports.aging'));
        });

        it('generates correct reports route for resident', function (): void {
            $response = $this->actingAs($this->resident)
                ->get('/resident.dashboard');

            $response->assertSee('href="'.route('resident.reports').'"');
            $response->assertDontSee(route('admin.reports.aging'));
        });

        it('does not show reports route for caretaker', function (): void {
            $response = $this->actingAs($this->caretaker)
                ->get('/caretaker.dashboard');

            $response->assertDontSee(route('management.reports'));
            $response->assertDontSee(route('reviewer.billing.reports'));
            $response->assertDontSee(route('resident.reports'));
        });
    });

    describe('Admin Routes', function (): void {
        it('shows admin routes only to admin user', function (): void {
            $response = $this->actingAs($this->admin)
                ->get('/admin.dashboard');

            $response->assertSee('href="'.route('admin.users').'"');
            $response->assertSee('href="'.route('admin.settings').'"');
            $response->assertSee('href="'.route('admin.audit').'"');
        });

        it('does not show admin routes to non-admin users', function (): void {
            $nonAdminUsers = [$this->manager, $this->reviewer, $this->caretaker, $this->resident];

            foreach ($nonAdminUsers as $nonAdminUser) {
                $route = match (true) {
                    $nonAdminUser->hasRole('manager') => '/management.dashboard',
                    $nonAdminUser->hasRole('reviewer') => '/reviewer.dashboard',
                    $nonAdminUser->hasRole('caretaker') => '/caretaker.dashboard',
                    $nonAdminUser->hasRole('resident') => '/resident.dashboard',
                    default => '/dashboard',
                };

                $response = $this->actingAs($nonAdminUser)->get($route);
                $response->assertDontSee(route('admin.users'));
                $response->assertDontSee(route('admin.settings'));
                $response->assertDontSee(route('admin.audit'));
            }
        });
    });

    describe('Export Routes', function (): void {
        it('shows export route to users with export permissions', function (): void {
            $usersWithExport = [$this->admin, $this->manager, $this->reviewer, $this->resident];

            foreach ($usersWithExport as $userWithExport) {
                $route = match ($userWithExport->role) {
                    'admin' => '/admin.dashboard',
                    'manager' => '/management.dashboard',
                    'reviewer' => '/reviewer.dashboard',
                    'resident' => '/resident.dashboard',
                };

                $response = $this->actingAs($userWithExport)->get($route);
                $response->assertSee('href="'.route('exports.index').'"');
            }
        });

        it('does not show export route to caretaker', function (): void {
            $response = $this->actingAs($this->caretaker)
                ->get('/caretaker.dashboard');

            $response->assertDontSee(route('exports.index'));
        });
    });
});
