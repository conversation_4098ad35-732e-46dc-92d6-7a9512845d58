<?php

declare(strict_types=1);

use App\Livewire\EstateManager;
use App\Models\Estate;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);
uses(\Tests\Traits\CreatesTestUsers::class);
uses(\Tests\Traits\CreatesTestData::class);
uses(\Tests\Traits\AuthenticationHelpers::class);
uses(\Tests\Traits\LivewireTestHelpers::class);

describe('Estate Management', function (): void {
    beforeEach(function (): void {
        // Clear any existing data
        Estate::query()->delete();
    });

    describe('Authentication and Authorization', function (): void {
        test('guests are redirected to login', function (): void {
            $this->assertGuestRedirectedToLogin('/estates');
        });

        test('only admin and manager can access estate management', function (): void {
            $this->testAuthenticationForRoute('/estates', ['admin', 'manager'], [
                'reviewer', 'caretaker', 'resident',
            ]);
        });

        test('Livewire component respects role-based access', function (): void {
            $this->testLivewireAuthentication(EstateManager::class, ['admin', 'manager'], [
                'reviewer', 'caretaker', 'resident',
            ]);
        });
    });

    describe('Estate CRUD Operations', function (): void {
        test('can create new estate', function (): void {
            $this->testLivewireCrudOperations(
                EstateManager::class,
                Estate::class,
                [
                    'name' => 'New Test Estate',
                    'code' => 'NTE-001',
                    'city' => 'Test City',
                    'address' => '123 Test Street',
                    'contact_email' => '<EMAIL>',
                    'contact_phone' => '+234-************',
                ],
                [
                    'name' => 'Updated Test Estate',
                    'code' => 'NTE-001',
                    'city' => 'Updated City',
                    'address' => '456 Updated Street',
                    'contact_email' => '<EMAIL>',
                    'contact_phone' => '+234-************',
                ]
            );
        });

        test('validates required fields', function (): void {
            $this->testLivewireValidation(
                EstateManager::class,
                [
                    'name' => 'Valid Estate',
                    'code' => 'VALID-001',
                    'city' => 'Valid City',
                    'address' => 'Valid Address',
                ],
                [
                    'name' => '',
                    'code' => '',
                    'city' => '',
                    'address' => '',
                ]
            );
        });

        test('validates email format', function (): void {
            $this->testLivewireComponentWithAuth(EstateManager::class, function ($livewire): void {
                $livewire->call('openCreateModal')
                    ->set('form.contact_email', 'invalid-email')
                    ->call('save')
                    ->assertHasErrors(['form.contact_email' => 'email']);
            });
        });

        test('validates unique estate code', function (): void {
            // Create first estate
            $estate = $this->createTestEstate(['code' => 'UNIQUE-001']);

            $this->testLivewireComponentWithAuth(EstateManager::class, function ($livewire): void {
                // Try to create estate with same code
                $livewire->call('openCreateModal')
                    ->set('form.name', 'Duplicate Estate')
                    ->set('form.code', 'UNIQUE-001')
                    ->set('form.city', 'Test City')
                    ->set('form.address', 'Test Address')
                    ->call('save')
                    ->assertHasErrors(['form.code' => 'unique']);
            });
        });
    });

    describe('Estate Listing and Display', function (): void {
        test('displays list of estates', function (): void {
            // Create test estates
            $estates = [
                $this->createTestEstate(['name' => 'First Estate', 'code' => 'FST-001']),
                $this->createTestEstate(['name' => 'Second Estate', 'code' => 'SND-001']),
                $this->createTestEstate(['name' => 'Third Estate', 'code' => 'TRD-001']),
            ];

            $this->testLivewireComponentWithAuth(EstateManager::class, function ($livewire) use ($estates): void {
                $livewire->assertSee($estates[0]->name)
                    ->assertSee($estates[1]->name)
                    ->assertSee($estates[2]->name)
                    ->assertSee($estates[0]->code)
                    ->assertSee($estates[1]->code)
                    ->assertSee($estates[2]->code);
            });
        });

        test('can search estates by name', function (): void {
            $estates = [
                $this->createTestEstate(['name' => 'Garden Estate', 'code' => 'GRD-001']),
                $this->createTestEstate(['name' => 'Valley Estate', 'code' => 'VLY-001']),
                $this->createTestEstate(['name' => 'Ocean Estate', 'code' => 'OCN-001']),
            ];

            $this->testLivewireSearch(
                EstateManager::class,
                Estate::class,
                [
                    ['name' => 'Garden Estate', 'code' => 'GRD-001', 'city' => 'Test City'],
                    ['name' => 'Valley Estate', 'code' => 'VLY-001', 'city' => 'Test City'],
                    ['name' => 'Ocean Estate', 'code' => 'OCN-001', 'city' => 'Test City'],
                ],
                'search'
            );
        });

        test('can filter estates by status', function (): void {
            $estates = [
                $this->createTestEstate(['name' => 'Active Estate', 'code' => 'ACT-001', 'is_active' => true]),
                $this->createTestEstate(['name' => 'Inactive Estate', 'code' => 'INACT-001', 'is_active' => false]),
            ];

            $this->testLivewireComponentWithAuth(EstateManager::class, function ($livewire) use ($estates): void {
                // Test active filter
                $livewire->set('filter', 'active')
                    ->assertSee($estates[0]->name)
                    ->assertDontSee($estates[1]->name);

                // Test inactive filter
                $livewire->set('filter', 'inactive')
                    ->assertDontSee($estates[0]->name)
                    ->assertSee($estates[1]->name);

                // Test all filter
                $livewire->set('filter', 'all')
                    ->assertSee($estates[0]->name)
                    ->assertSee($estates[1]->name);
            });
        });
    });

    describe('Estate Details and Actions', function (): void {
        test('can view estate details', function (): void {
            $estate = $this->createTestEstate();

            $this->testLivewireComponentWithAuth(EstateManager::class, function ($livewire) use ($estate): void {
                $livewire->call('showEstate', $estate->id)
                    ->assertSet('showModal', true)
                    ->assertSet('estateIdToShow', $estate->id)
                    ->assertSee($estate->name)
                    ->assertSee($estate->code)
                    ->assertSee($estate->city)
                    ->assertSee($estate->address);
            });
        });

        test('can delete estate', function (): void {
            $estate = $this->createTestEstate();

            $this->testLivewireComponentWithAuth(EstateManager::class, function ($livewire) use ($estate): void {
                $livewire->call('confirmDelete', $estate->id)
                    ->assertSet('estateIdToDelete', $estate->id)
                    ->call('delete')
                    ->assertHasNoErrors()
                    ->assertDispatched('estate-deleted');

                $this->assertDatabaseMissing('estates', ['id' => $estate->id]);
            });
        });

        test('handles estate not found gracefully', function (): void {
            $this->testLivewireComponentWithAuth(EstateManager::class, function ($livewire): void {
                $livewire->call('showEstate', 999)
                    ->assertDispatched('error', 'Estate not found');

                $livewire->call('openEditModal', 999)
                    ->assertDispatched('error', 'Estate not found');

                $livewire->call('confirmDelete', 999)
                    ->assertDispatched('error', 'Estate not found');
            });
        });
    });

    describe('Estate Statistics and Analytics', function (): void {
        test('shows correct estate statistics', function (): void {
            // Create estates with different statuses
            $activeEstates = [
                $this->createTestEstate(['name' => 'Active 1', 'is_active' => true]),
                $this->createTestEstate(['name' => 'Active 2', 'is_active' => true]),
            ];

            $inactiveEstates = [
                $this->createTestEstate(['name' => 'Inactive 1', 'is_active' => false]),
                $this->createTestEstate(['name' => 'Inactive 2', 'is_active' => false]),
            ];

            $this->testLivewireComponentWithAuth(EstateManager::class, function ($livewire): void {
                $livewire->assertSee('4') // Total estates
                    ->assertSee('2') // Active estates
                    ->assertSee('2'); // Inactive estates
            });
        });
    });

    describe('Performance and Edge Cases', function (): void {
        test('handles large number of estates efficiently', function (): void {
            // Create 50 estates
            $estates = [];
            for ($i = 0; $i < 50; $i++) {
                $estates[] = $this->createTestEstate([
                    'name' => 'Performance Test Estate '.$i,
                    'code' => 'PERF-'.str_pad($i, 3, '0', STR_PAD_LEFT),
                ]);
            }

            $this->testLivewireComponentWithAuth(EstateManager::class, function ($livewire) use ($estates): void {
                // Should load quickly and show first page
                $livewire->assertSee($estates[0]->name)
                    ->assertSee($estates[1]->name);

                // Test pagination
                $livewire->call('setPage', 2)
                    ->assertSee($estates[15]->name); // Assuming 15 items per page
            });
        });

        test('handles special characters in estate names', function (): void {
            $estate = $this->createTestEstate([
                'name' => 'Éstate naïve café',
                'code' => 'SPECIAL-001',
            ]);

            $this->testLivewireComponentWithAuth(EstateManager::class, function ($livewire) use ($estate): void {
                $livewire->assertSee($estate->name);
            });
        });

        test('handles very long estate names and addresses', function (): void {
            $longName = str_repeat('Very Long Estate Name ', 20);
            $longAddress = str_repeat('123 Very Long Address Street, ', 10);

            $estate = $this->createTestEstate([
                'name' => $longName,
                'code' => 'LONG-001',
                'address' => $longAddress,
            ]);

            $this->testLivewireComponentWithAuth(EstateManager::class, function ($livewire) use ($estate): void {
                $livewire->assertSee(substr((string) $estate->name, 0, 50).'...'); // Should truncate
            });
        });
    });
});
