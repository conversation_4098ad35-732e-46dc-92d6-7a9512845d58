<?php

declare(strict_types=1);

// use App\Enums\UserRole; // Replaced with Spatie roles
use App\Livewire\Admin\InvoiceApproval;
use App\Livewire\Invoice\InvoiceDetail;
use App\Models\Contact;
use App\Models\Invoice;
use App\Models\WhatsAppMessage;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;
use Tests\Traits\AuthenticationHelpers;
use Tests\Traits\CreatesTestData;
use Tests\Traits\CreatesTestUsers;
use Tests\Traits\LivewireTestHelpers;

uses(RefreshDatabase::class, CreatesTestUsers::class, CreatesTestData::class, AuthenticationHelpers::class, LivewireTestHelpers::class);

beforeEach(function (): void {
    // Enable WhatsApp for testing
    config(['services.whatsapp.enabled' => true]);

    // Mock the WhatsApp service to prevent actual API calls
    $this->mockWhatsAppService = mock(\App\Services\WhatsAppService::class);
    app()->instance(\App\Services\WhatsAppService::class, $this->mockWhatsAppService);

    // Mock the WhatsApp templates
    $this->mockWhatsAppTemplates = mock(\App\Services\WhatsAppTemplates::class);
    app()->instance(\App\Services\WhatsAppTemplates::class, $this->mockWhatsAppTemplates);
});

describe('Invoice Approval Dashboard', function (): void {
    test('renders invoice approval dashboard with pending invoices', function (): void {
        $data = $this->createBillingWorkflowTestData();

        $draftInvoice = $this->createTestInvoice($data['house'], $data['waterRate'], $data['currentReading'], [
            'status' => 'draft',
        ]);

        $submittedInvoice = $this->createTestInvoice($data['house'], $data['waterRate'], $data['currentReading'], [
            'status' => 'submitted',
            'submitted_by' => $data['manager']->id,
            'submitted_at' => now(),
        ]);

        $approvedInvoice = $this->createTestInvoice($data['house'], $data['waterRate'], $data['currentReading'], [
            'status' => 'approved',
            'approved_by' => $data['reviewer']->id,
            'approved_at' => now(),
        ]);

        Livewire::actingAs($data['reviewer'])
            ->test(InvoiceApproval::class)
            ->assertSet('filter', 'pending')
            ->assertSee('Invoice Approval')
            ->assertSee($draftInvoice->invoice_number)
            ->assertSee($submittedInvoice->invoice_number)
            ->assertDontSee($approvedInvoice->invoice_number);
    });

    test('can filter invoices by status', function (): void {
        $data = $this->createBillingWorkflowTestData();

        $draftInvoice = $this->createTestInvoice($data['house'], $data['waterRate'], $data['currentReading'], [
            'status' => 'draft',
        ]);

        $submittedInvoice = $this->createTestInvoice($data['house'], $data['waterRate'], $data['currentReading'], [
            'status' => 'submitted',
            'submitted_by' => $data['manager']->id,
            'submitted_at' => now(),
        ]);

        Livewire::actingAs($data['reviewer'])
            ->test(InvoiceApproval::class)
            ->assertSet('filter', 'pending')
            ->assertSee($draftInvoice->invoice_number)
            ->assertSee($submittedInvoice->invoice_number)
            ->set('filter', 'draft')
            ->assertSee($draftInvoice->invoice_number)
            ->assertDontSee($submittedInvoice->invoice_number)
            ->set('filter', 'submitted')
            ->assertDontSee($draftInvoice->invoice_number)
            ->assertSee($submittedInvoice->invoice_number);
    });

    test('shows correct statistics', function (): void {
        $data = $this->createBillingWorkflowTestData();

        // Create invoices in different states
        Invoice::factory()->count(3)->create([
            'house_id' => $data['house']->id,
            'water_rate_id' => $data['waterRate']->id,
            'meter_reading_id' => $data['currentReading']->id,
            'status' => 'draft',
        ]);

        Invoice::factory()->count(2)->create([
            'house_id' => $data['house']->id,
            'water_rate_id' => $data['waterRate']->id,
            'meter_reading_id' => $data['currentReading']->id,
            'status' => 'submitted',
            'submitted_by' => $data['manager']->id,
            'submitted_at' => now(),
        ]);

        Invoice::factory()->count(1)->create([
            'house_id' => $data['house']->id,
            'water_rate_id' => $data['waterRate']->id,
            'meter_reading_id' => $data['currentReading']->id,
            'status' => 'approved',
            'approved_by' => $data['reviewer']->id,
            'approved_at' => now(),
        ]);

        Livewire::actingAs($data['reviewer'])
            ->test(InvoiceApproval::class)
            ->assertSee('5') // Total pending (3 draft + 2 submitted)
            ->assertSee('3') // Draft count
            ->assertSee('2') // Submitted count
            ->assertSee('1'); // Approved count
    });

    test('can search invoices by invoice number', function (): void {
        $data = $this->createBillingWorkflowTestData();

        $invoice1 = $this->createTestInvoice($data['house'], $data['waterRate'], $data['currentReading'], [
            'status' => 'draft',
            'invoice_number' => 'INV-2025-001',
        ]);

        $invoice2 = $this->createTestInvoice($data['house'], $data['waterRate'], $data['currentReading'], [
            'status' => 'draft',
            'invoice_number' => 'INV-2025-002',
        ]);

        Livewire::actingAs($data['reviewer'])
            ->test(InvoiceApproval::class)
            ->set('search', 'INV-2025-001')
            ->assertSee($invoice1->invoice_number)
            ->assertDontSee($invoice2->invoice_number)
            ->set('search', 'INV-2025-002')
            ->assertDontSee($invoice1->invoice_number)
            ->assertSee($invoice2->invoice_number);
    });
});

describe('Invoice Submission', function (): void {
    test('can submit invoice for approval', function (): void {
        $data = $this->createBillingWorkflowTestData();

        $invoice = $this->createTestInvoice($data['house'], $data['waterRate'], $data['currentReading'], [
            'status' => 'draft',
        ]);

        Livewire::actingAs($data['manager'])
            ->test(InvoiceApproval::class)
            ->call('submitForApproval', $invoice->id)
            ->assertDispatched('invoice-submitted');

        $this->assertDatabaseHas('invoices', [
            'id' => $invoice->id,
            'status' => 'submitted',
            'submitted_by' => $data['manager']->id,
        ]);

        expect($invoice->fresh()->submitted_at)->not()->toBeNull();
    });

    test('cannot submit invoice for approval without proper permissions', function (): void {
        $data = $this->createBillingWorkflowTestData();
        $caretaker = $this->createCaretakerUser();

        $invoice = $this->createTestInvoice($data['house'], $data['waterRate'], $data['currentReading'], [
            'status' => 'draft',
        ]);

        Livewire::actingAs($caretaker)
            ->test(InvoiceApproval::class)
            ->call('submitForApproval', $invoice->id)
            ->assertForbidden();

        $this->assertDatabaseHas('invoices', [
            'id' => $invoice->id,
            'status' => 'draft',
            'submitted_by' => null,
            'submitted_at' => null,
        ]);
    });

    test('can submit invoice for approval from detail page', function (): void {
        $data = $this->createBillingWorkflowTestData();

        $invoice = $this->createTestInvoice($data['house'], $data['waterRate'], $data['currentReading'], [
            'status' => 'draft',
        ]);

        Livewire::actingAs($data['manager'])
            ->test(InvoiceDetail::class, ['invoice' => $invoice])
            ->call('submitForApproval')
            ->assertDispatched('success', 'Invoice submitted for approval successfully');

        $this->assertDatabaseHas('invoices', [
            'id' => $invoice->id,
            'status' => 'submitted',
            'submitted_by' => $data['manager']->id,
        ]);
    });
});

describe('Invoice Approval', function (): void {
    test('can approve invoice', function (): void {
        $data = $this->createBillingWorkflowTestData();

        $invoice = $this->createTestInvoice($data['house'], $data['waterRate'], $data['currentReading'], [
            'status' => 'submitted',
            'submitted_by' => $data['manager']->id,
            'submitted_at' => now(),
        ]);

        Livewire::actingAs($data['reviewer'])
            ->test(InvoiceApproval::class)
            ->call('approve', $invoice->id)
            ->assertDispatched('invoice-approved');

        $this->assertDatabaseHas('invoices', [
            'id' => $invoice->id,
            'status' => 'approved',
            'approved_by' => $data['reviewer']->id,
        ]);

        expect($invoice->fresh()->approved_at)->not()->toBeNull();
    });

    test('cannot approve invoice without proper permissions', function (): void {
        $data = $this->createBillingWorkflowTestData();
        $caretaker = $this->createCaretakerUser();

        $invoice = $this->createTestInvoice($data['house'], $data['waterRate'], $data['currentReading'], [
            'status' => 'submitted',
            'submitted_by' => $data['manager']->id,
            'submitted_at' => now(),
        ]);

        Livewire::actingAs($caretaker)
            ->test(InvoiceApproval::class)
            ->call('approve', $invoice->id)
            ->assertForbidden();

        $this->assertDatabaseHas('invoices', [
            'id' => $invoice->id,
            'status' => 'submitted',
            'approved_by' => null,
            'approved_at' => null,
        ]);
    });
});

describe('Invoice Rejection', function (): void {
    test('can reject invoice', function (): void {
        $data = $this->createBillingWorkflowTestData();

        $invoice = $this->createTestInvoice($data['house'], $data['waterRate'], $data['currentReading'], [
            'status' => 'submitted',
            'submitted_by' => $data['manager']->id,
            'submitted_at' => now(),
        ]);

        Livewire::actingAs($data['reviewer'])
            ->test(InvoiceApproval::class)
            ->set('rejectionReason', 'Incorrect meter reading')
            ->call('reject', $invoice->id)
            ->assertDispatched('invoice-rejected')
            ->assertSet('rejectionReason', '');

        $this->assertDatabaseHas('invoices', [
            'id' => $invoice->id,
            'status' => 'draft',
        ]);
    });

    test('validates rejection reason is required', function (): void {
        $data = $this->createBillingWorkflowTestData();

        $invoice = $this->createTestInvoice($data['house'], $data['waterRate'], $data['currentReading'], [
            'status' => 'submitted',
            'submitted_by' => $data['manager']->id,
            'submitted_at' => now(),
        ]);

        Livewire::actingAs($data['reviewer'])
            ->test(InvoiceApproval::class)
            ->set('rejectionReason', '')
            ->call('reject', $invoice->id)
            ->assertHasErrors(['rejectionReason' => 'required']);
    });
});

describe('Invoice Sending', function (): void {
    test('can send invoice after approval', function (): void {
        $data = $this->createBillingWorkflowTestData();

        $invoice = $this->createTestInvoice($data['house'], $data['waterRate'], $data['currentReading'], [
            'status' => 'approved',
            'approved_by' => $data['reviewer']->id,
            'approved_at' => now(),
            'pdf_path' => 'invoices/test.pdf',
        ]);

        Livewire::actingAs($data['manager'])
            ->test(InvoiceApproval::class)
            ->call('sendInvoice', $invoice->id)
            ->assertDispatched('invoice-sent');

        $this->assertDatabaseHas('invoices', [
            'id' => $invoice->id,
            'status' => 'sent',
        ]);

        expect($invoice->fresh()->sent_at)->not()->toBeNull();
    });
});

describe('Invoice Details', function (): void {
    test('can view invoice details', function (): void {
        $data = $this->createBillingWorkflowTestData();

        $invoice = $this->createTestInvoice($data['house'], $data['waterRate'], $data['currentReading'], [
            'status' => 'submitted',
            'submitted_by' => $data['manager']->id,
            'submitted_at' => now(),
        ]);

        Livewire::actingAs($data['reviewer'])
            ->test(InvoiceApproval::class)
            ->call('viewInvoice', $invoice->id)
            ->assertDispatched('show-invoice-details', invoiceId: $invoice->id);
    });

    test('invoice detail component shows approval workflow buttons', function (): void {
        $data = $this->createBillingWorkflowTestData();

        $invoice = $this->createTestInvoice($data['house'], $data['waterRate'], $data['currentReading'], [
            'status' => 'draft',
        ]);

        // Test as manager - should see submit button
        Livewire::actingAs($data['manager'])
            ->test(InvoiceDetail::class, ['invoice' => $invoice])
            ->assertSee('Submit for Approval')
            ->assertDontSee('Approve');

        // Test as reviewer - should not see submit button for draft
        Livewire::actingAs($data['reviewer'])
            ->test(InvoiceDetail::class, ['invoice' => $invoice])
            ->assertDontSee('Submit for Approval')
            ->assertDontSee('Approve');
    });

    test('invoice detail component shows approve/reject buttons for submitted invoices', function (): void {
        $data = $this->createBillingWorkflowTestData();

        $invoice = $this->createTestInvoice($data['house'], $data['waterRate'], $data['currentReading'], [
            'status' => 'submitted',
            'submitted_by' => $data['manager']->id,
            'submitted_at' => now(),
        ]);

        // Test as reviewer - should see approve/reject buttons
        Livewire::actingAs($data['reviewer'])
            ->test(InvoiceDetail::class, ['invoice' => $invoice])
            ->assertDontSee('Submit for Approval')
            ->assertSee('Approve')
            ->assertSee('Reject');

        // Test as manager - should not see approve/reject buttons
        Livewire::actingAs($data['manager'])
            ->test(InvoiceDetail::class, ['invoice' => $invoice])
            ->assertDontSee('Submit for Approval')
            ->assertDontSee('Approve')
            ->assertDontSeeHtml('<button wire:click="approve">')
            ->assertDontSeeHtml('<button wire:click="$set(\'modalState\', \'rejection\')">');
    });
});

describe('WhatsApp Notifications', function (): void {
    test('invoice submission sends notifications to reviewers', function (): void {
        $data = $this->createBillingWorkflowTestData();

        $reviewerContact = Contact::factory()->create([
            'user_id' => $data['reviewer']->id,
            'whatsapp_number' => '+1234567890',
            'receive_notifications' => true,
        ]);

        $invoice = $this->createTestInvoice($data['house'], $data['waterRate'], $data['currentReading'], [
            'status' => 'draft',
        ]);

        // Mock template rendering
        $this->mockWhatsAppTemplates
            ->shouldReceive('renderTemplate')
            ->with('invoice_submitted_for_approval', \Mockery::type('array'))
            ->andReturn('Test notification message');

        // Mock WhatsApp sending - expect at least one call
        $this->mockWhatsAppService
            ->shouldReceive('sendText')
            ->atLeast()
            ->once()
            ->andReturn(new WhatsAppMessage);

        // Submit invoice for approval
        $invoice->submitForApproval($data['manager']);

        // Verify invoice status changed
        expect($invoice->fresh()->status)->toBe('submitted');
        expect($invoice->fresh()->submitted_by)->toBe($data['manager']->id);
    });

    test('invoice approval sends notification to manager', function (): void {
        $data = $this->createBillingWorkflowTestData();

        $managerContact = Contact::factory()->create([
            'user_id' => $data['manager']->id,
            'whatsapp_number' => '+1234567890',
            'receive_notifications' => true,
        ]);

        $invoice = $this->createTestInvoice($data['house'], $data['waterRate'], $data['currentReading'], [
            'status' => 'submitted',
            'submitted_by' => $data['manager']->id,
        ]);

        // Mock template rendering
        $this->mockWhatsAppTemplates
            ->shouldReceive('renderTemplate')
            ->with('invoice_approved', \Mockery::type('array'))
            ->andReturn('Test approval notification');

        // Mock WhatsApp sending - expect at least one call
        $this->mockWhatsAppService
            ->shouldReceive('sendText')
            ->atLeast()
            ->once()
            ->andReturn(new WhatsAppMessage);

        // Approve invoice
        $invoice->approve($data['reviewer']);

        // Verify invoice status changed
        expect($invoice->fresh()->status)->toBe('approved');
        expect($invoice->fresh()->approved_by)->toBe($data['reviewer']->id);
    });

    test('invoice rejection sends notification to manager with reason', function (): void {
        $data = $this->createBillingWorkflowTestData();

        $managerContact = Contact::factory()->create([
            'user_id' => $data['manager']->id,
            'whatsapp_number' => '+1234567890',
            'receive_notifications' => true,
        ]);

        $invoice = $this->createTestInvoice($data['house'], $data['waterRate'], $data['currentReading'], [
            'status' => 'submitted',
            'submitted_by' => $data['manager']->id,
        ]);

        $rejectionReason = 'Incorrect meter reading';

        // Mock template rendering
        $this->mockWhatsAppTemplates
            ->shouldReceive('renderTemplate')
            ->with('invoice_rejected', \Mockery::type('array'))
            ->andReturn('Test rejection notification');

        // Mock WhatsApp sending - expect at least one call
        $this->mockWhatsAppService
            ->shouldReceive('sendText')
            ->atLeast()
            ->once()
            ->andReturn(new WhatsAppMessage);

        // Reject invoice
        $invoice->reject($data['reviewer'], $rejectionReason);

        // Verify invoice status changed back to draft
        expect($invoice->fresh()->status)->toBe('draft');
        expect($invoice->fresh()->approved_by)->toBe($data['reviewer']->id);
    });

    test('notifications are not sent when WhatsApp is disabled', function (): void {
        // Disable WhatsApp
        config(['services.whatsapp.enabled' => false]);

        $data = $this->createBillingWorkflowTestData();

        $invoice = $this->createTestInvoice($data['house'], $data['waterRate'], $data['currentReading'], [
            'status' => 'draft',
        ]);

        // Mock services to ensure they're not called
        $this->mockWhatsAppTemplates->shouldNotReceive('renderTemplate');
        $this->mockWhatsAppService->shouldNotReceive('sendText');

        // Submit invoice for approval - should not throw error but also not send notifications
        $invoice->submitForApproval($data['manager']);

        // Verify invoice status changed
        expect($invoice->fresh()->status)->toBe('submitted');
    });

    test('notifications are not sent when contact has no WhatsApp', function (): void {
        $data = $this->createBillingWorkflowTestData();

        // Create contact without WhatsApp
        Contact::factory()->create([
            'user_id' => $data['reviewer']->id,
            'whatsapp_number' => null,
            'receive_notifications' => true,
        ]);

        $invoice = $this->createTestInvoice($data['house'], $data['waterRate'], $data['currentReading'], [
            'status' => 'draft',
        ]);

        // Mock services to ensure they're not called
        $this->mockWhatsAppTemplates->shouldNotReceive('renderTemplate');
        $this->mockWhatsAppService->shouldNotReceive('sendText');

        // Submit invoice for approval - should not send notifications
        $invoice->submitForApproval($data['manager']);

        // Verify invoice status changed
        expect($invoice->fresh()->status)->toBe('submitted');
    });
});

describe('Error Handling', function (): void {
    test('handles invoice not found gracefully', function (): void {
        $reviewer = $this->createReviewerUser();

        Livewire::actingAs($reviewer)
            ->test(InvoiceApproval::class)
            ->call('submitForApproval', 999)
            ->assertDispatched('error', 'Invoice not found');
    });

    test('component renders with basic authentication', function (): void {
        $reviewer = $this->createReviewerUser();

        Livewire::actingAs($reviewer)
            ->test(InvoiceApproval::class)
            ->assertStatus(200);
    });
});
