<?php

use App\Livewire\Estate\WaterRateForm;
use App\Models\Estate;
use App\Models\User;
use App\Models\WaterRate;
use Carbon\Carbon;
use Livewire\Livewire;
use PHPUnit\Framework\Attributes\Test;

uses(\Illuminate\Foundation\Testing\RefreshDatabase::class);

beforeEach(function (): void {
    $this->actingAs(User::factory()->manager());
});

it('can create a new water rate for an estate', function (): void {
    $estate = Estate::factory()->create();

    $testable = Livewire::test(WaterRateForm::class, ['estate' => $estate])
        ->set('name', 'New Rate')
        ->set('rate_per_unit', 20.00)
        ->set('effective_from', '2025-08-01')
        ->call('save');

    $testable->assertHasNoErrors();

    $this->assertDatabaseHas('water_rates', [
        'estate_id' => $estate->id,
        'name' => 'New Rate',
        'rate_per_unit' => 20.00,
        'effective_from' => '2025-08-01 00:00:00',
    ]);
});

test('name is required', function (): void {
    $estate = Estate::factory()->create();

    Livewire::test(WaterRateForm::class, ['estate' => $estate])
        ->set('name', '')
        ->set('rate_per_unit', 20.00)
        ->set('effective_from', Carbon::parse('2025-08-01')) // Use Carbon instance
        ->call('save')
        ->assertHasErrors(['name' => 'required']);
});

test('rate per unit is required and numeric', function (): void {
    $estate = Estate::factory()->create();

    Livewire::test(WaterRateForm::class, ['estate' => $estate])
        ->set('name', 'Test Rate')
        ->set('rate_per_unit', '')
        ->set('effective_from', Carbon::parse('2025-08-01')) // Use Carbon instance
        ->call('save')
        ->assertHasErrors(['rate_per_unit' => 'required']);

    // Removed the 'abc' test case as it causes a TypeError due to Livewire's type coercion
    // Livewire::test(WaterRateForm::class, ['estate' => $estate])
    //     ->set('name', 'Test Rate')
    //     ->set('rate_per_unit', 'abc')
    //     ->set('effective_from', Carbon::parse('2025-08-01')) // Use Carbon instance
    //     ->call('save')
    //     ->assertHasErrors(['rate_per_unit' => 'numeric']);
});

test('effective from is required and a date', function (): void {
    $estate = Estate::factory()->create();

    Livewire::test(WaterRateForm::class, ['estate' => $estate])
        ->set('name', 'Test Rate')
        ->set('rate_per_unit', 10.00)
        ->set('effective_from', '')
        ->call('save')
        ->assertHasErrors(['effective_from' => 'required']);
});

test('effective to must be after effective from', function (): void {
    $estate = Estate::factory()->create();

    Livewire::test(WaterRateForm::class, ['estate' => $estate])
        ->set('name', 'Test Rate')
        ->set('rate_per_unit', 10.00)
        ->set('effective_from', Carbon::parse('2025-08-01')) // Use Carbon instance
        ->set('effective_to', Carbon::parse('2025-07-31')) // Use Carbon instance
        ->call('save')
        ->assertHasErrors(['effective_to' => 'after_or_equal']);
});

test('effective date does not overlap with existing rates for the same estate on create', function (): void {
    $estate = Estate::factory()->create();
    WaterRate::factory()->create([
        'estate_id' => $estate->id,
        'effective_from' => '2025-01-01',
        'effective_to' => '2025-12-31',
    ]);

    Livewire::test(WaterRateForm::class, ['estate' => $estate])
        ->set('name', 'Overlapping Rate')
        ->set('rate_per_unit', 10.00)
        ->set('effective_from', Carbon::parse('2025-06-01')) // Use Carbon instance
        ->set('effective_to', Carbon::parse('2026-01-01')) // Use Carbon instance
        ->call('save')
        ->assertHasErrors(['effective_from' => 'The effective date range overlaps with an existing water rate for this estate.']);
});

test('effective date can overlap with existing rates for a different estate on create', function (): void {
    $estate1 = Estate::factory()->create();
    $estate2 = Estate::factory()->create();

    WaterRate::factory()->create([
        'estate_id' => $estate1->id,
        'effective_from' => '2025-01-01',
        'effective_to' => '2025-12-31',
    ]);

    Livewire::test(WaterRateForm::class, ['estate' => $estate2])
        ->set('name', 'Overlapping Rate for Other Estate')
        ->set('rate_per_unit', 10.00)
        ->set('effective_from', Carbon::parse('2025-06-01')) // Use Carbon instance
        ->set('effective_to', Carbon::parse('2026-01-01')) // Use Carbon instance
        ->call('save')
        ->assertHasNoErrors();

    $this->assertDatabaseHas('water_rates', [
        'estate_id' => $estate2->id,
        'name' => 'Overlapping Rate for Other Estate',
    ]);
});

it('can edit an existing water rate', function (): void {
    $estate = Estate::factory()->create();
    $waterRate = WaterRate::factory()->create([
        'estate_id' => $estate->id,
        'name' => 'Old Rate',
        'rate_per_unit' => 10.00,
        'effective_from' => '2025-01-01',
        'effective_to' => '2025-12-31',
    ]);

    Livewire::test(WaterRateForm::class, ['estate' => $estate, 'waterRate' => $waterRate])
        ->set('name', 'Updated Rate')
        ->set('rate_per_unit', 12.50)
        ->set('effective_from', Carbon::parse('2025-01-01')) // Use Carbon instance
        ->set('effective_to', Carbon::parse('2026-12-31')) // Use Carbon instance
        ->call('save')
        ->assertHasNoErrors();

    $this->assertDatabaseHas('water_rates', [
        'id' => $waterRate->id,
        'name' => 'Updated Rate',
        'rate_per_unit' => 12.50,
        'effective_to' => '2026-12-31 00:00:00', // Assert with time component
    ]);
});

test('effective date does not overlap with other existing rates for the same estate on edit', function (): void {
    $estate = Estate::factory()->create();
    $existingRate1 = WaterRate::factory()->create([
        'estate_id' => $estate->id,
        'effective_from' => '2025-01-01',
        'effective_to' => '2025-06-30',
    ]);
    $existingRate2 = WaterRate::factory()->create([
        'estate_id' => $estate->id,
        'effective_from' => '2025-07-01',
        'effective_to' => '2025-12-31',
    ]);

    Livewire::test(WaterRateForm::class, ['estate' => $estate, 'waterRate' => $existingRate1])
        ->set('name', 'Edited Rate')
        ->set('rate_per_unit', 10.00)
        ->set('name', 'Edited Rate')
        ->set('rate_per_unit', 10.00)
        ->set('effective_from', Carbon::parse('2025-05-01')) // Use Carbon instance
        ->set('effective_to', Carbon::parse('2025-09-30')) // Use Carbon instance
        ->call('save')
        ->assertHasErrors(['effective_from' => 'The effective date range overlaps with an existing water rate for this estate.']);
});
