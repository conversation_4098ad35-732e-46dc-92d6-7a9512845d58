<?php

use App\Livewire\Admin\InvoiceApproval;
use App\Models\Estate;
use App\Models\House;
use App\Models\Invoice;
use App\Models\MeterReading;
use App\Models\User;
use App\Models\WaterRate;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;

uses(RefreshDatabase::class);

test('invoice approval component renders', function (): void {
    $estate = Estate::factory()->create();
    $house = House::factory()->create(['estate_id' => $estate->id]);
    $waterRate = WaterRate::factory()->create(['estate_id' => $estate->id]);
    $meterReading = MeterReading::factory()->create(['house_id' => $house->id]);
    $userFactory = User::factory()->reviewer();

    $invoice = Invoice::factory()->create([
        'house_id' => $house->id,
        'water_rate_id' => $waterRate->id,
        'meter_reading_id' => $meterReading->id,
        'status' => 'draft',
    ]);

    Livewire::actingAs($userFactory)
        ->test(InvoiceApproval::class)
        ->assertStatus(200);
});
