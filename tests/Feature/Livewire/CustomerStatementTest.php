<?php

declare(strict_types=1);
use App\Livewire\Admin\CustomerStatement;
use App\Models\AccountTransaction;
use App\Models\Contact;
use App\Models\Estate;
use App\Models\House;
use App\Models\HouseAccount;
use App\Models\Invoice;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;

uses(RefreshDatabase::class);
uses(\Tests\Traits\CreatesTestUsers::class);
uses(\Tests\Traits\SetsUpSpatiePermissions::class);

beforeEach(function (): void {
    $this->setUpSpatiePermissions();
    createTestData();
});
function createTestData(): void
{
    // Create test user with admin role
    $this->adminUser = $this->createAdminUser();

    // Create test estate
    $this->estate = Estate::factory()->create();

    // Create test house
    $this->house = House::factory()->create([
        'estate_id' => $this->estate->id,
    ]);

    // Create house account for the house
    $this->houseAccount = HouseAccount::factory()->create([
        'house_id' => $this->house->id,
        'current_balance' => 0,
    ]);

    // Create test contact with house
    $this->contact = Contact::factory()->create([
        'house_id' => $this->house->id,
    ]);
    $this->contact->houses()->attach($this->house->id, ['is_primary' => true]);
}
test('admin can view customer statement page', function (): void {
    $this->actingAs($this->adminUser);

    Livewire::test(CustomerStatement::class)
        ->assertStatus(200)
        ->assertSee('Customer Statements')
        ->assertSee('Generate and view customer account statements');
});
test('non admin users cannot access customer statement', function (): void {
    $managerUser = $this->createManagerUser();

    $this->actingAs($managerUser);

    Livewire::test(CustomerStatement::class)
        ->assertStatus(403);
});
test('displays list of houses', function (): void {
    $this->actingAs($this->adminUser);

    // Create additional houses
    $house2 = House::factory()->create(['estate_id' => $this->estate->id]);
    $house3 = House::factory()->create(['estate_id' => $this->estate->id]);

    Livewire::test(CustomerStatement::class)
        ->assertSee($this->house->house_number)
        ->assertSee($house2->house_number)
        ->assertSee($house3->house_number);
});
test('can search houses by house number', function (): void {
    $this->actingAs($this->adminUser);

    $houseA = House::factory()->create(['house_number' => 'A101', 'estate_id' => $this->estate->id]);
    $houseB = House::factory()->create(['house_number' => 'B202', 'estate_id' => $this->estate->id]);

    Livewire::test(CustomerStatement::class)
        ->set('search', 'A101')
        ->assertSee($houseA->house_number)
        ->assertDontSee($houseB->house_number);
});
test('can search houses by contact name', function (): void {
    $this->actingAs($this->adminUser);

    $houseA = House::factory()->create(['estate_id' => $this->estate->id]);
    $houseB = House::factory()->create(['estate_id' => $this->estate->id]);

    // Create house accounts
    HouseAccount::factory()->create(['house_id' => $houseA->id]);
    HouseAccount::factory()->create(['house_id' => $houseB->id]);

    // Create contacts directly associated with houses
    $contactA = Contact::factory()->create(['name' => 'John Doe', 'house_id' => $houseA->id]);
    $contactB = Contact::factory()->create(['name' => 'Jane Smith', 'house_id' => $houseB->id]);

    Livewire::test(CustomerStatement::class)
        ->set('search', 'John Doe')
        ->assertSee($houseA->house_number)
        ->assertDontSee($houseB->house_number);
});
test('can select house and view statement summary', function (): void {
    $this->actingAs($this->adminUser);

    // Create test invoices and transactions
    $invoice = Invoice::factory()->create([
        'house_id' => $this->house->id,
        'total_due' => 1000,
        'status' => 'sent',
    ]);

    AccountTransaction::factory()->create([
        'house_account_id' => $this->house->account->id,
        'transaction_type' => 'payment',
        'amount' => 500,
        'balance_after' => 500,
    ]);

    Livewire::test(CustomerStatement::class)
        ->call('selectHouse', $this->house->id)
        ->assertSee('Statement Summary - '.$this->house->house_number)
        ->assertSee('Opening Balance')
        ->assertSee('Total Invoices')
        ->assertSee('Total Payments')
        ->assertSee('Closing Balance');
});
test('displays invoices for selected house', function (): void {
    $this->actingAs($this->adminUser);

    $invoice = Invoice::factory()->create([
        'house_id' => $this->house->id,
        'invoice_number' => 'INV-001',
        'total_due' => 1000,
        'status' => 'sent',
    ]);

    Livewire::test(CustomerStatement::class)
        ->call('selectHouse', $this->house->id)
        ->assertSee('Invoices')
        ->assertSee($invoice->invoice_number)
        ->assertSee(number_format($invoice->total_due, 2))
        ->assertSee(ucfirst((string) $invoice->status));
});
test('displays transactions for selected house', function (): void {
    $this->actingAs($this->adminUser);

    $transaction = AccountTransaction::factory()->create([
        'house_account_id' => $this->house->account->id,
        'transaction_type' => 'payment',
        'amount' => 500,
        'description' => 'Test payment',
        'balance_after' => 500,
    ]);

    Livewire::test(CustomerStatement::class)
        ->call('selectHouse', $this->house->id)
        ->assertSee('Transactions')
        ->assertSee(ucfirst((string) $transaction->transaction_type))
        ->assertSee($transaction->description)
        ->assertSee(number_format((float) $transaction->amount, 2))
        ->assertSee(number_format((float) $transaction->balance_after, 2));
});
test('can filter transactions by date range', function (): void {
    $this->actingAs($this->adminUser);

    $oldTransaction = AccountTransaction::factory()->create([
        'house_account_id' => $this->house->account->id,
        'transaction_type' => 'payment',
        'amount' => 100,
        'created_at' => now()->subMonths(2),
    ]);

    $recentTransaction = AccountTransaction::factory()->create([
        'house_account_id' => $this->house->account->id,
        'transaction_type' => 'payment',
        'amount' => 200,
        'created_at' => now()->subDays(10),
    ]);

    Livewire::test(CustomerStatement::class)
        ->call('selectHouse', $this->house->id)
        ->set('startDate', now()->subMonth()->format('Y-m-d'))
        ->set('endDate', now()->format('Y-m-d'))
        ->assertSee($recentTransaction->description)
        ->assertDontSee($oldTransaction->description);
});
test('can sort transactions', function (): void {
    $this->actingAs($this->adminUser);

    $transaction1 = AccountTransaction::factory()->create([
        'house_account_id' => $this->house->account->id,
        'transaction_type' => 'payment',
        'amount' => 100,
        'created_at' => now()->subDays(5),
    ]);

    $transaction2 = AccountTransaction::factory()->create([
        'house_account_id' => $this->house->account->id,
        'transaction_type' => 'adjustment',
        'amount' => 200,
        'created_at' => now()->subDays(1),
    ]);

    Livewire::test(CustomerStatement::class)
        ->call('selectHouse', $this->house->id)
        ->assertSet('sortField', 'created_at')
        ->assertSet('sortDirection', 'desc')
        ->call('sortBy', 'amount')
        ->assertSet('sortField', 'amount')
        ->assertSet('sortDirection', 'asc');
});
test('can export to excel', function (): void {
    $this->actingAs($this->adminUser);

    $startDate = now()->subMonths(3)->format('Y-m-d');
    $endDate = now()->format('Y-m-d');

    Livewire::test(CustomerStatement::class)
        ->call('selectHouse', $this->house->id)
        ->call('exportExcel')
        ->assertFileDownloaded("statement-{$this->house->house_number}-{$startDate}-to-{$endDate}.xlsx");
});
test('can export to pdf', function (): void {
    $this->actingAs($this->adminUser);

    $startDate = now()->subMonths(3)->format('Y-m-d');
    $endDate = now()->format('Y-m-d');

    Livewire::test(CustomerStatement::class)
        ->call('selectHouse', $this->house->id)
        ->call('exportPdf')
        ->assertFileDownloaded("statement-{$this->house->house_number}-{$startDate}-to-{$endDate}.pdf");
});
test('cannot export without selecting house', function (): void {
    $this->actingAs($this->adminUser);

    Livewire::test(CustomerStatement::class)
        ->call('exportExcel')
        ->assertDispatched('notify', message: 'Please select a house first', type: 'error');

    Livewire::test(CustomerStatement::class)
        ->call('exportPdf')
        ->assertDispatched('notify', message: 'Please select a house first', type: 'error');
});
test('can reset filters', function (): void {
    $this->actingAs($this->adminUser);

    Livewire::test(CustomerStatement::class)
        ->set('search', 'test')
        ->set('startDate', '2024-01-01')
        ->set('endDate', '2024-12-31')
        ->set('perPage', 25)
        ->call('resetFilters')
        ->assertSet('search', '')
        ->assertSet('startDate', now()->subMonths(3)->format('Y-m-d'))
        ->assertSet('endDate', now()->format('Y-m-d'))
        ->assertSet('perPage', 10);
});
test('shows no data message when no house selected', function (): void {
    $this->actingAs($this->adminUser);

    Livewire::test(CustomerStatement::class)
        ->assertSee('No house selected')
        ->assertSee('Select a house from the list to view its statement');
});
test('shows no invoices message when no invoices found', function (): void {
    $this->actingAs($this->adminUser);

    Livewire::test(CustomerStatement::class)
        ->call('selectHouse', $this->house->id)
        ->assertSee('No invoices found for the selected period');
});
test('shows no transactions message when no transactions found', function (): void {
    $this->actingAs($this->adminUser);

    Livewire::test(CustomerStatement::class)
        ->call('selectHouse', $this->house->id)
        ->assertSee('No transactions found for the selected period');
});
test('calculates statement summary correctly', function (): void {
    $this->actingAs($this->adminUser);

    // Create invoices
    $invoice1 = Invoice::factory()->create([
        'house_id' => $this->house->id,
        'total_due' => 1000,
        'status' => 'sent',
    ]);

    $invoice2 = Invoice::factory()->create([
        'house_id' => $this->house->id,
        'total_due' => 500,
        'status' => 'sent',
    ]);

    // Create transactions
    AccountTransaction::factory()->create([
        'house_account_id' => $this->house->account->id,
        'transaction_type' => 'payment',
        'amount' => 800,
        'balance_after' => 700,
    ]);

    AccountTransaction::factory()->create([
        'house_account_id' => $this->house->account->id,
        'transaction_type' => 'adjustment',
        'amount' => -100,
        'balance_after' => 800,
    ]);

    Livewire::test(CustomerStatement::class)
        ->call('selectHouse', $this->house->id)
        ->assertSee(number_format(1500, 2)) // Total invoices
        ->assertSee(number_format(800, 2)) // Total payments
        ->assertSee(number_format(100, 2)) // Total adjustments (absolute value)
        ->assertSee(number_format(600, 2));
    // Closing balance
});
