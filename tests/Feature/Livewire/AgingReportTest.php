<?php

declare(strict_types=1);

use App\Livewire\Admin\AgingReport;
use App\Models\Estate;
use App\Models\House;
use App\Models\Invoice;
use App\Models\User;
use App\Models\WaterRate;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;

uses(RefreshDatabase::class);

beforeEach(function (): void {
    // Create admin user
    $this->admin = User::factory()->admin()->create();

    // Create test data
    $this->estate = Estate::factory()->create();
    $this->house = House::factory()->create(['estate_id' => $this->estate->id]);
    $this->waterRate = WaterRate::factory()->create(['estate_id' => $this->estate->id]);
});

test('admin can view aging report', function (): void {
    $this->actingAs($this->admin);

    Livewire::test(AgingReport::class)
        ->assertOk()
        ->assertSee('Aging Report')
        ->assertSee('Generate Report')
        ->assertSee('Estate (Optional)');
});

test('aging report displays correct data', function (): void {
    $this->actingAs($this->admin);

    // Create test invoices
    $invoice1 = Invoice::factory()->create([
        'house_id' => $this->house->id,
        'water_rate_id' => $this->waterRate->id,
        'status' => 'sent',
        'due_date' => now()->subDays(10),
        'total_amount' => 1000,
    ]);

    $invoice2 = Invoice::factory()->create([
        'house_id' => $this->house->id,
        'water_rate_id' => $this->waterRate->id,
        'status' => 'sent',
        'due_date' => now()->subDays(45),
        'total_amount' => 2000,
    ]);

    Livewire::test(AgingReport::class)
        ->call('generateReport')
        ->assertSee('KES 1,000.00') // 1-30 days
        ->assertSee('KES 2,000.00') // 31-60 days
        ->assertSee('KES 3,000.00'); // Total outstanding
});

test('aging report can be filtered by estate', function (): void {
    $this->actingAs($this->admin);

    // Create another estate and house
    $estate2 = Estate::factory()->create();
    $house2 = House::factory()->create(['estate_id' => $estate2->id]);
    $waterRate2 = WaterRate::factory()->create(['estate_id' => $estate2->id]);

    // Create invoices for different estates
    $invoice1 = Invoice::factory()->create([
        'house_id' => $this->house->id,
        'water_rate_id' => $this->waterRate->id,
        'status' => 'sent',
        'due_date' => now()->subDays(10),
        'total_amount' => 1000,
    ]);

    $invoice2 = Invoice::factory()->create([
        'house_id' => $house2->id,
        'water_rate_id' => $waterRate2->id,
        'status' => 'sent',
        'due_date' => now()->subDays(10),
        'total_amount' => 2000,
    ]);

    Livewire::test(AgingReport::class)
        ->set('estate_id', $this->estate->id)
        ->call('generateReport')
        ->assertSee('KES 1,000.00') // Only from first estate
        ->assertDontSee('KES 3,000.00'); // Not total of both estates
});

test('aging report shows empty state when no data', function (): void {
    $this->actingAs($this->admin);

    Livewire::test(AgingReport::class)
        ->call('generateReport')
        ->assertSee('KES 0.00') // All amounts should be zero
        ->assertSee('0%'); // All percentages should be zero
});

test('non-admin users cannot access aging report', function (): void {
    $user = User::factory()->manager()->create();

    $this->actingAs($user);

    Livewire::test(AgingReport::class)
        ->assertForbidden();
});

test('aging report generates excel export', function (): void {
    $this->actingAs($this->admin);

    // Create test invoice
    $invoice1 = Invoice::factory()->create([
        'house_id' => $this->house->id,
        'water_rate_id' => $this->waterRate->id,
        'status' => 'sent',
        'due_date' => now()->subDays(10),
        'total_amount' => 1000,
    ]);

    Livewire::test(AgingReport::class)
        ->call('generateReport')
        ->call('exportToExcel')
        ->assertFileDownloaded('aging-report-'.now()->format('Y-m-d').'.xlsx');
});
