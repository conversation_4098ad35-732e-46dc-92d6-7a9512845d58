<?php

use App\Livewire\Admin\BillingManager;
use App\Models\Estate;
use App\Models\House;
use App\Models\Invoice;
use App\Models\User;
use Livewire\Livewire;

beforeEach(function (): void {
    $this->setUpSpatiePermissions();
    // Create test data
    $this->user = User::factory()->create();
    $this->user->assignRole('manager');
    $this->estate = Estate::factory()->create();
    $this->houses = House::factory()->count(3)->create(['estate_id' => $this->estate->id]);
});

test('it renders billing manager dashboard', function (): void {
    Livewire::actingAs($this->user)
        ->test(BillingManager::class)
        ->assertStatus(200);
});

test('it displays billing summary', function (): void {
    // Assign estate to manager
    $this->user->assignedEstates()->attach($this->estate->id, [
        'assigned_by' => $this->user->id,
        'assigned_at' => now(),
    ]);

    // Create invoices
    foreach ($this->houses as $house) {
        Invoice::factory()->create([
            'house_id' => $house->id,
            'status' => 'draft',
            'amount' => 1000,
        ]);
    }

    Livewire::actingAs($this->user)
        ->test(BillingManager::class)
        ->assertSee('Total Houses')
        ->assertSee('Total Invoices')
        ->assertSee('3');
});

test('it can filter invoices by status', function (): void {
    // Assign estate to manager
    $this->user->assignedEstates()->attach($this->estate->id, [
        'assigned_by' => $this->user->id,
        'assigned_at' => now(),
    ]);

    // Create invoices with different statuses
    Invoice::factory()->create([
        'house_id' => $this->houses[0]->id,
        'status' => 'draft',
        'amount' => 1000,
    ]);

    Invoice::factory()->create([
        'house_id' => $this->houses[1]->id,
        'status' => 'overdue',
        'amount' => 1500,
    ]);

    Invoice::factory()->create([
        'house_id' => $this->houses[2]->id,
        'status' => 'paid',
        'amount' => 1200,
    ]);

    Livewire::actingAs($this->user)
        ->test(BillingManager::class)
        ->set('statusFilter', 'overdue')
        ->assertSee('1,500.00')
        ->assertDontSee('1,000.00')
        ->assertDontSee('1,200.00');
});

test('it can select multiple invoices for bulk operations', function (): void {
    $invoices = [];
    foreach ($this->houses as $house) {
        $invoices[] = Invoice::factory()->create([
            'house_id' => $house->id,
            'status' => 'draft',
        ]);
    }

    Livewire::actingAs($this->user)
        ->test(BillingManager::class)
        ->set('selectedInvoices', [$invoices[0]->id, $invoices[2]->id])
        ->assertSet('selectedInvoices', [$invoices[0]->id, $invoices[2]->id]);
});

test('it can generate bulk invoices from dashboard', function (): void {
    $admin = User::factory()->create();
    $admin->assignRole('admin');

    Livewire::actingAs($admin)
        ->test(BillingManager::class)
        ->set('estateFilter', $this->estate->id)
        ->set('bulkInvoiceAmount', 1500)
        ->set('bulkInvoiceDescription', 'Test Bulk Invoice')
        ->call('generateBulkInvoices')
        ->assertDispatched('invoice-generated');

    $this->assertDatabaseHas('invoices', [
        'amount' => 1500,
        'notes' => 'Test Bulk Invoice',
        'status' => 'draft',
    ]);
});

test('it can send bulk reminders from dashboard', function (): void {
    $reviewer = User::factory()->create();
    $reviewer->assignRole('reviewer');

    $invoices = [];
    foreach ($this->houses as $house) {
        $invoices[] = Invoice::factory()->create([
            'house_id' => $house->id,
            'status' => 'overdue',
        ]);
    }

    Livewire::actingAs($reviewer)
        ->test(BillingManager::class)
        ->set('selectedInvoices', [$invoices[0]->id, $invoices[1]->id])
        ->set('bulkReminderMessage', 'Test reminder message')
        ->call('sendBulkReminders')
        ->assertDispatched('reminders-sent');
});

test('it can bulk approve invoices from dashboard', function (): void {
    $reviewer = User::factory()->create();
    $reviewer->assignRole('reviewer');

    // Assign estate to reviewer
    $reviewer->assignedEstates()->attach($this->estate->id, [
        'assigned_by' => $this->user->id,
        'assigned_at' => now(),
    ]);

    $invoices = [];
    foreach ($this->houses as $house) {
        $invoices[] = Invoice::factory()->create([
            'house_id' => $house->id,
            'status' => 'submitted',
        ]);
    }

    Livewire::actingAs($reviewer)
        ->test(BillingManager::class)
        ->set('selectedInvoices', [$invoices[0]->id, $invoices[1]->id])
        ->call('bulkApproveInvoices')
        ->assertDispatched('invoices-approved');

    // Only check the invoices that were selected for approval
    expect($invoices[0]->fresh()->status)->toBe('approved');
    expect($invoices[1]->fresh()->status)->toBe('approved');
    // The third invoice should remain submitted as it wasn't selected
    expect($invoices[2]->fresh()->status)->toBe('submitted');
});

test('it validates user permissions for bulk operations', function (): void {
    $caretaker = User::factory()->create();
    $caretaker->assignRole('caretaker');

    $invoices = [];
    foreach ($this->houses as $house) {
        $invoices[] = Invoice::factory()->create([
            'house_id' => $house->id,
            'status' => 'submitted',
        ]);
    }

    Livewire::actingAs($caretaker)
        ->test(BillingManager::class)
        ->set('selectedInvoices', [$invoices[0]->id, $invoices[1]->id])
        ->call('bulkApproveInvoices')
        ->assertDispatched('permission-denied');
});

test('it displays aging analysis', function (): void {
    // Assign estate to manager
    $this->user->assignedEstates()->attach($this->estate->id, [
        'assigned_by' => $this->user->id,
        'assigned_at' => now(),
    ]);

    // Create overdue invoices
    Invoice::factory()->create([
        'house_id' => $this->houses[0]->id,
        'status' => 'overdue',
        'due_date' => now()->subDays(10),
        'amount' => 1000,
    ]);

    Invoice::factory()->create([
        'house_id' => $this->houses[1]->id,
        'status' => 'overdue',
        'due_date' => now()->subDays(40),
        'amount' => 1500,
    ]);

    Livewire::actingAs($this->user)
        ->test(BillingManager::class)
        ->assertSee('Aging Analysis')
        ->assertSee('0-30 Days')
        ->assertSee('31-60 Days')
        ->assertSee('1,000.00')
        ->assertSee('1,500.00');
});

test('it can export billing data', function (): void {
    foreach ($this->houses as $house) {
        Invoice::factory()->create([
            'house_id' => $house->id,
            'status' => 'overdue',
            'amount' => 1000,
        ]);
    }

    Livewire::actingAs($this->user)
        ->test(BillingManager::class)
        ->call('exportBillingData')
        ->assertDispatched('billing-data-exported');
});

test('it refreshes data automatically', function (): void {
    Livewire::actingAs($this->user)
        ->test(BillingManager::class)
        ->call('refreshData')
        ->assertSet('lastRefresh', now()->diffForHumans());
});

test('it handles invoice selection toggling', function (): void {
    $invoice = Invoice::factory()->create([
        'house_id' => $this->houses[0]->id,
        'status' => 'draft',
    ]);

    $component = Livewire::actingAs($this->user)
        ->test(BillingManager::class);

    // Select invoice
    $component->call('toggleInvoiceSelection', $invoice->id)
        ->assertSet('selectedInvoices', [$invoice->id]);

    // Deselect invoice
    $component->call('toggleInvoiceSelection', $invoice->id)
        ->assertSet('selectedInvoices', []);
});

test('it can select all invoices', function (): void {
    $invoices = [];
    foreach ($this->houses as $house) {
        $invoices[] = Invoice::factory()->create([
            'house_id' => $house->id,
            'status' => 'draft',
        ]);
    }

    $invoiceIds = collect($invoices)->pluck('id')->toArray();

    Livewire::actingAs($this->user)
        ->test(BillingManager::class)
        ->call('selectAllInvoices')
        ->assertSet('selectedInvoices', $invoiceIds);
});

test('it can clear selection', function (): void {
    $invoices = [];
    foreach ($this->houses as $house) {
        $invoices[] = Invoice::factory()->create([
            'house_id' => $house->id,
            'status' => 'draft',
        ]);
    }

    $invoiceIds = collect($invoices)->pluck('id')->toArray();

    Livewire::actingAs($this->user)
        ->test(BillingManager::class)
        ->set('selectedInvoices', $invoiceIds)
        ->call('clearSelection')
        ->assertSet('selectedInvoices', []);
});
