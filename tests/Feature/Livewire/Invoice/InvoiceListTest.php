<?php

use App\Livewire\Invoice\InvoiceList;
use App\Models\Estate;
use App\Models\House;
use App\Models\Invoice;
use App\Models\User;
use Livewire\Livewire;
use PHPUnit\Framework\Attributes\Test;

uses(\Illuminate\Foundation\Testing\RefreshDatabase::class);

beforeEach(function (): void {
    $this->user = User::factory()->admin();
    $this->estate = Estate::factory()->create();
    $this->house = House::factory()->create(['estate_id' => $this->estate->id]);
});

it('displays all invoices by default', function (): void {
    $invoices = Invoice::factory()->count(5)->create(['house_id' => $this->house->id]);

    Livewire::actingAs($this->user)
        ->test(InvoiceList::class)
        ->assertViewHas('invoices', fn ($viewInvoices) => $viewInvoices->count() === 5);
});

it('filters invoices by status', function (): void {
    Invoice::factory()->count(3)->create(['house_id' => $this->house->id, 'status' => 'draft']);
    Invoice::factory()->count(2)->create(['house_id' => $this->house->id, 'status' => 'paid']);

    Livewire::actingAs($this->user)
        ->test(InvoiceList::class, ['filters' => ['status' => 'draft']])
        ->assertViewHas('invoices', fn ($viewInvoices) => $viewInvoices->count() === 3);
});

it('filters invoices by estate', function (): void {
    $otherEstate = Estate::factory()->create();
    $otherHouse = House::factory()->create(['estate_id' => $otherEstate->id]);

    Invoice::factory()->count(2)->create(['house_id' => $this->house->id]);
    Invoice::factory()->count(3)->create(['house_id' => $otherHouse->id]);

    Livewire::actingAs($this->user)
        ->test(InvoiceList::class, ['filters' => ['estate_id' => $this->estate->id]])
        ->assertViewHas('invoices', fn ($viewInvoices) => $viewInvoices->count() === 2);
});

it('filters invoices by house', function (): void {
    $otherHouse = House::factory()->create(['estate_id' => $this->estate->id]);

    Invoice::factory()->count(2)->create(['house_id' => $this->house->id]);
    Invoice::factory()->count(3)->create(['house_id' => $otherHouse->id]);

    Livewire::actingAs($this->user)
        ->test(InvoiceList::class, ['filters' => ['house_id' => $this->house->id]])
        ->assertViewHas('invoices', fn ($viewInvoices) => $viewInvoices->count() === 2);
});

it('filters invoices by date range', function (): void {
    Invoice::factory()->count(2)->create([
        'house_id' => $this->house->id,
        'billing_period_start' => now()->subDays(10),
    ]);
    Invoice::factory()->count(3)->create([
        'house_id' => $this->house->id,
        'billing_period_start' => now()->subDays(5),
    ]);

    Livewire::actingAs($this->user)
        ->test(InvoiceList::class, [
            'filters' => [
                'date_from' => now()->subDays(7)->format('Y-m-d'),
                'date_to' => now()->format('Y-m-d'),
            ],
        ])
        ->assertViewHas('invoices', fn ($viewInvoices) => $viewInvoices->count() === 3);
});

it('paginates invoices', function (): void {
    Invoice::factory()->count(25)->create(['house_id' => $this->house->id]);

    Livewire::actingAs($this->user)
        ->test(InvoiceList::class)
        ->assertViewHas('invoices', function ($viewInvoices) {
            return $viewInvoices->count() <= 20; // Default pagination
        });
});

it('displays correct invoice data', function (): void {
    $invoice = Invoice::factory()->create([
        'house_id' => $this->house->id,
        'total_amount' => 150.00,
        'status' => 'draft',
    ]);

    Livewire::actingAs($this->user)
        ->test(InvoiceList::class)
        ->assertSee($invoice->invoice_number)
        ->assertSee('150.00')
        ->assertSee('Draft');
});

it('can reset filters', function (): void {
    Invoice::factory()->count(5)->create(['house_id' => $this->house->id]);

    Livewire::actingAs($this->user)
        ->test(InvoiceList::class, ['filters' => ['status' => 'pending']])
        ->call('resetFilters')
        ->assertViewHas('invoices', fn ($viewInvoices) => $viewInvoices->count() === 5);
});

it('requires authentication', function (): void {
    // Test the route instead of the component directly
    $response = $this->get('/invoices');
    $response->assertRedirect('/login');
});
