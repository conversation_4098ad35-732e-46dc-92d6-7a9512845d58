<?php

use App\Jobs\ProcessExportJob;
use App\Livewire\DataExportManager;
use App\Models\ExportJob;
use App\Models\ExportTemplate;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Queue;

beforeEach(function (): void {
    // Create a user with export permissions
    $this->user = User::factory()->admin()->create();

    // Give the user export permission
    $this->user->givePermissionTo('export.data_all');

    Auth::login($this->user);
});

test('export management page renders correctly', function (): void {
    $response = Livewire::test(DataExportManager::class);

    $response->assertStatus(200);
    $response->assertSee('Data Export Manager');
    $response->assertSee('New Export');
    $response->assertSee('Manage Templates');
});

test('user can create new export job', function (): void {
    Queue::fake();

    Livewire::test(DataExportManager::class)
        ->set('showCreateModal', true)
        ->set('exportName', 'Test Export')
        ->set('entityType', 'estates')
        ->set('format', 'xlsx')
        ->call('createExport')
        ->assertHasNoErrors()
        ->assertDispatched('close-modal')
        ->assertSee('Export job created successfully');

    $this->assertDatabaseHas('export_jobs', [
        'user_id' => $this->user->id,
        'entity_type' => 'estates',
        'format' => 'xlsx',
        'status' => 'pending',
    ]);

    Queue::assertPushed(ProcessExportJob::class);
});

test('export creation validates required fields', function (): void {
    Livewire::test(DataExportManager::class)
        ->set('showCreateModal', true)
        ->set('exportName', '') // Empty name
        ->call('createExport')
        ->assertHasErrors(['exportName']);
});

test('user can delete export job', function (): void {
    $exportJob = ExportJob::factory()->create([
        'user_id' => $this->user->id,
        'file_path' => 'exports/test.xlsx',
    ]);

    // Mock storage
    \Storage::shouldReceive('exists')->with('exports/test.xlsx')->andReturn(true);
    \Storage::shouldReceive('delete')->with('exports/test.xlsx')->andReturn(true);

    Livewire::test(DataExportManager::class)
        ->call('deleteExport', $exportJob->id)
        ->assertHasNoErrors()
        ->assertSee('Export deleted successfully');

    $this->assertDatabaseMissing('export_jobs', ['id' => $exportJob->id]);
});

test('user can retry failed export job', function (): void {
    Queue::fake();

    $exportJob = ExportJob::factory()->create([
        'user_id' => $this->user->id,
        'status' => 'failed',
        'error' => 'Test error',
    ]);

    Livewire::test(DataExportManager::class)
        ->call('retryExport', $exportJob->id)
        ->assertHasNoErrors()
        ->assertSee('Export job retried successfully');

    $this->assertDatabaseHas('export_jobs', [
        'id' => $exportJob->id,
        'status' => 'pending',
        'error' => null,
    ]);

    Queue::assertPushed(ProcessExportJob::class);
});

test('user cannot retry non-failed export job', function (): void {
    $exportJob = ExportJob::factory()->create([
        'user_id' => $this->user->id,
        'status' => 'completed',
    ]);

    Livewire::test(DataExportManager::class)
        ->call('retryExport', $exportJob->id)
        ->assertSee('Only failed exports can be retried');
});

test('export jobs are displayed with correct status', function (): void {
    $completedJob = ExportJob::factory()->create([
        'user_id' => $this->user->id,
        'status' => 'completed',
        'file_name' => 'completed_export.xlsx',
    ]);

    $pendingJob = ExportJob::factory()->create([
        'user_id' => $this->user->id,
        'status' => 'pending',
        'file_name' => 'pending_export.xlsx',
    ]);

    $failedJob = ExportJob::factory()->create([
        'user_id' => $this->user->id,
        'status' => 'failed',
        'file_name' => 'failed_export.xlsx',
    ]);

    Livewire::test(DataExportManager::class)
        ->assertSee('completed_export.xlsx')
        ->assertSee('pending_export.xlsx')
        ->assertSee('failed_export.xlsx')
        ->assertSee('Completed')
        ->assertSee('Pending')
        ->assertSee('Failed');
});

test('export templates are loaded correctly', function (): void {
    $template = ExportTemplate::factory()->create([
        'user_id' => $this->user->id,
        'entity_type' => 'estates',
    ]);

    Livewire::test(DataExportManager::class)
        ->assertSet('templates.0.id', $template->id)
        ->assertSet('templates.0.name', $template->name);
});

test('export creation with template applies template settings', function (): void {
    $template = ExportTemplate::factory()->create([
        'user_id' => $this->user->id,
        'entity_type' => 'invoices',
        'format' => 'csv',
        'filters' => ['status' => 'paid'],
    ]);

    Queue::fake();

    Livewire::test(DataExportManager::class)
        ->set('showCreateModal', true)
        ->set('exportName', 'Template Export')
        ->set('selectedTemplate', $template->id)
        ->call('createExport')
        ->assertHasNoErrors();

    $this->assertDatabaseHas('export_jobs', [
        'user_id' => $this->user->id,
        'export_template_id' => $template->id,
        'entity_type' => 'invoices',
        'format' => 'csv',
        'status' => 'pending',
    ]);
});

test('export stats are calculated correctly', function (): void {
    ExportJob::factory()->count(3)->create([
        'user_id' => $this->user->id,
        'status' => 'pending',
    ]);

    ExportJob::factory()->count(2)->create([
        'user_id' => $this->user->id,
        'status' => 'processing',
    ]);

    ExportJob::factory()->count(1)->create([
        'user_id' => $this->user->id,
        'status' => 'completed',
    ]);

    Livewire::test(DataExportManager::class)
        ->assertSee('Total Exports')
        ->assertSee('6') // Total exports
        ->assertSee('3') // Pending
        ->assertSee('2'); // Processing
});

test('polling is enabled when there are active jobs', function (): void {
    ExportJob::factory()->create([
        'user_id' => $this->user->id,
        'status' => 'processing',
    ]);

    Livewire::test(DataExportManager::class)
        ->assertSet('pollingInterval', 5);
});

test('polling is disabled when there are no active jobs', function (): void {
    ExportJob::factory()->create([
        'user_id' => $this->user->id,
        'status' => 'completed',
    ]);

    Livewire::test(DataExportManager::class)
        ->call('getPollingInterval')
        ->assertNull();
});
