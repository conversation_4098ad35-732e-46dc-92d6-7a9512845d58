<?php

use App\Livewire\Export\ExportTemplateForm;
use App\Models\ExportTemplate;
use App\Models\User;
use Illuminate\Support\Facades\Auth;

beforeEach(function (): void {
    // Create a user with export permissions
    $this->user = User::factory()->admin()->create();

    // Give the user export permission
    $this->user->givePermissionTo('export.data_all');

    Auth::login($this->user);
});

test('export template form renders correctly for creation', function (): void {
    Livewire::test(ExportTemplateForm::class)
        ->assertStatus(200)
        ->assertSee('Create Export Template')
        ->assertSee('Template Name')
        ->assertSee('Entity Type')
        ->assertSee('Export Format')
        ->assertSee('Select Columns');
});

test('export template form renders correctly for editing', function (): void {
    $template = ExportTemplate::factory()->create([
        'user_id' => $this->user->id,
        'name' => 'Test Template',
        'entity_type' => 'estates',
        'format' => 'xlsx',
    ]);

    Livewire::test(ExportTemplateForm::class, ['templateId' => $template->id])
        ->assertStatus(200)
        ->assertSee('Edit Export Template')
        ->assertSet('name', 'Test Template')
        ->assertSet('entityType', 'estates')
        ->assertSet('format', 'xlsx');
});

test('user cannot edit other users private templates', function (): void {
    $otherUser = User::factory()->create();
    $otherUser->givePermissionTo('export.data_all');

    $template = ExportTemplate::factory()->create([
        'user_id' => $otherUser->id,
        'is_public' => false,
    ]);

    Livewire::test(ExportTemplateForm::class, ['templateId' => $template->id])
        ->assertStatus(403);
});

test('user can edit public templates created by other users', function (): void {
    $otherUser = User::factory()->create();
    $otherUser->givePermissionTo('export.data_all');

    $template = ExportTemplate::factory()->create([
        'user_id' => $otherUser->id,
        'is_public' => true,
    ]);

    Livewire::test(ExportTemplateForm::class, ['templateId' => $template->id])
        ->assertStatus(200)
        ->assertSet('name', $template->name);
});

test('export template creation validates required fields', function (): void {
    Livewire::test(ExportTemplateForm::class)
        ->set('showModal', true)
        ->call('save')
        ->assertHasErrors(['name', 'entityType', 'format', 'columns']);
});

test('export template creation works with valid data', function (): void {
    Livewire::test(ExportTemplateForm::class)
        ->set('showModal', true)
        ->set('name', 'New Template')
        ->set('entityType', 'houses')
        ->set('format', 'csv')
        ->set('columns', ['ID', 'House Number', 'Status'])
        ->call('save')
        ->assertHasNoErrors()
        ->assertDispatched('templateSaved')
        ->assertSee('Export template created successfully');

    $this->assertDatabaseHas('export_templates', [
        'user_id' => $this->user->id,
        'name' => 'New Template',
        'entity_type' => 'houses',
        'format' => 'csv',
    ]);
});

test('export template editing works with valid data', function (): void {
    $template = ExportTemplate::factory()->create([
        'user_id' => $this->user->id,
        'name' => 'Original Name',
        'entity_type' => 'estates',
        'format' => 'xlsx',
    ]);

    Livewire::test(ExportTemplateForm::class, ['templateId' => $template->id])
        ->set('showModal', true)
        ->set('name', 'Updated Name')
        ->set('format', 'pdf')
        ->call('save')
        ->assertHasNoErrors()
        ->assertDispatched('templateSaved')
        ->assertSee('Export template updated successfully');

    $this->assertDatabaseHas('export_templates', [
        'id' => $template->id,
        'name' => 'Updated Name',
        'format' => 'pdf',
    ]);
});

test('entity type change updates available columns', function (): void {
    Livewire::test(ExportTemplateForm::class)
        ->set('entityType', 'estates')
        ->assertSet('columns', array_keys([
            'ID', 'Name', 'Location', 'Status', 'Total Houses', 'Occupied Houses',
            'Occupancy Rate', 'Created At', 'Updated At',
        ]))
        ->set('entityType', 'houses')
        ->assertSet('columns', array_keys([
            'ID', 'House Number', 'Estate', 'Address', 'Status', 'Meter Number',
            'Created At', 'Updated At',
        ]));
});

test('scheduled exports require frequency', function (): void {
    Livewire::test(ExportTemplateForm::class)
        ->set('showModal', true)
        ->set('name', 'Scheduled Template')
        ->set('entityType', 'invoices')
        ->set('format', 'xlsx')
        ->set('columns', ['ID', 'Invoice Number'])
        ->set('isScheduled', true)
        ->call('save')
        ->assertHasErrors(['scheduleFrequency']);
});

test('scheduled exports work with frequency', function (): void {
    Livewire::test(ExportTemplateForm::class)
        ->set('showModal', true)
        ->set('name', 'Scheduled Template')
        ->set('entityType', 'invoices')
        ->set('format', 'xlsx')
        ->set('columns', ['ID', 'Invoice Number'])
        ->set('isScheduled', true)
        ->set('scheduleFrequency', 'monthly')
        ->call('save')
        ->assertHasNoErrors();

    $this->assertDatabaseHas('export_templates', [
        'user_id' => $this->user->id,
        'name' => 'Scheduled Template',
        'is_scheduled' => true,
        'schedule_frequency' => 'monthly',
    ]);
});

test('form resets after successful creation', function (): void {
    Livewire::test(ExportTemplateForm::class)
        ->set('showModal', true)
        ->set('name', 'Test Template')
        ->set('entityType', 'contacts')
        ->set('format', 'csv')
        ->set('columns', ['ID', 'Name', 'Email'])
        ->call('save')
        ->assertHasNoErrors()
        ->assertSet('name', '')
        ->assertSet('entityType', 'estates') // Default value
        ->assertSet('format', 'xlsx') // Default value
        ->assertSet('showModal', false);
});

test('available entities are correctly listed', function (): void {
    Livewire::test(ExportTemplateForm::class)
        ->assertSet('availableEntities', [
            'estates' => 'Estates',
            'houses' => 'Houses',
            'contacts' => 'Contacts',
            'meter_readings' => 'Meter Readings',
            'invoices' => 'Invoices',
            'water_rates' => 'Water Rates',
        ]);
});

test('available formats are correctly listed', function (): void {
    Livewire::test(ExportTemplateForm::class)
        ->assertSet('availableFormats', [
            'xlsx' => 'Excel (.xlsx)',
            'csv' => 'CSV (.csv)',
            'pdf' => 'PDF (.pdf)',
        ]);
});

test('schedule frequencies are correctly listed', function (): void {
    Livewire::test(ExportTemplateForm::class)
        ->assertSet('scheduleFrequencies', [
            'daily' => 'Daily',
            'weekly' => 'Weekly',
            'monthly' => 'Monthly',
            'quarterly' => 'Quarterly',
        ]);
});
