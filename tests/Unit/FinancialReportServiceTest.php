<?php

declare(strict_types=1);

use App\Models\AccountTransaction;
use App\Models\Estate;
use App\Models\House;
use App\Models\HouseAccount;
use App\Models\Invoice;
use App\Models\MeterReading;
use App\Models\WaterRate;
use App\Services\FinancialReportService;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function (): void {
    // Create test data
    $this->estate = Estate::factory()->create();
    $this->house = House::factory()->create(['estate_id' => $this->estate->id]);
    $this->waterRate = WaterRate::factory()->create(['estate_id' => $this->estate->id]);

    // Create house account
    $this->houseAccount = HouseAccount::factory()->create([
        'house_id' => $this->house->id,
        'current_balance' => 0,
    ]);
});

describe('FinancialReportService', function (): void {
    test('can generate aging report', function (): void {
        $service = new FinancialReportService;

        // Create invoices with different aging periods using make() to avoid factory overrides
        $invoice1 = Invoice::factory()->make([
            'house_id' => $this->house->id,
            'water_rate_id' => $this->waterRate->id,
            'meter_reading_id' => MeterReading::factory()->create(['house_id' => $this->house->id])->id,
            'invoice_number' => 'INV-TEST-001',
            'billing_period_start' => now()->subDays(20),
            'billing_period_end' => now()->subDays(15),
            'previous_reading' => 1000,
            'current_reading' => 1050,
            'consumption' => 50,
            'rate_per_unit' => 20,
            'amount' => 1000,
            'fixed_charge' => 0,
            'total_amount' => 1000,
            'due_date' => now()->subDays(10),
            'status' => 'sent',
            'sent_at' => now()->subDays(12),
        ]);
        $invoice1->save();

        $invoice2 = Invoice::factory()->make([
            'house_id' => $this->house->id,
            'water_rate_id' => $this->waterRate->id,
            'meter_reading_id' => MeterReading::factory()->create(['house_id' => $this->house->id])->id,
            'invoice_number' => 'INV-TEST-002',
            'billing_period_start' => now()->subDays(50),
            'billing_period_end' => now()->subDays(45),
            'previous_reading' => 1050,
            'current_reading' => 1150,
            'consumption' => 100,
            'rate_per_unit' => 20,
            'amount' => 2000,
            'fixed_charge' => 0,
            'total_amount' => 2000,
            'due_date' => now()->subDays(40),
            'status' => 'sent',
            'sent_at' => now()->subDays(42),
        ]);
        $invoice2->save();

        $invoice3 = Invoice::factory()->make([
            'house_id' => $this->house->id,
            'water_rate_id' => $this->waterRate->id,
            'meter_reading_id' => MeterReading::factory()->create(['house_id' => $this->house->id])->id,
            'invoice_number' => 'INV-TEST-003',
            'billing_period_start' => now()->subDays(100),
            'billing_period_end' => now()->subDays(95),
            'previous_reading' => 1150,
            'current_reading' => 1300,
            'consumption' => 150,
            'rate_per_unit' => 20,
            'amount' => 3000,
            'fixed_charge' => 0,
            'total_amount' => 3000,
            'due_date' => now()->subDays(85), // 85 days ago to ensure it's in 61-90 bucket
            'status' => 'sent',
            'sent_at' => now()->subDays(87),
        ]);
        $invoice3->save();

        $invoice4 = Invoice::factory()->make([
            'house_id' => $this->house->id,
            'water_rate_id' => $this->waterRate->id,
            'meter_reading_id' => MeterReading::factory()->create(['house_id' => $this->house->id])->id,
            'invoice_number' => 'INV-TEST-004',
            'billing_period_start' => now()->subDays(20),
            'billing_period_end' => now()->subDays(15),
            'previous_reading' => 1300,
            'current_reading' => 1325,
            'consumption' => 25,
            'rate_per_unit' => 20,
            'amount' => 500,
            'fixed_charge' => 0,
            'total_amount' => 500,
            'due_date' => now()->subDays(10),
            'status' => 'paid',
            'sent_at' => now()->subDays(12),
            'paid_at' => now()->subDays(5),
        ]);
        $invoice4->save();

        $agingReport = $service->generateAgingReport();

        expect($agingReport)->toHaveKey('current');
        expect($agingReport)->toHaveKey('days_1_30');
        expect($agingReport)->toHaveKey('days_31_60');
        expect($agingReport)->toHaveKey('days_61_90');
        expect($agingReport)->toHaveKey('days_over_90');
        expect($agingReport)->toHaveKey('total_outstanding');

        // Check aging buckets
        expect($agingReport['current'])->toBe(0); // No current invoices
        expect($agingReport['days_1_30'])->toEqual(1000); // 10 days old
        expect($agingReport['days_31_60'])->toEqual(2000); // 40 days old
        expect($agingReport['days_61_90'])->toEqual(3000); // 90 days old
        expect($agingReport['days_over_90'])->toBe(0); // No invoices over 90 days
        expect($agingReport['total_outstanding'])->toEqual(6000); // Sum of unpaid invoices
    });

    test('can generate revenue report for date range', function (): void {
        $service = new FinancialReportService;

        $startDate = now()->subDays(30);
        $endDate = now();

        // Create invoices within date range
        $invoice1 = Invoice::factory()->create([
            'house_id' => $this->house->id,
            'water_rate_id' => $this->waterRate->id,
            'status' => 'paid',
            'paid_at' => now()->subDays(10),
            'total_amount' => 1000,
            'billing_period_start' => now()->subDays(15),
            'billing_period_end' => now()->subDays(10),
        ]);

        $invoice2 = Invoice::factory()->create([
            'house_id' => $this->house->id,
            'water_rate_id' => $this->waterRate->id,
            'status' => 'paid',
            'paid_at' => now()->subDays(5),
            'total_amount' => 2000,
            'billing_period_start' => now()->subDays(8),
            'billing_period_end' => now()->subDays(5),
        ]);

        // Create invoice outside date range
        $invoice3 = Invoice::factory()->create([
            'house_id' => $this->house->id,
            'water_rate_id' => $this->waterRate->id,
            'status' => 'paid',
            'paid_at' => now()->subDays(40),
            'total_amount' => 500,
            'billing_period_start' => now()->subDays(45),
            'billing_period_end' => now()->subDays(40),
        ]);

        $revenueReport = $service->generateRevenueReport($startDate, $endDate);

        expect($revenueReport)->toHaveKey('total_revenue');
        expect($revenueReport)->toHaveKey('total_invoices');
        expect($revenueReport)->toHaveKey('average_invoice_amount');
        expect($revenueReport)->toHaveKey('invoices_by_status');
        expect($revenueReport)->toHaveKey('revenue_by_estate');
        expect($revenueReport)->toHaveKey('revenue_by_month');

        expect($revenueReport['total_revenue'])->toEqual(3000);
        expect($revenueReport['total_invoices'])->toBe(2);
        expect($revenueReport['average_invoice_amount'])->toEqual(1500);
    });

    test('can generate customer statement', function (): void {
        $service = new FinancialReportService;

        // Create invoices for the house
        $invoice1 = Invoice::factory()->create([
            'house_id' => $this->house->id,
            'water_rate_id' => $this->waterRate->id,
            'status' => 'paid',
            'paid_at' => now()->subDays(10),
            'total_amount' => 1000,
        ]);

        $invoice2 = Invoice::factory()->create([
            'house_id' => $this->house->id,
            'water_rate_id' => $this->waterRate->id,
            'status' => 'sent',
            'total_amount' => 2000,
        ]);

        // Note: Payments and adjustments would be created here in a real scenario
        // For testing, we'll focus on the invoice-based transactions

        $statement = $service->generateCustomerStatement($this->house->id, now()->subDays(30), now());

        expect($statement)->toHaveKey('house_info');
        expect($statement)->toHaveKey('opening_balance');
        expect($statement)->toHaveKey('transactions');
        expect($statement)->toHaveKey('closing_balance');
        expect($statement)->toHaveKey('period_start');
        expect($statement)->toHaveKey('period_end');

        expect($statement['house_info']['id'])->toBe($this->house->id);
        expect($statement['transactions'])->toHaveCount(2); // 2 invoices
        expect($statement['closing_balance'])->toEqual(3000); // 2000 (unpaid) + 1000 (paid)
    });

    test('can filter aging report by estate', function (): void {
        $service = new FinancialReportService;

        // Create another estate and house
        $estate2 = Estate::factory()->create();
        $house2 = House::factory()->create(['estate_id' => $estate2->id]);
        $waterRate2 = WaterRate::factory()->create(['estate_id' => $estate2->id]);

        // Create invoices for different estates
        $invoice1 = Invoice::factory()->create([
            'house_id' => $this->house->id,
            'water_rate_id' => $this->waterRate->id,
            'status' => 'sent',
            'due_date' => now()->subDays(10),
            'total_amount' => 1000,
        ]);

        $invoice2 = Invoice::factory()->create([
            'house_id' => $house2->id,
            'water_rate_id' => $waterRate2->id,
            'status' => 'sent',
            'due_date' => now()->subDays(10),
            'total_amount' => 2000,
        ]);

        $agingReport = $service->generateAgingReport($this->estate->id);

        expect($agingReport['total_outstanding'])->toEqual(1000); // Only from first estate
    });

    test('can generate revenue report by payment method', function (): void {
        $service = new FinancialReportService;

        $startDate = now()->subDays(30);
        $endDate = now();

        // Create invoices with different payment methods
        $invoice1 = Invoice::factory()->create([
            'house_id' => $this->house->id,
            'water_rate_id' => $this->waterRate->id,
            'status' => 'paid',
            'paid_at' => now()->subDays(10),
            'total_amount' => 1000,
            'payment_reference' => 'MPESA-123',
        ]);

        $invoice2 = Invoice::factory()->create([
            'house_id' => $this->house->id,
            'water_rate_id' => $this->waterRate->id,
            'status' => 'paid',
            'paid_at' => now()->subDays(5),
            'total_amount' => 2000,
            'payment_reference' => 'BANK-456',
        ]);

        $revenueReport = $service->generateRevenueReport($startDate, $endDate);

        expect($revenueReport)->toHaveKey('revenue_by_payment_method');
        expect($revenueReport['revenue_by_payment_method'])->toHaveKey('MPESA');
        expect($revenueReport['revenue_by_payment_method'])->toHaveKey('BANK');
        expect($revenueReport['revenue_by_payment_method']['MPESA'])->toEqual(1000);
        expect($revenueReport['revenue_by_payment_method']['BANK'])->toEqual(2000);
    });

    test('can calculate collection rate', function (): void {
        $service = new FinancialReportService;

        $startDate = now()->subDays(30);
        $endDate = now();

        // Create paid and unpaid invoices
        $invoice1 = Invoice::factory()->create([
            'house_id' => $this->house->id,
            'water_rate_id' => $this->waterRate->id,
            'status' => 'paid',
            'paid_at' => now()->subDays(10),
            'total_amount' => 1000,
        ]);

        $invoice2 = Invoice::factory()->create([
            'house_id' => $this->house->id,
            'water_rate_id' => $this->waterRate->id,
            'status' => 'sent',
            'total_amount' => 2000,
        ]);

        $invoice3 = Invoice::factory()->create([
            'house_id' => $this->house->id,
            'water_rate_id' => $this->waterRate->id,
            'status' => 'overdue',
            'total_amount' => 500,
        ]);

        $revenueReport = $service->generateRevenueReport($startDate, $endDate);

        expect($revenueReport)->toHaveKey('collection_rate');
        expect($revenueReport)->toHaveKey('total_billed');
        expect($revenueReport)->toHaveKey('total_collected');

        expect($revenueReport['total_billed'])->toEqual(3500); // 1000 + 2000 + 500
        expect($revenueReport['total_collected'])->toEqual(1000); // Only paid invoice
        expect($revenueReport['collection_rate'])->toBe(28.57); // (1000 / 3500) * 100
    });

    test('can generate account transactions report', function (): void {
        $service = new FinancialReportService;

        // Create account transactions
        $transaction1 = AccountTransaction::factory()->create([
            'house_account_id' => $this->houseAccount->id,
            'transaction_type' => 'invoice',
            'amount' => 1000,
            'balance_before' => 0,
            'balance_after' => 1000,
        ]);

        $transaction2 = AccountTransaction::factory()->create([
            'house_account_id' => $this->houseAccount->id,
            'transaction_type' => 'payment',
            'amount' => -500,
            'balance_before' => 1000,
            'balance_after' => 500,
        ]);

        $transactionsReport = $service->generateAccountTransactionsReport($this->house->id, now()->subDays(30), now());

        expect($transactionsReport)->toHaveKey('transactions');
        expect($transactionsReport)->toHaveKey('total_debits');
        expect($transactionsReport)->toHaveKey('total_credits');
        expect($transactionsReport)->toHaveKey('net_change');

        expect($transactionsReport['transactions'])->toHaveCount(2);
        expect($transactionsReport['total_debits'])->toEqual(1000);
        expect($transactionsReport['total_credits'])->toEqual(500);
        expect($transactionsReport['net_change'])->toEqual(500);
    });

    test('can handle empty reports gracefully', function (): void {
        $service = new FinancialReportService;

        // Test with no data
        $agingReport = $service->generateAgingReport();
        $revenueReport = $service->generateRevenueReport(now()->subDays(30), now());
        $statement = $service->generateCustomerStatement($this->house->id, now()->subDays(30), now());

        expect($agingReport['total_outstanding'])->toBe(0);
        expect($revenueReport['total_revenue'])->toBe(0);
        expect($revenueReport['total_invoices'])->toBe(0);
        expect($statement['transactions'])->toHaveCount(0);
        expect($statement['opening_balance'])->toBe(0);
        expect($statement['closing_balance'])->toBe(0);
    });
});
