<?php

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Gate;
use Spatie\Permission\Models\Permission;

uses(RefreshDatabase::class);
uses(\Tests\Traits\CreatesTestUsers::class);
uses(\Tests\Traits\SetsUpSpatiePermissions::class);

beforeEach(function (): void {
    $this->setUpSpatiePermissions();
});
test('is admin gate works correctly', function (): void {
    // Clear any cached permissions
    app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

    $manager = $this->createManagerUser();
    $user = $this->createAdminUser();

    $this->actingAs($user);
    expect(Gate::allows('is-admin'))->toBeTrue();

    $this->actingAs($manager);

    // Debug: Check what roles the manager actually has
    $managerRoles = $manager->fresh()->getRoleNames()->toArray();
    expect($managerRoles)->not->toContain('admin', 'Manager should not have admin role, but has: '.implode(', ', $managerRoles));

    // Debug: Check hasRole method directly
    expect($manager->fresh()->hasRole('admin'))->toBeFalse('Manager hasRole(admin) should be false');
    expect($manager->fresh()->hasRole('manager'))->toBeTrue('Manager hasRole(manager) should be true');

    expect(Gate::allows('is-admin'))->toBeFalse();
});
test('is manager gate works correctly', function (): void {
    $user = $this->createManagerUser();
    $reviewer = $this->createReviewerUser();

    $this->actingAs($user);
    expect(Gate::allows('is-manager'))->toBeTrue();

    $this->actingAs($reviewer);
    expect(Gate::allows('is-manager'))->toBeFalse();
});
test('is reviewer gate works correctly', function (): void {
    $user = $this->createReviewerUser();
    $caretaker = $this->createCaretakerUser();

    $this->actingAs($user);
    expect(Gate::allows('is-reviewer'))->toBeTrue();

    $this->actingAs($caretaker);
    expect(Gate::allows('is-reviewer'))->toBeFalse();
});
test('is caretaker gate works correctly', function (): void {
    $user = $this->createCaretakerUser();
    $resident = $this->createResidentUser();

    $this->actingAs($user);
    expect(Gate::allows('is-caretaker'))->toBeTrue();

    $this->actingAs($resident);
    expect(Gate::allows('is-caretaker'))->toBeFalse();
});
test('is resident gate works correctly', function (): void {
    $user = $this->createResidentUser();
    $admin = $this->createAdminUser();

    $this->actingAs($user);
    expect(Gate::allows('is-resident'))->toBeTrue();

    $this->actingAs($admin);
    expect(Gate::allows('is-resident'))->toBeFalse();
});
test('system settings view gate works correctly', function (): void {
    $userWithPermission = $this->createAdminUser();
    $userWithPermission->givePermissionTo('system.settings.view');

    $userWithoutPermission = $this->createManagerUser();

    $this->actingAs($userWithPermission);
    expect(Gate::allows('system.settings.view'))->toBeTrue();

    $this->actingAs($userWithoutPermission);
    expect(Gate::allows('system.settings.view'))->toBeFalse();
});
test('view admin dashboard gate works correctly', function (): void {
    $user = $this->createAdminUser();
    $user->givePermissionTo('view-admin-dashboard');

    $manager = $this->createManagerUser();

    $this->actingAs($user);
    expect(Gate::allows('view-admin-dashboard'))->toBeTrue();

    $this->actingAs($manager);
    expect(Gate::allows('view-admin-dashboard'))->toBeFalse();
});
test('view manager dashboard gate works correctly', function (): void {
    $user = $this->createManagerUser();
    $user->givePermissionTo('view-manager-dashboard');

    $reviewer = $this->createReviewerUser();

    $this->actingAs($user);
    expect(Gate::allows('view-manager-dashboard'))->toBeTrue();

    $this->actingAs($reviewer);
    expect(Gate::allows('view-manager-dashboard'))->toBeFalse();
});
test('view reviewer dashboard gate works correctly', function (): void {
    $user = $this->createReviewerUser();
    $user->givePermissionTo('view-reviewer-dashboard');

    $caretaker = $this->createCaretakerUser();

    $this->actingAs($user);
    expect(Gate::allows('view-reviewer-dashboard'))->toBeTrue();

    $this->actingAs($caretaker);
    expect(Gate::allows('view-reviewer-dashboard'))->toBeFalse();
});
test('view caretaker dashboard gate works correctly', function (): void {
    $user = $this->createCaretakerUser();
    $user->givePermissionTo('view-caretaker-dashboard');

    $resident = $this->createResidentUser();

    $this->actingAs($user);
    expect(Gate::allows('view-caretaker-dashboard'))->toBeTrue();

    $this->actingAs($resident);
    expect(Gate::allows('view-caretaker-dashboard'))->toBeFalse();
});
test('view resident dashboard gate works correctly', function (): void {
    $user = $this->createResidentUser();
    $user->givePermissionTo('view-resident-dashboard');

    $admin = $this->createAdminUser();
    // Ensure admin doesn't have resident dashboard permission
    $admin->revokePermissionTo('view-resident-dashboard');

    $this->actingAs($user);
    expect(Gate::allows('view-resident-dashboard'))->toBeTrue();

    $this->actingAs($admin);
    expect(Gate::allows('view-resident-dashboard'))->toBeFalse();
});
test('system settings manage gate works correctly', function (): void {
    $userWithPermission = $this->createAdminUser();
    $userWithPermission->givePermissionTo('system.settings.manage');

    $userWithoutPermission = $this->createManagerUser();

    $this->actingAs($userWithPermission);
    expect(Gate::allows('system.settings.manage'))->toBeTrue();

    $this->actingAs($userWithoutPermission);
    expect(Gate::allows('system.settings.manage'))->toBeFalse();
});
test('audit logs view gate works correctly', function (): void {
    $userWithPermission = $this->createAdminUser();
    $userWithPermission->givePermissionTo('audit.logs.view');

    $userWithoutPermission = $this->createManagerUser();

    $this->actingAs($userWithPermission);
    expect(Gate::allows('audit.logs.view'))->toBeTrue();

    $this->actingAs($userWithoutPermission);
    expect(Gate::allows('audit.logs.view'))->toBeFalse();
});
test('audit logs export gate works correctly', function (): void {
    $userWithPermission = $this->createAdminUser();
    $userWithPermission->givePermissionTo('audit.logs.export');

    $userWithoutPermission = $this->createManagerUser();

    $this->actingAs($userWithPermission);
    expect(Gate::allows('audit.logs.export'))->toBeTrue();

    $this->actingAs($userWithoutPermission);
    expect(Gate::allows('audit.logs.export'))->toBeFalse();
});
test('permission checking works correctly', function (): void {
    $user = $this->createAdminUser();
    $manager = $this->createManagerUser();
    $resident = $this->createResidentUser();

    // Test admin can do anything (admin has these permissions)
    $this->actingAs($user);
    expect($user->can('estates.create'))->toBeTrue();
    expect($user->can('invoices.delete'))->toBeTrue();

    // Test manager can do specific things
    $this->actingAs($manager);
    expect($manager->can('estates.view_assigned'))->toBeTrue();
    expect($manager->can('estates.create'))->toBeFalse();

    // Test resident can do specific things
    $this->actingAs($resident);
    expect($resident->can('invoices.view_own'))->toBeTrue();
    expect($resident->can('estates.create'))->toBeFalse();
});
test('permission denial works correctly', function (): void {
    $user = $this->createAdminUser();
    $manager = $this->createManagerUser();
    $resident = $this->createResidentUser();

    // Test admin cannot do nonexistent permissions
    $this->actingAs($user);
    expect($user->cannot('nonexistent.permission'))->toBeTrue();
    expect($user->cannot('invoices.delete_nonexistent'))->toBeTrue();

    // Test manager cannot do admin things
    $this->actingAs($manager);
    expect($manager->cannot('estates.create'))->toBeTrue();
    expect($manager->cannot('estates.view_assigned'))->toBeFalse();

    // Test resident cannot do admin things
    $this->actingAs($resident);
    expect($resident->cannot('estates.create'))->toBeTrue();
    expect($resident->cannot('invoices.view_own'))->toBeFalse();
});
