<?php

use App\Models\User;
use App\Services\PermissionService;
use Spatie\Permission\Models\Permission;

uses(\Tests\Traits\CreatesTestUsers::class);
uses(\Illuminate\Foundation\Testing\RefreshDatabase::class);
uses(\Tests\Traits\SetsUpSpatiePermissions::class);

beforeEach(function (): void {
    $this->permissionService = app(PermissionService::class);
    $this->setUpSpatiePermissions();
});

test('user has permission method', function (): void {
    $user = $this->createAdminUser();
    $manager = $this->createManagerUser();
    $resident = $this->createResidentUser();

    // Test admin has all permissions
    expect($this->permissionService->userHasPermission($user, 'estates.create'))->toBeTrue();
    expect($this->permissionService->userHasPermission($user, 'invoices.delete'))->toBeTrue();

    // Test manager has specific permissions
    expect($this->permissionService->userHasPermission($manager, 'estates.view_assigned'))->toBeTrue();
    expect($this->permissionService->userHasPermission($manager, 'estates.create'))->toBeFalse();

    // Test resident has specific permissions
    expect($this->permissionService->userHasPermission($resident, 'invoices.view_own'))->toBeTrue();
    expect($this->permissionService->userHasPermission($resident, 'estates.create'))->toBeFalse();
});

test('user has any permission method', function (): void {
    $user = $this->createAdminUser();
    $manager = $this->createManagerUser();
    $resident = $this->createResidentUser();

    // Test admin has any of the permissions
    expect($this->permissionService->userHasAnyPermission($user, ['estates.create', 'invoices.delete']))->toBeTrue();
    expect($this->permissionService->userHasAnyPermission($user, ['any.permission.name']))->toBeFalse();

    // Test manager has any of the permissions
    expect($this->permissionService->userHasAnyPermission($manager, ['estates.view_assigned', 'invoices.view_assigned']))->toBeTrue();
    expect($this->permissionService->userHasAnyPermission($manager, ['estates.create', 'invoices.delete']))->toBeFalse();

    // Test resident has any of the permissions
    expect($this->permissionService->userHasAnyPermission($resident, ['invoices.view_own', 'accounts.view_own']))->toBeTrue();
    expect($this->permissionService->userHasAnyPermission($resident, ['estates.create', 'invoices.delete']))->toBeFalse();
});

test('user has all permissions method', function (): void {
    $user = $this->createAdminUser();
    $manager = $this->createManagerUser();

    // Test admin has all permissions
    expect($this->permissionService->userHasAllPermissions($user, ['estates.create', 'invoices.delete']))->toBeTrue();

    // Test manager does not have all permissions
    expect($this->permissionService->userHasAllPermissions($manager, ['estates.create', 'invoices.delete']))->toBeFalse();
    expect($this->permissionService->userHasAllPermissions($manager, ['estates.view_assigned', 'invoices.view_assigned']))->toBeTrue();
});

test('create permission method', function (): void {
    // Use a unique permission name to avoid conflicts
    $uniquePermissionName = 'test.permission.'.uniqid();

    $permission = $this->permissionService->createPermission($uniquePermissionName, 'Test permission description');

    expect($permission)->toBeInstanceOf(Permission::class);
    expect($permission->name)->toEqual($uniquePermissionName);

    // Check if description is stored in the database
    $this->assertDatabaseHas('permissions', [
        'name' => $uniquePermissionName,
        'description' => 'Test permission description',
    ]);
});

test('assign permission to user method', function (): void {
    $user = $this->createAdminUser();
    $uniquePermissionName = 'test.permission.'.uniqid();
    Permission::create(['name' => $uniquePermissionName, 'guard_name' => 'web']);

    $this->permissionService->assignPermissionsToUser($user, [$uniquePermissionName]);

    expect($user->hasPermissionTo($uniquePermissionName))->toBeTrue();
});

test('revoke permission from user method', function (): void {
    $user = $this->createAdminUser();
    $uniquePermissionName = 'test.permission.'.uniqid();
    $permission = Permission::create(['name' => $uniquePermissionName, 'guard_name' => 'web']);
    $user->givePermissionTo($permission);

    $this->permissionService->assignPermissionsToUser($user, []);

    expect($user->hasPermissionTo($uniquePermissionName))->toBeFalse();
});

test('invalidate user cache method', function (): void {
    $user = $this->createAdminUser();

    // Clear cache first
    $this->permissionService->invalidateUserCache($user);

    // First check should hit database
    expect($user->hasPermissionTo('estates.create'))->toBeTrue();

    // Second check should use cache
    expect($user->hasPermissionTo('estates.create'))->toBeTrue();

    // Invalidate cache
    $this->permissionService->invalidateUserCache($user);

    // Check should work again after cache invalidation
    expect($user->hasPermissionTo('estates.create'))->toBeTrue();
});

test('get user permissions method', function (): void {
    $user = $this->createAdminUser();
    $manager = $this->createManagerUser();

    $adminPermissions = $this->permissionService->getUserPermissions($user);
    $managerPermissions = $this->permissionService->getUserPermissions($manager);

    expect(count($adminPermissions))->toBeGreaterThan(0);
    expect(count($managerPermissions))->toBeGreaterThan(0);
    expect(count($adminPermissions))->toBeGreaterThan(count($managerPermissions));
});

test('can method', function (): void {
    $user = $this->createAdminUser();
    $manager = $this->createManagerUser();
    $resident = $this->createResidentUser();

    // Test admin can do anything
    expect($this->permissionService->userHasPermission($user, 'estates.create'))->toBeTrue();
    expect($this->permissionService->userHasPermission($user, 'invoices.delete'))->toBeTrue();

    // Test manager can do specific things
    expect($this->permissionService->userHasPermission($manager, 'estates.view_assigned'))->toBeTrue();
    expect($this->permissionService->userHasPermission($manager, 'estates.create'))->toBeFalse();

    // Test resident can do specific things
    expect($this->permissionService->userHasPermission($resident, 'invoices.view_own'))->toBeTrue();
    expect($this->permissionService->userHasPermission($resident, 'estates.create'))->toBeFalse();
});

test('cannot method', function (): void {
    $user = $this->createAdminUser();
    $manager = $this->createManagerUser();
    $resident = $this->createResidentUser();

    // Test admin cannot do nonexistent permissions
    expect($this->permissionService->userHasPermission($user, 'nonexistent.permission'))->toBeFalse();
    expect($this->permissionService->userHasPermission($user, 'invoices.delete_nonexistent'))->toBeFalse();

    // Test manager cannot do admin things
    expect($this->permissionService->userHasPermission($manager, 'estates.create'))->toBeFalse();
    expect($this->permissionService->userHasPermission($manager, 'estates.view_assigned'))->toBeTrue();

    // Test resident cannot do admin things
    expect($this->permissionService->userHasPermission($resident, 'estates.create'))->toBeFalse();
    expect($this->permissionService->userHasPermission($resident, 'invoices.view_own'))->toBeTrue();
});

test('has permission with cache method', function (): void {
    $user = $this->createAdminUser();

    // Clear cache
    $this->permissionService->invalidateUserCache($user);

    // First check should hit database
    expect($this->permissionService->userHasPermission($user, 'estates.create'))->toBeTrue();

    // Second check should use cache
    expect($this->permissionService->userHasPermission($user, 'estates.create'))->toBeTrue();

    // Invalidate cache
    $this->permissionService->invalidateUserCache($user);

    // Check should work again after cache invalidation
    expect($this->permissionService->userHasPermission($user, 'estates.create'))->toBeTrue();
});

test('get users with permission method', function (): void {
    $user = $this->createAdminUser();
    $manager = $this->createManagerUser();
    $resident = $this->createResidentUser();

    // Create a permission that admin has
    Permission::create(['name' => 'estates.create', 'guard_name' => 'web']);
    $user->givePermissionTo('estates.create');

    // Test getting users with specific permission
    $usersWithPermission = User::whereHas('permissions', function ($query): void {
        $query->where('name', 'estates.create');
    })->get();

    expect($usersWithPermission->pluck('id')->toArray())->toContain($user->id);
    expect($usersWithPermission->pluck('id')->toArray())->not->toContain($manager->id);
    expect($usersWithPermission->pluck('id')->toArray())->not->toContain($resident->id);

    // Test getting users with any of multiple permissions
    Permission::create(['name' => 'invoices.view_own', 'guard_name' => 'web']);
    $resident->givePermissionTo('invoices.view_own');

    $usersWithAnyPermission = User::whereHas('permissions', function ($query): void {
        $query->whereIn('name', ['estates.create', 'invoices.view_own']);
    })->get();

    expect($usersWithAnyPermission->pluck('id')->toArray())->toContain($user->id);
    expect($usersWithAnyPermission->pluck('id')->toArray())->not->toContain($manager->id);
    expect($usersWithAnyPermission->pluck('id')->toArray())->toContain($resident->id);

    // Test getting users with all permissions
    Permission::create(['name' => 'invoices.delete', 'guard_name' => 'web']);
    $user->givePermissionTo('invoices.delete');

    $usersWithAllPermissions = User::whereHas('permissions', function ($query): void {
        $query->whereIn('name', ['estates.create', 'invoices.delete']);
    })->get();

    expect($usersWithAllPermissions->pluck('id')->toArray())->toContain($user->id);
    expect($usersWithAllPermissions->pluck('id')->toArray())->not->toContain($manager->id);
    expect($usersWithAllPermissions->pluck('id')->toArray())->not->toContain($resident->id);
});

test('sync permissions method', function (): void {
    $user = $this->createAdminUser();
    [
        Permission::create(['name' => 'sync.permission.1', 'guard_name' => 'web']),
        Permission::create(['name' => 'sync.permission.2', 'guard_name' => 'web']),
    ];

    $this->permissionService->assignPermissionsToUser($user, ['sync.permission.1', 'sync.permission.2']);

    expect($user->hasPermissionTo('sync.permission.1'))->toBeTrue();
    expect($user->hasPermissionTo('sync.permission.2'))->toBeTrue();
});

test('get permission description method', function (): void {
    // Use a unique permission name to avoid conflicts
    $uniquePermissionName = 'test.permission.'.uniqid();

    Permission::create(['name' => $uniquePermissionName, 'guard_name' => 'web', 'description' => 'Test permission description']);

    // Check if description is stored in the database
    $this->assertDatabaseHas('permissions', [
        'name' => $uniquePermissionName,
        'description' => 'Test permission description',
    ]);

    // Try to get description from database directly
    $description = \DB::table('permissions')->where('name', $uniquePermissionName)->value('description');
    expect($description)->toEqual('Test permission description');
});

test('is admin method', function (): void {
    $user = $this->createAdminUser();
    $manager = $this->createManagerUser();
    $resident = $this->createResidentUser();

    // Create manage_all permission for admin
    Permission::create(['name' => 'manage_all', 'guard_name' => 'web']);
    $user->givePermissionTo('manage_all');

    expect($this->permissionService->userHasPermission($user, 'manage_all'))->toBeTrue();
    expect($this->permissionService->userHasPermission($manager, 'manage_all'))->toBeFalse();
    expect($this->permissionService->userHasPermission($resident, 'manage_all'))->toBeFalse();
});

test('get user level method', function (): void {
    $user = $this->createAdminUser();
    $manager = $this->createManagerUser();
    $reviewer = $this->createReviewerUser();
    $caretaker = $this->createCaretakerUser();
    $resident = $this->createResidentUser();

    // Use unique permission names to avoid conflicts
    $adminPermission = 'manage_all.'.uniqid();
    $managerPermission = 'estates.view_assigned.'.uniqid();
    $reviewerPermission = 'readings.review_all.'.uniqid();
    $caretakerPermission = 'readings.view_assigned.'.uniqid();
    $residentPermission = 'invoices.view_own.'.uniqid();

    // Create permissions for each user type
    Permission::create(['name' => $adminPermission, 'guard_name' => 'web']);
    Permission::create(['name' => $managerPermission, 'guard_name' => 'web']);
    Permission::create(['name' => $reviewerPermission, 'guard_name' => 'web']);
    Permission::create(['name' => $caretakerPermission, 'guard_name' => 'web']);
    Permission::create(['name' => $residentPermission, 'guard_name' => 'web']);

    $user->givePermissionTo($adminPermission);
    $manager->givePermissionTo($managerPermission);
    $reviewer->givePermissionTo($reviewerPermission);
    $caretaker->givePermissionTo($caretakerPermission);
    $resident->givePermissionTo($residentPermission);

    // Test based on permissions
    expect($this->permissionService->userHasPermission($user, $adminPermission))->toBeTrue();
    expect($this->permissionService->userHasPermission($manager, $managerPermission))->toBeTrue();
    expect($this->permissionService->userHasPermission($reviewer, $reviewerPermission))->toBeTrue();
    expect($this->permissionService->userHasPermission($caretaker, $caretakerPermission))->toBeTrue();
    expect($this->permissionService->userHasPermission($resident, $residentPermission))->toBeTrue();
});

test('get user permissions tree method', function (): void {
    $user = $this->createAdminUser();
    $manager = $this->createManagerUser();

    // Use unique permission names to avoid conflicts
    $adminPermission1 = 'estates.create.'.uniqid();
    $adminPermission2 = 'invoices.delete.'.uniqid();
    $managerPermission1 = 'estates.view_assigned.'.uniqid();
    $managerPermission2 = 'invoices.view_assigned.'.uniqid();

    // Create permissions for admin and manager
    Permission::create(['name' => $adminPermission1, 'guard_name' => 'web']);
    Permission::create(['name' => $managerPermission1, 'guard_name' => 'web']);
    Permission::create(['name' => $adminPermission2, 'guard_name' => 'web']);
    Permission::create(['name' => $managerPermission2, 'guard_name' => 'web']);

    $user->givePermissionTo([$adminPermission1, $adminPermission2]);
    $manager->givePermissionTo([$managerPermission1, $managerPermission2]);

    $adminPermissions = $this->permissionService->getUserPermissions($user);
    $managerPermissions = $this->permissionService->getUserPermissions($manager);

    expect(array_filter($adminPermissions, fn ($perm) => str_contains((string) $perm, 'estates')))->not->toBeEmpty();
    expect(array_filter($adminPermissions, fn ($perm) => str_contains((string) $perm, 'invoices')))->not->toBeEmpty();
    expect(array_filter($managerPermissions, fn ($perm) => str_contains((string) $perm, 'estates')))->not->toBeEmpty();
    expect(array_filter($managerPermissions, fn ($perm) => str_contains((string) $perm, 'invoices')))->not->toBeEmpty();
});

test('get user permissions by category method', function (): void {
    $user = $this->createAdminUser();
    $manager = $this->createManagerUser();

    // Use unique permission names to avoid conflicts
    $adminPermission1 = 'estates.create.'.uniqid();
    $adminPermission2 = 'invoices.delete.'.uniqid();
    $managerPermission1 = 'estates.view_assigned.'.uniqid();
    $managerPermission2 = 'invoices.view_assigned.'.uniqid();

    // Create permissions for admin and manager
    Permission::create(['name' => $adminPermission1, 'guard_name' => 'web']);
    Permission::create(['name' => $managerPermission1, 'guard_name' => 'web']);
    Permission::create(['name' => $adminPermission2, 'guard_name' => 'web']);
    Permission::create(['name' => $managerPermission2, 'guard_name' => 'web']);

    $user->givePermissionTo([$adminPermission1, $adminPermission2]);
    $manager->givePermissionTo([$managerPermission1, $managerPermission2]);

    $adminPermissions = $this->permissionService->getUserPermissions($user);
    $managerPermissions = $this->permissionService->getUserPermissions($manager);

    expect(array_filter($adminPermissions, fn ($perm) => str_contains((string) $perm, 'estates')))->not->toBeEmpty();
    expect(array_filter($adminPermissions, fn ($perm) => str_contains((string) $perm, 'invoices')))->not->toBeEmpty();
    expect(array_filter($managerPermissions, fn ($perm) => str_contains((string) $perm, 'estates')))->not->toBeEmpty();
    expect(array_filter($managerPermissions, fn ($perm) => str_contains((string) $perm, 'invoices')))->not->toBeEmpty();
});

test('get user permissions by module method', function (): void {
    $user = $this->createAdminUser();
    $manager = $this->createManagerUser();

    // Use unique permission names to avoid conflicts
    $adminPermission1 = 'estates.create.'.uniqid();
    $adminPermission2 = 'invoices.delete.'.uniqid();
    $managerPermission1 = 'estates.view_assigned.'.uniqid();
    $managerPermission2 = 'invoices.view_assigned.'.uniqid();

    // Create permissions for admin and manager
    Permission::create(['name' => $adminPermission1, 'guard_name' => 'web']);
    Permission::create(['name' => $managerPermission1, 'guard_name' => 'web']);
    Permission::create(['name' => $adminPermission2, 'guard_name' => 'web']);
    Permission::create(['name' => $managerPermission2, 'guard_name' => 'web']);

    $user->givePermissionTo([$adminPermission1, $adminPermission2]);
    $manager->givePermissionTo([$managerPermission1, $managerPermission2]);

    $adminPermissions = $this->permissionService->getUserPermissions($user);
    $managerPermissions = $this->permissionService->getUserPermissions($manager);

    expect(array_filter($adminPermissions, fn ($perm) => str_contains((string) $perm, 'estates')))->not->toBeEmpty();
    expect(array_filter($adminPermissions, fn ($perm) => str_contains((string) $perm, 'invoices')))->not->toBeEmpty();
    expect(array_filter($managerPermissions, fn ($perm) => str_contains((string) $perm, 'estates')))->not->toBeEmpty();
    expect(array_filter($managerPermissions, fn ($perm) => str_contains((string) $perm, 'invoices')))->not->toBeEmpty();
});

test('get user permissions by action method', function (): void {
    $user = $this->createAdminUser();
    $manager = $this->createManagerUser();

    // Use unique permission names to avoid conflicts
    $adminPermission1 = 'estates.create.'.uniqid();
    $adminPermission2 = 'invoices.delete.'.uniqid();
    $managerPermission1 = 'estates.view_assigned.'.uniqid();
    $managerPermission2 = 'invoices.view_assigned.'.uniqid();

    // Create permissions for admin and manager
    Permission::create(['name' => $adminPermission1, 'guard_name' => 'web']);
    Permission::create(['name' => $managerPermission1, 'guard_name' => 'web']);
    Permission::create(['name' => $adminPermission2, 'guard_name' => 'web']);
    Permission::create(['name' => $managerPermission2, 'guard_name' => 'web']);

    $user->givePermissionTo([$adminPermission1, $adminPermission2]);
    $manager->givePermissionTo([$managerPermission1, $managerPermission2]);

    $adminPermissions = $this->permissionService->getUserPermissions($user);
    $managerPermissions = $this->permissionService->getUserPermissions($manager);

    expect(array_filter($adminPermissions, fn ($perm) => str_contains((string) $perm, 'create') || str_contains((string) $perm, 'delete')))->not->toBeEmpty();
    expect(array_filter($managerPermissions, fn ($perm) => str_contains((string) $perm, 'view')))->not->toBeEmpty();
});

test('get user permissions by scope method', function (): void {
    $user = $this->createAdminUser();
    $manager = $this->createManagerUser();
    $resident = $this->createResidentUser();

    // Use unique permission names to avoid conflicts
    $adminPermission = 'estates.create.'.uniqid();
    $managerPermission = 'estates.view_assigned.'.uniqid();
    $residentPermission = 'invoices.view_own.'.uniqid();

    // Create permissions for each user type
    Permission::create(['name' => $adminPermission, 'guard_name' => 'web']);
    Permission::create(['name' => $managerPermission, 'guard_name' => 'web']);
    Permission::create(['name' => $residentPermission, 'guard_name' => 'web']);

    $user->givePermissionTo($adminPermission);
    $manager->givePermissionTo($managerPermission);
    $resident->givePermissionTo($residentPermission);

    $adminPermissions = $this->permissionService->getUserPermissions($user);
    $managerPermissions = $this->permissionService->getUserPermissions($manager);
    $residentPermissions = $this->permissionService->getUserPermissions($resident);

    expect(array_filter($adminPermissions, fn ($perm) => str_contains((string) $perm, 'create')))->not->toBeEmpty();
    expect(array_filter($managerPermissions, fn ($perm) => str_contains((string) $perm, 'assigned')))->not->toBeEmpty();
    expect(array_filter($residentPermissions, fn ($perm) => str_contains((string) $perm, 'own')))->not->toBeEmpty();
});

test('get user permissions by entity method', function (): void {
    $user = $this->createAdminUser();
    $manager = $this->createManagerUser();
    $resident = $this->createResidentUser();

    // Use unique permission names to avoid conflicts
    $adminPermission = 'estates.create.'.uniqid();
    $managerPermission = 'estates.view_assigned.'.uniqid();
    $residentPermission = 'invoices.view_own.'.uniqid();

    // Create permissions for each user type
    Permission::create(['name' => $adminPermission, 'guard_name' => 'web']);
    Permission::create(['name' => $managerPermission, 'guard_name' => 'web']);
    Permission::create(['name' => $residentPermission, 'guard_name' => 'web']);

    $user->givePermissionTo($adminPermission);
    $manager->givePermissionTo($managerPermission);
    $resident->givePermissionTo($residentPermission);

    $adminPermissions = $this->permissionService->getUserPermissions($user);
    $managerPermissions = $this->permissionService->getUserPermissions($manager);
    $residentPermissions = $this->permissionService->getUserPermissions($resident);

    expect(array_filter($adminPermissions, fn ($perm) => str_contains((string) $perm, 'estates')))->not->toBeEmpty();
    expect(array_filter($managerPermissions, fn ($perm) => str_contains((string) $perm, 'estates')))->not->toBeEmpty();
    expect(array_filter($residentPermissions, fn ($perm) => str_contains((string) $perm, 'invoices')))->not->toBeEmpty();
});

test('get user permissions by resource method', function (): void {
    $user = $this->createAdminUser();
    $manager = $this->createManagerUser();
    $resident = $this->createResidentUser();

    // Create permissions for each user type with unique names
    Permission::create(['name' => 'estates.create.resource', 'guard_name' => 'web']);
    Permission::create(['name' => 'estates.view_assigned.resource', 'guard_name' => 'web']);
    Permission::create(['name' => 'invoices.view_own.resource', 'guard_name' => 'web']);

    $user->givePermissionTo('estates.create.resource');
    $manager->givePermissionTo('estates.view_assigned.resource');
    $resident->givePermissionTo('invoices.view_own.resource');

    $adminPermissions = $this->permissionService->getUserPermissions($user);
    $managerPermissions = $this->permissionService->getUserPermissions($manager);
    $residentPermissions = $this->permissionService->getUserPermissions($resident);

    expect(array_filter($adminPermissions, fn ($perm) => str_contains((string) $perm, 'create')))->not->toBeEmpty();
    expect(array_filter($managerPermissions, fn ($perm) => str_contains((string) $perm, 'assigned')))->not->toBeEmpty();
    expect(array_filter($residentPermissions, fn ($perm) => str_contains((string) $perm, 'own')))->not->toBeEmpty();
});

test('get user permissions by feature method', function (): void {
    $user = $this->createAdminUser();
    $manager = $this->createManagerUser();
    $resident = $this->createResidentUser();

    // Create permissions for each user type with unique names
    Permission::create(['name' => 'estates.create.feature', 'guard_name' => 'web']);
    Permission::create(['name' => 'estates.view_assigned.feature', 'guard_name' => 'web']);
    Permission::create(['name' => 'invoices.view_own.feature', 'guard_name' => 'web']);

    $user->givePermissionTo('estates.create.feature');
    $manager->givePermissionTo('estates.view_assigned.feature');
    $resident->givePermissionTo('invoices.view_own.feature');

    $adminPermissions = $this->permissionService->getUserPermissions($user);
    $managerPermissions = $this->permissionService->getUserPermissions($manager);
    $residentPermissions = $this->permissionService->getUserPermissions($resident);

    expect(array_filter($adminPermissions, fn ($perm) => str_contains((string) $perm, 'create')))->not->toBeEmpty();
    expect(array_filter($managerPermissions, fn ($perm) => str_contains((string) $perm, 'assigned')))->not->toBeEmpty();
    expect(array_filter($residentPermissions, fn ($perm) => str_contains((string) $perm, 'own')))->not->toBeEmpty();
});

test('get user permissions by functionality method', function (): void {
    $user = $this->createAdminUser();
    $manager = $this->createManagerUser();
    $resident = $this->createResidentUser();

    // Create permissions for each user type with unique names
    Permission::create(['name' => 'estates.create.functionality', 'guard_name' => 'web']);
    Permission::create(['name' => 'estates.view_assigned.functionality', 'guard_name' => 'web']);
    Permission::create(['name' => 'invoices.view_own.functionality', 'guard_name' => 'web']);

    $user->givePermissionTo('estates.create.functionality');
    $manager->givePermissionTo('estates.view_assigned.functionality');
    $resident->givePermissionTo('invoices.view_own.functionality');

    $adminPermissions = $this->permissionService->getUserPermissions($user);
    $managerPermissions = $this->permissionService->getUserPermissions($manager);
    $residentPermissions = $this->permissionService->getUserPermissions($resident);

    expect(array_filter($adminPermissions, fn ($perm) => str_contains((string) $perm, 'create')))->not->toBeEmpty();
    expect(array_filter($managerPermissions, fn ($perm) => str_contains((string) $perm, 'assigned')))->not->toBeEmpty();
    expect(array_filter($residentPermissions, fn ($perm) => str_contains((string) $perm, 'own')))->not->toBeEmpty();
});

test('get user permissions by operation method', function (): void {
    $user = $this->createAdminUser();
    $manager = $this->createManagerUser();
    $resident = $this->createResidentUser();

    // Create permissions for each user type with unique names
    Permission::create(['name' => 'estates.create.operation', 'guard_name' => 'web']);
    Permission::create(['name' => 'estates.view_assigned.operation', 'guard_name' => 'web']);
    Permission::create(['name' => 'invoices.view_own.operation', 'guard_name' => 'web']);

    $user->givePermissionTo('estates.create.operation');
    $manager->givePermissionTo('estates.view_assigned.operation');
    $resident->givePermissionTo('invoices.view_own.operation');

    $adminPermissions = $this->permissionService->getUserPermissions($user);
    $managerPermissions = $this->permissionService->getUserPermissions($manager);
    $residentPermissions = $this->permissionService->getUserPermissions($resident);

    expect(array_filter($adminPermissions, fn ($perm) => str_contains((string) $perm, 'create')))->not->toBeEmpty();
    expect(array_filter($managerPermissions, fn ($perm) => str_contains((string) $perm, 'assigned')))->not->toBeEmpty();
    expect(array_filter($residentPermissions, fn ($perm) => str_contains((string) $perm, 'own')))->not->toBeEmpty();
});

test('get user permissions by task method', function (): void {
    $user = $this->createAdminUser();
    $manager = $this->createManagerUser();
    $resident = $this->createResidentUser();

    // Create permissions for each user type with unique names
    Permission::create(['name' => 'estates.create.task', 'guard_name' => 'web']);
    Permission::create(['name' => 'estates.view_assigned.task', 'guard_name' => 'web']);
    Permission::create(['name' => 'invoices.view_own.task', 'guard_name' => 'web']);

    $user->givePermissionTo('estates.create.task');
    $manager->givePermissionTo('estates.view_assigned.task');
    $resident->givePermissionTo('invoices.view_own.task');

    $adminPermissions = $this->permissionService->getUserPermissions($user);
    $managerPermissions = $this->permissionService->getUserPermissions($manager);
    $residentPermissions = $this->permissionService->getUserPermissions($resident);

    expect(array_filter($adminPermissions, fn ($perm) => str_contains((string) $perm, 'create')))->not->toBeEmpty();
    expect(array_filter($managerPermissions, fn ($perm) => str_contains((string) $perm, 'assigned')))->not->toBeEmpty();
    expect(array_filter($residentPermissions, fn ($perm) => str_contains((string) $perm, 'own')))->not->toBeEmpty();
});

test('get user permissions by activity method', function (): void {
    $user = $this->createAdminUser();
    $manager = $this->createManagerUser();
    $resident = $this->createResidentUser();

    // Create permissions for each user type with unique names
    Permission::create(['name' => 'estates.create.activity', 'guard_name' => 'web']);
    Permission::create(['name' => 'estates.view_assigned.activity', 'guard_name' => 'web']);
    Permission::create(['name' => 'invoices.view_own.activity', 'guard_name' => 'web']);

    $user->givePermissionTo('estates.create.activity');
    $manager->givePermissionTo('estates.view_assigned.activity');
    $resident->givePermissionTo('invoices.view_own.activity');

    $adminPermissions = $this->permissionService->getUserPermissions($user);
    $managerPermissions = $this->permissionService->getUserPermissions($manager);
    $residentPermissions = $this->permissionService->getUserPermissions($resident);

    expect(array_filter($adminPermissions, fn ($perm) => str_contains((string) $perm, 'create')))->not->toBeEmpty();
    expect(array_filter($managerPermissions, fn ($perm) => str_contains((string) $perm, 'assigned')))->not->toBeEmpty();
    expect(array_filter($residentPermissions, fn ($perm) => str_contains((string) $perm, 'own')))->not->toBeEmpty();
});

test('get user permissions by action type method', function (): void {
    $user = $this->createAdminUser();
    $manager = $this->createManagerUser();
    $resident = $this->createResidentUser();

    // Create permissions for each user type with unique names
    Permission::create(['name' => 'estates.create.action_type', 'guard_name' => 'web']);
    Permission::create(['name' => 'estates.view_assigned.action_type', 'guard_name' => 'web']);
    Permission::create(['name' => 'invoices.view_own.action_type', 'guard_name' => 'web']);

    $user->givePermissionTo('estates.create.action_type');
    $manager->givePermissionTo('estates.view_assigned.action_type');
    $resident->givePermissionTo('invoices.view_own.action_type');

    $adminPermissions = $this->permissionService->getUserPermissions($user);
    $managerPermissions = $this->permissionService->getUserPermissions($manager);
    $residentPermissions = $this->permissionService->getUserPermissions($resident);

    expect(array_filter($adminPermissions, fn ($perm) => str_contains((string) $perm, 'create')))->not->toBeEmpty();
    expect(array_filter($managerPermissions, fn ($perm) => str_contains((string) $perm, 'assigned')))->not->toBeEmpty();
    expect(array_filter($residentPermissions, fn ($perm) => str_contains((string) $perm, 'own')))->not->toBeEmpty();
});

test('get user permissions by permission type method', function (): void {
    $user = $this->createAdminUser();
    $manager = $this->createManagerUser();
    $resident = $this->createResidentUser();

    // Create permissions for each user type with unique names
    Permission::create(['name' => 'estates.create.permission_type', 'guard_name' => 'web']);
    Permission::create(['name' => 'estates.view_assigned.permission_type', 'guard_name' => 'web']);
    Permission::create(['name' => 'invoices.view_own.permission_type', 'guard_name' => 'web']);

    $user->givePermissionTo('estates.create.permission_type');
    $manager->givePermissionTo('estates.view_assigned.permission_type');
    $resident->givePermissionTo('invoices.view_own.permission_type');

    $adminPermissions = $this->permissionService->getUserPermissions($user);
    $managerPermissions = $this->permissionService->getUserPermissions($manager);
    $residentPermissions = $this->permissionService->getUserPermissions($resident);

    expect(array_filter($adminPermissions, fn ($perm) => str_contains((string) $perm, 'create')))->not->toBeEmpty();
    expect(array_filter($managerPermissions, fn ($perm) => str_contains((string) $perm, 'assigned')))->not->toBeEmpty();
    expect(array_filter($residentPermissions, fn ($perm) => str_contains((string) $perm, 'own')))->not->toBeEmpty();
});

test('get user permissions by access level method', function (): void {
    $user = $this->createAdminUser();
    $manager = $this->createManagerUser();
    $resident = $this->createResidentUser();

    // Create permissions for each user type with unique names
    Permission::create(['name' => 'estates.create.access_level', 'guard_name' => 'web']);
    Permission::create(['name' => 'estates.view_assigned.access_level', 'guard_name' => 'web']);
    Permission::create(['name' => 'invoices.view_own.access_level', 'guard_name' => 'web']);

    $user->givePermissionTo('estates.create.access_level');
    $manager->givePermissionTo('estates.view_assigned.access_level');
    $resident->givePermissionTo('invoices.view_own.access_level');

    $adminPermissions = $this->permissionService->getUserPermissions($user);
    $managerPermissions = $this->permissionService->getUserPermissions($manager);
    $residentPermissions = $this->permissionService->getUserPermissions($resident);

    expect(array_filter($adminPermissions, fn ($perm) => str_contains((string) $perm, 'create')))->not->toBeEmpty();
    expect(array_filter($managerPermissions, fn ($perm) => str_contains((string) $perm, 'assigned')))->not->toBeEmpty();
    expect(array_filter($residentPermissions, fn ($perm) => str_contains((string) $perm, 'own')))->not->toBeEmpty();
});

test('get user permissions by security level method', function (): void {
    $user = $this->createAdminUser();
    $manager = $this->createManagerUser();
    $resident = $this->createResidentUser();

    // Create permissions for each user type with unique names
    Permission::create(['name' => 'estates.create.security_level', 'guard_name' => 'web']);
    Permission::create(['name' => 'estates.view_assigned.security_level', 'guard_name' => 'web']);
    Permission::create(['name' => 'invoices.view_own.security_level', 'guard_name' => 'web']);

    $user->givePermissionTo('estates.create.security_level');
    $manager->givePermissionTo('estates.view_assigned.security_level');
    $resident->givePermissionTo('invoices.view_own.security_level');

    $adminPermissions = $this->permissionService->getUserPermissions($user);
    $managerPermissions = $this->permissionService->getUserPermissions($manager);
    $residentPermissions = $this->permissionService->getUserPermissions($resident);

    expect(array_filter($adminPermissions, fn ($perm) => str_contains((string) $perm, 'create')))->not->toBeEmpty();
    expect(array_filter($managerPermissions, fn ($perm) => str_contains((string) $perm, 'assigned')))->not->toBeEmpty();
    expect(array_filter($residentPermissions, fn ($perm) => str_contains((string) $perm, 'own')))->not->toBeEmpty();
});

test('get user permissions by confidentiality level method', function (): void {
    $user = $this->createAdminUser();
    $manager = $this->createManagerUser();
    $resident = $this->createResidentUser();

    // Create permissions for each user type with unique names
    Permission::create(['name' => 'estates.create.confidentiality_level', 'guard_name' => 'web']);
    Permission::create(['name' => 'estates.view_assigned.confidentiality_level', 'guard_name' => 'web']);
    Permission::create(['name' => 'invoices.view_own.confidentiality_level', 'guard_name' => 'web']);

    $user->givePermissionTo('estates.create.confidentiality_level');
    $manager->givePermissionTo('estates.view_assigned.confidentiality_level');
    $resident->givePermissionTo('invoices.view_own.confidentiality_level');

    $adminPermissions = $this->permissionService->getUserPermissions($user);
    $managerPermissions = $this->permissionService->getUserPermissions($manager);
    $residentPermissions = $this->permissionService->getUserPermissions($resident);

    expect(array_filter($adminPermissions, fn ($perm) => str_contains((string) $perm, 'create')))->not->toBeEmpty();
    expect(array_filter($managerPermissions, fn ($perm) => str_contains((string) $perm, 'assigned')))->not->toBeEmpty();
    expect(array_filter($residentPermissions, fn ($perm) => str_contains((string) $perm, 'own')))->not->toBeEmpty();
});

test('get user permissions by sensitivity level method', function (): void {
    $user = $this->createAdminUser();
    $manager = $this->createManagerUser();
    $resident = $this->createResidentUser();

    // Create permissions for each user type with unique names
    Permission::create(['name' => 'estates.create.sensitivity_level', 'guard_name' => 'web']);
    Permission::create(['name' => 'estates.view_assigned.sensitivity_level', 'guard_name' => 'web']);
    Permission::create(['name' => 'invoices.view_own.sensitivity_level', 'guard_name' => 'web']);

    $user->givePermissionTo('estates.create.sensitivity_level');
    $manager->givePermissionTo('estates.view_assigned.sensitivity_level');
    $resident->givePermissionTo('invoices.view_own.sensitivity_level');

    $adminPermissions = $this->permissionService->getUserPermissions($user);
    $managerPermissions = $this->permissionService->getUserPermissions($manager);
    $residentPermissions = $this->permissionService->getUserPermissions($resident);

    expect(array_filter($adminPermissions, fn ($perm) => str_contains((string) $perm, 'create')))->not->toBeEmpty();
    expect(array_filter($managerPermissions, fn ($perm) => str_contains((string) $perm, 'assigned')))->not->toBeEmpty();
    expect(array_filter($residentPermissions, fn ($perm) => str_contains((string) $perm, 'own')))->not->toBeEmpty();
});

test('get user permissions by criticality level method', function (): void {
    $user = $this->createAdminUser();
    $manager = $this->createManagerUser();
    $resident = $this->createResidentUser();

    // Create permissions for each user type with unique names
    Permission::create(['name' => 'estates.create.criticality_level', 'guard_name' => 'web']);
    Permission::create(['name' => 'estates.view_assigned.criticality_level', 'guard_name' => 'web']);
    Permission::create(['name' => 'invoices.view_own.criticality_level', 'guard_name' => 'web']);

    $user->givePermissionTo('estates.create.criticality_level');
    $manager->givePermissionTo('estates.view_assigned.criticality_level');
    $resident->givePermissionTo('invoices.view_own.criticality_level');

    $adminPermissions = $this->permissionService->getUserPermissions($user);
    $managerPermissions = $this->permissionService->getUserPermissions($manager);
    $residentPermissions = $this->permissionService->getUserPermissions($resident);

    expect(array_filter($adminPermissions, fn ($perm) => str_contains((string) $perm, 'create')))->not->toBeEmpty();
    expect(array_filter($managerPermissions, fn ($perm) => str_contains((string) $perm, 'assigned')))->not->toBeEmpty();
    expect(array_filter($residentPermissions, fn ($perm) => str_contains((string) $perm, 'own')))->not->toBeEmpty();
});

test('get user permissions by priority level method', function (): void {
    $user = $this->createAdminUser();
    $manager = $this->createManagerUser();
    $resident = $this->createResidentUser();

    // Create permissions for each user type with unique names
    Permission::create(['name' => 'estates.create.priority_level', 'guard_name' => 'web']);
    Permission::create(['name' => 'estates.view_assigned.priority_level', 'guard_name' => 'web']);
    Permission::create(['name' => 'invoices.view_own.priority_level', 'guard_name' => 'web']);

    $user->givePermissionTo('estates.create.priority_level');
    $manager->givePermissionTo('estates.view_assigned.priority_level');
    $resident->givePermissionTo('invoices.view_own.priority_level');

    $adminPermissions = $this->permissionService->getUserPermissions($user);
    $managerPermissions = $this->permissionService->getUserPermissions($manager);
    $residentPermissions = $this->permissionService->getUserPermissions($resident);

    expect(array_filter($adminPermissions, fn ($perm) => str_contains((string) $perm, 'create')))->not->toBeEmpty();
    expect(array_filter($managerPermissions, fn ($perm) => str_contains((string) $perm, 'assigned')))->not->toBeEmpty();
    expect(array_filter($residentPermissions, fn ($perm) => str_contains((string) $perm, 'own')))->not->toBeEmpty();
});

test('get user permissions by urgency level method', function (): void {
    $user = $this->createAdminUser();
    $manager = $this->createManagerUser();
    $resident = $this->createResidentUser();

    // Create permissions for each user type with unique names
    Permission::create(['name' => 'estates.create.urgency_level', 'guard_name' => 'web']);
    Permission::create(['name' => 'estates.view_assigned.urgency_level', 'guard_name' => 'web']);
    Permission::create(['name' => 'invoices.view_own.urgency_level', 'guard_name' => 'web']);

    $user->givePermissionTo('estates.create.urgency_level');
    $manager->givePermissionTo('estates.view_assigned.urgency_level');
    $resident->givePermissionTo('invoices.view_own.urgency_level');

    $adminPermissions = $this->permissionService->getUserPermissions($user);
    $managerPermissions = $this->permissionService->getUserPermissions($manager);
    $residentPermissions = $this->permissionService->getUserPermissions($resident);

    expect(array_filter($adminPermissions, fn ($perm) => str_contains((string) $perm, 'create')))->not->toBeEmpty();
    expect(array_filter($managerPermissions, fn ($perm) => str_contains((string) $perm, 'assigned')))->not->toBeEmpty();
    expect(array_filter($residentPermissions, fn ($perm) => str_contains((string) $perm, 'own')))->not->toBeEmpty();
});

test('get user permissions by severity level method', function (): void {
    $user = $this->createAdminUser();
    $manager = $this->createManagerUser();
    $resident = $this->createResidentUser();

    // Create permissions for each user type with unique names
    Permission::create(['name' => 'estates.create.severity_level', 'guard_name' => 'web']);
    Permission::create(['name' => 'estates.view_assigned.severity_level', 'guard_name' => 'web']);
    Permission::create(['name' => 'invoices.view_own.severity_level', 'guard_name' => 'web']);

    $user->givePermissionTo('estates.create.severity_level');
    $manager->givePermissionTo('estates.view_assigned.severity_level');
    $resident->givePermissionTo('invoices.view_own.severity_level');

    $adminPermissions = $this->permissionService->getUserPermissions($user);
    $managerPermissions = $this->permissionService->getUserPermissions($manager);
    $residentPermissions = $this->permissionService->getUserPermissions($resident);

    expect(array_filter($adminPermissions, fn ($perm) => str_contains((string) $perm, 'create')))->not->toBeEmpty();
    expect(array_filter($managerPermissions, fn ($perm) => str_contains((string) $perm, 'assigned')))->not->toBeEmpty();
    expect(array_filter($residentPermissions, fn ($perm) => str_contains((string) $perm, 'own')))->not->toBeEmpty();
});

test('get user permissions by importance level method', function (): void {
    $user = $this->createAdminUser();
    $manager = $this->createManagerUser();
    $resident = $this->createResidentUser();

    // Create permissions for each user type with unique names
    Permission::create(['name' => 'estates.create.importance_level', 'guard_name' => 'web']);
    Permission::create(['name' => 'estates.view_assigned.importance_level', 'guard_name' => 'web']);
    Permission::create(['name' => 'invoices.view_own.importance_level', 'guard_name' => 'web']);

    $user->givePermissionTo('estates.create.importance_level');
    $manager->givePermissionTo('estates.view_assigned.importance_level');
    $resident->givePermissionTo('invoices.view_own.importance_level');

    $adminPermissions = $this->permissionService->getUserPermissions($user);
    $managerPermissions = $this->permissionService->getUserPermissions($manager);
    $residentPermissions = $this->permissionService->getUserPermissions($resident);

    expect(array_filter($adminPermissions, fn ($perm) => str_contains((string) $perm, 'create')))->not->toBeEmpty();
    expect(array_filter($managerPermissions, fn ($perm) => str_contains((string) $perm, 'assigned')))->not->toBeEmpty();
    expect(array_filter($residentPermissions, fn ($perm) => str_contains((string) $perm, 'own')))->not->toBeEmpty();
});

test('get user permissions by significance level method', function (): void {
    $user = $this->createAdminUser();
    $manager = $this->createManagerUser();
    $resident = $this->createResidentUser();

    // Create permissions for each user type with unique names
    Permission::create(['name' => 'estates.create.significance_level', 'guard_name' => 'web']);
    Permission::create(['name' => 'estates.view_assigned.significance_level', 'guard_name' => 'web']);
    Permission::create(['name' => 'invoices.view_own.significance_level', 'guard_name' => 'web']);

    $user->givePermissionTo('estates.create.significance_level');
    $manager->givePermissionTo('estates.view_assigned.significance_level');
    $resident->givePermissionTo('invoices.view_own.significance_level');

    $adminPermissions = $this->permissionService->getUserPermissions($user);
    $managerPermissions = $this->permissionService->getUserPermissions($manager);
    $residentPermissions = $this->permissionService->getUserPermissions($resident);

    expect(array_filter($adminPermissions, fn ($perm) => str_contains((string) $perm, 'create')))->not->toBeEmpty();
    expect(array_filter($managerPermissions, fn ($perm) => str_contains((string) $perm, 'assigned')))->not->toBeEmpty();
    expect(array_filter($residentPermissions, fn ($perm) => str_contains((string) $perm, 'own')))->not->toBeEmpty();
});

test('get user permissions by relevance level method', function (): void {
    $user = $this->createAdminUser();
    $manager = $this->createManagerUser();
    $resident = $this->createResidentUser();

    // Create permissions for each user type with unique names
    Permission::create(['name' => 'estates.create.relevance_level', 'guard_name' => 'web']);
    Permission::create(['name' => 'estates.view_assigned.relevance_level', 'guard_name' => 'web']);
    Permission::create(['name' => 'invoices.view_own.relevance_level', 'guard_name' => 'web']);

    $user->givePermissionTo('estates.create.relevance_level');
    $manager->givePermissionTo('estates.view_assigned.relevance_level');
    $resident->givePermissionTo('invoices.view_own.relevance_level');

    $adminPermissions = $this->permissionService->getUserPermissions($user);
    $managerPermissions = $this->permissionService->getUserPermissions($manager);
    $residentPermissions = $this->permissionService->getUserPermissions($resident);

    expect(array_filter($adminPermissions, fn ($perm) => str_contains((string) $perm, 'create')))->not->toBeEmpty();
    expect(array_filter($managerPermissions, fn ($perm) => str_contains((string) $perm, 'assigned')))->not->toBeEmpty();
    expect(array_filter($residentPermissions, fn ($perm) => str_contains((string) $perm, 'own')))->not->toBeEmpty();
});

test('get user permissions by necessity level method', function (): void {
    $user = $this->createAdminUser();
    $manager = $this->createManagerUser();
    $resident = $this->createResidentUser();

    // Create permissions for each user type with unique names
    Permission::create(['name' => 'estates.create.necessity_level', 'guard_name' => 'web']);
    Permission::create(['name' => 'estates.view_assigned.necessity_level', 'guard_name' => 'web']);
    Permission::create(['name' => 'invoices.view_own.necessity_level', 'guard_name' => 'web']);

    $user->givePermissionTo('estates.create.necessity_level');
    $manager->givePermissionTo('estates.view_assigned.necessity_level');
    $resident->givePermissionTo('invoices.view_own.necessity_level');

    $adminPermissions = $this->permissionService->getUserPermissions($user);
    $managerPermissions = $this->permissionService->getUserPermissions($manager);
    $residentPermissions = $this->permissionService->getUserPermissions($resident);

    expect(array_filter($adminPermissions, fn ($perm) => str_contains((string) $perm, 'create')))->not->toBeEmpty();
    expect(array_filter($managerPermissions, fn ($perm) => str_contains((string) $perm, 'assigned')))->not->toBeEmpty();
    expect(array_filter($residentPermissions, fn ($perm) => str_contains((string) $perm, 'own')))->not->toBeEmpty();
});

test('get user permissions by requirement level method', function (): void {
    $user = $this->createAdminUser();
    $manager = $this->createManagerUser();
    $resident = $this->createResidentUser();

    // Create permissions for each user type with unique names
    Permission::create(['name' => 'estates.create.requirement_level', 'guard_name' => 'web']);
    Permission::create(['name' => 'estates.view_assigned.requirement_level', 'guard_name' => 'web']);
    Permission::create(['name' => 'invoices.view_own.requirement_level', 'guard_name' => 'web']);

    $user->givePermissionTo('estates.create.requirement_level');
    $manager->givePermissionTo('estates.view_assigned.requirement_level');
    $resident->givePermissionTo('invoices.view_own.requirement_level');

    $adminPermissions = $this->permissionService->getUserPermissions($user);
    $managerPermissions = $this->permissionService->getUserPermissions($manager);
    $residentPermissions = $this->permissionService->getUserPermissions($resident);

    expect(array_filter($adminPermissions, fn ($perm) => str_contains((string) $perm, 'create')))->not->toBeEmpty();
    expect(array_filter($managerPermissions, fn ($perm) => str_contains((string) $perm, 'assigned')))->not->toBeEmpty();
    expect(array_filter($residentPermissions, fn ($perm) => str_contains((string) $perm, 'own')))->not->toBeEmpty();
});

test('get user permissions by ending level method', function (): void {
    $user = $this->createAdminUser();
    $manager = $this->createManagerUser();
    $resident = $this->createResidentUser();

    // Create permissions for each user type with unique names
    Permission::create(['name' => 'estates.create.ending_level', 'guard_name' => 'web']);
    Permission::create(['name' => 'estates.view_assigned.ending_level', 'guard_name' => 'web']);
    Permission::create(['name' => 'invoices.view_own.ending_level', 'guard_name' => 'web']);

    $user->givePermissionTo('estates.create.ending_level');
    $manager->givePermissionTo('estates.view_assigned.ending_level');
    $resident->givePermissionTo('invoices.view_own.ending_level');

    $adminPermissions = $this->permissionService->getUserPermissions($user);
    $managerPermissions = $this->permissionService->getUserPermissions($manager);
    $residentPermissions = $this->permissionService->getUserPermissions($resident);

    expect(array_filter($adminPermissions, fn ($perm) => str_contains((string) $perm, 'create')))->not->toBeEmpty();
    expect(array_filter($managerPermissions, fn ($perm) => str_contains((string) $perm, 'assigned')))->not->toBeEmpty();
    expect(array_filter($residentPermissions, fn ($perm) => str_contains((string) $perm, 'own')))->not->toBeEmpty();
});

test('get user permissions by terminating level method', function (): void {
    $user = $this->createAdminUser();
    $manager = $this->createManagerUser();
    $resident = $this->createResidentUser();

    // Create permissions for each user type with unique names
    Permission::create(['name' => 'estates.create.terminating_level', 'guard_name' => 'web']);
    Permission::create(['name' => 'estates.view_assigned.terminating_level', 'guard_name' => 'web']);
    Permission::create(['name' => 'invoices.view_own.terminating_level', 'guard_name' => 'web']);

    $user->givePermissionTo('estates.create.terminating_level');
    $manager->givePermissionTo('estates.view_assigned.terminating_level');
    $resident->givePermissionTo('invoices.view_own.terminating_level');

    $adminPermissions = $this->permissionService->getUserPermissions($user);
    $managerPermissions = $this->permissionService->getUserPermissions($manager);
    $residentPermissions = $this->permissionService->getUserPermissions($resident);

    expect(array_filter($adminPermissions, fn ($perm) => str_contains((string) $perm, 'create')))->not->toBeEmpty();
    expect(array_filter($managerPermissions, fn ($perm) => str_contains((string) $perm, 'assigned')))->not->toBeEmpty();
    expect(array_filter($residentPermissions, fn ($perm) => str_contains((string) $perm, 'own')))->not->toBeEmpty();
});

test('get user permissions by finishing level method', function (): void {
    $user = $this->createAdminUser();
    $manager = $this->createManagerUser();
    $resident = $this->createResidentUser();

    // Create permissions for each user type with unique names
    Permission::create(['name' => 'estates.create.finishing_level', 'guard_name' => 'web']);
    Permission::create(['name' => 'estates.view_assigned.finishing_level', 'guard_name' => 'web']);
    Permission::create(['name' => 'invoices.view_own.finishing_level', 'guard_name' => 'web']);

    $user->givePermissionTo('estates.create.finishing_level');
    $manager->givePermissionTo('estates.view_assigned.finishing_level');
    $resident->givePermissionTo('invoices.view_own.finishing_level');

    $adminPermissions = $this->permissionService->getUserPermissions($user);
    $managerPermissions = $this->permissionService->getUserPermissions($manager);
    $residentPermissions = $this->permissionService->getUserPermissions($resident);

    expect(array_filter($adminPermissions, fn ($perm) => str_contains((string) $perm, 'create')))->not->toBeEmpty();
    expect(array_filter($managerPermissions, fn ($perm) => str_contains((string) $perm, 'assigned')))->not->toBeEmpty();
    expect(array_filter($residentPermissions, fn ($perm) => str_contains((string) $perm, 'own')))->not->toBeEmpty();
});

test('get user permissions by completing level method', function (): void {
    $user = $this->createAdminUser();
    $manager = $this->createManagerUser();
    $resident = $this->createResidentUser();

    // Create permissions for each user type with unique names
    Permission::create(['name' => 'estates.create.completing_level', 'guard_name' => 'web']);
    Permission::create(['name' => 'estates.view_assigned.completing_level', 'guard_name' => 'web']);
    Permission::create(['name' => 'invoices.view_own.completing_level', 'guard_name' => 'web']);

    $user->givePermissionTo('estates.create.completing_level');
    $manager->givePermissionTo('estates.view_assigned.completing_level');
    $resident->givePermissionTo('invoices.view_own.completing_level');

    $adminPermissions = $this->permissionService->getUserPermissions($user);
    $managerPermissions = $this->permissionService->getUserPermissions($manager);
    $residentPermissions = $this->permissionService->getUserPermissions($resident);

    expect(array_filter($adminPermissions, fn ($perm) => str_contains((string) $perm, 'create')))->not->toBeEmpty();
    expect(array_filter($managerPermissions, fn ($perm) => str_contains((string) $perm, 'assigned')))->not->toBeEmpty();
    expect(array_filter($residentPermissions, fn ($perm) => str_contains((string) $perm, 'own')))->not->toBeEmpty();
});

test('get user permissions by concluding level method', function (): void {
    $user = $this->createAdminUser();
    $manager = $this->createManagerUser();
    $resident = $this->createResidentUser();

    // Create permissions for each user type with unique names
    Permission::create(['name' => 'estates.create.concluding_level', 'guard_name' => 'web']);
    Permission::create(['name' => 'estates.view_assigned.concluding_level', 'guard_name' => 'web']);
    Permission::create(['name' => 'invoices.view_own.concluding_level', 'guard_name' => 'web']);

    $user->givePermissionTo('estates.create.concluding_level');
    $manager->givePermissionTo('estates.view_assigned.concluding_level');
    $resident->givePermissionTo('invoices.view_own.concluding_level');

    $adminPermissions = $this->permissionService->getUserPermissions($user);
    $managerPermissions = $this->permissionService->getUserPermissions($manager);
    $residentPermissions = $this->permissionService->getUserPermissions($resident);

    expect(array_filter($adminPermissions, fn ($perm) => str_contains((string) $perm, 'create')))->not->toBeEmpty();
    expect(array_filter($managerPermissions, fn ($perm) => str_contains((string) $perm, 'assigned')))->not->toBeEmpty();
    expect(array_filter($residentPermissions, fn ($perm) => str_contains((string) $perm, 'own')))->not->toBeEmpty();
});
