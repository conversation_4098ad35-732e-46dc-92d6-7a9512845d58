<?php

namespace Tests;

use Database\Seeders\PermissionSeeder;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\TestCase as BaseTestCase;

// Add other seeder imports if needed, e.g.:
// use Database\Seeders\CoreSeeder;
// use Database\Seeders\TestDataSeeder;

abstract class TestCase extends BaseTestCase
{
    use RefreshDatabase;

    /**
     * Indicates that the database should be seeded.
     *
     * @var bool
     */
    protected $seed = true;

    /**
     * The specific seeder class that should be used.
     *
     * @var string
     */
    protected $seeder = PermissionSeeder::class;

    /**
     * Define environment setup.
     *
     * @param  \Illuminate\Foundation\Application  $app
     * @return void
     */
    protected function defineEnvironment($app)
    {
        // Configure your environment settings if needed
    }

    /**
     * Define database migrations.
     *
     * @return void
     */
    protected function defineDatabaseMigrations()
    {
        $this->artisan('migrate:fresh')->run();
    }
}
