<?php

namespace Tests\Traits;

use Livewire\Livewire;

trait LivewireTestHelpers
{
    /**
     * Test a Livewire component with authentication.
     */
    protected function testLivewireComponentWithAuth(string $componentClass, callable $testCallback): void
    {
        $user = $this->actAsAdmin(); // Default to admin, can be overridden in callback
        $testCallback(Livewire::actingAs($user)->test($componentClass));
    }

    /**
     * Test a Livewire component with a specific role.
     */
    protected function testLivewireComponentWithRole(string $componentClass, string $role, callable $testCallback): void
    {
        $user = $this->actAsUserWithRole($role);
        $testCallback(Livewire::actingAs($user)->test($componentClass));
    }

    /**
     * Test that a Livewire component is forbidden for a specific role.
     */
    protected function assertLivewireForbiddenForRole(string $componentClass, string $role): void
    {
        $user = $this->actAsUserWithRole($role);
        Livewire::actingAs($user)->test($componentClass)->assertForbidden();
    }

    /**
     * Test that a Livewire component is accessible for a specific role.
     */
    protected function assertLivewireAccessibleForRole(string $componentClass, string $role): void
    {
        $user = $this->actAsUserWithRole($role);
        Livewire::actingAs($user)->test($componentClass)->assertStatus(200);
    }

    /**
     * Test Livewire component authentication for multiple roles.
     */
    protected function testLivewireAuthentication(string $componentClass, array $allowedRoles, array $forbiddenRoles = []): void
    {
        // Test forbidden roles
        foreach ($forbiddenRoles as $forbiddenRole) {
            $this->assertLivewireForbiddenForRole($componentClass, $forbiddenRole);
        }

        // Test allowed roles
        foreach ($allowedRoles as $allowedRole) {
            $this->assertLivewireAccessibleForRole($componentClass, $allowedRole);
        }
    }

    /**
     * Test standard CRUD operations for a Livewire component.
     */
    protected function testLivewireCrudOperations(string $componentClass, string $modelClass, array $createData, array $updateData): void
    {
        $this->testLivewireComponentWithAuth($componentClass, function ($livewire) use ($createData, $updateData, $modelClass): void {
            // Test create
            $livewire->call('openCreateModal')
                ->set('form', $createData)
                ->call('save')
                ->assertHasNoErrors()
                ->assertDispatched('item-saved');

            $this->assertDatabaseHas((new $modelClass)->getTable(), $createData);

            // Test edit
            $model = $modelClass::first();
            $livewire->call('openEditModal', $model->id)
                ->set('form', $updateData)
                ->call('save')
                ->assertHasNoErrors()
                ->assertDispatched('item-saved');

            $this->assertDatabaseHas((new $modelClass)->getTable(), $updateData);

            // Test delete
            $livewire->call('confirmDelete', $model->id)
                ->call('delete')
                ->assertHasNoErrors()
                ->assertDispatched('item-deleted');

            $this->assertDatabaseMissing((new $modelClass)->getTable(), ['id' => $model->id]);
        });
    }

    /**
     * Test Livewire component search functionality.
     */
    protected function testLivewireSearch(string $componentClass, string $modelClass, array $searchableItems, string $searchProperty = 'search'): void
    {
        $this->testLivewireComponentWithAuth($componentClass, function ($livewire) use ($searchableItems, $searchProperty, $modelClass): void {
            // Create test data
            foreach ($searchableItems as $item) {
                $modelClass::create($item);
            }

            // Test search for each item
            foreach ($searchableItems as $searchableItem) {
                $searchTerm = $searchableItem['name'] ?? $searchableItem['title'] ?? $searchableItem['code'] ?? array_values($searchableItem)[0];

                $livewire->set($searchProperty, $searchTerm)
                    ->assertSee($searchTerm);

                // Test that other items are not shown
                foreach ($searchableItems as $searchableItem) {
                    $otherTerm = $searchableItem['name'] ?? $searchableItem['title'] ?? $searchableItem['code'] ?? array_values($searchableItem)[0];
                    if ($otherTerm !== $searchTerm) {
                        $livewire->assertDontSee($otherTerm);
                    }
                }

                // Clear search
                $livewire->set($searchProperty, '');
            }
        });
    }

    /**
     * Test Livewire component filtering.
     */
    protected function testLivewireFiltering(string $componentClass, string $modelClass, array $testData, string $filterProperty = 'filter'): void
    {
        $this->testLivewireComponentWithAuth($componentClass, function ($livewire) use ($testData, $filterProperty, $modelClass): void {
            // Create test data
            $models = [];
            foreach ($testData as $data) {
                $models[] = $modelClass::create($data);
            }

            // Test each filter value
            foreach ($testData as $index => $data) {
                $filterValue = $data['status'] ?? $data['type'] ?? $data['category'] ?? 'test';

                $livewire->set($filterProperty, $filterValue)
                    ->assertSee($data['name'] ?? $data['title'] ?? $data['code'] ?? array_values($data)[0]);

                // Test that items with different filter values are not shown
                foreach ($testData as $otherIndex => $otherData) {
                    if ($otherIndex !== $index) {
                        $otherFilterValue = $otherData['status'] ?? $otherData['type'] ?? $otherData['category'] ?? 'other';
                        if ($otherFilterValue !== $filterValue) {
                            $otherTerm = $otherData['name'] ?? $otherData['title'] ?? $otherData['code'] ?? array_values($otherData)[0];
                            $livewire->assertDontSee($otherTerm);
                        }
                    }
                }
            }
        });
    }

    /**
     * Test Livewire component pagination.
     */
    protected function testLivewirePagination(string $componentClass, string $modelClass, array $itemsData, int $pageSize = 10): void
    {
        $this->testLivewireComponentWithAuth($componentClass, function ($livewire) use ($itemsData, $pageSize, $modelClass): void {
            // Create test data
            foreach ($itemsData as $itemData) {
                $modelClass::create($itemData);
            }

            // Test first page
            $livewire->assertSee($itemsData[0]['name'] ?? $itemsData[0]['title'] ?? $itemsData[0]['code'] ?? array_values($itemsData[0])[0]);

            // Test pagination if we have more items than page size
            if (count($itemsData) > $pageSize) {
                $livewire->call('setPage', 2)
                    ->assertSee($itemsData[$pageSize]['name'] ?? $itemsData[$pageSize]['title'] ?? $itemsData[$pageSize]['code'] ?? array_values($itemsData[$pageSize])[0]);
            }
        });
    }

    /**
     * Test Livewire component validation.
     */
    protected function testLivewireValidation(string $componentClass, array $validData, array $invalidFields): void
    {
        $this->testLivewireComponentWithAuth($componentClass, function ($livewire) use ($validData, $invalidFields): void {
            // Test valid data
            $livewire->call('openCreateModal')
                ->set('form', $validData)
                ->call('save')
                ->assertHasNoErrors();

            // Test invalid fields
            foreach ($invalidFields as $field => $invalidValue) {
                $livewire->call('openCreateModal')
                    ->set('form', array_merge($validData, [$field => $invalidValue]))
                    ->call('save')
                    ->assertHasErrors([$field]);
            }
        });
    }
}
