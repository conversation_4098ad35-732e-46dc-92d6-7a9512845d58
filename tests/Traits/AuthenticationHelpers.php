<?php

namespace Tests\Traits;

use App\Models\User;

trait AuthenticationHelpers
{
    /**
     * Act as an admin user.
     */
    protected function actAsAdmin(): User
    {
        $user = $this->createAdminUser();
        $this->actingAs($user);

        return $user;
    }

    /**
     * Act as a manager user.
     */
    protected function actAsManager(): User
    {
        $user = $this->createManagerUser();
        $this->actingAs($user);

        return $user;
    }

    /**
     * Act as a reviewer user.
     */
    protected function actAsReviewer(): User
    {
        $user = $this->createReviewerUser();
        $this->actingAs($user);

        return $user;
    }

    /**
     * Act as a caretaker user.
     */
    protected function actAsCaretaker(): User
    {
        $user = $this->createCaretakerUser();
        $this->actingAs($user);

        return $user;
    }

    /**
     * Act as a resident user.
     */
    protected function actAsResident(): User
    {
        $user = $this->createResidentUser();
        $this->actingAs($user);

        return $user;
    }

    /**
     * Act as a user with a specific role.
     */
    protected function actAsUserWithRole(string $role): User
    {
        return match ($role) {
            'admin' => $this->actAsAdmin(),
            'manager' => $this->actAsManager(),
            'reviewer' => $this->actAsReviewer(),
            'caretaker' => $this->actAsCaretaker(),
            'resident' => $this->actAsResident(),
            default => $this->actAsAdmin(),
        };
    }

    /**
     * Test that a guest is redirected to login.
     */
    protected function assertGuestRedirectedToLogin(string $path): void
    {
        $response = $this->get($path);
        $response->assertRedirect('/login');
    }

    /**
     * Test that a user with a specific role cannot access a route.
     */
    protected function assertRoleCannotAccess(string $role, string $path): void
    {
        $this->actAsUserWithRole($role);
        $response = $this->get($path);
        $response->assertForbidden();
    }

    /**
     * Test that a user with a specific role can access a route.
     */
    protected function assertRoleCanAccess(string $role, string $path): void
    {
        $this->actAsUserWithRole($role);
        $response = $this->get($path);
        $response->assertStatus(200);
    }

    /**
     * Test authentication for multiple roles against a route.
     */
    protected function testAuthenticationForRoute(string $path, array $allowedRoles, array $forbiddenRoles = []): void
    {
        // Test guest access
        $this->assertGuestRedirectedToLogin($path);

        // Test forbidden roles
        foreach ($forbiddenRoles as $forbiddenRole) {
            $this->assertRoleCannotAccess($forbiddenRole, $path);
        }

        // Test allowed roles
        foreach ($allowedRoles as $allowedRole) {
            $this->assertRoleCanAccess($allowedRole, $path);
        }
    }

    /**
     * Create and authenticate a user with estate assignment.
     */
    protected function actAsUserWithEstateAssignment(string $role, ?int $estateId = null): User
    {
        $estate = $estateId ? \App\Models\Estate::find($estateId) : \App\Models\Estate::first();

        if (! $estate) {
            $estate = \App\Models\Estate::factory()->create();
        }

        $user = $this->createUserWithEstate($role, $estate);
        $this->actingAs($user);

        return $user;
    }
}
