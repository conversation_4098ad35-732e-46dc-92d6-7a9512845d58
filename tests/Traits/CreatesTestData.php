<?php

namespace Tests\Traits;

use App\Models\Contact;
use App\Models\Estate;
use App\Models\House;
use App\Models\HouseAccount;
use App\Models\Invoice;
use App\Models\MeterReading;
use App\Models\User;
use App\Models\WaterRate;

trait CreatesTestData
{
    /**
     * Create a test estate.
     */
    protected function createTestEstate(array $attributes = []): Estate
    {
        return Estate::factory()->create(array_merge([
            'name' => 'Test Estate',
            'code' => 'TST-001',
            'city' => 'Test City',
            'is_active' => true,
        ], $attributes));
    }

    /**
     * Create a test house.
     */
    protected function createTestHouse(Estate $estate, array $attributes = []): House
    {
        return House::factory()->create(array_merge([
            'estate_id' => $estate->id,
            'house_number' => 'TST-HOUSE-001',
            'is_active' => true,
        ], $attributes));
    }

    /**
     * Create a test contact.
     */
    protected function createTestContact(House $house, array $attributes = []): Contact
    {
        $contact = Contact::factory()->create(array_merge([
            'type' => 'owner',
            'is_primary' => true,
            'receive_invoices' => true,
            'is_active' => true,
        ], $attributes));

        $contact->houses()->attach($house->id, [
            'relationship_type' => 'owner',
            'start_date' => now(),
            'is_current' => true,
        ]);

        return $contact;
    }

    /**
     * Create a test water rate.
     */
    protected function createTestWaterRate(Estate $estate, array $attributes = []): WaterRate
    {
        return WaterRate::factory()->create(array_merge([
            'estate_id' => $estate->id,
            'name' => 'Test Water Rate',
            'rate_per_unit' => 30,
            'minimum_charge' => 200,
            'fixed_charge' => 300,
            'is_active' => true,
        ], $attributes));
    }

    /**
     * Create a test meter reading.
     */
    protected function createTestMeterReading(House $house, User $user, array $attributes = []): MeterReading
    {
        return MeterReading::factory()->create(array_merge([
            'house_id' => $house->id,
            'user_id' => $user->id,
            'reading_date' => now(),
            'status' => 'approved',
            'validation_status' => 'valid',
            'is_validated' => true,
        ], $attributes));
    }

    /**
     * Create a test house account.
     */
    protected function createTestHouseAccount(House $house, array $attributes = []): HouseAccount
    {
        return HouseAccount::factory()->create(array_merge([
            'house_id' => $house->id,
            'current_balance' => 0,
            'account_status' => 'active',
        ], $attributes));
    }

    /**
     * Create a test invoice.
     */
    protected function createTestInvoice(House $house, WaterRate $waterRate, MeterReading $meterReading, array $attributes = []): Invoice
    {
        return Invoice::factory()->create(array_merge([
            'house_id' => $house->id,
            'water_rate_id' => $waterRate->id,
            'meter_reading_id' => $meterReading->id,
            'status' => 'draft',
            'approval_status' => 'pending',
        ], $attributes));
    }

    /**
     * Create a complete test dataset (estate, house, contact, etc.).
     */
    protected function createCompleteTestData(): array
    {
        // Create estate
        $estate = $this->createTestEstate();

        // Create house
        $house = $this->createTestHouse($estate);

        // Create contact
        $contact = $this->createTestContact($house);

        // Create water rate
        $waterRate = $this->createTestWaterRate($estate);

        // Create caretaker user for readings
        $caretaker = $this->createCaretakerUser();

        // Create meter readings
        $previousReading = $this->createTestMeterReading($house, $caretaker, [
            'reading_date' => now()->subMonth(),
            'current_reading' => 1000,
            'consumption' => 50,
        ]);

        $currentReading = $this->createTestMeterReading($house, $caretaker, [
            'reading_date' => now(),
            'current_reading' => 1060,
            'consumption' => 60,
        ]);

        // Create house account
        $houseAccount = $this->createTestHouseAccount($house);

        // Create invoice
        $invoice = $this->createTestInvoice($house, $waterRate, $currentReading);

        return [
            'estate' => $estate,
            'house' => $house,
            'contact' => $contact,
            'waterRate' => $waterRate,
            'caretaker' => $caretaker,
            'previousReading' => $previousReading,
            'currentReading' => $currentReading,
            'houseAccount' => $houseAccount,
            'invoice' => $invoice,
        ];
    }

    /**
     * Create multiple test estates with houses.
     */
    protected function createMultipleEstatesWithHouses(int $estateCount = 2, int $housesPerEstate = 5): array
    {
        $estates = [];
        $houses = [];

        for ($i = 0; $i < $estateCount; $i++) {
            $estate = $this->createTestEstate([
                'name' => 'Test Estate '.($i + 1),
                'code' => 'TST-'.str_pad($i + 1, 3, '0', STR_PAD_LEFT),
            ]);
            $estates[] = $estate;

            for ($j = 0; $j < $housesPerEstate; $j++) {
                $house = $this->createTestHouse($estate, [
                    'house_number' => $estate->code.'-HOUSE-'.str_pad($j + 1, 3, '0', STR_PAD_LEFT),
                ]);
                $houses[] = $house;

                // Create contact for each house
                $this->createTestContact($house);
            }
        }

        return [
            'estates' => $estates,
            'houses' => $houses,
        ];
    }

    /**
     * Create test data for billing workflow.
     */
    protected function createBillingWorkflowTestData(): array
    {
        $data = $this->createCompleteTestData();

        // Create manager and reviewer users
        $manager = $this->createManagerUser();
        $reviewer = $this->createReviewerUser();

        // Assign users to estate
        $manager->assignedEstates()->attach($data['estate']->id, [
            'assigned_by' => 1,
            'assigned_at' => now(),
        ]);
        $reviewer->assignedEstates()->attach($data['estate']->id, [
            'assigned_by' => 1,
            'assigned_at' => now(),
        ]);

        // Update invoice to be ready for approval
        $data['invoice']->update([
            'status' => 'submitted',
            'submitted_by' => $manager->id,
            'submitted_at' => now(),
        ]);

        return array_merge($data, [
            'manager' => $manager,
            'reviewer' => $reviewer,
        ]);
    }
}
