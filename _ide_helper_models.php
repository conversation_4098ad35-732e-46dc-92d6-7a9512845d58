<?php

// @formatter:off
// phpcs:ignoreFile
/**
 * A helper file for your Eloquent Models
 * Copy the phpDocs from this file to the correct Model,
 * And remove them from this file, to prevent double declarations.
 *
 * <AUTHOR> vd. <PERSON> <<EMAIL>>
 */


namespace App\Models{
/**
 * @property int $id
 * @property int $house_id
 * @property string|null $name
 * @property string|null $email
 * @property string|null $phone
 * @property string|null $whatsapp_number
 * @property string $type
 * @property bool $is_primary
 * @property bool $receive_invoices
 * @property bool $receive_notifications
 * @property string|null $notes
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string $first_name
 * @property string $last_name
 * @property string|null $middle_name
 * @property string|null $id_number
 * @property \Illuminate\Support\Carbon|null $date_of_birth
 * @property string|null $occupation
 * @property string|null $company
 * @property string|null $postal_address
 * @property string|null $emergency_contact_name
 * @property string|null $emergency_contact_phone
 * @property string|null $deleted_at
 * @property bool $is_active
 * @property-read mixed $display_name
 * @property-read mixed $full_name
 * @property-read mixed $has_whats_app
 * @property-read mixed $preferred_contact
 * @property-read \App\Models\House $house
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\HouseContact> $houseContacts
 * @property-read int|null $house_contacts_count
 * @property-read \App\Models\HouseContact|null $pivot
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\House> $houses
 * @property-read int|null $houses_count
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact active()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact byEmail($email)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact byHouse($houseId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact byName($name)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact byPhone($phone)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact byType($type)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact canReceiveMessages()
 * @method static \Database\Factories\ContactFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact invoiceRecipients()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact notificationRecipients()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact primary()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereCompany($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereDateOfBirth($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereEmergencyContactName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereEmergencyContactPhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereFirstName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereHouseId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereIdNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereIsPrimary($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereLastName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereMiddleName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereNotes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereOccupation($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact wherePhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact wherePostalAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereReceiveInvoices($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereReceiveNotifications($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Contact whereWhatsappNumber($value)
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperContact {}
}

namespace App\Models{
/**
 * @property int $id
 * @property string $name
 * @property string|null $address
 * @property string|null $city
 * @property string|null $state
 * @property string|null $postal_code
 * @property string $country
 * @property string|null $description
 * @property array<array-key, mixed>|null $settings
 * @property bool $is_active
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string $code
 * @property string|null $manager_name
 * @property string|null $manager_phone
 * @property string|null $manager_email
 * @property int $total_houses
 * @property int $occupied_houses
 * @property string|null $deleted_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Contact> $contacts
 * @property-read int|null $contacts_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\House> $houses
 * @property-read int|null $houses_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Invoice> $invoices
 * @property-read int|null $invoices_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\MeterReading> $meterReadings
 * @property-read int|null $meter_readings_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\WaterRate> $waterRates
 * @property-read int|null $water_rates_count
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Estate active()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Estate byCode($code)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Estate byLocation($city, $state = null)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Estate byName($name)
 * @method static \Database\Factories\EstateFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Estate newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Estate newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Estate query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Estate whereAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Estate whereCity($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Estate whereCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Estate whereCountry($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Estate whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Estate whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Estate whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Estate whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Estate whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Estate whereManagerEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Estate whereManagerName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Estate whereManagerPhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Estate whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Estate whereOccupiedHouses($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Estate wherePostalCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Estate whereSettings($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Estate whereState($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Estate whereTotalHouses($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Estate whereUpdatedAt($value)
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperEstate {}
}

namespace App\Models{
/**
 * @property int $id
 * @property int $estate_id
 * @property string $house_number
 * @property string|null $block
 * @property string|null $floor
 * @property string $type
 * @property string $meter_number
 * @property numeric $initial_reading
 * @property bool $is_active
 * @property string|null $notes
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property int|null $bedrooms
 * @property numeric|null $size_sqft
 * @property numeric|null $monthly_rent
 * @property \Illuminate\Support\Carbon|null $occupancy_date
 * @property string|null $gps_coordinates
 * @property string|null $deleted_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Contact> $contacts
 * @property-read int|null $contacts_count
 * @property-read \App\Models\Estate $estate
 * @property-read mixed $current_reading
 * @property-read mixed $full_address
 * @property-read mixed $is_occupied
 * @property-read mixed $primary_contact
 * @property-read mixed $status
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\HouseContact> $houseContacts
 * @property-read int|null $house_contacts_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Invoice> $invoices
 * @property-read int|null $invoices_count
 * @property-read \App\Models\MeterReading|null $latestReading
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\MeterReading> $meterReadings
 * @property-read int|null $meter_readings_count
 * @method static \Illuminate\Database\Eloquent\Builder<static>|House active()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|House byBlock($block)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|House byBlockAndFloor($block, $floor = null)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|House byEstate($estateId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|House byHouseNumber($houseNumber)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|House byMeterNumber($meterNumber)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|House byType($type)
 * @method static \Database\Factories\HouseFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|House newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|House newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|House occupied()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|House query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|House vacant()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|House whereBedrooms($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|House whereBlock($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|House whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|House whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|House whereEstateId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|House whereFloor($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|House whereGpsCoordinates($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|House whereHouseNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|House whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|House whereInitialReading($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|House whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|House whereMeterNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|House whereMonthlyRent($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|House whereNotes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|House whereOccupancyDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|House whereSizeSqft($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|House whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|House whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|House withContactCount()
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperHouse {}
}

namespace App\Models{
/**
 * @property int $id
 * @property int $house_id
 * @property int $contact_id
 * @property string $relationship_type
 * @property \Illuminate\Support\Carbon|null $start_date
 * @property \Illuminate\Support\Carbon|null $end_date
 * @property bool $is_active
 * @property string|null $notes
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HouseContact newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HouseContact newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HouseContact query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HouseContact whereContactId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HouseContact whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HouseContact whereEndDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HouseContact whereHouseId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HouseContact whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HouseContact whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HouseContact whereNotes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HouseContact whereRelationshipType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HouseContact whereStartDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HouseContact whereUpdatedAt($value)
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperHouseContact {}
}

namespace App\Models{
/**
 * @property int $id
 * @property string $invoice_number
 * @property int $house_id
 * @property int $meter_reading_id
 * @property int $water_rate_id
 * @property \Illuminate\Support\Carbon $billing_period_start
 * @property \Illuminate\Support\Carbon $billing_period_end
 * @property numeric $previous_reading
 * @property numeric $current_reading
 * @property numeric $consumption
 * @property numeric $rate_per_unit
 * @property numeric $amount
 * @property numeric $fixed_charge
 * @property numeric $total_amount
 * @property \Illuminate\Support\Carbon $due_date
 * @property string $status
 * @property \Illuminate\Support\Carbon|null $sent_at
 * @property \Illuminate\Support\Carbon|null $paid_at
 * @property string|null $payment_reference
 * @property string|null $notes
 * @property string|null $pdf_path
 * @property string|null $public_link
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string $previous_balance
 * @property string $late_fee
 * @property string $tax_amount
 * @property string $total_due
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\InvoiceAdjustment> $adjustments
 * @property-read int|null $adjustments_count
 * @property-read mixed $days_overdue
 * @property-read mixed $public_url
 * @property-read mixed $status_color
 * @property-read mixed $status_label
 * @property-read mixed $total_amount_with_currency
 * @property-read \App\Models\House $house
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\InvoiceLineItem> $lineItems
 * @property-read int|null $line_items_count
 * @property-read \App\Models\MeterReading $meterReading
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\InvoicePayment> $payments
 * @property-read int|null $payments_count
 * @property-read \App\Models\WaterRate $waterRate
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\WhatsAppMessage> $whatsappMessages
 * @property-read int|null $whatsapp_messages_count
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice byHouse($houseId)
 * @method static \Database\Factories\InvoiceFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice forPeriod($startDate, $endDate)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice overdue()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice paid()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice pending()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereBillingPeriodEnd($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereBillingPeriodStart($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereConsumption($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereCurrentReading($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereDueDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereFixedCharge($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereHouseId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereInvoiceNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereLateFee($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereMeterReadingId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereNotes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice wherePaidAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice wherePaymentReference($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice wherePdfPath($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice wherePreviousBalance($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice wherePreviousReading($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice wherePublicLink($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereRatePerUnit($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereSentAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereTaxAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereTotalAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereTotalDue($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereWaterRateId($value)
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperInvoice {}
}

namespace App\Models{
/**
 * @property int $id
 * @property int $invoice_id
 * @property string $type
 * @property numeric $amount
 * @property string $reason
 * @property string|null $description
 * @property int|null $user_id
 * @property \Illuminate\Support\Carbon $adjustment_date
 * @property string $reference_number
 * @property array<array-key, mixed>|null $metadata
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read mixed $formatted_amount
 * @property-read \App\Models\Invoice $invoice
 * @property-read \App\Models\User|null $user
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceAdjustment newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceAdjustment newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceAdjustment query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceAdjustment whereAdjustmentDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceAdjustment whereAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceAdjustment whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceAdjustment whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceAdjustment whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceAdjustment whereInvoiceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceAdjustment whereMetadata($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceAdjustment whereReason($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceAdjustment whereReferenceNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceAdjustment whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceAdjustment whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceAdjustment whereUserId($value)
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperInvoiceAdjustment {}
}

namespace App\Models{
/**
 * @property int $id
 * @property int $invoice_id
 * @property string $type
 * @property string $description
 * @property numeric|null $quantity
 * @property numeric|null $rate
 * @property numeric $amount
 * @property int|null $tier
 * @property array<array-key, mixed>|null $metadata
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read mixed $formatted_amount
 * @property-read mixed $formatted_quantity
 * @property-read mixed $formatted_rate
 * @property-read \App\Models\Invoice $invoice
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceLineItem newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceLineItem newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceLineItem query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceLineItem whereAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceLineItem whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceLineItem whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceLineItem whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceLineItem whereInvoiceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceLineItem whereMetadata($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceLineItem whereQuantity($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceLineItem whereRate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceLineItem whereTier($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceLineItem whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoiceLineItem whereUpdatedAt($value)
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperInvoiceLineItem {}
}

namespace App\Models{
/**
 * @property int $id
 * @property int $invoice_id
 * @property numeric $amount
 * @property \Illuminate\Support\Carbon $payment_date
 * @property string $payment_method
 * @property string $reference_number
 * @property string|null $transaction_id
 * @property string|null $notes
 * @property int|null $user_id
 * @property array<array-key, mixed>|null $metadata
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read mixed $formatted_amount
 * @property-read \App\Models\Invoice $invoice
 * @property-read \App\Models\User|null $user
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoicePayment newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoicePayment newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoicePayment query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoicePayment whereAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoicePayment whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoicePayment whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoicePayment whereInvoiceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoicePayment whereMetadata($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoicePayment whereNotes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoicePayment wherePaymentDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoicePayment wherePaymentMethod($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoicePayment whereReferenceNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoicePayment whereTransactionId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoicePayment whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InvoicePayment whereUserId($value)
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperInvoicePayment {}
}

namespace App\Models{
/**
 * @property int $id
 * @property int $house_id
 * @property int $user_id
 * @property numeric $current_reading
 * @property numeric|null $previous_reading
 * @property numeric|null $consumption
 * @property \Illuminate\Support\Carbon $reading_date
 * @property string|null $photo_path
 * @property string|null $notes
 * @property string $status
 * @property int|null $reviewed_by
 * @property \Illuminate\Support\Carbon|null $reviewed_at
 * @property string|null $review_notes
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $latitude
 * @property string|null $longitude
 * @property string|null $accuracy
 * @property-read mixed $status_color
 * @property-read mixed $status_label
 * @property-read \App\Models\House $house
 * @property-read \App\Models\User|null $reviewer
 * @property-read \App\Models\User $user
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading approved()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading byHouse($houseId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading byUser($userId)
 * @method static \Database\Factories\MeterReadingFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading forPeriod($startDate, $endDate)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading pendingReview()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading rejected()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading submitted()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading whereAccuracy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading whereConsumption($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading whereCurrentReading($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading whereHouseId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading whereLatitude($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading whereLongitude($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading whereNotes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading wherePhotoPath($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading wherePreviousReading($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading whereReadingDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading whereReviewNotes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading whereReviewedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading whereReviewedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MeterReading whereUserId($value)
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperMeterReading {}
}

namespace App\Models{
/**
 * @property int $id
 * @property string $name
 * @property string $email
 * @property \Illuminate\Support\Carbon|null $email_verified_at
 * @property string $password
 * @property string|null $remember_token
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Estate> $assignedEstates
 * @property-read int|null $assigned_estates_count
 * @property-read \Illuminate\Notifications\DatabaseNotificationCollection<int, \Illuminate\Notifications\DatabaseNotification> $notifications
 * @property-read int|null $notifications_count
 * @method static \Database\Factories\UserFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereEmailVerifiedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User wherePassword($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereRememberToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereRole($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereUpdatedAt($value)
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperUser {}
}

namespace App\Models{
/**
 * @property int $id
 * @property int $estate_id
 * @property string $name
 * @property numeric $rate_per_unit
 * @property numeric $minimum_charge
 * @property int $minimum_units
 * @property numeric $fixed_charge
 * @property \Illuminate\Support\Carbon $effective_from
 * @property \Illuminate\Support\Carbon|null $effective_to
 * @property bool $is_active
 * @property string|null $description
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Estate $estate
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WaterRate active()
 * @method static \Database\Factories\WaterRateFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WaterRate forEstate($estateId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WaterRate newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WaterRate newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WaterRate query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WaterRate whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WaterRate whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WaterRate whereEffectiveFrom($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WaterRate whereEffectiveTo($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WaterRate whereEstateId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WaterRate whereFixedCharge($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WaterRate whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WaterRate whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WaterRate whereMinimumCharge($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WaterRate whereMinimumUnits($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WaterRate whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WaterRate whereRatePerUnit($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WaterRate whereUpdatedAt($value)
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperWaterRate {}
}

namespace App\Models{
/**
 * @property int $id
 * @property int $sender_id
 * @property int $recipient_contact_id
 * @property int $house_id
 * @property int $estate_id
 * @property string $recipient
 * @property string $message_type
 * @property string $content
 * @property string|null $message_id
 * @property string $status
 * @property string|null $failed_reason
 * @property string|null $template_name
 * @property array<array-key, mixed>|null $parameters
 * @property array<array-key, mixed>|null $response_data
 * @property array<array-key, mixed>|null $interactive_data
 * @property string|null $media_url
 * @property string|null $conversation_id
 * @property int $retry_count
 * @property \Illuminate\Support\Carbon|null $sent_at
 * @property \Illuminate\Support\Carbon|null $delivered_at
 * @property \Illuminate\Support\Carbon|null $read_at
 * @property string|null $messageable_type
 * @property int|null $messageable_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Estate $estate
 * @property-read \App\Models\House $house
 * @property-read \App\Models\Invoice|null $invoice
 * @property-read \Illuminate\Database\Eloquent\Model|\Eloquent|null $messageable
 * @property-read \App\Models\Contact $recipientContact
 * @property-read \App\Models\User $sender
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage byDateRange($startDate, $endDate)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage byStatus($status)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage byTemplate($templateName)
 * @method static \Database\Factories\WhatsAppMessageFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage forContact(\App\Models\Contact $contact)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage forEstate($estateId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage forHouse($houseId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage forInvoice($invoiceId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage recent($days = 30)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage whereContent($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage whereConversationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage whereDeliveredAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage whereEstateId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage whereFailedReason($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage whereHouseId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage whereInteractiveData($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage whereMediaUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage whereMessageId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage whereMessageType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage whereMessageableId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage whereMessageableType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage whereParameters($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage whereReadAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage whereRecipient($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage whereRecipientContactId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage whereResponseData($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage whereRetryCount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage whereSenderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage whereSentAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage whereTemplateName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage whereUpdatedAt($value)
 * @mixin \Eloquent
 */
	#[\AllowDynamicProperties]
	class IdeHelperWhatsAppMessage {}
}

