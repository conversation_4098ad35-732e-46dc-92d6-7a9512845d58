<?php

use App\Livewire\Admin\EstateAssignmentManager;
use App\Livewire\Admin\TeamManagement;
use App\Livewire\Caretaker\HouseManager;
use App\Livewire\Dashboard;
use App\Livewire\Invoice\InvoiceCreate;
use App\Livewire\Invoice\InvoiceDetail;
use App\Livewire\Invoice\InvoiceEdit;
use App\Livewire\Invoice\InvoiceList;
use App\Livewire\Invoice\InvoicePdf;
use App\Livewire\Invoice\InvoiceReports;
use App\Livewire\Settings\Appearance;
use App\Livewire\Settings\Password;
use App\Livewire\Settings\Profile;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;

Route::get('/', \App\Livewire\HomePage::class)->name('home');

Route::get('/contact', \App\Livewire\ContactPage::class)->name('contact');

Route::middleware(['auth', 'verified'])->group(function (): void {
    // Main dashboard - handles permission-based redirection
    Route::get('dashboard', function () {
        $user = Auth::user();

        return match (true) {
            $user->hasPermissionTo('users.manage_all') => redirect()->route('admin.dashboard'),
            $user->hasPermissionTo('invoices.view_assigned') => redirect()->route('reviewer.dashboard'),
            $user->hasPermissionTo('estates.view_assigned') => redirect()->route('caretaker.dashboard'),
            default => redirect()->route('dashboard'),
        };
    })->name('dashboard');

    // Admin routes
    Route::prefix('admin')->middleware('can:users.manage_all')->group(function (): void {
        Route::get('dashboard', \App\Livewire\Admin\AdminDashboard::class)->name('admin.dashboard');
        Route::get('users', \App\Livewire\Admin\UserManager::class)->name('admin.users');
        Route::get('settings', \App\Livewire\Admin\SystemSettings::class)->name('admin.settings')->middleware('can:system.settings.view');
        Route::get('estate-assignments', EstateAssignmentManager::class)->name('admin.estate-assignments');
        Route::get('team-management', TeamManagement::class)->name('admin.team-management');
        Route::get('audit', \App\Livewire\Admin\AuditLogs::class)->name('admin.audit')->middleware('can:audit.view_logs');
        Route::get('audit/export', [\App\Http\Controllers\Admin\AuditLogController::class, 'export'])->name('admin.audit.export')->middleware('can:audit.export_logs');
        Route::get('billing', \App\Livewire\Admin\BillingManager::class)->name('admin.billing');
        Route::get('billing/invoice-approval', \App\Livewire\Admin\InvoiceApproval::class)->name('admin.billing.invoice-approval');
        Route::get('permissions', \App\Livewire\Admin\PermissionManager::class)->name('admin.permissions');
        Route::get('reports/aging', \App\Livewire\Admin\AgingReport::class)->name('admin.reports.aging')->middleware('can:reports.view_all');
        Route::get('reports/revenue', \App\Livewire\Admin\RevenueReport::class)->name('admin.reports.revenue')->middleware('can:reports.view_all');
        Route::get('reports/customer-statement', \App\Livewire\Admin\CustomerStatement::class)->name('admin.reports.customer-statement')->middleware('can:reports.view_all');
    });

    // Unified dashboard route for consolidated roles
    Route::get('unified-dashboard', Dashboard::class)->name('unified.dashboard');

    // Manager routes
    Route::prefix('management')->middleware('can:estates.manage_assigned')->group(function (): void {
        Route::get('dashboard', Dashboard::class)->name('management.dashboard');
        Route::get('estates', \App\Livewire\EstateManager::class)->name('management.estates');
        Route::get('estates/{estate}', \App\Livewire\EstateShow::class)->name('management.estates.show');
        Route::get('estates/{estate}/edit', \App\Livewire\EstateForm::class)->name('management.estates.edit');
        Route::get('estates/analytics', \App\Livewire\EstateAnalytics::class)->name('management.estates.analytics');
        Route::get('houses', \App\Livewire\HouseRegistry::class)->name('management.houses');
        Route::get('houses/create', \App\Livewire\HouseForm::class)->name('management.houses.create');
        Route::get('houses/{house}', \App\Livewire\HouseShow::class)->name('management.houses.show');
        Route::get('houses/{house}/edit', \App\Livewire\HouseForm::class)->name('management.houses.edit');
        Route::get('contacts', \App\Livewire\ContactManager::class)->name('management.contacts');

        // Billing/Invoice Management
        Route::get('billing', InvoiceList::class)->name('management.billing');
        Route::get('billing/{invoice}', InvoiceDetail::class)->name('management.billing.show');
        Route::get('invoices/create', InvoiceCreate::class)->name('management.invoices.create');

        Route::get('team', TeamManagement::class)->name('management.team');
        Route::get('reports', fn () => redirect()->route('dashboard')->with('message', 'Reports feature coming soon'))->name('management.reports');

        // Water Rates Management
        Route::get('estates/{estate}/water-rates', \App\Livewire\Estate\WaterRateList::class)->name('management.estates.water-rates');
        Route::get('estates/{estate}/water-rates/create', \App\Livewire\Estate\WaterRateForm::class)->name('management.estates.water-rates.create');
        Route::get('estates/{estate}/water-rates/{waterRate}/edit', \App\Livewire\Estate\WaterRateForm::class)->name('management.estates.water-rates.edit');
    });

    // Reviewer routes
    Route::prefix('reviewer')->middleware('can:invoices.generate_assigned')->group(function (): void {
        Route::get('dashboard', Dashboard::class)->name('reviewer.dashboard');
        Route::get('billing', InvoiceList::class)->name('reviewer.billing');
        Route::get('billing/create', InvoiceCreate::class)->name('reviewer.billing.create');
        Route::get('billing/reports', InvoiceReports::class)->name('reviewer.billing.reports');
        Route::get('billing/{invoice}', InvoiceDetail::class)->name('reviewer.billing.show');
        Route::get('billing/{invoice}/edit', InvoiceEdit::class)->name('reviewer.billing.edit');
        Route::get('billing/{invoice}/pdf', InvoicePdf::class)->name('reviewer.billing.pdf');
        Route::get('readings', \App\Livewire\MeterReadingEntry::class)->name('reviewer.readings');
        Route::get('readings/pending', \App\Livewire\MeterReadingEntry::class)->name('reviewer.readings.pending');
        Route::get('estates', \App\Livewire\EstateManager::class)->name('reviewer.estates');
        Route::get('houses', \App\Livewire\HouseRegistry::class)->name('reviewer.houses');
        Route::get('houses/{house}', \App\Livewire\HouseShow::class)->name('reviewer.houses.show');
        Route::get('houses/{house}/edit', \App\Livewire\HouseForm::class)->name('reviewer.houses.edit');
        Route::get('contacts', \App\Livewire\ContactManager::class)->name('reviewer.contacts');
        Route::get('reports', fn () => redirect()->route('dashboard')->with('message', 'Reports feature coming soon'))->name('reviewer.reports');
    });

    // Caretaker routes
    Route::prefix('caretaker')->middleware('can:readings.create_assigned')->group(function (): void {
        Route::get('dashboard', Dashboard::class)->name('caretaker.dashboard');
        Route::get('estates', \App\Livewire\Caretaker\EstateManager::class)->name('caretaker.estates');
        Route::get('houses/{estateId?}', HouseManager::class)->name('caretaker.houses');
        Route::get('houses/{house}/show', \App\Livewire\HouseShow::class)->name('caretaker.houses.show');
        Route::get('houses/{house}/edit', \App\Livewire\HouseForm::class)->name('caretaker.houses.edit');
        Route::get('readings', \App\Livewire\MeterReadingEntry::class)->name('caretaker.readings');
        Route::get('readings/create', \App\Livewire\MeterReadingEntry::class)->name('caretaker.readings.create');
        Route::get('readings/{reading}/edit', \App\Livewire\MeterReadingEntry::class)->name('caretaker.readings.edit');
        Route::get('contacts', \App\Livewire\ContactManager::class)->name('caretaker.contacts');

        // Billing routes for caretakers
        Route::get('billing/create', InvoiceCreate::class)->name('caretaker.billing.create');
        Route::get('billing/{invoice}', InvoiceDetail::class)->name('caretaker.billing.show');
    });

    // Resident routes
    Route::prefix('resident')->middleware('can:resident.portal.access')->group(function (): void {
        Route::get('dashboard', Dashboard::class)->name('resident.dashboard');
        Route::get('invoices', \App\Livewire\Resident\InvoiceHistory::class)->name('resident.invoices');
        Route::get('invoices/{invoice}', \App\Livewire\Invoice\InvoiceDetail::class)->name('resident.invoices.show');
        Route::get('invoices/{invoice}/pdf', \App\Livewire\Invoice\InvoicePdf::class)->name('resident.invoices.pdf');
        Route::get('readings', \App\Livewire\Resident\MeterReadingHistory::class)->name('resident.readings');
        Route::get('messages', \App\Livewire\Resident\MessageHistory::class)->name('resident.messages');
        Route::get('profile', \App\Livewire\Resident\ContactProfile::class)->name('resident.profile');
        Route::get('inquiry', \App\Livewire\Resident\InquiryForm::class)->name('resident.inquiry');
        Route::get('reports', fn () => redirect()->route('dashboard')->with('message', 'Reports coming soon'))->name('resident.reports');
    });

    // Resident authentication routes (no auth middleware)
    Route::prefix('resident')->group(function (): void {
        Route::get('login', \App\Livewire\Resident\Auth\Login::class)->name('resident.login');
        Route::post('logout', function () {
            Auth::logout();
            session()->invalidate();
            session()->regenerateToken();

            return redirect()->route('resident.login');
        })->name('resident.logout');
    });

    // Legacy routes removed - use new role-based routes instead
    // Previous legacy routes were causing conflicts with new role-based routing structure
    // All functionality is now available through the prefixed routes:
    // - management/dashboard -> management.dashboard
    // - reviewer/dashboard -> reviewer.dashboard
    // - caretaker/dashboard -> caretaker.dashboard

    // Settings routes (available to all authenticated users)
    Route::redirect('settings', 'settings/profile');
    Route::get('settings/profile', Profile::class)->name('settings.profile');
    Route::get('settings/password', Password::class)->name('settings.password');
    Route::get('settings/appearance', Appearance::class)->name('settings.appearance');

    // Validation Dashboard routes
    Route::get('validation', \App\Livewire\ValidationDashboard::class)
        ->middleware('can:validation.view_rules')
        ->name('validation.dashboard');

    // General routes with proper permission checking
    Route::get('estates', \App\Livewire\EstateManager::class)
        ->middleware('can:estates.view_all')
        ->name('estates');
    Route::get('estates/{estate}', \App\Livewire\EstateShow::class)
        ->middleware('can:estates.view_all')
        ->name('estates.show');
    Route::get('estates/{estate}/edit', \App\Livewire\EstateForm::class)
        ->middleware('can:estates.manage_all')
        ->name('estates.edit');
    Route::get('estates/analytics', \App\Livewire\EstateAnalytics::class)
        ->middleware('can:analytics.view_all')
        ->name('estates.analytics');
    Route::get('houses', \App\Livewire\HouseRegistry::class)
        ->middleware('can:houses.view_all')
        ->name('houses');
    Route::get('houses/{house}', \App\Livewire\HouseShow::class)
        ->middleware('can:houses.view_all')
        ->name('houses.show');
    Route::get('houses/create', \App\Livewire\HouseForm::class)
        ->middleware('can:houses.manage_all')
        ->name('houses.create');
    Route::get('houses/{house}/edit', \App\Livewire\HouseForm::class)
        ->middleware('can:houses.manage_all')
        ->name('houses.edit');
    Route::get('contacts', \App\Livewire\ContactManager::class)
        ->middleware('can:contacts.view_all')
        ->name('contacts');

    // Meter Reading Routes
    Route::get('readings', \App\Livewire\MeterReadingEntry::class)
        ->middleware('can:readings.view_all')
        ->name('readings.index');
    Route::get('readings/create', \App\Livewire\MeterReadingEntry::class)
        ->middleware('can:readings.create_all')
        ->name('readings.create');
    Route::get('readings/{reading}/edit', \App\Livewire\MeterReadingEntry::class)
        ->middleware('can:readings.edit_all')
        ->name('readings.edit');
    Route::get('readings/pending', \App\Livewire\MeterReadingEntry::class)
        ->middleware('can:readings.review_all')
        ->name('readings.pending');
    Route::get('readings/review', \App\Livewire\MeterReadingReview::class)
        ->middleware('can:readings.review_all')
        ->name('readings.review');

    // Invoice Routes
    Route::get('invoices', InvoiceList::class)
        ->middleware('can:invoices.view_all')
        ->name('invoices.index');
    Route::get('invoices/{invoice}', InvoiceDetail::class)
        ->middleware('can:invoices.view_all')
        ->name('invoices.show');
    Route::get('invoices/create', InvoiceCreate::class)
        ->middleware('can:invoices.create_manual')
        ->name('invoices.create');
    Route::get('invoices/{invoice}/edit', InvoiceEdit::class)
        ->middleware('can:invoices.edit_all')
        ->name('invoices.edit');
    Route::get('invoices/{invoice}/pdf', InvoicePdf::class)
        ->middleware('can:invoices.view_all')
        ->name('invoices.pdf');
    Route::get('invoices/reports', InvoiceReports::class)
        ->middleware('can:reports.view_all')
        ->name('invoices.reports');

    // Payment Routes
    Route::prefix('payments')->group(function (): void {
        Route::get('{invoice}', \App\Livewire\Payment\PaymentManager::class)
            ->middleware('can:payments.view_all')
            ->name('payments.index');
        Route::get('{invoice}/process', fn () => redirect()->route('payments.index', request('invoice'))->with('message', 'Use the "Process Payment" button on the payment management page.'))->middleware('can:payments.process')
            ->name('payments.process');
        Route::get('{invoice}/export', fn () => redirect()->route('payments.index', request('invoice'))->with('message', 'Use the "Export Payments" button on the payment management page.'))->middleware('can:payments.export_all')
            ->name('payments.export');
    });

    // Analytics Route
    Route::get('analytics', \App\Livewire\EstateAnalytics::class)
        ->middleware('can:analytics.view_all')
        ->name('analytics');

    // Billing Route
    Route::get('billing', InvoiceList::class)
        ->middleware('can:invoices.view_all')
        ->name('billing.index');

    // Reports and Exports
    Route::get('reports', \App\Livewire\Invoice\InvoiceReports::class)
        ->middleware('can:reports.view_all')
        ->name('reports.index');
    Route::get('exports', \App\Livewire\DataExportManager::class)
        ->middleware('can:export.data_all')
        ->name('exports.index');
    Route::get('exports/{exportJob}/download', function (\App\Models\ExportJob $exportJob) {
        if (Auth::user()->cannot('export.data_all') && $exportJob->user_id !== Auth::id()) {
            abort(403);
        }

        if ($exportJob->status !== 'completed' || ! $exportJob->file_path) {
            return redirect()->route('exports.index')->with('error', 'Export is not ready for download.');
        }

        return response()->download(storage_path('app/'.$exportJob->file_path), $exportJob->file_name);
    })->name('exports.download');

    // Export Template Management
    Route::prefix('export')->middleware('can:export.data_all')->group(function (): void {
        Route::get('templates', \App\Livewire\Export\ExportTemplateList::class)->name('export.templates.index');
        Route::get('templates/create', \App\Livewire\Export\ExportTemplateForm::class)->name('export.templates.create');
        Route::get('templates/{template}/edit', \App\Livewire\Export\ExportTemplateForm::class)->name('export.templates.edit');
    });

    // API Routes for Export Management
    Route::prefix('api')->middleware('permission:export.data_all')->group(function (): void {
        Route::prefix('exports')->group(function (): void {
            Route::post('/', [\App\Http\Controllers\Api\ExportController::class, 'initiate'])->name('api.exports.initiate');
            Route::get('/', [\App\Http\Controllers\Api\ExportController::class, 'index'])->name('api.exports.index');
            Route::get('/{id}', [\App\Http\Controllers\Api\ExportController::class, 'show'])->name('api.exports.show');
            Route::get('/{id}/download', [\App\Http\Controllers\Api\ExportController::class, 'download'])->name('api.exports.download');
            Route::delete('/{id}', [\App\Http\Controllers\Api\ExportController::class, 'destroy'])->name('api.exports.destroy');
            Route::post('/{id}/retry', [\App\Http\Controllers\Api\ExportController::class, 'retry'])->name('api.exports.retry');

            Route::prefix('templates')->group(function (): void {
                Route::get('/', [\App\Http\Controllers\Api\ExportController::class, 'templates'])->name('api.exports.templates');
                Route::post('/', [\App\Http\Controllers\Api\ExportController::class, 'storeTemplate'])->name('api.exports.templates.store');
                Route::get('/{id}', [\App\Http\Controllers\Api\ExportController::class, 'showTemplate'])->name('api.exports.templates.show');
                Route::put('/{id}', [\App\Http\Controllers\Api\ExportController::class, 'updateTemplate'])->name('api.exports.templates.update');
                Route::delete('/{id}', [\App\Http\Controllers\Api\ExportController::class, 'destroyTemplate'])->name('api.exports.templates.destroy');
            });

            Route::post('/preview', [\App\Http\Controllers\Api\ExportController::class, 'previewData'])->name('api.exports.preview');
        });
    });

    // API Routes for House Contact Management
    Route::prefix('api')->middleware('auth:api')->group(function (): void {
        // Estate routes
        Route::prefix('estates')->middleware('permission:estates.view_all')->group(function (): void {
            Route::get('/', [\App\Http\Controllers\EstateController::class, 'index']);
            Route::get('/{estate}', [\App\Http\Controllers\EstateController::class, 'show']);
            Route::get('/{estate}/houses', [\App\Http\Controllers\EstateController::class, 'getHouses']);
            Route::get('/{estate}/contacts', [\App\Http\Controllers\EstateController::class, 'getContacts']);
            Route::get('/{estate}/stats', [\App\Http\Controllers\EstateController::class, 'getStats']);
            Route::get('/export/template', [\App\Http\Controllers\EstateController::class, 'downloadTemplate']);
            Route::get('/export', [\App\Http\Controllers\EstateController::class, 'export']);
        });

        Route::prefix('estates')->middleware('permission:estates.manage_all')->group(function (): void {
            Route::post('/', [\App\Http\Controllers\EstateController::class, 'store']);
            Route::put('/{estate}', [\App\Http\Controllers\EstateController::class, 'update']);
            Route::delete('/{estate}', [\App\Http\Controllers\EstateController::class, 'destroy']);
            Route::post('/import', [\App\Http\Controllers\EstateController::class, 'import']);
        });

        // House routes
        Route::prefix('houses')->middleware('permission:houses.view_all')->group(function (): void {
            Route::get('/', [\App\Http\Controllers\HouseController::class, 'index']);
            Route::get('/{house}', [\App\Http\Controllers\HouseController::class, 'show']);
            Route::get('/{house}/contacts', [\App\Http\Controllers\HouseController::class, 'getContacts']);
            Route::get('/{house}/meter-readings', [\App\Http\Controllers\HouseController::class, 'getMeterReadings']);
            Route::get('/{house}/invoices', [\App\Http\Controllers\HouseController::class, 'getInvoices']);
            Route::get('/export/template', [\App\Http\Controllers\HouseController::class, 'downloadTemplate']);
            Route::get('/export', [\App\Http\Controllers\HouseController::class, 'export']);
        });

        Route::prefix('houses')->middleware('permission:houses.manage_all')->group(function (): void {
            Route::post('/', [\App\Http\Controllers\HouseController::class, 'store']);
            Route::put('/{house}', [\App\Http\Controllers\HouseController::class, 'update']);
            Route::delete('/{house}', [\App\Http\Controllers\HouseController::class, 'destroy']);
            Route::post('/import', [\App\Http\Controllers\HouseController::class, 'import']);
        });

        Route::prefix('houses')->middleware('permission:houses.view_all')->group(function (): void {
            Route::post('/{house}/contacts', [\App\Http\Controllers\HouseController::class, 'attachContact']);
            Route::delete('/{house}/contacts/{contact}', [\App\Http\Controllers\HouseController::class, 'detachContact']);
        });

        // Contact routes
        Route::prefix('contacts')->middleware('permission:contacts.view_all')->group(function (): void {
            Route::get('/', [\App\Http\Controllers\ContactController::class, 'index']);
            Route::get('/{contact}', [\App\Http\Controllers\ContactController::class, 'show']);
            Route::get('/type/{type}', [\App\Http\Controllers\ContactController::class, 'getByType']);
            Route::get('/house/{house}', [\App\Http\Controllers\ContactController::class, 'getByHouse']);
            Route::get('/export/template', [\App\Http\Controllers\ContactController::class, 'downloadTemplate']);
            Route::get('/export', [\App\Http\Controllers\ContactController::class, 'export']);
        });

        Route::prefix('contacts')->middleware('permission:contacts.manage_all')->group(function (): void {
            Route::post('/', [\App\Http\Controllers\ContactController::class, 'store']);
            Route::put('/{contact}', [\App\Http\Controllers\ContactController::class, 'update']);
            Route::delete('/{contact}', [\App\Http\Controllers\ContactController::class, 'destroy']);
            Route::post('/import', [\App\Http\Controllers\ContactController::class, 'import']);
        });

        Route::prefix('contacts')->middleware('permission:contacts.view_all')->group(function (): void {
            Route::post('/{contact}/attach-house', [\App\Http\Controllers\ContactController::class, 'attachToHouse']);
            Route::post('/{contact}/detach-house', [\App\Http\Controllers\ContactController::class, 'detachFromHouse']);
        });
    });
});

// WhatsApp webhook routes - must be public
Route::prefix('api/webhooks/whatsapp')->middleware(['whatsapp.webhook', 'permission:whatsapp.settings'])->group(function (): void {
    Route::get('/', [\App\Http\Controllers\Api\WhatsAppWebhookController::class, 'verify']);
    Route::post('/', [\App\Http\Controllers\Api\WhatsAppWebhookController::class, 'handle']);
});

require __DIR__.'/auth.php';
