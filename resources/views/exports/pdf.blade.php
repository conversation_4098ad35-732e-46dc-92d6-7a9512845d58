<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{{ ucfirst(str_replace('_', ' ', $entityType)) }} Export</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            margin: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #4F46E5;
            padding-bottom: 10px;
        }
        .header h1 {
            color: #4F46E5;
            margin: 0;
            font-size: 24px;
        }
        .header p {
            margin: 5px 0 0 0;
            color: #6B7280;
            font-size: 14px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th {
            background-color: #4F46E5;
            color: white;
            font-weight: bold;
            text-align: left;
            padding: 12px 8px;
            border: 1px solid #E5E7EB;
        }
        td {
            padding: 10px 8px;
            border: 1px solid #E5E7EB;
            vertical-align: top;
        }
        tr:nth-child(even) {
            background-color: #F9FAFB;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            color: #6B7280;
            font-size: 10px;
            border-top: 1px solid #E5E7EB;
            padding-top: 10px;
        }
        .summary {
            background-color: #F3F4F6;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .summary p {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>{{ ucfirst(str_replace('_', ' ', $entityType)) }} Export Report</h1>
        <p>Generated on {{ now()->format('F j, Y \a\t g:i A') }}</p>
        <p>Export ID: #{{ $exportJob->id }}</p>
    </div>

    <div class="summary">
        <p><strong>Total Records:</strong> {{ $data->count() }}</p>
        <p><strong>Export Format:</strong> {{ strtoupper($exportJob->format) }}</p>
        <p><strong>Generated By:</strong> {{ $exportJob->user->name }}</p>
    </div>

    @if($data->count() > 0)
        <table>
            <thead>
                <tr>
                    @foreach(array_keys($data->first()) as $column)
                        <th>{{ $column }}</th>
                    @endforeach
                </tr>
            </thead>
            <tbody>
                @foreach($data as $row)
                    <tr>
                        @foreach($row as $cell)
                            <td>{{ $cell }}</td>
                        @endforeach
                    </tr>
                @endforeach
            </tbody>
        </table>
    @else
        <p style="text-align: center; color: #6B7280; margin: 50px 0;">
            No data available for the selected criteria.
        </p>
    @endif

    <div class="footer">
        <p>This report was generated automatically by the Water Management System.</p>
        <p>Page {{ '{PAGE}' }} of {{ '{NUMPAGES}' }}</p>
    </div>
</body>
</html>