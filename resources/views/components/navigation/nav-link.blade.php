@props([
    'href' => '',           // Required: Route URL
    'icon' => '',           // Optional: SVG icon markup
    'permission' => null,   // Optional: Permission required to view link
    'badge' => null,        // Optional: Badge count or text
    'activeRoute' => null,  // Optional: Single route pattern for active state
    'activeRoutes' => [],   // Optional: Array of route patterns for active state
    'external' => false,    // Optional: Whether link is external
])

@php
    $isVisible = !$permission || (auth()->check() && auth()->user()->hasPermissionTo($permission));
    $isActive = false;
    
    // Check activeRoutes array first
    if (!empty($activeRoutes)) {
        foreach ($activeRoutes as $pattern) {
            if (request()->route() && method_exists(request()->route(), 'named') && request()->route()->named($pattern)) {
                $isActive = true;
                break;
            }
            if (str_starts_with(request()->path(), $pattern)) {
                $isActive = true;
                break;
            }
        }
    }
    // Check single activeRoute for backward compatibility
    elseif ($activeRoute) {
        if (request()->route() && method_exists(request()->route(), 'named') && request()->route()->named($activeRoute)) {
            $isActive = true;
        }
        if (str_starts_with(request()->path(), $activeRoute)) {
            $isActive = true;
        }
    }
    // Check URL match as fallback
    elseif (request()->url() === $href) {
        $isActive = true;
    }
@endphp

@if($isVisible)
    <a href="{{ $href }}" 
       @if(!$external) target="_self" @endif
       class="menu-item group flex items-center rounded-lg px-3 py-2 text-sm font-medium transition-colors duration-200
{{ $isActive ? 
                  'menu-item-active bg-blue-50 text-blue-600 dark:bg-blue-900/50 dark:text-blue-400' : 
                  'menu-item-inactive text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800' }}"       
       aria-current="{{ $isActive ? 'page' : 'false' }}"
       role="link"
       tabindex="0">
         
        @if($icon)
            <span class="menu-item-icon mr-3 h-8 w-8 flex-shrink-0 {{ $isActive ? 'menu-item-icon-active' : 'menu-item-icon-inactive' }}">
                <svg class="h-8 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="{{ $icon }}" />
                </svg>
            </span>
        @endif
        
        <span class="menu-item-text flex-1">{{ $slot }}</span>
        
        @if($badge)
            <span class="ml-auto inline-flex items-center rounded-full bg-red-100 px-2 py-0.5 text-xs font-medium text-red-800 dark:bg-red-900 dark:text-red-200">
                {{ $badge }}
            </span>
        @endif
    </a>
@endif