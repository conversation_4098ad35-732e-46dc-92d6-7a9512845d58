@props([
    'session' => null,
])

@php
    $session = $session ?? request()->session();
    $messages = [
        'success' => $session->get('success'),
        'error' => $session->get('error'),
        'warning' => $session->get('warning'),
        'info' => $session->get('info'),
    ];
@endphp

@if ($messages['success'] || $messages['error'] || $messages['warning'] || $messages['info'])
    <div class="fixed top-4 right-4 z-50 space-y-4 max-w-sm w-full">
        @foreach ($messages as $type => $message)
            @if ($message)
                <x-notification 
                    type="{{ $type }}" 
                    dismissible="true" 
                    timeout="5000"
                >
                    {{ $message }}
                </x-notification>
            @endif
        @endforeach
    </div>
@endif
