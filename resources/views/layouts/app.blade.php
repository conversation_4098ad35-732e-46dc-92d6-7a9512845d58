<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="dark">
<head>
    @include('partials.head')
</head>
<body class="font-sans antialiased bg-gray-50 dark:bg-gray-900">
    <div id="app" class="min-h-screen">
        <!-- Flash Messages -->
        <x-flash-messages />

        <!-- Header -->
        @include('layouts.app.header')

        <!-- Sidebar -->
        @include('layouts.app.sidebar')

        <!-- Main Content -->
        <main class="lg:pl-64">
            <div class="py-6">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    {{ $slot }}
                </div>
            </div>
        </main>
    </div>

    <!-- Livewire Scripts -->
    @livewireScripts

    <!-- Alpine.js for sidebar toggle -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Handle sidebar toggle
            window.addEventListener('sidebar-toggle', function() {
                const sidebar = document.querySelector('aside');
                if (sidebar) {
                    sidebar.classList.toggle('-translate-x-full');
                }
            });

            // Close sidebar when clicking outside on mobile
            document.addEventListener('click', function(event) {
                const sidebar = document.querySelector('aside');
                const sidebarToggle = document.querySelector('[data-sidebar-toggle]');
                
                if (window.innerWidth < 1024 && 
                    sidebar && 
                    !sidebar.contains(event.target) && 
                    !sidebarToggle?.contains(event.target) &&
                    !sidebar.classList.contains('-translate-x-full')) {
                    
                    const toggleEvent = new CustomEvent('sidebar-toggle');
                    window.dispatchEvent(toggleEvent);
                }
            });
        });
    </script>
</body>
</html>
