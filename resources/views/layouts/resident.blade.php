<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{{ config('app.name', 'Water Management') }} - Resident Portal</title>
    
    <!-- Fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap">
    
    <!-- Scripts -->
    <script src="{{ asset('js/app.js') }}" defer></script>
    
    <!-- Styles -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    
    <!-- Livewire Styles -->
    @livewireStyles
</head>
<body class="font-sans antialiased bg-gray-50">
    <div id="app">
        <!-- Navigation -->
        <header class="sticky top-0 z-40 flex h-16 items-center border-b border-gray-200 bg-white px-4 shadow-sm dark:border-gray-800 dark:bg-gray-900 sm:px-6">
            <div class="flex grow flex-col items-center justify-between lg:flex-row lg:px-6">
                <div class="flex w-full items-center justify-between gap-2 border-b border-gray-200 px-3 py-3 sm:gap-4 lg:justify-normal lg:border-b-0 lg:px-0 lg:py-4 dark:border-gray-800">
                    <!-- Logo -->
                    <div class="flex items-center space-x-3">
                        <x-application-logo class="h-8 w-auto" />
                        <span class="text-lg font-semibold text-gray-800 dark:text-white/90">
                            Resident Portal
                        </span>
                    </div>
                    
                    <!-- Mobile menu button -->
                    <button class="z-99999 flex h-10 w-10 items-center justify-center rounded-lg border-gray-200 text-gray-500 lg:hidden dark:border-gray-800 dark:text-gray-400">
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>
                </div>

                <div class="flex w-full items-center justify-between gap-4 px-5 py-4 lg:flex lg:justify-end lg:px-0 lg:shadow-none">
                    <!-- Navigation Links -->
                    <div class="hidden items-center space-x-1 md:flex">
                        <a href="{{ route('resident.dashboard') }}" class="inline-flex items-center rounded-lg px-3 py-2 text-sm font-medium transition-colors duration-200 {{ request()->routeIs('resident.dashboard') ? 'bg-blue-100 text-blue-700 dark:bg-blue-900/50 dark:text-blue-400' : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800' }}">
                            Dashboard
                        </a>
                        <a href="{{ route('resident.invoices') }}" class="inline-flex items-center rounded-lg px-3 py-2 text-sm font-medium transition-colors duration-200 {{ request()->routeIs('resident.invoices*') ? 'bg-blue-100 text-blue-700 dark:bg-blue-900/50 dark:text-blue-400' : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800' }}">
                            Invoices
                        </a>
                        <a href="{{ route('resident.readings') }}" class="inline-flex items-center rounded-lg px-3 py-2 text-sm font-medium transition-colors duration-200 {{ request()->routeIs('resident.readings*') ? 'bg-blue-100 text-blue-700 dark:bg-blue-900/50 dark:text-blue-400' : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800' }}">
                            Readings
                        </a>
                        <a href="{{ route('resident.messages') }}" class="inline-flex items-center rounded-lg px-3 py-2 text-sm font-medium transition-colors duration-200 {{ request()->routeIs('resident.messages*') ? 'bg-blue-100 text-blue-700 dark:bg-blue-900/50 dark:text-blue-400' : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800' }}">
                            Messages
                        </a>
                        <a href="{{ route('resident.inquiry') }}" class="inline-flex items-center rounded-lg px-3 py-2 text-sm font-medium transition-colors duration-200 {{ request()->routeIs('resident.inquiry') ? 'bg-blue-100 text-blue-700 dark:bg-blue-900/50 dark:text-blue-400' : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800' }}">
                            Contact Us
                        </a>
                    </div>
                    
                    <!-- User Menu -->
                    <div class="flex items-center space-x-4">
                        <!-- House Selector -->
                        @if (auth()->user()->contacts()->count() > 1)
                            <livewire:resident.navigation />
                        @endif
                        
                        <!-- User Dropdown -->
                        <div class="relative" x-data="{ open: false }">
                            <button 
                                @click="open = !open"
                                class="flex h-10 w-10 items-center justify-center rounded-full bg-gray-100 text-gray-800 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700"
                            >
                                {{ auth()->user()->initials() }}
                            </button>

                            <div 
                                x-show="open"
                                @click.away="open = false"
                                class="absolute right-0 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none dark:bg-gray-800"
                            >
                                <div class="px-4 py-2 border-b border-gray-200 dark:border-gray-700">
                                    <p class="text-sm font-medium text-gray-900 dark:text-white">{{ auth()->user()->name }}</p>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">{{ auth()->user()->email }}</p>
                                </div>
                                <a 
                                    href="{{ route('settings.profile') }}" 
                                    class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"
                                >
                                    Profile
                                </a>
                                <a 
                                    href="{{ route('settings.appearance') }}" 
                                    class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"
                                >
                                    Settings
                                </a>
                                <form method="POST" action="{{ route('logout') }}">
                                    @csrf
                                    <button 
                                        type="submit"
                                        class="block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"
                                    >
                                        Logout
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
            <!-- Flash Messages -->
            <x-flash-messages />

                                            <!-- Page Content -->
                                            {{ $slot }}
                                        </main>
                                    </div>

                                    <!-- Livewire Scripts -->
                                    @livewireScripts

                                    <!-- User Menu Script -->
                                    <script>
                                        document.addEventListener('DOMContentLoaded', function() {
                                            const userMenuButton = document.getElementById('user-menu-button');
                                            const userMenu = userMenuButton.nextElementSibling;

                                            userMenuButton.addEventListener('click', function() {
                                                const expanded = userMenuButton.getAttribute('aria-expanded') === 'true';
                                                userMenuButton.setAttribute('aria-expanded', !expanded);
                                                userMenu.classList.toggle('hidden');
                                            });

                                            // Close the dropdown when clicking outside
                                            document.addEventListener('click', function(event) {
                                                if (!userMenuButton.contains(event.target) && !userMenu.contains(event.target)) {
                                                    userMenuButton.setAttribute('aria-expanded', 'false');
                                                    userMenu.classList.add('hidden');
                                                }
                                            });
                                        });
                                    </script>
                                </body>
                            </html>
