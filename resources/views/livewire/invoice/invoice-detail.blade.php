<div>
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow">
        <!-- Header -->
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                    Invoice #{{ $invoice->invoice_number }}
                </h3>
                <div class="flex items-center space-x-2">
                    <span class="px-2 py-1 text-xs font-medium rounded-full
                        {{ $invoice->status_color === 'green' ? 'bg-green-100 text-green-800' : '' }}
                        {{ $invoice->status_color === 'yellow' ? 'bg-yellow-100 text-yellow-800' : '' }}
                        {{ $invoice->status_color === 'red' ? 'bg-red-100 text-red-800' : '' }}
                        {{ $invoice->status_color === 'blue' ? 'bg-blue-100 text-blue-800' : '' }}
                        {{ $invoice->status_color === 'gray' ? 'bg-gray-100 text-gray-800' : '' }}">
                        {{ $invoice->status_label }}
                    </span>
                    <button wire:click="downloadPdf" class="text-blue-600 hover:text-blue-800" title="Download PDF">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                    </button>
                    <button wire:click="regeneratePdf" class="text-orange-600 hover:text-orange-800" title="Regenerate PDF">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                        </svg>
                    </button>
                    <button wire:click="$dispatch('closeModal')" class="text-gray-400 hover:text-gray-500">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- Invoice Details -->
        <div class="px-6 py-4">
            <div class="grid grid-cols-2 gap-4 mb-6">
                <div>
                    <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Period</h4>
                    <p class="text-gray-900 dark:text-gray-100">
                        {{ optional($invoice->billing_period_start)->format('M d, Y') ?? 'N/A' }} - 
                        {{ optional($invoice->billing_period_end)->format('M d, Y') ?? 'N/A' }}
                    </p>
                </div>
                <div>
                    <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Due Date</h4>
                    <p class="text-gray-900 dark:text-gray-100">{{ optional($invoice->due_date)->format('M d, Y') ?? 'N/A' }}</p>
                </div>
                <div>
                    <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Rate per Unit</h4>
                    <p class="text-gray-900 dark:text-gray-100">KES {{ number_format($invoice->rate_per_unit, 2) }}</p>
                </div>
                <div>
                    <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Fixed Charge</h4>
                    <p class="text-gray-900 dark:text-gray-100">KES {{ number_format($invoice->fixed_charge, 2) }}</p>
                </div>
            </div>

            <!-- Line Items -->
            <div class="mb-6">
                <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">Invoice Items</h4>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead class="bg-gray-50 dark:bg-gray-700">
                            <tr>
                                <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Description</th>
                                <th class="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Quantity</th>
                                <th class="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Rate</th>
                                <th class="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Amount</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                            @foreach($invoice->lineItems as $item)
                                <tr>
                                    <td class="px-3 py-2 text-sm text-gray-900 dark:text-gray-100">{{ $item->description }}</td>
                                    <td class="px-3 py-2 text-sm text-right text-gray-900 dark:text-gray-100">{{ number_format($item->quantity, 2) }}</td>
                                    <td class="px-3 py-2 text-sm text-right text-gray-900 dark:text-gray-100">KES {{ number_format($item->rate, 2) }}</td>
                                    <td class="px-3 py-2 text-sm text-right text-gray-900 dark:text-gray-100">KES {{ number_format($item->amount, 2) }}</td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Totals -->
            <div class="border-t border-gray-200 dark:border-gray-700 pt-4">
                <div class="flex justify-end">
                    <div class="w-64">
                        <div class="flex justify-between py-1">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Subtotal:</span>
                            <span class="text-sm text-gray-900 dark:text-gray-100">KES {{ number_format($invoice->getSubtotalAttribute(), 2) }}</span>
                        </div>
                        @if($invoice->getTotalAdjustmentsAttribute() != 0)
                            <div class="flex justify-between py-1">
                                <span class="text-sm text-gray-600 dark:text-gray-400">Adjustments:</span>
                                <span class="text-sm text-gray-900 dark:text-gray-100">KES {{ number_format($invoice->getTotalAdjustmentsAttribute(), 2) }}</span>
                            </div>
                        @endif
                        <div class="flex justify-between py-1 border-t border-gray-200 dark:border-gray-700">
                            <span class="text-sm font-medium text-gray-900 dark:text-gray-100">Total:</span>
                            <span class="text-sm font-medium text-gray-900 dark:text-gray-100">KES {{ number_format($invoice->total_amount, 2) }}</span>
                        </div>
                        <div class="flex justify-between py-1">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Amount Paid:</span>
                            <span class="text-sm text-gray-900 dark:text-gray-100">KES {{ number_format($invoice->getTotalPaidAttribute(), 2) }}</span>
                        </div>
                        <div class="flex justify-between py-1 border-t border-gray-200 dark:border-gray-700">
                            <span class="text-sm font-medium text-gray-900 dark:text-gray-100">Balance Due:</span>
                            <span class="text-sm font-medium text-gray-900 dark:text-gray-100">KES {{ number_format($invoice->getBalanceDueAttribute(), 2) }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="mt-6 flex justify-between items-center">
                <div class="flex space-x-3">
                    <button wire:click="downloadPdf" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        Download PDF
                    </button>
                    <button wire:click="regeneratePdf" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                        </svg>
                        Regenerate PDF
                    </button>
                    @if($invoice->status === 'draft' && auth()->user()->role === 'manager')
                        <button wire:click="submitForApproval" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                            </svg>
                            Submit for Approval
                        </button>
                    @endif

                    @if($invoice->status === 'submitted' && auth()->user()->role === 'reviewer')
                        <button wire:click="approve" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Approve
                        </button>
                        <button wire:click="$set('modalState', 'rejection')" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                            Reject
                        </button>
                    @endif

                    @if($invoice->status === 'approved' && auth()->user()->role === 'manager')
                        <button wire:click="sendInvoice" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                            </svg>
                            Send Invoice
                        </button>
                    @endif
                </div>
                @if($invoice->balance_due > 0)
                    <div class="flex space-x-3">
                        <button wire:click="openPaymentModal" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700">
                            Record Payment
                        </button>
                        <button wire:click="openAdjustmentModal" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            Add Adjustment
                        </button>
                    </div>
                @endif
            </div>
        </div>
    </div>

            <!-- Payment History -->
            @if($invoice->payments->count() > 0)
                <div class="mt-6">
                    <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">Payment History</h4>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                            <thead class="bg-gray-50 dark:bg-gray-700">
                                <tr>
<th class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Date>
                                    <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Method</th>
                                    <th class="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Amount>
                                    <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Reference</th>
                                    <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Notes</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                @foreach($invoice->payments as $payment)
                                    <tr>
                                        <td class="px-3 py-2 text-sm text-gray-900 dark:text-gray-100">{{ $payment->payment_date->format('M d, Y') }}</td>
                                        <td class="px-3 py-2 text-sm text-gray-900 dark:text-gray-100">{{ ucfirst($payment->payment_method) }}</td>
                                        <td class="px-3 py-2 text-sm text-right text-gray-900 dark:text-gray-100">KES {{ number_format($payment->amount, 2) }}</td>
                                        <td class="px-3 py-2 text-sm text-gray-900 dark:text-gray-100">{{ $payment->reference_number }}</td>
                                        <td class="px-3 py-2 text-sm text-gray-900 dark:text-gray-100">{{ $payment->notes ?: '-' }}</td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            @endif

            <!-- Adjustment History -->
            @if(is_iterable($invoice->adjustments) && $invoice->adjustments instanceof \Illuminate\Database\Eloquent\Collection && $invoice->adjustments->count() > 0)
                <div class="mt-6">
                    <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">Adjustment History</h4>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                            <thead class="bg-gray-50 dark:bg-gray-700">
                                <tr>
                                    <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Date>
                                    <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Type>
                                    <th class="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Amount</th>
                                    <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Description>
                                    <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Reference</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                @foreach($invoice->adjustments as $adjustment)
                                    <tr>
                                        <td class="px-3 py-2 text-sm text-gray-900 dark:text-gray-100">{{ $adjustment->adjustment_date->format('M d, Y') }}</td>
                                        <td class="px-3 py-2 text-sm text-gray-900 dark:text-gray-100">{{ ucfirst($adjustment->type) }}</td>
                                        <td class="px-3 py-2 text-sm text-right text-gray-900 dark:text-gray-100">{{ $adjustment->formatted_amount }}</td>
                                        <td class="px-3 py-2 text-sm text-gray-900 dark:text-gray-100">{{ $adjustment->description }}</td>
                                        <td class="px-3 py-2 text-sm text-gray-900 dark:text-gray-100">{{ $adjustment->reference_number }}</td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            @endif

    <!-- Payment Modal -->
    @if($modalState === 'payment')
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
            <div class="bg-white dark:bg-gray-800 rounded-lg max-w-md w-full mx-4">
                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">Record Payment</h3>
                </div>
                <form wire:submit="savePayment">
                    <div class="px-6 py-4 space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Amount</label>
                            <input type="number" 
                                   wire:model="paymentAmount" 
                                   step="0.01" 
                                   min="0.01" 
                                   max="{{ $invoice->balance_due }}"
                                   class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-100">
                            @error('paymentAmount') <span class="text-red-600 text-sm">{{ $message }}</span> @enderror
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Payment Date</label>
                            <input type="date" 
                                   wire:model="paymentDate" 
                                   class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-100">
                            @error('paymentDate') <span class="text-red-600 text-sm">{{ $message }}</span> @enderror
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Payment Method</label>
                            <select wire:model="paymentMethod" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-100">
                                <option value="">Select method</option>
                                <option value="cash">Cash</option>
                                <option value="mpesa">M-Pesa</option>
                                <option value="bank_transfer">Bank Transfer</option>
                                <option value="cheque">Cheque</option>
                            </select>
                            @error('paymentMethod') <span class="text-red-600 text-sm">{{ $message }}</span> @enderror
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Notes</label>
                            <textarea wire:model="paymentNotes" 
                                      rows="3" 
                                      class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-100"></textarea>
                            @error('paymentNotes') <span class="text-red-600 text-sm">{{ $message }}</span> @enderror
                        </div>
                    </div>
                    <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700 flex justify-end space-x-3">
                        <button type="button" 
                                wire:click="closeModal" 
                                class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                            Cancel
                        </button>
                        <button type="submit" 
                                class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700">
                            Save Payment
                        </button>
                    </div>
                </form>
            </div>
        </div>
    @endif

    <!-- Adjustment Modal -->
    @if($modalState === 'adjustment')
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
            <div class="bg-white dark:bg-gray-800 rounded-lg max-w-md w-full mx-4">
                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">Add Adjustment</h3>
                </div>
                <form wire:submit="saveAdjustment">
                    <div class="px-6 py-4 space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Adjustment Type</label>
                            <select wire:model="adjustmentType" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-100">
                                <option value="">Select type</option>
                                <option value="discount">Discount</option>
                                <option value="penalty">Penalty</option>
                                <option value="correction">Correction</option>
                                <option value="other">Other</option>
                            </select>
                            @error('adjustmentType') <span class="text-red-600 text-sm">{{ $message }}</span> @enderror
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Amount</label>
                            <input type="number" 
                                   wire:model="adjustmentAmount" 
                                   step="0.01" 
                                   class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-100">
                            @error('adjustmentAmount') <span class="text-red-600 text-sm">{{ $message }}</span> @enderror
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Description</label>
                            <textarea wire:model="adjustmentDescription" 
                                      rows="3" 
                                      class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-100"
                                      placeholder="Enter reason for adjustment"></textarea>
                            @error('adjustmentDescription') <span class="text-red-600 text-sm">{{ $message }}</span> @enderror
                        </div>
                    </div>
                    <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700 flex justify-end space-x-3">
                        <button type="button" 
                                wire:click="closeModal" 
                                class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                            Cancel
                        </button>
                        <button type="submit" 
                                class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">
                            Save Adjustment
                        </button>
                    </div>
                </form>
            </div>
        </div>
    @endif

    <!-- Rejection Modal -->
    @if($modalState === 'rejection')
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
            <div class="bg-white dark:bg-gray-800 rounded-lg max-w-md w-full mx-4">
                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">Reject Invoice</h3>
                </div>
                <form wire:submit="reject">
                    <div class="px-6 py-4 space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Rejection Reason</label>
                            <textarea wire:model="rejectionReason" 
                                      rows="4" 
                                      class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-100"
                                      placeholder="Please provide a reason for rejecting this invoice..."></textarea>
                            @error('rejectionReason') <span class="text-red-600 text-sm">{{ $message }}</span> @enderror
                        </div>
                    </div>
                    <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700 flex justify-end space-x-3">
                        <button type="button" 
                                wire:click="closeModal" 
                                class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                            Cancel
                        </button>
                        <button type="submit" 
                                class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700">
                            Reject Invoice
                        </button>
                    </div>
                </form>
            </div>
        </div>
    @endif
</div>
