<div class="bg-white rounded-lg shadow-md p-6">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Payment Management</h2>
        <div class="flex space-x-2">
            @can('payments.export_all')
                <button wire:click="exportPayments" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-md transition duration-200">
                    <i class="fas fa-download mr-2"></i>Export Payments
                </button>
            @endcan
        </div>
    </div>

    <!-- Invoice Information -->
    <div class="bg-gray-50 rounded-lg p-4 mb-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
                <p class="text-sm text-gray-600">Invoice Number</p>
                <p class="font-semibold">{{ $invoice->invoice_number }}</p>
            </div>
            <div>
                <p class="text-sm text-gray-600">Total Amount</p>
                <p class="font-semibold">KES {{ number_format($invoice->total_amount, 2) }}</p>
            </div>
            <div>
                <p class="text-sm text-gray-600">Balance Due</p>
                <p class="font-semibold text-red-600">KES {{ number_format($balanceDue, 2) }}</p>
            </div>
        </div>
    </div>

    <!-- Payment Actions -->
    <div class="flex flex-wrap gap-2 mb-6">
        @can('payments.process')
            <button wire:click="$set('showPaymentForm', true)" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md transition duration-200">
                <i class="fas fa-plus mr-2"></i>Process Payment
            </button>
        @endcan

        @can('payments.refund')
            <button wire:click="showRefundModal" class="bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-md transition duration-200">
                <i class="fas fa-undo mr-2"></i>Process Refund
            </button>
        @endcan

        @can('payments.adjust')
            <button wire:click="showAdjustmentModal" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-md transition duration-200">
                <i class="fas fa-edit mr-2"></i>Adjust Payment
            </button>
        @endcan

        <button wire:click="selectAllPayments" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md transition duration-200">
            <i class="fas fa-check-square mr-2"></i>Select All
        </button>
        <button wire:click="clearSelection" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md transition duration-200">
            <i class="fas fa-times mr-2"></i>Clear Selection
        </button>
    </div>

    <!-- Payment Table -->
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        <input type="checkbox" wire:model="selectedPayments" wire:change="togglePaymentSelection(selectedPayments)" class="rounded border-gray-300">
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" wire:click="sortPayments('payment_date')">
                        Payment Date {{ $sortColumn === 'payment_date' ? ($sortDirection === 'asc' ? '↑' : '↓') : '' }}
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" wire:click="sortPayments('amount')">
                        Amount {{ $sortColumn === 'amount' ? ($sortDirection === 'asc' ? '↑' : '↓') : '' }}
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" wire:click="sortPayments('payment_method')">
                        Method {{ $sortColumn === 'payment_method' ? ($sortDirection === 'asc' ? '↑' : '↓') : '' }}
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" wire:click="sortPayments('payment_status')">
                        Status {{ $sortColumn === 'payment_status' ? ($sortDirection === 'asc' ? '↑' : '↓') : '' }}
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        User
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Notes
                    </th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                @forelse ($payments as $payment)
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <input type="checkbox" value="{{ $payment->id }}" wire:model="selectedPayments" wire:change="togglePaymentSelection({{ $payment->id }})" class="rounded border-gray-300">
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {{ $payment->payment_date->format('Y-m-d H:i') }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            KES {{ number_format($payment->amount, 2) }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {{ $payment->payment_method }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                {{ $payment->payment_status === 'completed' ? 'bg-green-100 text-green-800' : 
                                   $payment->payment_status === 'pending' ? 'bg-yellow-100 text-yellow-800' : 
                                   $payment->payment_status === 'failed' ? 'bg-red-100 text-red-800' : 
                                   'bg-gray-100 text-gray-800' }}">
                                {{ ucfirst($payment->payment_status) }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {{ $payment->user->name ?? 'N/A' }}
                        </td>
                        <td class="px-6 py-4 text-sm text-gray-900 max-w-xs truncate">
                            {{ $payment->notes }}
                        </td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="7" class="px-6 py-4 text-center text-sm text-gray-500">
                            No payments found for this invoice.
                        </td>
                    </tr>
                @endforelse
            </tbody>
        </table>
    </div>

    <!-- Summary -->
    <div class="mt-6 bg-gray-50 rounded-lg p-4">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <p class="text-sm text-gray-600">Total Payments</p>
                <p class="font-semibold text-green-600">KES {{ number_format($totalPaid, 2) }}</p>
            </div>
            <div>
                <p class="text-sm text-gray-600">Balance Due</p>
                <p class="font-semibold text-red-600">KES {{ number_format($balanceDue, 2) }}</p>
            </div>
        </div>
    </div>

    <!-- Refund Modal -->
    @if ($showRefundForm)
        <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                <div class="mt-3">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Process Refund</h3>
                    <form wire:submit.prevent="processRefund">
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Refund Amount</label>
                            <input type="number" step="0.01" min="0.01" max="{{ $totalPaid }}" wire:model.live="refundAmount" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            @error('refundAmount')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Refund Reason</label>
                            <textarea wire:model.live="refundReason" rows="3" 
                                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                            @error('refundReason')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                        <div class="flex justify-end space-x-2">
                            <button type="button" wire:click="$set('showRefundForm', false)" 
                                    class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition duration-200">
                                Cancel
                            </button>
                            <button type="submit" 
                                    class="px-4 py-2 bg-yellow-500 text-white rounded-md hover:bg-yellow-600 transition duration-200">
                                Process Refund
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    @endif

    <!-- Adjustment Modal -->
    @if ($showAdjustmentForm)
        <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                <div class="mt-3">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Adjust Payment</h3>
                    <form wire:submit.prevent="processAdjustment">
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Adjustment Amount</label>
                            <input type="number" step="0.01" min="-999999.99" max="999999.99" wire:model.live="adjustmentAmount" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            @error('adjustmentAmount')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Adjustment Reason</label>
                            <textarea wire:model.live="adjustmentReason" rows="3" 
                                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                            @error('adjustmentReason')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                        <div class="flex justify-end space-x-2">
                            <button type="button" wire:click="$set('showAdjustmentForm', false)" 
                                    class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition duration-200">
                                Cancel
                            </button>
                            <button type="submit" 
                                    class="px-4 py-2 bg-purple-500 text-white rounded-md hover:bg-purple-600 transition duration-200">
                                Process Adjustment
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    @endif
</div>
