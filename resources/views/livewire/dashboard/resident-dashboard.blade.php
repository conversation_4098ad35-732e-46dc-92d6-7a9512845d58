<div>
    <!-- House Selector -->
    @if(count($userHouses) > 1)
        <div class="bg-white rounded-lg shadow p-4 mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-2">Select House</label>
            <select wire:model.live="selectedHouseId" class="w-full md:w-auto rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                @foreach($userHouses as $house)
                    <option value="{{ $house['id'] }}">{{ $house['house_number'] }} - {{ $house['estate_name'] }}</option>
                @endforeach
            </select>
        </div>
    @endif

    @if($currentHouse)
        <!-- Current House Info -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <div class="flex items-center justify-between">
                <div>
                    <h2 class="text-xl font-bold text-gray-900">{{ $currentHouse->house_number }}</h2>
                    <p class="text-gray-600">{{ $currentHouse->address }}, {{ $currentHouse->estate->name }}</p>
                </div>
                <div class="text-right">
                    <div class="text-sm text-gray-500">Status</div>
                    <div class="text-lg font-medium {{ $currentHouse->status == 'occupied' ? 'text-green-600' : 'text-gray-600' }}">
                        {{ ucfirst($currentHouse->status) }}
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div class="bg-white rounded-lg shadow p-4">
                <div class="text-2xl font-bold text-red-600">{{ $unpaidInvoicesCount }}</div>
                <div class="text-sm text-gray-600">Unpaid Invoices</div>
                @if($totalUnpaidAmount > 0)
                    <div class="text-sm text-red-600 mt-1">KES {{ number_format($totalUnpaidAmount, 2) }}</div>
                @endif
            </div>
            <div class="bg-white rounded-lg shadow p-4">
                <div class="text-2xl font-bold text-blue-600">{{ $unreadMessagesCount }}</div>
                <div class="text-sm text-gray-600">Unread Messages</div>
                @if($unreadMessagesCount > 0)
                    <button wire:click="markMessagesAsRead" class="text-sm text-blue-600 hover:text-blue-800 mt-1">Mark as read</button>
                @endif
            </div>
            <div class="bg-white rounded-lg shadow p-4">
                <div class="text-2xl font-bold text-green-600">{{ number_format($consumptionStats['average_consumption'], 1) }}L</div>
                <div class="text-sm text-gray-600">Avg Consumption</div>
                <div class="text-sm {{ $consumptionStats['trend'] == 'increasing' ? 'text-red-600' : ($consumptionStats['trend'] == 'decreasing' ? 'text-green-600' : 'text-gray-600') }} mt-1">
                    {{ $consumptionStats['trend'] }} {{ $consumptionStats['change_percentage'] }}%
                </div>
            </div>
        </div>

        <!-- Latest Invoice -->
        @if($latestInvoice)
            <div class="bg-white rounded-lg shadow p-6 mb-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Latest Invoice</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <div class="text-sm text-gray-500">Invoice Number</div>
                        <div class="font-medium">{{ $latestInvoice->invoice_number }}</div>
                    </div>
                    <div>
                        <div class="text-sm text-gray-500">Amount</div>
                        <div class="font-medium">KES {{ number_format($latestInvoice->total_amount, 2) }}</div>
                    </div>
                    <div>
                        <div class="text-sm text-gray-500">Due Date</div>
                        <div class="font-medium">{{ $latestInvoice->due_date->format('M d, Y') }}</div>
                    </div>
                    <div>
                        <div class="text-sm text-gray-500">Status</div>
                        <div class="font-medium">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                @if($latestInvoice->status == 'paid') bg-green-100 text-green-800
                                @elseif($latestInvoice->status == 'overdue') bg-red-100 text-red-800
                                @else bg-yellow-100 text-yellow-800
                                @endif">
                                {{ ucfirst($latestInvoice->status) }}
                            </span>
                        </div>
                    </div>
                </div>
                <div class="mt-4">
                    <button wire:click="downloadInvoice({{ $latestInvoice->id }})" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                        Download Invoice
                    </button>
                </div>
            </div>
        @endif

        <!-- Latest Reading -->
        @if($latestReading)
            <div class="bg-white rounded-lg shadow p-6 mb-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Latest Meter Reading</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <div class="text-sm text-gray-500">Reading Date</div>
                        <div class="font-medium">{{ $latestReading->reading_date->format('M d, Y') }}</div>
                    </div>
                    <div>
                        <div class="text-sm text-gray-500">Meter Reading</div>
                        <div class="font-medium">{{ number_format($latestReading->reading_value, 1) }} L</div>
                    </div>
                    <div>
                        <div class="text-sm text-gray-500">Consumption</div>
                        <div class="font-medium">{{ number_format($latestReading->consumption, 1) }} L</div>
                    </div>
                </div>
            </div>
        @endif

        <!-- Recent Activity Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Recent Invoices -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Recent Invoices</h3>
                </div>
                <div class="p-6">
                    @if($recentInvoices->count() > 0)
                        <div class="space-y-4">
                            @foreach($recentInvoices as $invoice)
                                <div class="flex items-center justify-between">
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">{{ $invoice->invoice_number }}</div>
                                        <div class="text-sm text-gray-500">{{ $invoice->invoice_date->format('M d, Y') }}</div>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-sm font-medium text-gray-900">KES {{ number_format($invoice->total_amount, 2) }}</div>
                                        <div class="text-sm">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                                @if($invoice->status == 'paid') bg-green-100 text-green-800
                                                @elseif($invoice->status == 'overdue') bg-red-100 text-red-800
                                                @else bg-yellow-100 text-yellow-800
                                                @endif">
                                                {{ ucfirst($invoice->status) }}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <p class="text-gray-500 text-center py-8">No invoices found</p>
                    @endif
                </div>
            </div>

            <!-- Recent Readings -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Recent Readings</h3>
                </div>
                <div class="p-6">
                    @if($recentReadings->count() > 0)
                        <div class="space-y-4">
                            @foreach($recentReadings as $reading)
                                <div class="flex items-center justify-between">
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">{{ $reading->reading_date->format('M d, Y') }}</div>
                                        <div class="text-sm text-gray-500">Meter: {{ number_format($reading->reading_value, 1) }}L</div>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-sm font-medium text-gray-900">{{ number_format($reading->consumption, 1) }}L</div>
                                        <div class="text-sm text-gray-500">Consumption</div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <p class="text-gray-500 text-center py-8">No readings found</p>
                    @endif
                </div>
            </div>
        </div>

        <!-- Unread Messages -->
        @if($unreadMessages->count() > 0)
            <div class="bg-white rounded-lg shadow mt-6">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Recent Messages</h3>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        @foreach($unreadMessages as $message)
                            <div class="border-l-4 border-blue-500 pl-4">
                                <div class="text-sm font-medium text-gray-900">{{ $message->created_at->format('M d, H:i') }}</div>
                                <div class="text-sm text-gray-700">{{ $message->message }}</div>
                            </div>
                        @endforeach
                    </div>
                    <div class="mt-4">
                        <button wire:click="markMessagesAsRead" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                            Mark All as Read
                        </button>
                    </div>
                </div>
            </div>
        @endif
    @else
        <div class="bg-white rounded-lg shadow p-8 text-center">
            <div class="text-gray-500">No house selected. Please select a house to view your dashboard.</div>
        </div>
    @endif
</div>