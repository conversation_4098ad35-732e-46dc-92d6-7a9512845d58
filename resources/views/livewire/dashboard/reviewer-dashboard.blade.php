<div>
    <!-- Reviewer Stats -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div class="bg-white rounded-lg shadow p-4">
            <div class="text-2xl font-bold text-orange-600">{{ $reviewerStats['pending_review'] }}</div>
            <div class="text-sm text-gray-600">Pending Review</div>
        </div>
        <div class="bg-white rounded-lg shadow p-4">
            <div class="text-2xl font-bold text-green-600">{{ $reviewerStats['approved_today'] }}</div>
            <div class="text-sm text-gray-600">Approved Today</div>
        </div>
        <div class="bg-white rounded-lg shadow p-4">
            <div class="text-2xl font-bold text-red-600">{{ $reviewerStats['rejected_today'] }}</div>
            <div class="text-sm text-gray-600">Rejected Today</div>
        </div>
        <div class="bg-white rounded-lg shadow p-4">
            <div class="text-2xl font-bold text-blue-600">{{ $reviewerStats['this_month'] }}</div>
            <div class="text-sm text-gray-600">This Month</div>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-lg shadow p-4 mb-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                <select wire:model.live="reviewerStatus" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                    <option value="all">All Status</option>
                    <option value="submitted">Submitted</option>
                    <option value="approved">Approved</option>
                    <option value="rejected">Rejected</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Date Range</label>
                <select wire:model.live="dateRange" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                    <option value="today">Today</option>
                    <option value="this_week">This Week</option>
                    <option value="this_month">This Month</option>
                    <option value="last_month">Last Month</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Search</label>
                <input 
                    type="text" 
                    wire:model.live="search" 
                    placeholder="Search house number or address..."
                    class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                >
            </div>
        </div>
    </div>

    <!-- Batch Actions -->
    <div class="bg-white rounded-lg shadow p-4 mb-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <input type="checkbox" wire:model.live="selectAll" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                <span class="text-sm text-gray-700">Select All</span>
                <button wire:click="openBatchModal" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50" disabled="{{ empty($selectedReadings) }}">
                    Batch Process ({{ count($selectedReadings) }})
                </button>
            </div>
        </div>
    </div>

    <!-- Anomalous Readings Alert -->
    @if($anomalousReadings->count() > 0)
        <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <h3 class="text-lg font-medium text-red-800 mb-2">Anomalous Readings Detected</h3>
            <p class="text-red-700 mb-3">{{ $anomalousReadings->count() }} readings require attention:</p>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
                @foreach($anomalousReadings->take(4) as $reading)
                    <div class="bg-white rounded p-2 text-sm">
                        <span class="font-medium">{{ $reading->house->house_number }}</span>
                        <span class="text-gray-600">- {{ number_format($reading->consumption, 1) }}L</span>
                        <span class="ml-2 px-2 py-1 text-xs rounded-full {{ getRiskLevelBadgeClass(isAnomalous($reading) ? getValidationResult($reading)['risk_level'] : 'low') }}">
                            {{ isAnomalous($reading) ? getValidationResult($reading)['risk_level'] : 'low' }}
                        </span>
                    </div>
                @endforeach
            </div>
        </div>
    @endif

    <!-- Readings Table -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Meter Readings Review</h3>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            <input type="checkbox" wire:model.live="selectAll" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">House</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reading</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Consumption</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Submitted By</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @foreach($reviewerReadings as $reading)
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" wire:model="selectedReadings" value="{{ $reading->id }}" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">{{ $reading->house->house_number }}</div>
                                <div class="text-sm text-gray-500">{{ $reading->house->address }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ number_format($reading->reading_value, 1) }} L
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ number_format($reading->consumption, 1) }} L
                                @if(isAnomalous($reading))
                                    <span class="ml-2 px-2 py-1 text-xs rounded-full {{ getRiskLevelBadgeClass(getValidationResult($reading)['risk_level']) }}">
                                        {{ getValidationResult($reading)['risk_level'] }}
                                    </span>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ $reading->user->name }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                    @if($reading->status == 'submitted') bg-yellow-100 text-yellow-800
                                    @elseif($reading->status == 'approved') bg-green-100 text-green-800
                                    @elseif($reading->status == 'rejected') bg-red-100 text-red-800
                                    @endif">
                                    {{ ucfirst($reading->status) }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                @if($reading->status == 'submitted')
                                    <button wire:click="openReviewModal({{ $reading->id }})" 
                                            class="text-blue-600 hover:text-blue-900 mr-3">Review</button>
                                    <button wire:click="openValidationModal({{ $reading->id }})" 
                                            class="text-purple-600 hover:text-purple-900">Validate</button>
                                @endif
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
        <div class="px-6 py-4 border-t border-gray-200">
            {{ $reviewerReadings->links() }}
        </div>
    </div>

    <!-- Review Modal -->
    @if($showReviewModal)
        <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                <div class="mt-3">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Review Reading</h3>
                    @if($currentReading)
                        <div class="mb-4">
                            <p><strong>House:</strong> {{ $currentReading->house->house_number }}</p>
                            <p><strong>Reading:</strong> {{ number_format($currentReading->reading_value, 1) }} L</p>
                            <p><strong>Consumption:</strong> {{ number_format($currentReading->consumption, 1) }} L</p>
                            <p><strong>Submitted By:</strong> {{ $currentReading->user->name }}</p>
                        </div>
                        <form wire:submit.prevent="submitReview">
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Action</label>
                                <select wire:model="reviewAction" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                    <option value="approve">Approve</option>
                                    <option value="reject">Reject</option>
                                </select>
                            </div>
                            @if($reviewAction == 'reject')
                                <div class="mb-4">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Reason</label>
                                    <textarea wire:model="reviewNotes" rows="3" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" placeholder="Please provide reason for rejection..."></textarea>
                                </div>
                            @endif
                            <div class="flex justify-end space-x-3">
                                <button type="button" wire:click="closeReviewModal" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">Cancel</button>
                                <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">{{ $reviewAction == 'approve' ? 'Approve' : 'Reject' }}</button>
                            </div>
                        </form>
                    @endif
                </div>
            </div>
        </div>
    @endif

    <!-- Batch Review Modal -->
    @if($showBatchModal)
        <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                <div class="mt-3">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Batch Review ({{ count($selectedReadings) }} readings)</h3>
                    <form wire:submit.prevent="submitBatchReview">
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Action</label>
                            <select wire:model="batchAction" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <option value="approve">Approve All</option>
                                <option value="reject">Reject All</option>
                            </select>
                        </div>
                        @if($batchAction == 'reject')
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Reason</label>
                                <textarea wire:model="batchNotes" rows="3" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" placeholder="Please provide reason for rejection..."></textarea>
                            </div>
                        @endif
                        <div class="flex justify-end space-x-3">
                            <button type="button" wire:click="closeBatchModal" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">Cancel</button>
                            <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">{{ $batchAction == 'approve' ? 'Approve All' : 'Reject All' }}</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    @endif
</div>