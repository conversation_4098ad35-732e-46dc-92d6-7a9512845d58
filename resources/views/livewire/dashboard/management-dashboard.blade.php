<div>
    <!-- KPI Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4 mb-6">
        <div class="bg-white rounded-lg shadow p-4">
            <div class="text-2xl font-bold text-blue-600">{{ number_format($managementKpiData['total_houses']) }}</div>
            <div class="text-sm text-gray-600">Total Houses</div>
        </div>
        <div class="bg-white rounded-lg shadow p-4">
            <div class="text-2xl font-bold text-green-600">{{ number_format($managementKpiData['total_estates']) }}</div>
            <div class="text-sm text-gray-600">Total Estates</div>
        </div>
        <div class="bg-white rounded-lg shadow p-4">
            <div class="text-2xl font-bold text-purple-600">KES {{ number_format($managementKpiData['total_revenue'], 2) }}</div>
            <div class="text-sm text-gray-600">Total Revenue</div>
        </div>
        <div class="bg-white rounded-lg shadow p-4">
            <div class="text-2xl font-bold text-orange-600">{{ number_format($managementKpiData['avg_consumption'], 1) }}L</div>
            <div class="text-sm text-gray-600">Avg Consumption</div>
        </div>
        <div class="bg-white rounded-lg shadow p-4">
            <div class="text-2xl font-bold text-yellow-600">{{ $managementKpiData['pending_readings'] }}</div>
            <div class="text-sm text-gray-600">Pending Readings</div>
        </div>
        <div class="bg-white rounded-lg shadow p-4">
            <div class="text-2xl font-bold text-red-600">{{ $managementKpiData['overdue_invoices'] }}</div>
            <div class="text-sm text-gray-600">Overdue Invoices</div>
        </div>
    </div>

    <!-- Chart Controls -->
    <div class="bg-white rounded-lg shadow p-4 mb-6">
        <div class="flex flex-wrap items-center justify-between gap-4">
            <div class="flex items-center space-x-4">
                <label class="block text-sm font-medium text-gray-700">Chart Type</label>
                <select wire:model.live="chartType" class="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                    <option value="consumption">Consumption</option>
                    <option value="revenue">Revenue</option>
                    <option value="estate_comparison">Estate Comparison</option>
                </select>
            </div>
            <div class="flex items-center space-x-4">
                <label class="block text-sm font-medium text-gray-700">Date Range</label>
                <select wire:model.live="managementDateRange" class="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                    <option value="7">Last 7 days</option>
                    <option value="30">Last 30 days</option>
                    <option value="90">Last 90 days</option>
                    <option value="365">Last year</option>
                </select>
            </div>
            <div class="flex items-center space-x-4">
                <label class="block text-sm font-medium text-gray-700">Export</label>
                <select wire:model="exportFormat" class="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                    <option value="pdf">PDF</option>
                    <option value="excel">Excel</option>
                </select>
            </div>
        </div>
    </div>

    <!-- Chart -->
    <div class="bg-white rounded-lg shadow p-6 mb-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">
            @if($chartType == 'consumption') Consumption Trends
            @elseif($chartType == 'revenue') Revenue Trends
            @else Estate Comparison
            @endif
        </h3>
        <div class="h-64">
            <canvas id="managementChart"></canvas>
        </div>
    </div>

    <!-- Analytics Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <!-- Top Consumers -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Top Consumers</h3>
            </div>
            <div class="p-6">
                @if($topConsumers->count() > 0)
                    <div class="space-y-4">
                        @foreach($topConsumers as $consumer)
                            <div class="flex items-center justify-between">
                                <div>
                                    <div class="text-sm font-medium text-gray-900">{{ $consumer['house']->house_number }}</div>
                                    <div class="text-sm text-gray-500">{{ $consumer['house']->estate->name }}</div>
                                </div>
                                <div class="text-right">
                                    <div class="text-sm font-medium text-gray-900">{{ number_format($consumer['total_consumption'], 1) }}L</div>
                                    <div class="text-sm text-gray-500">{{ number_format($consumer['avg_daily'], 1) }}L/day</div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <p class="text-gray-500 text-center py-8">No consumption data available</p>
                @endif
            </div>
        </div>

        <!-- Recent Readings -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Recent Readings</h3>
            </div>
            <div class="p-6">
                @if($recentReadings->count() > 0)
                    <div class="space-y-4">
                        @foreach($recentReadings as $reading)
                            <div class="flex items-center justify-between">
                                <div>
                                    <div class="text-sm font-medium text-gray-900">{{ $reading->house->house_number }}</div>
                                    <div class="text-sm text-gray-500">{{ $reading->house->estate->name }}</div>
                                </div>
                                <div class="text-right">
                                    <div class="text-sm font-medium text-gray-900">{{ number_format($reading->consumption, 1) }}L</div>
                                    <div class="text-sm text-gray-500">{{ $reading->created_at->format('M d, H:i') }}</div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <p class="text-gray-500 text-center py-8">No recent readings</p>
                @endif
            </div>
        </div>
    </div>

    <!-- Estate Analytics -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Estate Analytics</h3>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Estate</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Houses</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Consumption</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Revenue</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Avg/House</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Occupancy</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @foreach($estateAnalytics as $analytics)
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">{{ $analytics['estate']->name }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ $analytics['total_houses'] }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ number_format($analytics['total_consumption'], 1) }}L
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                KES {{ number_format($analytics['total_revenue'], 2) }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ number_format($analytics['avg_consumption_per_house'], 1) }}L
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-1 bg-gray-200 rounded-full h-2 mr-2">
                                        <div class="bg-green-600 h-2 rounded-full" style="width: {{ $analytics['occupancy_rate'] }}%"></div>
                                    </div>
                                    <span class="text-sm text-gray-600">{{ round($analytics['occupancy_rate']) }}%</span>
                                </div>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>

    @push('scripts')
        <script>
            document.addEventListener('livewire:navigated', function () {
                const ctx = document.getElementById('managementChart').getContext('2d');
                const chartData = @json($managementChartData);
                
                new Chart(ctx, {
                    type: @if($chartType == 'estate_comparison') 'bar' @else 'line' @endif,
                    data: chartData,
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
            });
        </script>
    @endpush
</div>