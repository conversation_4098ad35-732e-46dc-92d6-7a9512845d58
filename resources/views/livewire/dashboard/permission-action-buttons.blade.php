<div>
    @if(!empty($availableActions))
        <div class="flex items-center space-x-2">
            @foreach($availableActions as $action)
                <button 
                    wire:click="performAction('{{ $action['action'] }}')"
                    class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-white bg-{{ $action['color'] }}-600 hover:bg-{{ $action['color'] }}-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-{{ $action['color'] }}-500"
                    title="{{ $action['label'] }}"
                >
                    @if(isset($action['icon']))
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M{{ $action['icon'] }}"></path>
                        </svg>
                    @endif
                    {{ $action['label'] }}
                </button>
            @endforeach
        </div>
    @endif
</div>