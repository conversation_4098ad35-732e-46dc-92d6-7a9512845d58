<div>
    <!-- Permission-based Navigation -->
    {{-- The permission-navigation component needs to be a child of this component or its state needs to be accessible. --}}
    {{-- For simplicity, let's assume this view (base-dashboard.blade.php) is part of a component that also holds the $currentSection state --}}
    {{-- or that permission-navigation emits an event that this component can listen to. --}}
    {{-- A more robust way would be for BaseDashboard.php to also manage $currentSection and pass it down. --}}
    {{-- For now, we'll assume $currentSection is available in this view's component scope. --}}
    {{-- If PermissionNavigation is a sibling or not directly sharing state, this needs adjustment. --}}
    {{-- Let's assume the component rendering this view (e.g. app/Livewire/Dashboard.php) has $currentSection --}}
    {{-- and livewire:dashboard.permission-navigation is also a child of that component, sharing the same $currentSection. --}}
    {{-- If PermissionNavigation is in this file, it would be: --}}
    {{-- <livewire:dashboard.permission-navigation wire:model.live="currentSection" key="nav" /> --}}

    @php
        // This $currentSection should ideally come from the Livewire component rendering this view.
        // If PermissionNavigation is a sibling component, this needs event emission or a shared state manager.
        // For this fix, we'll assume it's available.
        // If the component is app/Livewire/Dashboard.php, it would have:
        // public $currentSection = 'overview'; (and maybe #[\Livewire\Attributes\Modelable])
    @endphp

    <livewire:dashboard.permission-navigation 
        wire:model.live="currentSection" 
        key="nav" 
    />

    <!-- Main Content Area based on selected section -->
    <div>
        <!-- Header -->
        <div class="mb-6">
            <h1 class="text-2xl font-bold text-gray-900">
                {{ isset($navigationItems[$currentSection]) ? $navigationItems[$currentSection]['label'] : 'Dashboard' }}
            </h1>
            <p class="text-gray-600">Welcome back! Here's what's happening with your water management system.</p>
        </div>

        <!-- Estate Selector (common for many sections) -->
        @if($estates->count() > 1)
            <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700 mb-2">Select Estate</label>
                <select 
                    wire:model.live="selectedEstate" 
                    class="w-full md:w-auto rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                >
                    <option value="">All Estates</option>
                    @foreach($estates as $estate)
                        <option value="{{ $estate->id }}">{{ $estate->name }}</option>
                    @endforeach
                </select>
            </div>
        @endif

        <!-- Overview Section -->
        @if($currentSection === 'overview')
            <div class="space-y-6">
                <!-- Permission-based Stats Cards -->
                <livewire:dashboard.permission-stats-cards 
                    :selectedEstate="$selectedEstate" 
                    key="stats-{{ $selectedEstate }}"
                />

                <!-- Permission-based Chart Widget -->
                <livewire:dashboard.permission-chart-widget 
                    :selectedEstate="$selectedEstate" 
                    key="chart-{{ $selectedEstate }}"
                />

                <!-- Permission-based Data Table -->
                <livewire:dashboard.permission-data-table 
                    :selectedEstate="$selectedEstate" 
                    key="table-{{ $selectedEstate }}"
                />
            </div>
        @endif

        <!-- Caretaker Dashboard Section -->
        @if($currentSection === 'readings' && auth()->user()->can('view-caretaker-dashboard'))
            <livewire:dashboard.caretaker-dashboard 
                :selectedEstate="$selectedEstate" 
                key="caretaker-{{ $selectedEstate }}"
            />
        @endif

        <!-- Reviewer Dashboard Section -->
        @if($currentSection === 'review' && auth()->user()->can('view-reviewer-dashboard'))
            <livewire:dashboard.reviewer-dashboard 
                :selectedEstate="$selectedEstate" 
                key="reviewer-{{ $selectedEstate }}"
            />
        @endif

        <!-- Management Dashboard Section -->
        @if($currentSection === 'management' && auth()->user()->can('view-manager-dashboard'))
            <livewire:dashboard.management-dashboard 
                :selectedEstate="$selectedEstate" 
                key="management-{{ $selectedEstate }}"
            />
        @endif

        <!-- Resident Dashboard Section -->
        @if($currentSection === 'resident' && auth()->user()->can('view-resident-dashboard'))
            <livewire:dashboard.resident-dashboard key="resident" />
        @endif
        
        <!-- Placeholder for other specific sub-items if needed -->
        {{-- Example: if a sub-item like 'analytics' under 'management' was selected --}}
        @if($currentSection === 'analytics' && auth()->user()->can('view-manager-dashboard'))
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-xl font-semibold mb-4">Analytics</h2>
                <p>Content for the Analytics section would go here.</p>
                {{-- This could load a specific Livewire component for analytics --}}
                {{-- <livewire:dashboard.analytics-dashboard :selectedEstate="$selectedEstate" /> --}}
            </div>
        @endif

        {{-- Add more conditions for other sub-items as needed --}}
        {{-- Example: Estates sub-item --}}
        @if($currentSection === 'estates' && auth()->user()->can('estates.view_all'))
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-xl font-semibold mb-4">Estates Management</h2>
                <p>Content for Estates management.</p>
                {{-- <livewire:estate.index /> --}}
            </div>
        @endif
    </div>

    <!-- Flash Messages -->
    @if(session()->has('message'))
        <div class="fixed top-4 right-4 z-50">
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
                {{ session('message') }}
            </div>
        </div>
    @endif

    @if(session()->has('error'))
        <div class="fixed top-4 right-4 z-50">
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                {{ session('error') }}
            </div>
        </div>
    @endif
</div>
