<div>
    @if($chartData && !empty($availableChartTypes))
        <!-- Chart Controls -->
        <div class="bg-white rounded-lg shadow p-4 mb-6">
            <div class="flex flex-wrap items-center justify-between gap-4">
                <div class="flex items-center space-x-4">
                    <label class="block text-sm font-medium text-gray-700">Chart Type</label>
                    <select wire:model.live="chartType" class="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        @foreach($availableChartTypes as $key => $label)
                            <option value="{{ $key }}">{{ $label }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="flex items-center space-x-4">
                    <label class="block text-sm font-medium text-gray-700">Date Range</label>
                    <select wire:model.live="dateRange" class="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        <option value="7">Last 7 days</option>
                        <option value="30">Last 30 days</option>
                        <option value="90">Last 90 days</option>
                        <option value="365">Last year</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Chart -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">
                @if($chartType == 'consumption') Consumption Trends
                @elseif($chartType == 'revenue') Revenue Trends
                @else Estate Comparison
                @endif
            </h3>
            <div class="h-64">
                <canvas id="permissionChart"></canvas>
            </div>
        </div>
    @else
        <!-- No Chart Access -->
        <div class="bg-gray-50 border border-gray-200 rounded-lg p-6 text-center mb-6">
            <div class="text-gray-500">Chart access not available for your current permissions.</div>
            <div class="text-sm text-gray-400 mt-2">Contact your administrator to request chart viewing permissions.</div>
        </div>
    @endif

    @push('scripts')
        <script>
            document.addEventListener('livewire:navigated', function () {
                const chartCanvas = document.getElementById('permissionChart');
                if (chartCanvas && window.Livewire) {
                    const chartData = @json($chartData);
                    
                    if (chartData && chartData.labels && chartData.datasets) {
                        new Chart(chartCanvas.getContext('2d'), {
                            type: @if($chartType == 'estate_comparison') 'bar' @else 'line' @endif,
                            data: chartData,
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                scales: {
                                    y: {
                                        beginAtZero: true
                                    }
                                },
                                plugins: {
                                    legend: {
                                        display: true,
                                        position: 'top'
                                    }
                                }
                            }
                        });
                    }
                }
            });
        </script>
    @endpush
</div>