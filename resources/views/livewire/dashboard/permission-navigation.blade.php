<div>
    <!-- Permission-based Navigation -->
    <nav class="bg-white rounded-lg shadow mb-6" wire:model.live="currentSection">
        <div class="px-4 py-3">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-8">
                    @foreach($navigationItems as $key => $item)
                        <div class="relative" x-data="{ open: false }">
                            {{-- Main Tab Button --}}
                            <button 
                                wire:click="selectSection('{{ $key }}')"
                                @click="open = !open"
                                class="flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200
                                    {{ $item['isActive'] 
                                        ? 'bg-blue-100 text-blue-700' 
                                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50' 
                                    }}"
                            >
                                @if(isset($item['icon']))
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M{{ $item['icon'] }}"></path>
                                    </svg>
                                @endif
                                {{ $item['label'] }}
                                @if(isset($item['subItems']) && !empty($item['subItems']))
                                    <svg class="ml-1.5 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                @endif
                            </button>

                            <!-- Dropdown Menu for Sub-items -->
                            @if(isset($item['subItems']) && !empty($item['subItems']))
                                <div 
                                    x-show="open"
                                    @click.away="open = false"
                                    @click.outside="open = false"
                                    class="absolute left-0 mt-1 w-48 bg-white rounded-md shadow-lg py-1 z-10 transition-opacity duration-200"
                                    x-transition:enter="ease-out duration-200"
                                    x-transition:enter-start="opacity-0 transform scale-95"
                                    x-transition:enter-end="opacity-100 transform scale-100"
                                    x-transition:leave="ease-in duration-150"
                                    x-transition:leave-start="opacity-100 transform scale-100"
                                    x-transition:leave-end="opacity-0 transform scale-95"
                                >
                                    @foreach($item['subItems'] as $subKey => $subItem)
                                        <a 
                                            href="#" 
                                            wire:click="selectSection('{{ $subKey }}'); open = false"
                                            class="block px-4 py-2 text-sm transition-colors duration-200
                                                {{ $currentSection === $subKey 
                                                    ? 'bg-blue-50 text-blue-700 font-medium' 
                                                    : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900' 
                                                }}"
                                        >
                                            {{ $subItem['label'] }}
                                        </a>
                                    @endforeach
                                </div>
                            @endif
                        </div>
                    @endforeach
                </div>

                <!-- User Info -->
                <div class="flex items-center space-x-3">
                    <div class="text-sm text-gray-600">
                        {{ auth()->user()->name }}
                    </div>
                    <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                        <span class="text-white text-sm font-medium">
                            {{ strtoupper(substr(auth()->user()->name, 0, 1)) }}
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Breadcrumb Navigation -->
        @if($currentSection !== 'overview')
            <div class="px-4 py-2 bg-gray-50 border-t border-gray-200">
                <div class="flex items-center text-sm text-gray-600">
                    <a href="#" wire:click="selectSection('overview')" class="hover:text-gray-900">Dashboard</a>
                    <svg class="w-4 h-4 mx-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                    <span class="text-gray-900 font-medium">
                        @php
                            $breadcrumbLabel = '';
                            if (isset($navigationItems[$currentSection])) {
                                $breadcrumbLabel = $navigationItems[$currentSection]['label'];
                            } else {
                                foreach ($navigationItems as $navItem) {
                                    if (isset($navItem['subItems'][$currentSection])) {
                                        $breadcrumbLabel = $navItem['label'] . ' / ' . $navItem['subItems'][$currentSection]['label'];
                                        break;
                                    }
                                }
                                if (empty($breadcrumbLabel)) {
                                    $breadcrumbLabel = ucfirst(str_replace('-', ' ', $currentSection));
                                }
                            }
                        @endphp
                        {{ $breadcrumbLabel }}
                    </span>
                </div>
            </div>
        @endif
    </nav>

    <!-- No Navigation Items Message -->
    @if(empty($navigationItems))
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-yellow-800">Limited Access</h3>
                    <div class="mt-2 text-sm text-yellow-700">
                        You have limited dashboard access. Contact your administrator to request additional permissions.
                    </div>
                </div>
            </div>
        </div>
    @endif
</div>
