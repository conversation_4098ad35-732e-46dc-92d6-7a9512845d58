<div>
    <!-- Permission-based Stats Cards Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4 mb-6">
        <!-- Caretaker Stats -->
        @if(isset($stats['caretaker']))
            <div class="bg-white rounded-lg shadow p-4">
                <div class="text-2xl font-bold text-blue-600">{{ $stats['caretaker']['total_readings'] }}</div>
                <div class="text-sm text-gray-600">Total Readings</div>
            </div>
            <div class="bg-white rounded-lg shadow p-4">
                <div class="text-2xl font-bold text-yellow-600">{{ $stats['caretaker']['draft_readings'] }}</div>
                <div class="text-sm text-gray-600">Draft</div>
            </div>
            <div class="bg-white rounded-lg shadow p-4">
                <div class="text-2xl font-bold text-orange-600">{{ $stats['caretaker']['submitted_readings'] }}</div>
                <div class="text-sm text-gray-600">Submitted</div>
            </div>
            <div class="bg-white rounded-lg shadow p-4">
                <div class="text-2xl font-bold text-green-600">{{ $stats['caretaker']['approved_readings'] }}</div>
                <div class="text-sm text-gray-600">Approved</div>
            </div>
            <div class="bg-white rounded-lg shadow p-4">
                <div class="text-2xl font-bold text-red-600">{{ $stats['caretaker']['rejected_readings'] }}</div>
                <div class="text-sm text-gray-600">Rejected</div>
            </div>
            <div class="bg-white rounded-lg shadow p-4">
                <div class="text-2xl font-bold text-purple-600">{{ $stats['caretaker']['today_readings'] }}</div>
                <div class="text-sm text-gray-600">Today</div>
            </div>
        @endif

        <!-- Reviewer Stats -->
        @if(isset($stats['reviewer']))
            <div class="bg-white rounded-lg shadow p-4">
                <div class="text-2xl font-bold text-orange-600">{{ $stats['reviewer']['pending_review'] }}</div>
                <div class="text-sm text-gray-600">Pending Review</div>
            </div>
            <div class="bg-white rounded-lg shadow p-4">
                <div class="text-2xl font-bold text-green-600">{{ $stats['reviewer']['approved_today'] }}</div>
                <div class="text-sm text-gray-600">Approved Today</div>
            </div>
            <div class="bg-white rounded-lg shadow p-4">
                <div class="text-2xl font-bold text-red-600">{{ $stats['reviewer']['rejected_today'] }}</div>
                <div class="text-sm text-gray-600">Rejected Today</div>
            </div>
            <div class="bg-white rounded-lg shadow p-4">
                <div class="text-2xl font-bold text-blue-600">{{ $stats['reviewer']['this_month'] }}</div>
                <div class="text-sm text-gray-600">This Month</div>
            </div>
        @endif

        <!-- Management Stats -->
        @if(isset($stats['management']))
            <div class="bg-white rounded-lg shadow p-4">
                <div class="text-2xl font-bold text-blue-600">{{ number_format($stats['management']['total_houses']) }}</div>
                <div class="text-sm text-gray-600">Total Houses</div>
            </div>
            <div class="bg-white rounded-lg shadow p-4">
                <div class="text-2xl font-bold text-green-600">{{ number_format($stats['management']['total_estates']) }}</div>
                <div class="text-sm text-gray-600">Total Estates</div>
            </div>
            <div class="bg-white rounded-lg shadow p-4">
                <div class="text-2xl font-bold text-purple-600">KES {{ number_format($stats['management']['total_revenue'], 2) }}</div>
                <div class="text-sm text-gray-600">Total Revenue</div>
            </div>
            <div class="bg-white rounded-lg shadow p-4">
                <div class="text-2xl font-bold text-orange-600">{{ number_format($stats['management']['avg_consumption'], 1) }}L</div>
                <div class="text-sm text-gray-600">Avg Consumption</div>
            </div>
            <div class="bg-white rounded-lg shadow p-4">
                <div class="text-2xl font-bold text-yellow-600">{{ $stats['management']['pending_readings'] }}</div>
                <div class="text-sm text-gray-600">Pending Readings</div>
            </div>
            <div class="bg-white rounded-lg shadow p-4">
                <div class="text-2xl font-bold text-red-600">{{ $stats['management']['overdue_invoices'] }}</div>
                <div class="text-sm text-gray-600">Overdue Invoices</div>
            </div>
        @endif

        <!-- Resident Stats -->
        @if(isset($stats['resident']))
            <div class="bg-white rounded-lg shadow p-4">
                <div class="text-2xl font-bold text-red-600">{{ $stats['resident']['unpaid_invoices'] }}</div>
                <div class="text-sm text-gray-600">Unpaid Invoices</div>
                @if($stats['resident']['total_unpaid'] > 0)
                    <div class="text-sm text-red-600 mt-1">KES {{ number_format($stats['resident']['total_unpaid'], 2) }}</div>
                @endif
            </div>
            <div class="bg-white rounded-lg shadow p-4">
                <div class="text-2xl font-bold text-blue-600">{{ $stats['resident']['unread_messages'] }}</div>
                <div class="text-sm text-gray-600">Unread Messages</div>
            </div>
        @endif
    </div>

    <!-- No Stats Available Message -->
    @if(empty($stats))
        <div class="bg-gray-50 border border-gray-200 rounded-lg p-6 text-center">
            <div class="text-gray-500">No statistics available for your current permissions.</div>
            <div class="text-sm text-gray-400 mt-2">Contact your administrator to request additional permissions.</div>
        </div>
    @endif
</div>