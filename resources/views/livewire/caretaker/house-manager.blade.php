<div>
    <div class="mb-6">
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Houses in {{ $estateName }}</h1>
        <p class="text-gray-600 dark:text-gray-400">View and manage houses and their meter readings in your assigned estatesp>
    </div>

    <!-- Estate Selector -->
    <div class="mb-6 bg-white dark:bg-gray-800 rounded-lg shadow p-4">
        <div>
            <label for="estate_selector" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Select Estate</label>
            <select id="estate_selector" wire:model.live="estateId" class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white">
                <option value="">-- Select an Estate --</option>
                @foreach($estates as $estate)
                    <option value="{{ $estate->id }}">{{ $estate->name }}</option>
                @endforeach
            </select>
        </div>
    </div>

    <!-- Submit All Readings Button -->
    @if($estateId)
        <div class="mb-6">
            <button wire:click="submitAllDraftReadings" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Submit All Draft Readings for {{ $estateName }}
            </button>
        </div>
    @endif

    <!-- Add Reading Form Modal/Section -->
    @if($showAddReadingForm)
        <div class="mb-6 bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h2 class="text-xl font-semibold mb-4">Add New Reading for House #{{ House::find($selectedHouseForReading)->house_number }}</h2>
            <form wire:submit.prevent="saveReading">
                <div class="mb-4">
                    <label for="current_reading" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Current Reading</label>
                    <input type="number" id="current_reading" wire:model="currentReading" class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white" placeholder="Enter current meter reading">
                    @error('currentReading') <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p> @enderror
                </div>
                <div class="mb-6">
                    <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Notes (Optional)</label>
                    <textarea id="notes" wire:model="notes" rows="3" class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white" placeholder="Any additional notes..."></textarea>
                    @error('notes') <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p> @enderror
                </div>
                <div class="flex space-x-4">
                    <button type="submit" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">Submit as Draft</button>
                    <button type="button" wire:click="cancelAddReading" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded">Cancel</button>
                </div>
            </form>
        </div>
    @endif

    <div class="mt-6 bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">House Number</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Last Reading Date</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Last Reading Value</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Last Reading Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Action</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                    @forelse ($houses as $house)
                        @php
                            $lastReading = $house->meterReadings->where('user_id', Auth::id())->sortByDesc('reading_date')->first();
                        @endphp
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">{{ $house->house_number }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                    @if($house->contacts->count() > 0)
                                        bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-200
                                    @else
                                        bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-200
                                    @endif
                                ">
                                    {{ $house->contacts->count() > 0 ? 'Occupied' : 'Vacant' }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                {{ $lastReading ? $lastReading->reading_date->format('Y-m-d') : 'N/A' }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                {{ $lastReading ? number_format($lastReading->current_reading, 2) : 'N/A' }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                @if($lastReading)
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-{{ $lastReading->status_color }}-100 text-{{ $lastReading->status_color }}-800 dark:bg-{{ $lastReading->status_color }}-800 dark:text-{{ $lastReading->status_color }}-200">
                                        {{ $lastReading->status_label }}
                                    </span>
                                @else
                                    <span class="text-gray-400">N/A</span>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button wire:click="addReading({{ $house->id }})" class="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300">
                                    Add Reading
                                </button>
                            </td>
                        </tr>
                    @empty
                    <tr>
                        <td colspan="6" class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">No houses found for this estate or you are not assigned to any houses in this estate.</td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>

    <div class="mt-4">
        {{ $houses->links() }}
    </div>
</div>
