<div>
    <x-modal wire:model="showModal" maxWidth="2xl">
        <x-slot name="title">
            {{ $templateId ? 'Edit Export Template' : 'Create Export Template' }}
        </x-slot>

        <x-slot name="content">
            <form wire:submit="save">
                <div class="space-y-6">
                    <!-- Basic Information -->
                    <div>
                        <x-label for="name" :value="__('Template Name')" />
                        <x-input
                            id="name"
                            type="text"
                            wire:model="name"
                            class="mt-1 block w-full"
                            required
                            autofocus
                        />
                        <x-input-error :messages="$errors->get('name')" class="mt-2" />
                    </div>

                    <!-- Entity Type -->
                    <div>
                        <x-label for="entityType" :value="__('Entity Type')" />
                        <select
                            id="entityType"
                            wire:model="entityType"
                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                        >
                            @foreach($availableEntities as $key => $label)
                                <option value="{{ $key }}">{{ $label }}</option>
                            @endforeach
                        </select>
                        <x-input-error :messages="$errors->get('entityType')" class="mt-2" />
                    </div>

                    <!-- Format -->
                    <div>
                        <x-label for="format" :value="__('Export Format')" />
                        <select
                            id="format"
                            wire:model="format"
                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                        >
                            @foreach($availableFormats as $key => $label)
                                <option value="{{ $key }}">{{ $label }}</option>
                            @endforeach
                        </select>
                        <x-input-error :messages="$errors->get('format')" class="mt-2" />
                    </div>

                    <!-- Columns Selection -->
                    <div>
                        <x-label :value="__('Select Columns')" />
                        <div class="mt-2 space-y-2">
                            @foreach($getAvailableColumnsForEntity($entityType) as $column)
                                <label class="flex items-center">
                                    <input
                                        type="checkbox"
                                        wire:model="columns"
                                        value="{{ $column }}"
                                        class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-offset-0 focus:ring-indigo-200 focus:ring-opacity-50"
                                    >
                                    <span class="ml-2 text-sm text-gray-700">{{ $column }}</span>
                                </label>
                            @endforeach
                        </div>
                        <x-input-error :messages="$errors->get('columns')" class="mt-2" />
                    </div>

                    <!-- Options -->
                    <div class="space-y-4">
                        <div class="flex items-center">
                            <input
                                type="checkbox"
                                wire:model="isPublic"
                                id="isPublic"
                                class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-offset-0 focus:ring-indigo-200 focus:ring-opacity-50"
                            >
                            <label for="isPublic" class="ml-2 text-sm text-gray-700">
                                Make this template public (visible to other users)
                            </label>
                        </div>

                        <div class="flex items-center">
                            <input
                                type="checkbox"
                                wire:model="isScheduled"
                                id="isScheduled"
                                class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-offset-0 focus:ring-indigo-200 focus:ring-opacity-50"
                            >
                            <label for="isScheduled" class="ml-2 text-sm text-gray-700">
                                Enable scheduled exports
                            </label>
                        </div>

                        @if($isScheduled)
                            <div>
                                <x-label for="scheduleFrequency" :value="__('Schedule Frequency')" />
                                <select
                                    id="scheduleFrequency"
                                    wire:model="scheduleFrequency"
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                >
                                    @foreach($scheduleFrequencies as $key => $label)
                                        <option value="{{ $key }}">{{ $label }}</option>
                                    @endforeach
                                </select>
                                <x-input-error :messages="$errors->get('scheduleFrequency')" class="mt-2" />
                            </div>
                        @endif
                    </div>
                </div>

                <div class="mt-6 flex justify-end space-x-3">
                    <x-secondary-button wire:click="$set('showModal', false)" type="button">
                        {{ __('Cancel') }}
                    </x-secondary-button>

                    <x-primary-button type="submit">
                        {{ $templateId ? 'Update Template' : 'Create Template' }}
                    </x-primary-button>
                </div>
            </form>
        </x-slot>
    </x-modal>
div>