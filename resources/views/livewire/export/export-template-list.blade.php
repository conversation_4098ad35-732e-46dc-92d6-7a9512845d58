<div>
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
        <div>
            <h2 class="text-lg font-medium text-gray-900">Export Templates</h2>
            <p class="mt-1 text-sm text-gray-600">Manage your export templates for quick data exports.</p>
        </div>
        <x-primary-button wire:click="$set('showCreateModal', true)">
            {{ __('Create Template') }}
        </x-primary-button>
    </div>

    <!-- Filters -->
    <div class="mb-6 flex flex-col sm:flex-row gap-4">
        <div class="flex-1">
            <x-input
                type="text"
                wire:model.live="search"
                placeholder="Search templates..."
                class="w-full"
            />
        </div>
        <div>
            <select
                wire:model.live="entityTypeFilter"
                class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            >
                @foreach($availableEntities as $key => $label)
                    <option value="{{ $key }}">{{ $label }}</option>
                @endforeach
            </select>
        </div>
    </div>

    <!-- Templates List -->
    <div class="overflow-hidden bg-white shadow-sm ring-1 ring-gray-900/5 sm:rounded-lg">
        @if($templates->count() > 0)
            <ul class="divide-y divide-gray-100">
                @foreach($templates as $template)
                    <li class="relative flex justify-between gap-x-6 px-4 py-5 hover:bg-gray-50 sm:px-6 lg:px-8">
                        <div class="flex min-w-0 gap-x-4">
                            <div class="min-w-0 flex-auto">
                                <p class="text-sm font-semibold leading-6 text-gray-900">
                                    {{ $template->name }}
                                    @if($template->is_public)
                                        <span class="ml-2 inline-flex items-center rounded-full bg-green-50 px-2 py-1 text-xs font-medium text-green-700 ring-1 ring-inset ring-green-600/20">
                                            Public
                                        </span>
                                    @endif
                                </p>
                                <p class="mt-1 flex text-xs leading-5 text-gray-500">
                                    <span class="truncate">{{ $getEntityLabel($template->entity_type) }}</span>
                                    <span class="mx-1">•</span>
                                    <span>{{ $getFormatLabel($template->format) }}</span>
                                    <span class="mx-1">•</span>
                                    <span>{{ count($template->columns) }} columns</span>
                                </p>
                                @if($template->is_scheduled)
                                    <p class="mt-1 text-xs leading-5 text-gray-500">
                                        Scheduled: {{ ucfirst($template->schedule_frequency) }}
                                    </p>
                                @endif
                            </div>
                        </div>
                        <div class="flex items-center gap-x-4">
                            <div class="hidden sm:flex sm:flex-col sm:items-end">
                                <p class="text-sm leading-6 text-gray-900">
                                    {{ $template->export_jobs_count }} export{{ $template->export_jobs_count != 1 ? 's' : '' }}
                                </p>
                                <p class="mt-1 text-xs leading-5 text-gray-500">
                                    Last used: {{ $template->updated_at->diffForHumans() }}
                                </p>
                            </div>
                            <div class="flex gap-x-2">
                                <x-secondary-button
                                    wire:click="editTemplate({{ $template->id }})"
                                    size="sm"
                                >
                                    Edit
                                </x-secondary-button>
                                <x-danger-button
                                    wire:click="deleteTemplate({{ $template->id }})"
                                    wire:confirm="Are you sure you want to delete this template?"
                                    size="sm"
                                >
                                    Delete
                                </x-danger-button>
                            </div>
                        </div>
                    </li>
                @endforeach
            </ul>

            <!-- Pagination -->
            <div class="border-t border-gray-200 px-4 py-3 sm:px-6">
                {{ $templates->links() }}
            </div>
        @else
            <div class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No templates found</h3>
                <p class="mt-1 text-sm text-gray-500">Get started by creating a new export template.</p>
                <div class="mt-6">
                    <x-primary-button wire:click="$set('showCreateModal', true)">
                        {{ __('Create Template') }}
                    </x-primary-button>
                </div>
            </div>
        @endif
    </div>

    <!-- Create Template Modal -->
    <livewire:export.export-template-form :showModal="$showCreateModal" />

    <!-- Edit Template Modal -->
    @if($showEditModal)
        <livewire:export.export-template-form 
            :templateId="$editingTemplateId" 
            :showModal="$showEditModal" 
        />
    @endif
</div>