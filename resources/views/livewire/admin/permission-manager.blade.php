<div>
    <!-- Tabs -->
    <div class="border-b border-gray-200">
        <nav class="-mb-px flex space-x-8">
            <button
                wire:click="switchTab('users')"
                class="{{ $activeTab === 'users' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' }} whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
            >
                User Permissions
            </button>
            <button
                wire:click="switchTab('roles')"
                class="{{ $activeTab === 'roles' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' }} whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
            >
                Role Management
            </button>
            <button
                wire:click="switchTab('permissions')"
                class="{{ $activeTab === 'permissions' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' }} whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
            >
                Permission Management
            </button>
        </nav>
    </div>

    <!-- Flash Messages -->
    @if (session()->has('message'))
        <div class="mt-4 p-4 bg-green-100 border border-green-400 text-green-700 rounded">
            {{ session('message') }}
        </div>
    @endif

    <!-- User Permissions Tab -->
    @if ($activeTab === 'users')
        <div class="mt-6 grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Users List -->
            <div class="lg:col-span-1">
                <div class="bg-white shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Users</h3>
                        
                        <!-- Search -->
                        <input
                            type="text"
                            wire:model.live="userSearch"
                            placeholder="Search users..."
                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        >

                        <!-- Users List -->
                        <div class="mt-4 space-y-2 max-h-96 overflow-y-auto">
                            @foreach ($users as $user)
                                <button
                                    wire:click="selectUser({{ $user->id }})"
                                    class="{{ $selectedUserId === $user->id ? 'bg-blue-50 border-blue-200' : 'hover:bg-gray-50' }} w-full text-left p-3 border rounded-lg transition-colors"
                                >
                                    <div class="font-medium text-gray-900">{{ $user->name }}</div>
                                    <div class="text-sm text-gray-500">{{ $user->email }}</div>
                                    <div class="text-xs text-gray-400 mt-1">
                                        {{ implode(', ', $user->roles->pluck('name')->toArray()) }}
                                    </div>
                                </button>
                            @endforeach
                        </div>

                        <!-- Pagination -->
                        <div class="mt-4">
                            {{ $users->links() }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- User Permissions/Roles -->
            <div class="lg:col-span-2">
                @if ($selectedUser)
                    <div class="bg-white shadow rounded-lg">
                        <div class="px-4 py-5 sm:p-6">
                            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                                Manage {{ $selectedUser->name }}
                            </h3>

                            <!-- Roles Section -->
                            <div class="mb-6">
                                <h4 class="text-md font-medium text-gray-900 mb-3">Roles</h4>
                                <div class="grid grid-cols-2 md:grid-cols-3 gap-2">
                                    @foreach ($roles as $role)
                                        <label class="flex items-center">
                                            <input
                                                type="checkbox"
                                                wire:model="selectedUserRoles"
                                                value="{{ $role->name }}"
                                                class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                                            >
                                            <span class="ml-2 text-sm text-gray-700">{{ $role->name }}</span>
                                        </label>
                                    @endforeach
                                </div>
                                <button
                                    wire:click="updateUserRoles"
                                    class="mt-3 inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                                >
                                    Update Roles
                                </button>
                            </div>

                            <!-- Permissions Section -->
                            <div>
                                <h4 class="text-md font-medium text-gray-900 mb-3">Direct Permissions</h4>
                                <div class="space-y-4 max-h-96 overflow-y-auto">
                                    @foreach ($permissionGroups as $category => $permissions)
                                        <div>
                                            <h5 class="text-sm font-medium text-gray-700 mb-2">{{ $category }}</h5>
                                            <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
                                                @foreach ($permissions as $permission)
                                                    <label class="flex items-center">
                                                        <input
                                                            type="checkbox"
                                                            wire:model="selectedUserPermissions"
                                                            value="{{ $permission['name'] }}"
                                                            class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                                                        >
                                                        <span class="ml-2 text-sm text-gray-700">{{ $permission['name'] }}</span>
                                                    </label>
                                                @endforeach
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                                <button
                                    wire:click="updateUserPermissions"
                                    class="mt-3 inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                                >
                                    Update Permissions
                                </button>
                            </div>
                        </div>
                    </div>
                @else
                    <div class="bg-white shadow rounded-lg">
                        <div class="px-4 py-5 sm:p-6 text-center">
                            <div class="text-gray-400">Select a user to manage their permissions and roles</div>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    @endif

    <!-- Role Management Tab -->
    @if ($activeTab === 'roles')
        <div class="mt-6 grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Roles List -->
            <div class="lg:col-span-1">
                <div class="bg-white shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Roles</h3>
                        
                        <!-- Create New Role -->
                        <div class="mb-4 p-4 bg-gray-50 rounded-lg">
                            <h4 class="text-sm font-medium text-gray-900 mb-2">Create New Role</h4>
                            <input
                                type="text"
                                wire:model="newRoleName"
                                placeholder="Role name"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 mb-2"
                            >
                            <input
                                type="text"
                                wire:model="newRoleDescription"
                                placeholder="Description (optional)"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 mb-2"
                            >
                            <button
                                wire:click="createRole"
                                class="w-full inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                            >
                                Create Role
                            </button>
                        </div>

                        <!-- Search -->
                        <input
                            type="text"
                            wire:model.live="roleSearch"
                            placeholder="Search roles..."
                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        >

                        <!-- Roles List -->
                        <div class="mt-4 space-y-2 max-h-96 overflow-y-auto">
                            @foreach ($roles as $role)
                                <div class="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50">
                                    <button
                                        wire:click="selectRole({{ $role->id }})"
                                        class="{{ $selectedRoleId === $role->id ? 'bg-blue-50' : '' }} flex-1 text-left"
                                    >
                                        <div class="font-medium text-gray-900">{{ $role->name }}</div>
                                        <div class="text-sm text-gray-500">{{ $role->permissions->count() }} permissions</div>
                                    </button>
                                    @if ($role->name !== 'admin')
                                        <button
                                            wire:click="deleteRole({{ $role->id }})"
                                            wire:confirm="Are you sure you want to delete this role?"
                                            class="ml-2 text-red-600 hover:text-red-800"
                                        >
                                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                            </svg>
                                        </button>
                                    @endif
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>

            <!-- Role Permissions -->
            <div class="lg:col-span-2">
                @if ($selectedRole)
                    <div class="bg-white shadow rounded-lg">
                        <div class="px-4 py-5 sm:p-6">
                            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                                Manage {{ $selectedRole->name }} Permissions
                            </h3>

                            <div class="space-y-4 max-h-96 overflow-y-auto">
                                @foreach ($permissionGroups as $category => $permissions)
                                    <div>
                                        <h5 class="text-sm font-medium text-gray-700 mb-2">{{ $category }}</h5>
                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
                                            @foreach ($permissions as $permission)
                                                <label class="flex items-center">
                                                    <input
                                                        type="checkbox"
                                                        wire:model="selectedRolePermissions"
                                                        value="{{ $permission['name'] }}"
                                                        class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                                                    >
                                                    <span class="ml-2 text-sm text-gray-700">{{ $permission['name'] }}</span>
                                                </label>
                                            @endforeach
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                            <button
                                wire:click="updateRolePermissions"
                                class="mt-3 inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                            >
                                Update Role Permissions
                            </button>
                        </div>
                    </div>
                @else
                    <div class="bg-white shadow rounded-lg">
                        <div class="px-4 py-5 sm:p-6 text-center">
                            <div class="text-gray-400">Select a role to manage its permissions</div>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    @endif

    <!-- Permission Management Tab -->
    @if ($activeTab === 'permissions')
        <div class="mt-6">
            <!-- Create New Permission -->
            <div class="bg-white shadow rounded-lg mb-6">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Create New Permission</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <input
                            type="text"
                            wire:model="newPermissionName"
                            placeholder="Permission name (e.g., users.create)"
                            class="px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        >
                        <input
                            type="text"
                            wire:model="newPermissionDescription"
                            placeholder="Description (optional)"
                            class="px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        >
                    </div>
                    <button
                        wire:click="createPermission"
                        class="mt-3 inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                    >
                        Create Permission
                    </button>
                </div>
            </div>

            <!-- Search -->
            <div class="mb-4">
                <input
                    type="text"
                    wire:model.live="permissionSearch"
                    placeholder="Search permissions..."
                    class="w-full md:w-1/2 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
            </div>

            <!-- Permission Groups -->
            <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
                @foreach ($permissionGroups as $category => $permissions)
                    <div class="bg-white shadow rounded-lg">
                        <div class="px-4 py-5 sm:p-6">
                            <h4 class="text-md font-medium text-gray-900 mb-4">{{ $category }}</h4>
                            <div class="space-y-2 max-h-64 overflow-y-auto">
                                @foreach ($permissions as $permission)
                                    <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">{{ $permission['name'] }}</div>
                                            @if ($permission['description'])
                                                <div class="text-xs text-gray-500">{{ $permission['description'] }}</div>
                                            @endif
                                        </div>
                                        <button
                                            wire:click="deletePermission('{{ $permission['name'] }}')"
                                            wire:confirm="Are you sure you want to delete this permission?"
                                            class="text-red-600 hover:text-red-800"
                                        >
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                            </svg>
                                        </button>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    @endif
</div>