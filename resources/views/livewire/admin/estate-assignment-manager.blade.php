<div>
    <!-- Header -->
    <div class="mb-6">
        <h2 class="text-2xl font-bold text-gray-900">Estate Assignment Manager</h2>
        <p class="text-gray-600 mt-1">Manage user assignments to estates</p>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Search Users</label>
                <input
                    type="text"
                    wire:model.live="search"
                    placeholder="Search by name or email..."
                    class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                >
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Filter by Role</label>
                <select
                    wire:model.live="roleFilter"
                    class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                >
                    <option value="">All Roles</option>
                    @foreach($roles as $role)
                        <option value="{{ $role }}">{{ ucfirst($role) }}</option>
                    @endforeach
                </select>
            </div>
            <div class="flex items-end">
                <button
                    wire:click="bulkAssignEstates"
                    class="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
                >
                    Bulk Assign Estates
                </button>
            </div>
        </div>
    </div>

    <!-- Users Table -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            User
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Role
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Assigned Estates
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @foreach($users as $user)
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <div class="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                                            <span class="text-blue-600 font-medium">
                                                {{ $user->initials() }}
                                            </span>
                                        </div>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">
                                            {{ $user->name }}
                                        </div>
                                        <div class="text-sm text-gray-500">
                                            {{ $user->email }}
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                    @if($user->role === 'manager') bg-green-100 text-green-800
                                    @elseif($user->role === 'reviewer') bg-blue-100 text-blue-800
                                    @elseif($user->role === 'caretaker') bg-yellow-100 text-yellow-800
                                    @else bg-gray-100 text-gray-800
                                    @endif">
                                    {{ ucfirst($user->role) }}
                                </span>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex flex-wrap gap-1">
                                    @foreach($user->assignedEstates as $estate)
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                            {{ $estate->name }}
                                            <button
                                                wire:click="removeUserFromEstate({{ $user->id }}, {{ $estate->id }})"
                                                class="ml-1 text-purple-600 hover:text-purple-800"
                                                title="Remove from estate"
                                            >
                                                ×
                                            </button>
                                        </span>
                                    @endforeach
                                    @if($user->assignedEstates->count() === 0)
                                        <span class="text-gray-500 text-sm">No estates assigned</span>
                                    @endif
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button
                                    wire:click="openAssignmentModal({{ $user->id }})"
                                    class="text-blue-600 hover:text-blue-900 mr-3"
                                >
                                    Manage Estates
                                </button>
                                <button
                                    wire:click="openHistoryModal({{ $user->id }})"
                                    class="text-gray-600 hover:text-gray-900"
                                >
                                    History
                                </button>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
            <div class="flex-1 flex justify-between sm:hidden">
                {{ $users->links() }}
            </div>
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                    <p class="text-sm text-gray-700">
                        Showing <span class="font-medium">{{ $users->firstItem() }}</span>
                        to <span class="font-medium">{{ $users->lastItem() }}</span>
                        of <span class="font-medium">{{ $users->total() }}</span>
                        results
                    </p>
                </div>
                <div>
                    {{ $users->links() }}
                </div>
            </div>
        </div>
    </div>

    <!-- Assignment Modal -->
    @if($showAssignmentModal)
        <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                <div class="mt-3">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                        Manage Estate Assignments
                    </h3>
                    <p class="text-sm text-gray-500 mb-4">
                        User: <span class="font-medium">{{ $selectedUser->name }}</span>
                    </p>
                    
                    <form wire:submit="saveAssignments">
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                Select Estates
                            </label>
                            <div class="space-y-2 max-h-60 overflow-y-auto border rounded-md p-3">
                                @foreach($allEstates as $estate)
                                    <label class="flex items-center">
                                        <input
                                            type="checkbox"
                                            wire:model="selectedEstates"
                                            value="{{ $estate->id }}"
                                            class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-offset-0 focus:ring-blue-200 focus:ring-opacity-50"
                                        >
                                        <span class="ml-2 text-sm text-gray-700">{{ $estate->name }}</span>
                                    </label>
                                @endforeach
                            </div>
                            @error('selectedEstates')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="flex justify-end space-x-3">
                            <button
                                type="button"
                                wire:click="closeAssignmentModal"
                                class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors"
                            >
                                Cancel
                            </button>
                            <button
                                type="submit"
                                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                            >
                                Save Assignments
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    @endif

    <!-- History Modal -->
    @if($showHistoryModal)
        <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                <div class="mt-3">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                        Assignment History
                    </h3>
                    <p class="text-sm text-gray-500 mb-4">
                        User: <span class="font-medium">{{ $selectedUser->name }}</span>
                    </p>
                    
                    <div class="max-h-60 overflow-y-auto">
                        @if($assignmentHistory->count() > 0)
                            <div class="space-y-3">
                                @foreach($assignmentHistory as $log)
                                    <div class="border-l-4 border-blue-500 pl-3 py-2">
                                        <div class="text-sm font-medium text-gray-900">
                                            {{ $log->action }}
                                        </div>
                                        <div class="text-xs text-gray-500">
                                            {{ $log->created_at->format('M j, Y g:i A') }}
                                        </div>
                                        <div class="text-xs text-gray-600 mt-1">
                                            {{ json_decode($log->details)->user_name ?? 'Unknown' }}
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <p class="text-gray-500 text-sm">No assignment history found.</p>
                        @endif
                    </div>

                    <div class="flex justify-end mt-4">
                        <button
                            wire:click="closeHistoryModal"
                            class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors"
                        >
                            Close
                        </button>
                    </div>
                </div>
            </div>
        </div>
    @endif

    <!-- Notifications -->
    @if (session()->has('notify'))
        <div class="fixed bottom-4 right-4 z-50">
            <div class="bg-green-500 text-white px-4 py-2 rounded-md shadow-lg">
                {{ session('notify') }}
            </div>
        </div>
    @endif
</div>
