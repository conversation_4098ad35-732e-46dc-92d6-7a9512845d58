<div>
    <!-- Header -->
    <div class="mb-6">
        <h2 class="text-2xl font-bold text-gray-900">Team Management</h2>
        <p class="text-gray-600 mt-1">Manage organizational hierarchy and team assignments</p>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Search Managers</label>
                <input
                    type="text"
                    wire:model.live="search"
                    placeholder="Search by name or email..."
                    class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                >
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Filter by Role</label>
                <select
                    wire:model.live="roleFilter"
                    class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                >
                    <option value="">All Manager Roles</option>
                    @foreach($roles as $role)
                        @if(in_array($role, ['admin', 'manager', 'reviewer']))
                            <option value="{{ $role }}">{{ ucfirst($role) }}</option>
                        @endif
                    @endforeach
                </select>
            </div>
        </div>
    </div>

    <!-- Managers Table -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Manager
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Role
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Team Members
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @foreach($managers as $manager)
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <div class="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                                            <span class="text-blue-600 font-medium">
                                                {{ $manager->initials() }}
                                            </span>
                                        </div>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">
                                            {{ $manager->name }}
                                        </div>
                                        <div class="text-sm text-gray-500">
                                            {{ $manager->email }}
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                    @if($manager->role === 'admin') bg-red-100 text-red-800
                                    @elseif($manager->role === 'manager') bg-green-100 text-green-800
                                    @elseif($manager->role === 'reviewer') bg-blue-100 text-blue-800
                                    @else bg-gray-100 text-gray-800
                                    @endif">
                                    {{ ucfirst($manager->role) }}
                                </span>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex flex-wrap gap-1">
                                    @foreach($manager->subordinates as $subordinate)
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium 
                                            @if($subordinate->pivot->relationship === 'manages') bg-green-100 text-green-800
                                            @else bg-blue-100 text-blue-800
                                            @endif">
                                            {{ $subordinate->name }}
                                            <button
                                                wire:click="removeSubordinate({{ $manager->id }}, {{ $subordinate->id }})"
                                                class="ml-1 hover:text-red-600"
                                                title="Remove from team"
                                            >
                                                ×
                                            </button>
                                        </span>
                                    @endforeach
                                    @if($manager->subordinates->count() === 0)
                                        <span class="text-gray-500 text-sm">No team members</span>
                                    @endif
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button
                                    wire:click="openTeamModal({{ $manager->id }})"
                                    class="text-blue-600 hover:text-blue-900 mr-3"
                                >
                                    Manage Team
                                </button>
                                <button
                                    wire:click="openHierarchyModal({{ $manager->id }})"
                                    class="text-gray-600 hover:text-gray-900"
                                >
                                    View Hierarchy
                                </button>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
            <div class="flex-1 flex justify-between sm:hidden">
                {{ $managers->links() }}
            </div>
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                    <p class="text-sm text-gray-700">
                        Showing <span class="font-medium">{{ $managers->firstItem() }}</span>
                        to <span class="font-medium">{{ $managers->lastItem() }}</span>
                        of <span class="font-medium">{{ $managers->total() }}</span>
                        results
                    </p>
                </div>
                <div>
                    {{ $managers->links() }}
                </div>
            </div>
        </div>
    </div>

    <!-- Team Management Modal -->
    @if($showTeamModal)
        <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                <div class="mt-3">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                        Manage Team Assignments
                    </h3>
                    <p class="text-sm text-gray-500 mb-4">
                        Manager: <span class="font-medium">{{ $selectedManager->name }}</span>
                    </p>
                    
                    <form wire:submit="saveTeamAssignments">
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                Relationship Type
                            </label>
                            <select
                                wire:model="relationshipType"
                                class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                            >
                                <option value="manages">Manages (Direct Management)</option>
                                <option value="oversees">Oversees (Oversight Only)</option>
                            </select>
                        </div>

                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                Select Team Members
                            </label>
                            <div class="space-y-2 max-h-60 overflow-y-auto border rounded-md p-3">
                                @foreach($this->getAvailableSubordinates() as $subordinate)
                                    <label class="flex items-center">
                                        <input
                                            type="checkbox"
                                            wire:model="selectedSubordinates"
                                            value="{{ $subordinate->id }}"
                                            class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-offset-0 focus:ring-blue-200 focus:ring-opacity-50"
                                        >
                                        <span class="ml-2 text-sm text-gray-700">
                                            {{ $subordinate->name }}
                                            <span class="text-gray-500">({{ ucfirst($subordinate->role) }})</span>
                                        </span>
                                    </label>
                                @endforeach
                            </div>
                            @error('selectedSubordinates')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="flex justify-end space-x-3">
                            <button
                                type="button"
                                wire:click="closeTeamModal"
                                class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors"
                            >
                                Cancel
                            </button>
                            <button
                                type="submit"
                                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                            >
                                Save Team
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    @endif

    <!-- Hierarchy Modal -->
    @if($showHierarchyModal)
        <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                <div class="mt-3">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                        Team Hierarchy
                    </h3>
                    <p class="text-sm text-gray-500 mb-4">
                        Manager: <span class="font-medium">{{ $selectedManager->name }}</span>
                    </p>
                    
                    <div class="max-h-60 overflow-y-auto">
                        @if(count($teamHierarchy) > 0)
                            <div class="space-y-2">
                                @foreach($teamHierarchy as $subordinate)
                                    <div class="border-l-4 
                                        @if($subordinate->pivot->relationship === 'manages') border-green-500
                                        @else border-blue-500
                                        @endif
                                        pl-3 py-2">
                                        <div class="flex items-center justify-between">
                                            <div>
                                                <div class="text-sm font-medium text-gray-900">
                                                    {{ $subordinate->name }}
                                                </div>
                                                <div class="text-xs text-gray-500">
                                                    {{ ucfirst($subordinate->role) }} • 
                                                    {{ $this->getRelationshipLabel($subordinate->pivot->relationship) }}
                                                </div>
                                            </div>
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                                @if($subordinate->role === 'manager') bg-green-100 text-green-800
                                                @elseif($subordinate->role === 'reviewer') bg-blue-100 text-blue-800
                                                @elseif($subordinate->role === 'caretaker') bg-yellow-100 text-yellow-800
                                                @else bg-gray-100 text-gray-800
                                                @endif">
                                                Level {{ $this->getHierarchyLevel($subordinate) }}
                                            </span>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <p class="text-gray-500 text-sm">No team members found.</p>
                        @endif
                    </div>

                    <div class="flex justify-end mt-4">
                        <button
                            wire:click="closeHierarchyModal"
                            class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors"
                        >
                            Close
                        </button>
                    </div>
                </div>
            </div>
        </div>
    @endif

    <!-- Notifications -->
    @if (session()->has('notify'))
        <div class="fixed bottom-4 right-4 z-50">
            <div class="bg-green-500 text-white px-4 py-2 rounded-md shadow-lg">
                {{ session('notify') }}
            </div>
        </div>
    @endif
</div>
