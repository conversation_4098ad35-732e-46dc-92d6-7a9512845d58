<div>
    <!-- <PERSON>er -->
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Water Rates</h1>
                <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                    Manage water rates for all estates
                </p>
                @if($requiredPermissionsHint)
                    <div class="mt-2">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300">
                            <svg class="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                            </svg>
                            Requires: {{ implode(', ', $requiredPermissionsHint) }}
                        </span>
                    </div>
                @endif
            </div>
            @if($showCreateButton)
                <button wire:click="setMode('create')" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-blue-500 dark:hover:bg-blue-600">
                    <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                    </svg>
                    Add Water Rate
                </button>
            @else
                <div class="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-500 dark:text-gray-400">
                    <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                    </svg>
                    Add Water Rate (requires permission)
                </div>
            @endif
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 mb-6">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Search Water Rates</label>
                <input type="text" wire:model.live="search" placeholder="Search by name or estate..." 
                       class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Estate</label>
                <select wire:model="estateId" class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                    <option value="">All Estates</option>
                    @foreach ($estates as $estate)
                        <option value="{{ $estate->id }}">{{ $estate->name }}</option>
                    @endforeach
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Sort By</label>
                <select wire:model="sortBy" class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                    <option value="effective_from">Effective Date</option>
                    <option value="name">Name</option>
                    <option value="rate_per_unit">Rate per Unit</option>
                    <option value="created_at">Created Date</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Sort Direction</label>
                <select wire:model="sortDirection" class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                    <option value="desc">Newest First</option>
                    <option value="asc">Oldest First</option>
                </select>
            </div>
        </div>
    </div>

    <!-- Water Rates Table -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Estate</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rate per Unit</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Effective Date</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                @foreach ($waterRates as $waterRate)
                <tr class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-medium text-gray-900">{{ $waterRate->name }}</div>
                        @if($waterRate->description)
                            <div class="text-sm text-gray-500">{{ $waterRate->description }}</div>
                        @endif
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">{{ $waterRate->estate->name }}</div>
                        <div class="text-sm text-gray-500">{{ $waterRate->estate->city }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900 font-semibold">KES {{ number_format($waterRate->rate_per_unit, 2) }}</div>
                        @if($waterRate->minimum_charge > 0)
                            <div class="text-sm text-gray-500">Min: KES {{ number_format($waterRate->minimum_charge, 2) }}</div>
                        @endif
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">{{ $waterRate->effective_from->format('M d, Y') }}</div>
                        @if($waterRate->effective_to)
                            <div class="text-sm text-gray-500">To: {{ $waterRate->effective_to->format('M d, Y') }}</div>
                        @endif
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        @php
                            if (!$waterRate->is_active) {
                                $statusClass = 'bg-gray-100 text-gray-800';
                                $statusText = 'Inactive';
                            } else {
                                $now = now();
                                if ($waterRate->effective_from > $now) {
                                    $statusClass = 'bg-blue-100 text-blue-800';
                                    $statusText = 'Future';
                                } elseif ($waterRate->effective_to && $waterRate->effective_to < $now) {
                                    $statusClass = 'bg-red-100 text-red-800';
                                    $statusText = 'Expired';
                                } else {
                                    $statusClass = 'bg-green-100 text-green-800';
                                    $statusText = 'Active';
                                }
                            }
                        @endphp
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ $statusClass }}">
                            {{ $statusText }}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div class="flex justify-end space-x-2">
                            @if($showEditActions && $canEditWaterRate($waterRate))
                                <button wire:click="setMode('edit', {{ $waterRate->id }})" class="inline-flex items-center px-2.5 py-1.5 border border-gray-300 dark:border-gray-600 shadow-sm text-xs font-medium rounded text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 mr-2">
                                    Edit
                                </button>
                            @else
                                <button disabled class="inline-flex items-center px-2.5 py-1.5 border border-gray-300 dark:border-gray-600 shadow-sm text-xs font-medium rounded text-gray-400 dark:text-gray-500 bg-gray-100 dark:bg-gray-800 cursor-not-allowed mr-2">
                                    Edit (no permission)
                                </button>
                            @endif
                            @if($showEditActions && $canDeleteWaterRate($waterRate))
                                <button wire:click="delete({{ $waterRate->id }})" class="inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:text-red-200 dark:bg-red-900/50 dark:hover:bg-red-900/70">
                                    Delete
                                </button>
                            @else
                                <button disabled class="inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-red-400 bg-red-50 dark:bg-red-900/30 cursor-not-allowed">
                                    Delete (no permission)
                                </button>
                            @endif
                        </div>
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <div class="mt-4">
        {{ $waterRates->links() }}
    </div>

    <!-- Create/Edit Form -->
    @if($mode === 'create' || $mode === 'edit')
        <x-form-modal
            :show="$mode !== 'list'"
            title="{{ $mode === 'create' ? 'Create New Water Rate' : 'Edit Water Rate' }}"
            onSave="save"
            onClose="resetToList"
            saveText="{{ $mode === 'create' ? 'Create' : 'Update' }}"
            maxWidth="2xl"
            name="water-rate-{{ $mode }}-modal"
        >
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Estate <span class="text-red-500">*</span></label>
                    <select wire:model="estateId" class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm" required>
                        <option value="">Select Estate</option>
                        @foreach ($estates as $estate)
                            <option value="{{ $estate->id }}">{{ $estate->name }} ({{ $estate->city }})</option>
                        @endforeach
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Rate Name <span class="text-red-500">*</span></label>
                    <input type="text" wire:model="name" class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm" required>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Rate per Unit (KES) <span class="text-red-500">*</span></label>
                    <input type="number" step="0.01" min="0" wire:model="rate_per_unit" class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm" required>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Minimum Charge (KES)</label>
                        <input type="number" step="0.01" min="0" wire:model="minimum_charge" class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Minimum Units</label>
                        <input type="number" min="0" wire:model="minimum_units" class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Fixed Charge (KES)</label>
                        <input type="number" step="0.01" min="0" wire:model="fixed_charge" class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                    </div>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Effective From <span class="text-red-500">*</span></label>
                        <input type="date" wire:model="effective_from" class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm" required>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Effective To</label>
                        <input type="date" wire:model="effective_to" class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                    </div>
                </div>
                <div>
                    <label class="flex items-center">
                        <input type="checkbox" wire:model="is_active" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        <label class="ml-2 block text-sm text-gray-700 dark:text-gray-300">Active</label>
                    </label>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Description</label>
                    <textarea wire:model="description" rows="3" class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"></textarea>
                </div>
            </div>
        </x-form-modal>
    @endif

    <!-- Delete Confirmation Modal -->
    @if($mode === 'delete' && $waterRateId)
        <x-confirmation-modal
            :show="true"
            title="Confirm Delete"
            message="Are you sure you want to delete this water rate? This action cannot be undone."
            confirmText="Delete"
            confirmColor="red"
            onConfirm="delete"
            onCancel="resetToList"
            name="water-rate-delete-modal"
        />
    @endif
</div>
