# Water Management System - Agent Instructions

This is a Laravel-based water management system with Livewire components for managing estates, houses, meter readings, and billing.

## Project Overview
- **Framework**: Laravel 12 with Livewire
- **Frontend**: Tailwind CSS with Tailadmin components
- **Database**: MySQL with Eloquent ORM
- **Testing**: Pest PHP
- **Build**: Vite for asset compilation

## Architecture

### Core Models
- **User**: System users with role-based access (management, reviewer, caretaker)
- **Estate**: Residential estates/compounds
- **House**: Individual housing units within estates
- **Contact**: Contact information for house occupants
- **WaterRate**: Pricing tiers for water consumption
- **MeterReading**: Monthly meter readings for houses
- **Invoice**: Generated bills based on readings
- **WhatsAppMessage**: WhatsApp integration for notifications


## Development Standards

### Code Style
- Follow PSR-12 coding standards
- Use strict types in all PHP files
- Type-hint all method parameters and return types
- Use Laravel's built-in features and helpers where possible

### Database Conventions
- Use Laravel migrations for all schema changes
- Follow <PERSON><PERSON> naming conventions for tables and columns
- Add indexes for frequently queried columns
- Use foreign key constraints for data integrity

### Livewire Components
- Place components in `app/Livewire/` directory
- Use kebab-case for component names
- Keep components focused on single responsibilities
- Use Livewire's validation features

### Testing
- Write feature tests for all user-facing functionality
- Use Pest PHP for testing syntax
- Test both happy paths and error scenarios
- Mock external services (WhatsApp, etc.)

## File Structure
```
app/
├── Models/          # Eloquent models
├── Livewire/        # Livewire components
├── Services/        # Business logic services
└── Exports/         # Excel/PDF export classes

database/
├── migrations/      # Database migrations
├── seeders/         # Database seeders
└── factories/       # Model factories

resources/
├── views/           # Blade templates
├── css/             # Tailwind CSS
└── js/              # JavaScript/Vite entry
```

## Common Tasks

### Adding a New Feature
1. Create migration for any new tables/columns
2. Create/update models with relationships
3. Create Livewire components for UI
4. Add routes in routes/web.php
5. Create/update views
6. Write tests
7. Update seeders if needed

### Working with Meter Readings
- Use ReadingValidationService for validation
- Check for duplicate readings
- Validate reading progression (new > old)
- Handle edge cases (meter resets, etc.)

### Invoice Generation
- Calculate based on water rates tiers
- Include previous balance if any
- Generate PDF for download
- Send WhatsApp notifications

## External References
When encountering @~/.agent-os/instructions/ references, use local .agent-os/instructions/ files instead.
all specs are under the local .agent-os/specs/ folder
