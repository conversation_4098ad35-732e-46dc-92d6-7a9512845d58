<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Water rates table (from 2024_01_01_000006_create_water_rates_table.php)
        Schema::create('water_rates', function (Blueprint $table) {
            $table->id();
            $table->foreignId('estate_id')->constrained()->onDelete('cascade');
            $table->string('name');
            $table->decimal('rate_per_unit', 10, 4); // Rate per unit (liter/m³)
            $table->decimal('minimum_charge', 10, 2)->default(0);
            $table->integer('minimum_units')->default(0);
            $table->decimal('fixed_charge', 10, 2)->default(0);
            $table->date('effective_from');
            $table->date('effective_to')->nullable();
            $table->boolean('is_active')->default(true);
            $table->text('description')->nullable();
            $table->timestamps();

            $table->index(['estate_id', 'is_active']);
            $table->index('effective_from');
            $table->index('effective_to');
        });

        // Meter readings table (consolidated from 2024_01_01_000007_create_meter_readings_table.php,
        // 2025_07_25_174800_add_location_fields_to_meter_readings_table.php,
        // and 2025_08_02_000001_add_validation_columns_to_meter_readings_table.php)
        Schema::create('meter_readings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('house_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('cascade'); // Who entered the reading
            $table->decimal('current_reading', 10, 2)->nullable(); // Made nullable for validation cases
            $table->decimal('previous_reading', 10, 2)->nullable();
            $table->decimal('consumption', 10, 2)->nullable(); // Auto-calculated
            $table->date('reading_date');
            $table->string('photo_path')->nullable(); // Path to meter photo
            $table->string('location_coordinates')->nullable(); // GPS coordinates of meter
            $table->text('notes')->nullable();
            $table->enum('status', ['draft', 'submitted', 'reviewed', 'approved', 'rejected'])->default('draft');
            $table->boolean('is_validated')->default(false);
            $table->string('validation_status')->default('pending'); // pending, valid, invalid, needs_review
            $table->text('validation_notes')->nullable();
            $table->foreignId('reviewed_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('reviewed_at')->nullable();
            $table->text('review_notes')->nullable();
            $table->timestamps();

            $table->index(['house_id', 'reading_date']);
            $table->index(['user_id', 'status']);
            $table->index('status');
            $table->index('validation_status');
            $table->unique(['house_id', 'reading_date']);
        });

        // Invoices table (consolidated from 2024_01_01_000008_create_invoices_table.php,
        // 2025_07_25_064633_add_status_fields_to_invoices_table.php,
        // and 2025_08_04_170000_add_approval_workflow_to_invoices_table.php)
        Schema::create('invoices', function (Blueprint $table) {
            $table->id();
            $table->string('invoice_number')->unique();
            $table->foreignId('house_id')->constrained()->onDelete('cascade');
            $table->foreignId('meter_reading_id')->constrained()->onDelete('cascade');
            $table->foreignId('water_rate_id')->constrained()->onDelete('cascade');
            $table->date('billing_period_start');
            $table->date('billing_period_end');
            $table->decimal('previous_reading', 10, 2);
            $table->decimal('current_reading', 10, 2);
            $table->decimal('consumption', 10, 2);
            $table->decimal('rate_per_unit', 10, 4);
            $table->decimal('amount', 10, 2);
            $table->decimal('fixed_charge', 10, 2)->default(0);
            $table->decimal('total_amount', 10, 2);
            $table->decimal('previous_balance', 10, 2)->default(0);
            $table->decimal('adjustments', 10, 2)->default(0);
            $table->decimal('final_amount', 10, 2);
            $table->date('due_date');
            $table->enum('status', ['draft', 'pending_approval', 'approved', 'sent', 'paid', 'overdue', 'cancelled'])->default('draft');
            $table->enum('approval_status', ['pending', 'approved', 'rejected'])->default('pending');
            $table->foreignId('approved_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('approved_at')->nullable();
            $table->text('approval_notes')->nullable();
            $table->dateTime('sent_at')->nullable();
            $table->dateTime('paid_at')->nullable();
            $table->string('payment_reference')->nullable();
            $table->text('notes')->nullable();
            $table->string('pdf_path')->nullable();
            $table->string('public_link')->nullable(); // Secure link for WhatsApp
            $table->timestamps();

            $table->index(['house_id', 'billing_period_start']);
            $table->index('invoice_number');
            $table->index('status');
            $table->index('approval_status');
            $table->index('due_date');
            $table->index('sent_at');
            $table->index('paid_at');
        });

        // WhatsApp messages table (consolidated from 2024_01_01_000009_create_whatsapp_messages_table.php,
        // 2025_08_02_151455_add_direction_and_media_type_to_whatsapp_messages_table.php,
        // and 2025_08_02_151537_make_sender_id_nullable_in_whatsapp_messages.php)
        Schema::create('whatsapp_messages', function (Blueprint $table) {
            $table->id();
            $table->foreignId('house_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('contact_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('invoice_id')->nullable()->constrained()->onDelete('set null');
            $table->string('message_id')->unique();
            $table->string('phone_number');
            $table->enum('direction', ['incoming', 'outgoing'])->default('outgoing');
            $table->enum('status', ['pending', 'sent', 'delivered', 'read', 'failed'])->default('pending');
            $table->enum('message_type', ['text', 'image', 'document', 'invoice', 'reminder'])->default('text');
            $table->text('message_content');
            $table->string('media_url')->nullable();
            $table->string('media_type')->nullable();
            $table->string('sender_id')->nullable(); // WhatsApp sender ID
            $table->timestamp('sent_at')->nullable();
            $table->timestamp('delivered_at')->nullable();
            $table->timestamp('read_at')->nullable();
            $table->text('error_message')->nullable();
            $table->json('metadata')->nullable();
            $table->timestamps();

            $table->index(['house_id', 'status']);
            $table->index(['contact_id', 'status']);
            $table->index('phone_number');
            $table->index('message_id');
            $table->index('status');
            $table->index('direction');
            $table->index('sent_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('whatsapp_messages');
        Schema::dropIfExists('invoices');
        Schema::dropIfExists('meter_readings');
        Schema::dropIfExists('water_rates');
    }
};
