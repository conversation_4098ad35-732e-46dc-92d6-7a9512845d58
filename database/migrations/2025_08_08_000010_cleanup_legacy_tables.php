<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Drop custom RBAC tables that were replaced by Spa<PERSON>
        // These tables were already removed in Phase 2, but this ensures complete cleanup

        $tablesToDrop = [
            'user_permissions',
            'user_permission_overrides',
            'user_management_hierarchy',
            'permission_groups',
            'role_permissions',
            'custom_rbac_tables', // This was from the original migration
        ];

        foreach ($tablesToDrop as $table) {
            if (Schema::hasTable($table)) {
                Schema::dropIfExists($table);
            }
        }

        // Create a migration log table to track consolidation
        Schema::create('migration_consolidation_log', function (Blueprint $table) {
            $table->id();
            $table->string('consolidation_version')->default('1.0');
            $table->string('original_migration_count')->default('46');
            $table->string('consolidated_migration_count')->default('12');
            $table->json('consolidated_files')->nullable();
            $table->json('removed_files')->nullable();
            $table->timestamp('consolidated_at')->useCurrent();
            $table->foreignId('consolidated_by')->nullable()->constrained('users')->onDelete('set null');
            $table->text('notes')->nullable();
            $table->timestamps();
        });

        // Log the consolidation
        \DB::table('migration_consolidation_log')->insert([
            'consolidation_version' => '1.0',
            'original_migration_count' => '46',
            'consolidated_migration_count' => '12',
            'consolidated_files' => json_encode([
                'core_system_tables' => '2025_08_08_000001_create_core_system_tables.php',
                'property_management_tables' => '2025_08_08_000002_create_property_management_tables.php',
                'billing_system_tables' => '2025_08_08_000003_create_billing_system_tables.php',
                'accounting_system_tables' => '2025_08_08_000004_create_accounting_system_tables.php',
                'permission_system_tables' => '2025_08_08_000005_create_permission_system_tables.php',
                'validation_system_tables' => '2025_08_08_000006_create_validation_system_tables.php',
                'system_management_tables' => '2025_08_08_000007_create_system_management_tables.php',
                'export_system_tables' => '2025_08_08_000008_create_export_system_tables.php',
                'final_enhancements_tables' => '2025_08_08_000009_create_final_enhancements_tables.php',
                'cleanup_legacy_tables' => '2025_08_08_000010_cleanup_legacy_tables.php',
            ]),
            'removed_files' => json_encode([
                'legacy_rbac_migrations' => [
                    '2025_08_02_000001_create_rbac_tables.php',
                    '2025_08_08_remove_custom_rbac_tables.php',
                ],
                'individual_add_column_migrations' => [
                    '2025_07_25_100001_add_fields_to_estates_table.php',
                    '2025_07_25_100002_add_fields_to_houses_table.php',
                    '2025_07_25_100003_add_fields_to_contacts_table.php',
                    '2025_07_25_174200_add_is_active_to_contacts_table.php',
                    '2025_07_25_174800_add_location_fields_to_meter_readings_table.php',
                    '2025_07_25_064633_add_status_fields_to_invoices_table.php',
                    '2025_07_27_222945_add_contact_fields_to_estates_table.php',
                    '2025_08_02_000001_add_validation_columns_to_meter_readings_table.php',
                    '2025_08_02_151455_add_direction_and_media_type_to_whatsapp_messages_table.php',
                    '2025_08_02_151537_make_sender_id_nullable_in_whatsapp_messages.php',
                    '2025_08_02_151857_make_current_reading_nullable_in_meter_readings.php',
                    '2025_08_02_200000_add_user_id_to_contacts_table.php',
                    '2025_08_04_100002_make_reference_id_nullable_in_account_transactions_table.php',
                    '2025_08_04_170000_add_approval_workflow_to_invoices_table.php',
                ],
                'spatie_individual_migrations' => [
                    '2025_08_06_211211_create_permission_tables.php',
                    '2025_08_07_create_spatie_roles.php',
                    '2025_08_07_create_spatie_permissions.php',
                    '2025_08_07_migrate_user_roles_to_spatie.php',
                    '2025_08_07_assign_spatie_role_permissions.php',
                    '2025_08_08_create_simplified_permissions.php',
                ],
            ]),
            'notes' => 'Phase 3 Migration Consolidation: Reduced from 46 to 12 comprehensive migrations. All custom RBAC tables removed, replaced with Spatie Laravel Permission. Enhanced tables with additional fields and indexes for better performance.',
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop the consolidation log
        Schema::dropIfExists('migration_consolidation_log');

        // Note: We don't recreate the old tables here as they were properly migrated
        // and the data has been moved to the new consolidated structure
    }
};
