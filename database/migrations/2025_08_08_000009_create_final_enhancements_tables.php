<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Estate contact fields (from 2025_07_27_222945_add_contact_fields_to_estates_table.php)
        Schema::table('estates', function (Blueprint $table) {
            $table->string('contact_person')->nullable()->after('manager_email');
            $table->string('contact_phone')->nullable()->after('contact_person');
            $table->string('contact_email')->nullable()->after('contact_phone');
            $table->string('emergency_contact')->nullable()->after('contact_email');
            $table->string('emergency_phone')->nullable()->after('emergency_contact');
            $table->string('website')->nullable()->after('emergency_phone');
            $table->string('social_media')->nullable()->after('website');
        });

        // House registry enhancements
        Schema::table('houses', function (Blueprint $table) {
            $table->string('building_name')->nullable()->after('block');
            $table->string('unit_number')->nullable()->after('house_number');
            $table->string('property_id')->nullable()->after('meter_number');
            $table->enum('water_meter_type', ['analog', 'digital', 'smart'])->default('analog')->after('meter_number');
            $table->date('meter_installation_date')->nullable()->after('water_meter_type');
            $table->string('meter_brand')->nullable()->after('meter_installation_date');
            $table->string('meter_model')->nullable()->after('meter_brand');
            $table->decimal('meter_capacity', 10, 2)->nullable()->after('meter_model');
            $table->enum('billing_cycle', ['monthly', 'bimonthly', 'quarterly'])->default('monthly')->after('type');
            $table->string('utility_account_number')->nullable()->after('billing_cycle');
            $table->json('amenities')->nullable()->after('notes');
            $table->json('utilities')->nullable()->after('amenities');
        });

        // Contact communication preferences
        Schema::table('contacts', function (Blueprint $table) {
            $table->string('preferred_language')->default('en')->after('receive_notifications');
            $table->boolean('email_notifications')->default(true)->after('preferred_language');
            $table->boolean('sms_notifications')->default(true)->after('email_notifications');
            $table->boolean('whatsapp_notifications')->default(true)->after('sms_notifications');
            $table->enum('invoice_delivery_method', ['email', 'whatsapp', 'sms', 'print'])->default('whatsapp')->after('whatsapp_notifications');
            $table->string('alternative_email')->nullable()->after('invoice_delivery_method');
            $table->string('alternative_phone')->nullable()->after('alternative_email');
            $table->json('communication_preferences')->nullable()->after('alternative_phone');
        });

        // Water rate enhancements
        Schema::table('water_rates', function (Blueprint $table) {
            $table->enum('rate_type', ['flat', 'tiered', 'seasonal'])->default('flat')->after('name');
            $table->json('tier_rates')->nullable()->after('rate_per_unit'); // For tiered pricing
            $table->string('currency')->default('KES')->after('tier_rates');
            $table->enum('billing_unit', ['liters', 'cubic_meters', 'gallons'])->default('cubic_meters')->after('currency');
            $table->decimal('tax_rate', 5, 2)->default(0)->after('billing_unit');
            $table->boolean('includes_tax')->default(false)->after('tax_rate');
            $table->json('seasonal_rates')->nullable()->after('effective_to'); // For seasonal pricing
        });

        // Meter reading enhancements
        Schema::table('meter_readings', function (Blueprint $table) {
            $table->enum('reading_method', ['manual', 'photo', 'automatic', 'estimated'])->default('manual')->after('photo_path');
            $table->string('meter_serial_number')->nullable()->after('reading_method');
            $table->decimal('estimated_consumption', 10, 2)->nullable()->after('consumption');
            $table->boolean('is_estimated')->default(false)->after('estimated_consumption');
            $table->string('estimation_reason')->nullable()->after('is_estimated');
            $table->json('reading_metadata')->nullable()->after('validation_notes');
            $table->string('device_id')->nullable()->after('reading_metadata'); // For smart meters
            $table->timestamp('synced_at')->nullable()->after('device_id'); // For smart meter sync
        });

        // Invoice enhancements
        Schema::table('invoices', function (Blueprint $table) {
            $table->string('currency')->default('KES')->after('total_amount');
            $table->decimal('tax_amount', 10, 2)->default(0)->after('currency');
            $table->decimal('discount_amount', 10, 2)->default(0)->after('tax_amount');
            $table->string('discount_reason')->nullable()->after('discount_amount');
            $table->enum('invoice_type', ['regular', 'adjustment', 'final', 'proforma', 'bill'])->default('regular')->after('discount_reason');
            $table->string('parent_invoice_id')->nullable()->after('invoice_type'); // For invoice adjustments
            $table->json('payment_terms')->nullable()->after('notes');
            $table->json('billing_address')->nullable()->after('payment_terms');
            $table->string('customer_reference')->nullable()->after('billing_address');
        });

        // Account transaction enhancements
        Schema::table('account_transactions', function (Blueprint $table) {
            $table->string('currency')->default('KES')->after('amount');
            $table->decimal('exchange_rate', 10, 4)->default(1)->after('currency');
            $table->string('transaction_reference')->unique()->after('exchange_rate');
            $table->string('batch_reference')->nullable()->after('transaction_reference');
            $table->json('payment_details')->nullable()->after('payment_reference');
            $table->json('reconciliation_data')->nullable()->after('payment_details');
            $table->timestamp('reconciled_at')->nullable()->after('reconciliation_data');
            $table->foreignId('reconciled_by')->nullable()->constrained('users')->onDelete('set null')->after('reconciled_at');
        });

        // WhatsApp message enhancements
        Schema::table('whatsapp_messages', function (Blueprint $table) {
            $table->string('campaign_id')->nullable()->after('message_id');
            $table->string('template_name')->nullable()->after('campaign_id');
            $table->json('template_variables')->nullable()->after('template_name');
            $table->string('message_category')->nullable()->after('message_type'); // invoice, payment, reminder, notification
            $table->integer('retry_count')->default(0)->after('error_message');
            $table->timestamp('next_retry_at')->nullable()->after('retry_count');
            $table->json('delivery_metadata')->nullable()->after('metadata');
            $table->string('conversation_id')->nullable()->after('delivery_metadata');
        });

        // Create indexes for performance optimization
        Schema::table('estates', function (Blueprint $table) {
            $table->index(['is_active', 'deleted_at']);
            $table->index('manager_email');
            $table->index('contact_email');
        });

        Schema::table('houses', function (Blueprint $table) {
            $table->index(['estate_id', 'is_active', 'deleted_at']);
            $table->index(['type', 'billing_cycle']);
            $table->index('water_meter_type');
            $table->index('property_id');
        });

        Schema::table('contacts', function (Blueprint $table) {
            $table->index(['house_id', 'is_active', 'deleted_at']);
            $table->index(['type', 'is_primary']);
            $table->index('preferred_language');
            $table->index('invoice_delivery_method');
        });

        Schema::table('meter_readings', function (Blueprint $table) {
            $table->index(['house_id', 'status', 'validation_status']);
            $table->index(['reading_date', 'status']);
            $table->index('reading_method');
            $table->index('is_estimated');
        });

        Schema::table('invoices', function (Blueprint $table) {
            $table->index(['house_id', 'status', 'approval_status']);
            $table->index(['billing_period_start', 'billing_period_end']);
            $table->index('invoice_type');
            $table->index('currency');
        });

        Schema::table('account_transactions', function (Blueprint $table) {
            $table->index(['house_account_id', 'transaction_type', 'status']);
            $table->index('transaction_reference');
            $table->index('batch_reference');
            $table->index('currency');
        });

        Schema::table('whatsapp_messages', function (Blueprint $table) {
            $table->index(['house_id', 'status', 'direction']);
            $table->index('campaign_id');
            $table->index('template_name');
            $table->index('message_category');
            $table->index('conversation_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop indexes first
        Schema::table('whatsapp_messages', function (Blueprint $table) {
            $table->dropIndex(['house_id', 'status', 'direction']);
            $table->dropIndex('campaign_id');
            $table->dropIndex('template_name');
            $table->dropIndex('message_category');
            $table->dropIndex('conversation_id');
        });

        Schema::table('account_transactions', function (Blueprint $table) {
            $table->dropIndex(['house_account_id', 'transaction_type', 'status']);
            $table->dropIndex('transaction_reference');
            $table->dropIndex('batch_reference');
            $table->dropIndex('currency');
        });

        Schema::table('invoices', function (Blueprint $table) {
            $table->dropIndex(['house_id', 'status', 'approval_status']);
            $table->dropIndex(['billing_period_start', 'billing_period_end']);
            $table->dropIndex('invoice_type');
            $table->dropIndex('currency');
        });

        Schema::table('meter_readings', function (Blueprint $table) {
            $table->dropIndex(['house_id', 'status', 'validation_status']);
            $table->dropIndex(['reading_date', 'status']);
            $table->dropIndex('reading_method');
            $table->dropIndex('is_estimated');
        });

        Schema::table('contacts', function (Blueprint $table) {
            $table->dropIndex(['house_id', 'is_active', 'deleted_at']);
            $table->dropIndex(['type', 'is_primary']);
            $table->dropIndex('preferred_language');
            $table->dropIndex('invoice_delivery_method');
        });

        Schema::table('houses', function (Blueprint $table) {
            $table->dropIndex(['estate_id', 'is_active', 'deleted_at']);
            $table->dropIndex(['type', 'billing_cycle']);
            $table->dropIndex('water_meter_type');
            $table->dropIndex('property_id');
        });

        Schema::table('estates', function (Blueprint $table) {
            $table->dropIndex(['is_active', 'deleted_at']);
            $table->dropIndex('manager_email');
            $table->dropIndex('contact_email');
        });

        // Drop enhanced columns
        Schema::table('whatsapp_messages', function (Blueprint $table) {
            $table->dropColumn([
                'campaign_id', 'template_name', 'template_variables', 'message_category',
                'retry_count', 'next_retry_at', 'delivery_metadata', 'conversation_id',
            ]);
        });

        Schema::table('account_transactions', function (Blueprint $table) {
            $table->dropColumn([
                'currency', 'exchange_rate', 'transaction_reference', 'batch_reference',
                'payment_details', 'reconciliation_data', 'reconciled_at', 'reconciled_by',
            ]);
        });

        Schema::table('invoices', function (Blueprint $table) {
            $table->dropColumn([
                'currency', 'tax_amount', 'discount_amount', 'discount_reason',
                'invoice_type', 'parent_invoice_id', 'payment_terms', 'billing_address',
                'customer_reference',
            ]);
        });

        Schema::table('meter_readings', function (Blueprint $table) {
            $table->dropColumn([
                'reading_method', 'meter_serial_number', 'estimated_consumption',
                'is_estimated', 'estimation_reason', 'reading_metadata', 'device_id', 'synced_at',
            ]);
        });

        Schema::table('water_rates', function (Blueprint $table) {
            $table->dropColumn([
                'rate_type', 'tier_rates', 'currency', 'billing_unit', 'tax_rate',
                'includes_tax', 'seasonal_rates',
            ]);
        });

        Schema::table('contacts', function (Blueprint $table) {
            $table->dropColumn([
                'preferred_language', 'email_notifications', 'sms_notifications',
                'whatsapp_notifications', 'invoice_delivery_method', 'alternative_email',
                'alternative_phone', 'communication_preferences',
            ]);
        });

        Schema::table('houses', function (Blueprint $table) {
            $table->dropColumn([
                'building_name', 'unit_number', 'property_id', 'water_meter_type',
                'meter_installation_date', 'meter_brand', 'meter_model', 'meter_capacity',
                'billing_cycle', 'utility_account_number', 'amenities', 'utilities',
            ]);
        });

        Schema::table('estates', function (Blueprint $table) {
            $table->dropColumn([
                'contact_person', 'contact_phone', 'contact_email', 'emergency_contact',
                'emergency_phone', 'website', 'social_media',
            ]);
        });
    }
};
