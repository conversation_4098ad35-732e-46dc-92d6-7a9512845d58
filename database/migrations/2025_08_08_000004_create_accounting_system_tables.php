<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // House accounts table (from 2025_08_04_100000_create_house_accounts_table.php)
        Schema::create('house_accounts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('house_id')->unique()->constrained()->onDelete('cascade');
            $table->decimal('current_balance', 10, 2)->default(0);
            $table->decimal('total_credit', 10, 2)->default(0);
            $table->decimal('total_debit', 10, 2)->default(0);
            $table->decimal('available_credit', 10, 2)->default(0);
            $table->decimal('credit_limit', 10, 2)->default(0);
            $table->timestamp('last_transaction_date')->nullable();
            $table->timestamp('last_payment_date')->nullable();
            $table->timestamp('last_invoice_date')->nullable();
            $table->enum('account_status', ['active', 'suspended', 'closed'])->default('active');
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->index('house_id');
            $table->index('current_balance');
            $table->index('account_status');
            $table->index('last_transaction_date');
        });

        // Account transactions table (consolidated from 2025_08_04_100001_create_account_transactions_table.php
        // and 2025_08_04_100002_make_reference_id_nullable_in_account_transactions_table.php)
        Schema::create('account_transactions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('house_account_id')->constrained()->onDelete('cascade');
            $table->string('transaction_type'); // invoice, payment, adjustment, credit_note, penalty
            $table->string('reference_type'); // Invoice, Payment, Adjustment, CreditNote, Penalty
            $table->unsignedBigInteger('reference_id')->nullable(); // ID of referenced model
            $table->decimal('amount', 10, 2);
            $table->decimal('balance_before', 10, 2);
            $table->decimal('balance_after', 10, 2);
            $table->enum('direction', ['credit', 'debit'])->default('debit');
            $table->string('description')->nullable();
            $table->text('notes')->nullable();
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');
            $table->string('payment_method')->nullable(); // cash, mpesa, bank_transfer, cheque
            $table->string('payment_reference')->nullable();
            $table->string('receipt_number')->nullable();
            $table->enum('status', ['pending', 'completed', 'failed', 'reversed'])->default('completed');
            $table->timestamp('processed_at')->nullable();
            $table->timestamps();

            $table->index(['house_account_id', 'created_at']);
            $table->index('transaction_type');
            $table->index('reference_type');
            $table->index('direction');
            $table->index('status');
            $table->index('processed_at');
            $table->index(['reference_type', 'reference_id']);
        });

        // Invoice line items table (from 2025_07_25_064645_create_invoice_line_items_table.php)
        Schema::create('invoice_line_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('invoice_id')->constrained()->onDelete('cascade');
            $table->string('item_type'); // water_consumption, fixed_charge, adjustment, penalty
            $table->string('description');
            $table->decimal('quantity', 10, 2)->default(1);
            $table->decimal('unit_price', 10, 4);
            $table->decimal('amount', 10, 2);
            $table->decimal('tax_rate', 5, 2)->default(0);
            $table->decimal('tax_amount', 10, 2)->default(0);
            $table->decimal('total_amount', 10, 2);
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->index(['invoice_id', 'item_type']);
            $table->index('item_type');
        });

        // Invoice payments table (from 2025_07_25_064654_create_invoice_payments_table.php)
        Schema::create('invoice_payments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('invoice_id')->constrained()->onDelete('cascade');
            $table->foreignId('account_transaction_id')->constrained()->onDelete('cascade');
            $table->decimal('amount', 10, 2);
            $table->decimal('remaining_balance', 10, 2);
            $table->enum('payment_status', ['partial', 'full', 'overpaid'])->default('partial');
            $table->string('payment_method'); // cash, mpesa, bank_transfer, cheque
            $table->string('payment_reference')->nullable();
            $table->string('receipt_number')->nullable();
            $table->timestamp('payment_date');
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->index(['invoice_id', 'payment_date']);
            $table->index('account_transaction_id');
            $table->index('payment_method');
            $table->index('payment_status');
            $table->index('payment_date');
        });

        // Invoice adjustments table (from 2025_07_25_064700_create_invoice_adjustments_table.php)
        Schema::create('invoice_adjustments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('invoice_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->enum('adjustment_type', ['discount', 'waiver', 'penalty', 'correction']);
            $table->string('reason');
            $table->decimal('amount', 10, 2);
            $table->decimal('percentage', 5, 2)->nullable();
            $table->enum('status', ['pending', 'approved', 'rejected'])->default('pending');
            $table->foreignId('approved_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('approved_at')->nullable();
            $table->text('approval_notes')->nullable();
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->index(['invoice_id', 'adjustment_type']);
            $table->index('adjustment_type');
            $table->index('status');
            $table->index('approved_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('invoice_adjustments');
        Schema::dropIfExists('invoice_payments');
        Schema::dropIfExists('invoice_line_items');
        Schema::dropIfExists('account_transactions');
        Schema::dropIfExists('house_accounts');
    }
};
