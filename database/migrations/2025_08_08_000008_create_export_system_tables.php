<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Export templates table (from 2025_07_26_230837_create_export_templates_table.php)
        Schema::create('export_templates', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('entity_type'); // invoice, meter_reading, account_transaction, contact, house
            $table->string('format'); // xlsx, csv, pdf, json
            $table->json('columns'); // Column configuration
            $table->json('filters')->nullable(); // Default filter configuration
            $table->json('sorting')->nullable(); // Default sorting configuration
            $table->json('formatting')->nullable(); // Formatting rules
            $table->boolean('is_public')->default(false);
            $table->boolean('is_system')->default(false); // System templates cannot be deleted
            $table->foreignId('estate_id')->nullable()->constrained()->onDelete('cascade');
            $table->foreignId('created_by')->constrained('users')->onDelete('set null');
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();

            $table->unique(['name', 'entity_type', 'format', 'estate_id']);
            $table->index(['entity_type', 'format']);
            $table->index('is_public');
            $table->index('is_system');
            $table->index('estate_id');
        });

        // Export jobs table (from 2025_07_26_230927_create_export_jobs_table.php)
        Schema::create('export_jobs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->cascadeOnDelete();
            $table->foreignId('export_template_id')->nullable()->constrained()->nullOnDelete();
            $table->string('status')->default('pending'); // pending, processing, completed, failed, cancelled
            $table->string('entity_type');
            $table->string('format')->default('xlsx');
            $table->json('filters')->nullable();
            $table->json('columns')->nullable();
            $table->string('file_path')->nullable();
            $table->string('file_name')->nullable();
            $table->unsignedInteger('total_records')->default(0);
            $table->unsignedInteger('processed_records')->default(0);
            $table->unsignedInteger('failed_records')->default(0);
            $table->decimal('progress_percentage', 5, 2)->default(0);
            $table->timestamp('started_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->timestamp('failed_at')->nullable();
            $table->text('error')->nullable();
            $table->json('error_details')->nullable();
            $table->string('download_url')->nullable();
            $table->timestamp('expires_at')->nullable();
            $table->timestamps();

            $table->index(['user_id', 'status']);
            $table->index('entity_type');
            $table->index('format');
            $table->index('status');
            $table->index('started_at');
            $table->index('completed_at');
            $table->index('expires_at');
        });

        // Export job batches table (for batch processing)
        Schema::create('export_job_batches', function (Blueprint $table) {
            $table->id();
            $table->string('batch_id')->unique();
            $table->foreignId('user_id')->constrained()->cascadeOnDelete();
            $table->string('name');
            $table->string('entity_type');
            $table->string('format')->default('xlsx');
            $table->unsignedInteger('total_jobs')->default(0);
            $table->unsignedInteger('completed_jobs')->default(0);
            $table->unsignedInteger('failed_jobs')->default(0);
            $table->enum('status', ['pending', 'processing', 'completed', 'failed', 'cancelled'])->default('pending');
            $table->json('filters')->nullable();
            $table->json('metadata')->nullable();
            $table->timestamp('started_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->timestamp('failed_at')->nullable();
            $table->text('error')->nullable();
            $table->string('download_url')->nullable();
            $table->timestamp('expires_at')->nullable();
            $table->timestamps();

            $table->index(['user_id', 'status']);
            $table->index('batch_id');
            $table->index('entity_type');
            $table->index('status');
            $table->index('started_at');
            $table->index('completed_at');
        });

        // Export schedules table (for automated exports)
        Schema::create('export_schedules', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->cascadeOnDelete();
            $table->foreignId('export_template_id')->constrained()->cascadeOnDelete();
            $table->string('name');
            $table->string('schedule_type'); // daily, weekly, monthly, custom
            $table->json('schedule_config'); // Cron-like configuration
            $table->json('filters')->nullable();
            $table->string('recipients')->nullable(); // Email addresses for notification
            $table->boolean('is_active')->default(true);
            $table->timestamp('last_run_at')->nullable();
            $table->timestamp('next_run_at');
            $table->unsignedInteger('run_count')->default(0);
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->index(['user_id', 'is_active']);
            $table->index('schedule_type');
            $table->index('is_active');
            $table->index('next_run_at');
            $table->index('last_run_at');
        });

        // Export history table (for tracking export usage)
        Schema::create('export_history', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->cascadeOnDelete();
            $table->foreignId('export_job_id')->nullable()->constrained()->nullOnDelete();
            $table->string('entity_type');
            $table->string('format');
            $table->unsignedInteger('record_count')->default(0);
            $table->decimal('file_size_mb', 10, 2)->default(0);
            $table->timestamp('downloaded_at')->nullable();
            $table->string('ip_address')->nullable();
            $table->string('user_agent')->nullable();
            $table->timestamps();

            $table->index(['user_id', 'entity_type']);
            $table->index('entity_type');
            $table->index('format');
            $table->index('downloaded_at');
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('export_history');
        Schema::dropIfExists('export_schedules');
        Schema::dropIfExists('export_job_batches');
        Schema::dropIfExists('export_jobs');
        Schema::dropIfExists('export_templates');
    }
};
