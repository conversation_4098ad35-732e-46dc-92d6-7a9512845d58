<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Activity logs table (from 2025_07_26_230829_create_activity_logs_table.php)
        Schema::create('activity_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->nullable()->constrained()->nullOnDelete();
            $table->string('action');
            $table->string('entity_type')->nullable();
            $table->unsignedBigInteger('entity_id')->nullable();
            $table->text('description');
            $table->json('old_values')->nullable();
            $table->json('new_values')->nullable();
            $table->string('ip_address')->nullable();
            $table->string('user_agent')->nullable();
            $table->string('session_id')->nullable();
            $table->enum('log_level', ['info', 'warning', 'error', 'debug'])->default('info');
            $table->timestamps();

            $table->index(['entity_type', 'entity_id']);
            $table->index('user_id');
            $table->index('action');
            $table->index('log_level');
            $table->index('created_at');
        });

        // System settings table (from 2025_07_26_230833_create_system_settings_table.php)
        Schema::create('system_settings', function (Blueprint $table) {
            $table->id();
            $table->string('key')->unique();
            $table->string('group')->index();
            $table->text('value')->nullable();
            $table->string('type')->default('string');
            $table->string('display_name');
            $table->text('description')->nullable();
            $table->boolean('is_public')->default(false);
            $table->boolean('is_required')->default(false);
            $table->json('options')->nullable();
            $table->json('validation_rules')->nullable();
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();

            $table->index('key');
            $table->index('type');
            $table->index('is_public');
            $table->index('is_required');
        });

        // Message templates table (from 2025_07_26_230841_create_message_templates_table.php)
        Schema::create('message_templates', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('type'); // whatsapp, sms, email, notification
            $table->string('category'); // invoice, payment, reminder, welcome, system
            $table->string('subject')->nullable();
            $table->text('content');
            $table->json('variables')->nullable(); // Available template variables
            $table->json('default_values')->nullable(); // Default values for variables
            $table->boolean('is_active')->default(true);
            $table->boolean('is_system')->default(false); // System templates cannot be deleted
            $table->foreignId('estate_id')->nullable()->constrained()->onDelete('cascade');
            $table->foreignId('created_by')->constrained('users')->onDelete('set null');
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();

            $table->unique(['name', 'type', 'estate_id']);
            $table->index(['type', 'category']);
            $table->index('is_active');
            $table->index('is_system');
            $table->index('estate_id');
        });

        // System notifications table (for in-app notifications)
        Schema::create('system_notifications', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('type'); // info, warning, error, success
            $table->string('title');
            $table->text('message');
            $table->json('data')->nullable(); // Additional notification data
            $table->string('action_url')->nullable();
            $table->string('action_text')->nullable();
            $table->boolean('is_read')->default(false);
            $table->timestamp('read_at')->nullable();
            $table->timestamp('expires_at')->nullable();
            $table->timestamps();

            $table->index(['user_id', 'is_read']);
            $table->index('type');
            $table->index('is_read');
            $table->index('expires_at');
            $table->index('created_at');
        });

        // System audit logs table (for security and compliance)
        Schema::create('system_audit_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->nullable()->constrained()->nullOnDelete();
            $table->string('event_type'); // login, logout, permission_change, data_access, system_change
            $table->string('event_category'); // authentication, authorization, data, system
            $table->text('description');
            $table->json('event_data')->nullable();
            $table->string('ip_address');
            $table->string('user_agent')->nullable();
            $table->string('session_id')->nullable();
            $table->enum('severity', ['low', 'medium', 'high', 'critical'])->default('medium');
            $table->timestamps();

            $table->index(['user_id', 'event_type']);
            $table->index('event_type');
            $table->index('event_category');
            $table->index('severity');
            $table->index('ip_address');
            $table->index('created_at');
        });

        // System health checks table (for monitoring system health)
        Schema::create('system_health_checks', function (Blueprint $table) {
            $table->id();
            $table->string('check_name');
            $table->string('check_type'); // database, storage, external_service, system_resource
            $table->enum('status', ['healthy', 'warning', 'critical', 'unknown'])->default('unknown');
            $table->text('message')->nullable();
            $table->json('metrics')->nullable();
            $table->decimal('response_time', 10, 3)->nullable();
            $table->timestamp('checked_at');
            $table->timestamps();

            $table->index('check_name');
            $table->index('check_type');
            $table->index('status');
            $table->index('checked_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('system_health_checks');
        Schema::dropIfExists('system_audit_logs');
        Schema::dropIfExists('system_notifications');
        Schema::dropIfExists('message_templates');
        Schema::dropIfExists('system_settings');
        Schema::dropIfExists('activity_logs');
    }
};
