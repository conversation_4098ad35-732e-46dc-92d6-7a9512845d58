<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Estates table (consolidated from 2024_01_01_000003_create_estates_table.php
        // and 2025_07_25_100001_add_fields_to_estates_table.php)
        Schema::create('estates', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('code')->unique();
            $table->string('address')->nullable();
            $table->string('city')->nullable();
            $table->string('state')->nullable();
            $table->string('postal_code')->nullable();
            $table->string('country')->default('Kenya');
            $table->text('description')->nullable();
            $table->string('manager_name')->nullable();
            $table->string('manager_phone')->nullable();
            $table->string('manager_email')->nullable();
            $table->json('settings')->nullable();
            $table->integer('total_houses')->default(0);
            $table->integer('occupied_houses')->default(0);
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            $table->softDeletes();

            $table->index('name');
            $table->index('code');
            $table->index('is_active');
        });

        // Houses table (consolidated from 2024_01_01_000004_create_houses_table.php
        // and 2025_07_25_100002_add_fields_to_houses_table.php)
        Schema::create('houses', function (Blueprint $table) {
            $table->id();
            $table->foreignId('estate_id')->constrained()->onDelete('cascade');
            $table->string('house_number')->unique();
            $table->string('block')->nullable();
            $table->string('floor')->nullable();
            $table->string('type')->default('residential'); // residential, commercial
            $table->string('meter_number')->unique();
            $table->decimal('initial_reading', 10, 2)->default(0);
            $table->integer('bedrooms')->nullable();
            $table->decimal('size_sqft', 8, 2)->nullable();
            $table->decimal('monthly_rent', 10, 2)->nullable();
            $table->date('occupancy_date')->nullable();
            $table->boolean('is_active')->default(true);
            $table->text('notes')->nullable();
            $table->string('gps_coordinates')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->index(['estate_id', 'house_number']);
            $table->index('meter_number');
            $table->index('type');
            $table->index('is_active');
        });

        // Contacts table (consolidated from 2024_01_01_000005_create_contacts_table.php,
        // 2025_07_25_100003_add_fields_to_contacts_table.php,
        // 2025_07_25_174200_add_is_active_to_contacts_table.php,
        // and 2025_08_02_200000_add_user_id_to_contacts_table.php)
        Schema::create('contacts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('house_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');
            $table->string('name')->nullable(); // Made nullable for first/last name split
            $table->string('first_name')->nullable();
            $table->string('last_name')->nullable();
            $table->string('middle_name')->nullable();
            $table->string('email')->nullable();
            $table->string('phone')->nullable();
            $table->string('whatsapp_number')->nullable();
            $table->string('id_number')->nullable();
            $table->date('date_of_birth')->nullable();
            $table->string('occupation')->nullable();
            $table->string('company')->nullable();
            $table->string('postal_address')->nullable();
            $table->string('emergency_contact_name')->nullable();
            $table->string('emergency_contact_phone')->nullable();
            $table->enum('type', ['owner', 'tenant', 'caretaker', 'emergency'])->default('owner');
            $table->boolean('is_primary')->default(false);
            $table->boolean('receive_invoices')->default(true);
            $table->boolean('receive_notifications')->default(true);
            $table->boolean('is_active')->default(true);
            $table->text('notes')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->index(['house_id', 'type']);
            $table->index('phone');
            $table->index('whatsapp_number');
            $table->index('email');
            $table->index('is_active');
        });

        // House contacts pivot table (from 2025_07_25_100000_create_house_contacts_table.php)
        Schema::create('house_contacts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('house_id')->constrained()->onDelete('cascade');
            $table->foreignId('contact_id')->constrained()->onDelete('cascade');
            $table->date('start_date');
            $table->date('end_date')->nullable();
            $table->enum('relationship_type', ['owner', 'tenant', 'caretaker', 'emergency'])->default('tenant');
            $table->boolean('is_current')->default(true);
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->unique(['house_id', 'contact_id', 'start_date']);
            $table->index(['house_id', 'is_current']);
            $table->index(['contact_id', 'is_current']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('house_contacts');
        Schema::dropIfExists('contacts');
        Schema::dropIfExists('houses');
        Schema::dropIfExists('estates');
    }
};
