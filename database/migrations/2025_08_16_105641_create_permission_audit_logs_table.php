<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('permission_audit_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->nullable()->constrained()->nullOnDelete();
            $table->string('action'); // assigned, revoked, role_assigned, role_revoked, etc.
            $table->json('details')->nullable(); // Additional context data
            $table->string('target_type')->nullable(); // Type of target (user, role, permission, etc.)
            $table->string('target_id')->nullable(); // ID of the target
            $table->string('permission_name')->nullable(); // For permission-specific actions
            $table->unsignedBigInteger('target_user_id')->nullable(); // For user-specific actions
            $table->string('ip_address')->nullable();
            $table->text('user_agent')->nullable();
            $table->timestamps();

            $table->index(['user_id', 'action']);
            $table->index(['target_type', 'target_id']);
            $table->index(['permission_name']);
            $table->index(['target_user_id']);
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('permission_audit_logs');
    }
};
