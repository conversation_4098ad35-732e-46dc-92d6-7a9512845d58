<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Role;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Define simplified permission structure
        $simplifiedPermissions = [
            // Dashboard permissions (1)
            ['name' => 'dashboard.access', 'guard_name' => 'web'],

            // Estate permissions (6)
            ['name' => 'estates.view', 'guard_name' => 'web'],
            ['name' => 'estates.create', 'guard_name' => 'web'],
            ['name' => 'estates.edit', 'guard_name' => 'web'],
            ['name' => 'estates.delete', 'guard_name' => 'web'],
            ['name' => 'estates.manage', 'guard_name' => 'web'],
            ['name' => 'estates.analytics', 'guard_name' => 'web'],

            // House permissions (6)
            ['name' => 'houses.view', 'guard_name' => 'web'],
            ['name' => 'houses.create', 'guard_name' => 'web'],
            ['name' => 'houses.edit', 'guard_name' => 'web'],
            ['name' => 'houses.delete', 'guard_name' => 'web'],
            ['name' => 'houses.manage', 'guard_name' => 'web'],
            ['name' => 'houses.registry', 'guard_name' => 'web'],

            // Contact permissions (5)
            ['name' => 'contacts.view', 'guard_name' => 'web'],
            ['name' => 'contacts.create', 'guard_name' => 'web'],
            ['name' => 'contacts.edit', 'guard_name' => 'web'],
            ['name' => 'contacts.delete', 'guard_name' => 'web'],
            ['name' => 'contacts.manage', 'guard_name' => 'web'],

            // Meter reading permissions (6)
            ['name' => 'readings.view', 'guard_name' => 'web'],
            ['name' => 'readings.create', 'guard_name' => 'web'],
            ['name' => 'readings.edit', 'guard_name' => 'web'],
            ['name' => 'readings.delete', 'guard_name' => 'web'],
            ['name' => 'readings.approve', 'guard_name' => 'web'],
            ['name' => 'readings.validate', 'guard_name' => 'web'],

            // Invoice permissions (8)
            ['name' => 'invoices.view', 'guard_name' => 'web'],
            ['name' => 'invoices.create', 'guard_name' => 'web'],
            ['name' => 'invoices.edit', 'guard_name' => 'web'],
            ['name' => 'invoices.delete', 'guard_name' => 'web'],
            ['name' => 'invoices.generate', 'guard_name' => 'web'],
            ['name' => 'invoices.send', 'guard_name' => 'web'],
            ['name' => 'invoices.approve', 'guard_name' => 'web'],
            ['name' => 'invoices.adjust', 'guard_name' => 'web'],

            // Account permissions (6)
            ['name' => 'accounts.view', 'guard_name' => 'web'],
            ['name' => 'accounts.manage', 'guard_name' => 'web'],
            ['name' => 'accounts.view_balance', 'guard_name' => 'web'],
            ['name' => 'accounts.view_transactions', 'guard_name' => 'web'],
            ['name' => 'accounts.view_statement', 'guard_name' => 'web'],
            ['name' => 'accounts.adjust_balance', 'guard_name' => 'web'],

            // Payment permissions (5)
            ['name' => 'payments.view', 'guard_name' => 'web'],
            ['name' => 'payments.create', 'guard_name' => 'web'],
            ['name' => 'payments.edit', 'guard_name' => 'web'],
            ['name' => 'payments.approve', 'guard_name' => 'web'],
            ['name' => 'payments.reconcile', 'guard_name' => 'web'],

            // Rate permissions (4)
            ['name' => 'rates.view', 'guard_name' => 'web'],
            ['name' => 'rates.create', 'guard_name' => 'web'],
            ['name' => 'rates.edit', 'guard_name' => 'web'],
            ['name' => 'rates.delete', 'guard_name' => 'web'],

            // Report permissions (5)
            ['name' => 'reports.view', 'guard_name' => 'web'],
            ['name' => 'reports.generate', 'guard_name' => 'web'],
            ['name' => 'reports.export', 'guard_name' => 'web'],
            ['name' => 'reports.analytics', 'guard_name' => 'web'],
            ['name' => 'reports.aging', 'guard_name' => 'web'],

            // User permissions (7)
            ['name' => 'users.view', 'guard_name' => 'web'],
            ['name' => 'users.create', 'guard_name' => 'web'],
            ['name' => 'users.edit', 'guard_name' => 'web'],
            ['name' => 'users.delete', 'guard_name' => 'web'],
            ['name' => 'users.manage', 'guard_name' => 'web'],
            ['name' => 'users.assign_estates', 'guard_name' => 'web'],
            ['name' => 'users.assign_roles', 'guard_name' => 'web'],

            // System permissions (4)
            ['name' => 'system.settings', 'guard_name' => 'web'],
            ['name' => 'system.audit', 'guard_name' => 'web'],
            ['name' => 'system.whatsapp', 'guard_name' => 'web'],
            ['name' => 'system.export', 'guard_name' => 'web'],

            // Resident permissions (4)
            ['name' => 'resident.portal', 'guard_name' => 'web'],
            ['name' => 'resident.inquiries', 'guard_name' => 'web'],
            ['name' => 'resident.messages', 'guard_name' => 'web'],
            ['name' => 'resident.payments', 'guard_name' => 'web'],
        ];

        // Delete all existing permissions and role assignments
        DB::table('role_has_permissions')->delete();
        DB::table('permissions')->delete();

        // Insert simplified permissions with timestamps
        foreach ($simplifiedPermissions as $permission) {
            $permission['created_at'] = now();
            $permission['updated_at'] = now();
            DB::table('permissions')->insert($permission);
        }

        // Update role permissions with simplified structure
        $this->updateRolePermissions();
    }

    /**
     * Update role permissions with simplified structure
     */
    private function updateRolePermissions(): void
    {
        // Get role IDs
        $roles = DB::table('roles')->pluck('id', 'name');

        // Admin role - gets all permissions
        if (isset($roles['admin'])) {
            $allPermissionIds = DB::table('spatie_permissions')->pluck('id')->toArray();
            $adminRolePermissions = array_map(function ($permissionId) use ($roles) {
                return [
                    'role_id' => $roles['admin'],
                    'permission_id' => $permissionId,
                ];
            }, $allPermissionIds);
            DB::table('spatie_role_has_permissions')->insert($adminRolePermissions);
        }

        // Manager role permissions
        if (isset($roles['manager'])) {
            $managerPermissions = [
                'dashboard.access',
                'estates.view', 'estates.edit', 'estates.manage', 'estates.analytics',
                'houses.view', 'houses.edit', 'houses.manage', 'houses.registry',
                'contacts.view', 'contacts.manage',
                'readings.view', 'readings.approve', 'readings.validate',
                'invoices.view', 'invoices.approve', 'invoices.send', 'invoices.adjust',
                'accounts.view', 'accounts.view_balance', 'accounts.view_transactions', 'accounts.view_statement',
                'rates.view',
                'reports.view', 'reports.generate', 'reports.export', 'reports.analytics',
                'payments.view', 'payments.approve',
                'users.view', 'users.create', 'users.edit', 'users.assign_estates',
                'system.whatsapp',
                'resident.portal', 'resident.inquiries', 'resident.messages',
            ];

            $managerPermissionIds = DB::table('permissions')
                ->whereIn('name', $managerPermissions)
                ->pluck('id')
                ->toArray();

            $managerRolePermissions = array_map(function ($permissionId) use ($roles) {
                return [
                    'role_id' => $roles['manager'],
                    'permission_id' => $permissionId,
                ];
            }, $managerPermissionIds);
            DB::table('spatie_role_has_permissions')->insert($managerRolePermissions);
        }

        // Reviewer role permissions
        if (isset($roles['reviewer'])) {
            $reviewerPermissions = [
                'dashboard.access',
                'estates.view',
                'houses.view', 'houses.edit',
                'contacts.view', 'contacts.edit',
                'readings.view', 'readings.create', 'readings.edit', 'readings.approve', 'readings.validate',
                'invoices.view', 'invoices.create', 'invoices.edit', 'invoices.send', 'invoices.approve', 'invoices.adjust', 'invoices.delete',
                'accounts.view', 'accounts.manage', 'accounts.view_balance', 'accounts.view_transactions', 'accounts.view_statement', 'accounts.adjust_balance',
                'rates.view', 'rates.edit',
                'reports.view', 'reports.generate', 'reports.export', 'reports.aging',
                'payments.view', 'payments.create', 'payments.edit', 'payments.approve', 'payments.reconcile',
                'system.whatsapp',
                'resident.portal', 'resident.inquiries', 'resident.messages',
            ];

            $reviewerPermissionIds = DB::table('permissions')
                ->whereIn('name', $reviewerPermissions)
                ->pluck('id')
                ->toArray();

            $reviewerRolePermissions = array_map(function ($permissionId) use ($roles) {
                return [
                    'role_id' => $roles['reviewer'],
                    'permission_id' => $permissionId,
                ];
            }, $reviewerPermissionIds);
            DB::table('spatie_role_has_permissions')->insert($reviewerRolePermissions);
        }

        // Caretaker role permissions
        if (isset($roles['caretaker'])) {
            $caretakerPermissions = [
                'dashboard.access',
                'estates.view',
                'houses.view', 'houses.edit',
                'contacts.view', 'contacts.create', 'contacts.edit', 'contacts.manage',
                'readings.view', 'readings.create', 'readings.edit', 'readings.validate',
                'accounts.view_balance',
                'invoices.view',
                'reports.view',
                'resident.portal', 'resident.inquiries',
            ];

            $caretakerPermissionIds = DB::table('permissions')
                ->whereIn('name', $caretakerPermissions)
                ->pluck('id')
                ->toArray();

            $caretakerRolePermissions = array_map(function ($permissionId) use ($roles) {
                return [
                    'role_id' => $roles['caretaker'],
                    'permission_id' => $permissionId,
                ];
            }, $caretakerPermissionIds);
            DB::table('spatie_role_has_permissions')->insert($caretakerRolePermissions);
        }

        // Resident role permissions
        if (isset($roles['resident'])) {
            $residentPermissions = [
                'dashboard.access',
                'houses.view',
                'contacts.view',
                'readings.view',
                'invoices.view',
                'accounts.view', 'accounts.view_balance', 'accounts.view_transactions', 'accounts.view_statement',
                'reports.view',
                'payments.view',
                'resident.portal', 'resident.inquiries', 'resident.messages', 'resident.payments',
            ];

            $residentPermissionIds = DB::table('permissions')
                ->whereIn('name', $residentPermissions)
                ->pluck('id')
                ->toArray();

            $residentRolePermissions = array_map(function ($permissionId) use ($roles) {
                return [
                    'role_id' => $roles['resident'],
                    'permission_id' => $permissionId,
                ];
            }, $residentPermissionIds);
            DB::table('spatie_role_has_permissions')->insert($residentRolePermissions);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Clear all permissions and role assignments
        DB::table('role_has_permissions')->delete();
        DB::table('permissions')->delete();
    }
};
