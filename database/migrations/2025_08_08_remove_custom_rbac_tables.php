<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Drop custom RBAC tables that are no longer needed
        // We keep user_estate_assignments as it's still needed for estate-based access control

        Schema::dropIfExists('user_management_hierarchy');
        Schema::dropIfExists('permission_audit_logs');
        Schema::dropIfExists('user_permission_overrides');
        Schema::dropIfExists('role_permissions');
        Schema::dropIfExists('permissions');
        Schema::dropIfExists('user_roles');

        // Note: user_estate_assignments table is kept as it's essential for estate-based access control
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Recreate the custom RBAC tables if needed for rollback
        Schema::create('user_roles', function (Blueprint $table) {
            $table->string('value')->primary();
            $table->string('label')->notNullable();
            $table->text('description')->nullable();
            $table->timestamps();
        });

        Schema::create('permissions', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique();
            $table->string('display_name');
            $table->text('description')->nullable();
            $table->string('category');
            $table->boolean('requires_estate_assignment')->default(true);
            $table->boolean('system_level_only')->default(false);
            $table->boolean('allow_user_overrides')->default(true);
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            $table->index(['category', 'is_active']);
            $table->index('name');
        });

        Schema::create('role_permissions', function (Blueprint $table) {
            $table->id();
            $table->string('role');
            $table->foreignId('permission_id')->constrained()->onDelete('cascade');
            $table->foreignId('assigned_by')->constrained('users');
            $table->timestamp('assigned_at')->default(DB::raw('CURRENT_TIMESTAMP'));
            $table->timestamps();

            $table->unique(['role', 'permission_id']);
            $table->foreign('role')->references('value')->on('user_roles');
        });

        Schema::create('user_permission_overrides', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('permission_id')->constrained()->onDelete('cascade');
            $table->enum('action', ['grant', 'revoke'])->default('grant');
            $table->foreignId('granted_by')->constrained('users');
            $table->timestamp('expires_at')->nullable();
            $table->text('reason')->nullable();
            $table->timestamps();

            $table->unique(['user_id', 'permission_id']);
            $table->index(['user_id', 'expires_at']);
            $table->index('permission_id');
        });

        Schema::create('permission_audit_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('action');
            $table->json('details');
            $table->string('target_type');
            $table->string('target_id');
            $table->ipAddress('ip_address')->nullable();
            $table->text('user_agent')->nullable();
            $table->timestamps();

            $table->index(['user_id', 'created_at']);
            $table->index(['target_type', 'target_id']);
            $table->index('action');
        });

        Schema::create('user_management_hierarchy', function (Blueprint $table) {
            $table->id();
            $table->foreignId('manager_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('subordinate_id')->constrained('users')->onDelete('cascade');
            $table->enum('relationship', ['manages', 'oversees']);
            $table->timestamps();

            $table->unique(['manager_id', 'subordinate_id']);
            $table->index(['manager_id', 'relationship']);
            $table->index('subordinate_id');
        });
    }
};
