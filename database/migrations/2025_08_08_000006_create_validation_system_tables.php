<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Validation logs table (from 2025_08_02_000002_create_validation_logs_table.php)
        Schema::create('validation_logs', function (Blueprint $table) {
            $table->id();
            $table->string('validation_type'); // meter_reading, invoice, account_transaction
            $table->string('entity_type'); // MeterReading, Invoice, AccountTransaction
            $table->unsignedBigInteger('entity_id');
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');
            $table->enum('status', ['passed', 'failed', 'warning', 'skipped'])->default('passed');
            $table->string('rule_name');
            $table->text('rule_description');
            $table->text('failure_reason')->nullable();
            $table->json('validation_data')->nullable();
            $table->json('metadata')->nullable();
            $table->timestamps();

            $table->index(['validation_type', 'status']);
            $table->index(['entity_type', 'entity_id']);
            $table->index('rule_name');
            $table->index('status');
            $table->index('created_at');
        });

        // Estate validation rules table (from 2025_08_02_000003_create_estate_validation_rules_table.php)
        Schema::create('estate_validation_rules', function (Blueprint $table) {
            $table->id();
            $table->foreignId('estate_id')->constrained()->onDelete('cascade');
            $table->string('rule_name');
            $table->string('rule_type'); // meter_reading_validation, invoice_validation, account_validation
            $table->text('rule_description');
            $table->json('rule_config'); // Configuration for the validation rule
            $table->enum('severity', ['error', 'warning', 'info'])->default('error');
            $table->boolean('is_active')->default(true);
            $table->foreignId('created_by')->constrained('users')->onDelete('set null');
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();

            $table->unique(['estate_id', 'rule_name', 'rule_type']);
            $table->index(['estate_id', 'rule_type']);
            $table->index('rule_type');
            $table->index('is_active');
            $table->index('severity');
        });

        // Validation baselines table (from 2025_08_02_000004_create_validation_baselines_table.php)
        Schema::create('validation_baselines', function (Blueprint $table) {
            $table->id();
            $table->foreignId('estate_id')->constrained()->onDelete('cascade');
            $table->string('baseline_type'); // consumption_baseline, payment_baseline, reading_frequency
            $table->string('metric_name');
            $table->decimal('baseline_value', 10, 2);
            $table->decimal('upper_threshold', 10, 2)->nullable();
            $table->decimal('lower_threshold', 10, 2)->nullable();
            $table->string('calculation_method'); // average, median, percentile, fixed
            $table->json('calculation_config')->nullable();
            $table->date('effective_from');
            $table->date('effective_to')->nullable();
            $table->boolean('is_active')->default(true);
            $table->text('notes')->nullable();
            $table->foreignId('created_by')->constrained('users')->onDelete('set null');
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();

            $table->unique(['estate_id', 'baseline_type', 'metric_name', 'effective_from']);
            $table->index(['estate_id', 'baseline_type']);
            $table->index('baseline_type');
            $table->index('metric_name');
            $table->index('is_active');
            $table->index('effective_from');
        });

        // Reading validation exceptions table (for handling special cases)
        Schema::create('reading_validation_exceptions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('house_id')->constrained()->onDelete('cascade');
            $table->foreignId('meter_reading_id')->nullable()->constrained()->onDelete('set null');
            $table->string('exception_type'); // meter_replacement, initial_reading, system_error, manual_override
            $table->text('reason');
            $table->decimal('expected_reading', 10, 2)->nullable();
            $table->decimal('actual_reading', 10, 2)->nullable();
            $table->decimal('variance', 10, 2)->nullable();
            $table->enum('status', ['pending', 'approved', 'rejected'])->default('pending');
            $table->foreignId('approved_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('approved_at')->nullable();
            $table->text('approval_notes')->nullable();
            $table->foreignId('created_by')->constrained('users')->onDelete('set null');
            $table->timestamps();

            $table->index(['house_id', 'exception_type']);
            $table->index('exception_type');
            $table->index('status');
            $table->index('approved_at');
            $table->index('created_at');
        });

        // Validation rule templates table (for reusable validation rules)
        Schema::create('validation_rule_templates', function (Blueprint $table) {
            $table->id();
            $table->string('template_name');
            $table->string('rule_category'); // meter_reading, invoice, account, system
            $table->string('rule_type');
            $table->text('description');
            $table->json('default_config'); // Default configuration for the rule
            $table->json('required_parameters'); // Parameters that must be provided
            $table->boolean('is_system')->default(false); // System rules cannot be deleted
            $table->boolean('is_active')->default(true);
            $table->foreignId('created_by')->constrained('users')->onDelete('set null');
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();

            $table->unique(['template_name', 'rule_category', 'rule_type']);
            $table->index('rule_category');
            $table->index('rule_type');
            $table->index('is_system');
            $table->index('is_active');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('validation_rule_templates');
        Schema::dropIfExists('reading_validation_exceptions');
        Schema::dropIfExists('validation_baselines');
        Schema::dropIfExists('estate_validation_rules');
        Schema::dropIfExists('validation_logs');
    }
};
