<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Spatie permission tables (consolidated from 2025_08_06_211211_create_permission_tables.php,
        // 2025_08_07_create_spatie_roles.php, 2025_08_07_create_spatie_permissions.php,
        // 2025_08_07_migrate_user_roles_to_spatie.php, 2025_08_07_assign_spatie_role_permissions.php,
        // and 2025_08_08_create_simplified_permissions.php)

        // Roles table
        Schema::create('roles', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique();
            $table->string('guard_name')->default('web');
            $table->timestamps();
            $table->index('name');
            $table->index('guard_name');
        });

        // Permissions table
        Schema::create('permissions', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique();
            $table->string('guard_name')->default('web');
            $table->string('group')->nullable(); // For grouping permissions
            $table->string('description')->nullable();
            $table->timestamps();
            $table->index('name');
            $table->index('guard_name');
            $table->index('group');
        });

        // Model has roles table
        Schema::create('model_has_roles', function (Blueprint $table) {
            $table->unsignedBigInteger('role_id');
            $table->string('model_type');
            $table->unsignedBigInteger('model_id');
            $table->foreign('role_id')->references('id')->on('roles')->onDelete('cascade');
            $table->primary(['role_id', 'model_id', 'model_type']);
            $table->index('model_id');
            $table->index('model_type');
        });

        // Model has permissions table
        Schema::create('model_has_permissions', function (Blueprint $table) {
            $table->unsignedBigInteger('permission_id');
            $table->string('model_type');
            $table->unsignedBigInteger('model_id');
            $table->foreign('permission_id')->references('id')->on('permissions')->onDelete('cascade');
            $table->primary(['permission_id', 'model_id', 'model_type']);
            $table->index('model_id');
            $table->index('model_type');
        });

        // Role has permissions table
        Schema::create('role_has_permissions', function (Blueprint $table) {
            $table->unsignedBigInteger('permission_id');
            $table->unsignedBigInteger('role_id');
            $table->foreign('permission_id')->references('id')->on('permissions')->onDelete('cascade');
            $table->foreign('role_id')->references('id')->on('roles')->onDelete('cascade');
            $table->primary(['permission_id', 'role_id']);
            $table->index('role_id');
            $table->index('permission_id');
        });

        // User estate assignments table (from 2025_07_24_190922_create_user_estate_assignments_table.php)
        Schema::create('user_estate_assignments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('estate_id')->constrained()->onDelete('cascade');
            $table->enum('role_type', ['manager', 'reviewer', 'caretaker'])->default('manager');
            $table->date('assigned_from');
            $table->date('assigned_to')->nullable();
            $table->boolean('is_active')->default(true);
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->unique(['user_id', 'estate_id', 'assigned_from']);
            $table->index(['user_id', 'is_active']);
            $table->index(['estate_id', 'role_type']);
            $table->index('is_active');
        });

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('role_has_permissions');
        Schema::dropIfExists('model_has_permissions');
        Schema::dropIfExists('model_has_roles');
        Schema::dropIfExists('permissions');
        Schema::dropIfExists('roles');
    }
};
