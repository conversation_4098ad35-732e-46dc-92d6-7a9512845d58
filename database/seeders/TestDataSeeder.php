<?php

namespace Database\Seeders;

use App\Models\Contact;
use App\Models\House;
use App\Models\HouseAccount;
use App\Models\Invoice;
use App\Models\InvoiceLineItem;
use App\Models\MeterReading;
use App\Models\User;
use App\Models\WaterRate;
use App\Models\WhatsAppMessage;
use Illuminate\Database\Seeder;

class TestDataSeeder extends Seeder
{
    /**
     * Run the test data seeds.
     * This seeder creates realistic test data for development and testing.
     */
    public function run(): void
    {
        $this->command->info('Starting TestDataSeeder...');

        // Seed houses for existing estates
        $this->seedHouses();

        // Seed water rates for estates
        $this->seedWaterRates();

        // Seed contacts for houses
        $this->seedContacts();

        // Seed meter readings
        $this->seedMeterReadings();

        // Seed house accounts
        $this->seedHouseAccounts();

        // Seed invoices
        $this->seedInvoices();

        // Seed WhatsApp messages (sample)
        $this->seedWhatsAppMessages();

        $this->command->info('TestDataSeeder completed successfully!');
    }

    /**
     * Seed houses for existing estates.
     */
    private function seedHouses(): void
    {
        $estates = \App\Models\Estate::all();

        foreach ($estates as $estate) {
            // Create varying numbers of houses per estate
            $houseCount = rand(8, 12);

            for ($i = 1; $i <= $houseCount; $i++) {
                $houseNumber = $this->generateHouseNumber($estate, $i);

                // Check if house already exists
                $existingHouse = House::where('estate_id', $estate->id)
                    ->where('house_number', $houseNumber)
                    ->first();

                if (! $existingHouse) {
                    House::factory()->create([
                        'estate_id' => $estate->id,
                        'house_number' => $houseNumber,
                        'unit_number' => str_pad($i, 3, '0', STR_PAD_LEFT),
                        'building_name' => $this->getBuildingName($i),
                        'block' => $this->getBlockName($i),
                        'floor' => $this->getFloorNumber($i),
                        'type' => $this->getHouseType($i),
                        'bedrooms' => $this->getBedroomCount($i),
                        'size_sqft' => rand(800, 2500),
                        'monthly_rent' => rand(50000, 500000),
                        'occupancy_date' => now()->subMonths(rand(1, 24)),
                        'is_active' => true,
                    ]);
                }
            }
        }

        $this->command->info('Houses seeded successfully!');
    }

    /**
     * Seed water rates for estates.
     */
    private function seedWaterRates(): void
    {
        $estates = \App\Models\Estate::all();

        foreach ($estates as $estate) {
            // Create a single water rate for each estate with tiered pricing
            $baseRate = rand(25, 40); // Base rate per unit
            $minimumCharge = rand(150, 300); // Minimum charge
            $minimumUnits = rand(5, 10); // Minimum units before tiered pricing
            $fixedCharge = rand(200, 400); // Fixed monthly charge

            WaterRate::firstOrCreate(
                ['estate_id' => $estate->id, 'name' => 'Domestic Water Rate - '.$estate->name],
                [
                    'rate_per_unit' => $baseRate,
                    'minimum_charge' => $minimumCharge,
                    'minimum_units' => $minimumUnits,
                    'fixed_charge' => $fixedCharge,
                    'currency' => 'NGN',
                    'billing_unit' => 'cubic_meters',
                    'effective_from' => now()->subMonths(12),
                    'effective_to' => null,
                    'is_active' => true,
                    'description' => 'Standard domestic water consumption rate for '.$estate->name,
                    'tier_rates' => json_encode([
                        [
                            'min_units' => 0,
                            'max_units' => $minimumUnits,
                            'rate' => $baseRate,
                        ],
                        [
                            'min_units' => $minimumUnits + 1,
                            'max_units' => $minimumUnits * 2,
                            'rate' => $baseRate * 1.2,
                        ],
                        [
                            'min_units' => ($minimumUnits * 2) + 1,
                            'max_units' => null,
                            'rate' => $baseRate * 1.5,
                        ],
                    ]),
                ]
            );
        }

        $this->command->info('Water rates seeded successfully!');
    }

    /**
     * Seed contacts for houses.
     */
    private function seedContacts(): void
    {
        $houses = House::all();

        foreach ($houses as $house) {
            // Create 1-3 contacts per house
            $contactCount = rand(1, 3);

            for ($i = 0; $i < $contactCount; $i++) {
                $contact = Contact::factory()->create([
                    'type' => $i === 0 ? 'owner' : 'tenant',
                    'is_primary' => $i === 0,
                    'receive_invoices' => true,
                    'receive_notifications' => true,
                    'preferred_language' => 'en',
                    'email_notifications' => true,
                    'sms_notifications' => rand(0, 1) === 1,
                    'whatsapp_notifications' => rand(0, 1) === 1,
                    'invoice_delivery_method' => rand(0, 1) === 1 ? 'email' : 'whatsapp',
                    'is_active' => true,
                ]);

                // Attach contact to house
                $contact->houses()->attach($house->id, [
                    'relationship_type' => $i === 0 ? 'owner' : 'tenant',
                    'start_date' => now()->subMonths(rand(1, 24)),
                    'end_date' => null,
                    'is_current' => true,
                    'notes' => $i === 0 ? 'Primary contact and property owner' : 'Tenant occupant',
                ]);
            }
        }

        $this->command->info('Contacts seeded successfully!');
    }

    /**
     * Seed meter readings.
     */
    private function seedMeterReadings(): void
    {
        $houses = House::all();
        $caretakers = User::role('caretaker')->get();

        if ($caretakers->isEmpty()) {
            $this->command->error('No caretakers found. Please seed users first.');

            return;
        }

        foreach ($houses as $house) {
            $this->createReadingsForHouse($house, $caretakers);
        }

        $this->command->info('Meter readings seeded successfully!');
    }

    /**
     * Create readings for a specific house.
     */
    private function createReadingsForHouse($house, $caretakers)
    {
        // Create readings for the last 12 months
        $currentReading = rand(1000, 8000); // Starting meter reading
        $caretaker = $caretakers->random();

        for ($i = 12; $i >= 0; $i--) {
            $readingDate = now()->subMonths($i);

            // Skip some months randomly to make it more realistic (90% chance of creating reading)
            if (rand(0, 10) > 1) {
                // Generate realistic consumption based on house size
                $baseConsumption = rand(8, 25);
                $houseSizeMultiplier = $house->size_sqft / 1000; // Larger houses use more water
                $consumption = (int) ($baseConsumption * $houseSizeMultiplier);
                $consumption = max(5, min($consumption, 80)); // Keep within reasonable bounds

                $newReading = $currentReading + $consumption;

                // Create reading with appropriate status
                $status = 'approved';
                $reviewedAt = $readingDate->copy()->addDays(rand(1, 7));

                MeterReading::create([
                    'house_id' => $house->id,
                    'user_id' => $caretaker->id,
                    'reading_date' => $readingDate,
                    'previous_reading' => $currentReading,
                    'current_reading' => $newReading,
                    'consumption' => $consumption,
                    'estimated_consumption' => null,
                    'reading_method' => 'manual',
                    'meter_serial_number' => 'MTR-'.$house->house_number,
                    'status' => $status,
                    'validation_status' => 'valid',
                    'is_validated' => true,
                    'is_estimated' => false,
                    'reviewed_by' => $caretaker->id,
                    'reviewed_at' => $reviewedAt,
                    'review_notes' => $this->generateRandomNotes(),
                    'reading_metadata' => json_encode([
                        'source' => 'manual_entry',
                        'device_type' => 'analog_meter',
                        'access_granted' => true,
                    ]),
                    'created_at' => $readingDate,
                    'updated_at' => $readingDate,
                ]);

                $currentReading = $newReading;
            }
        }
    }

    /**
     * Seed house accounts.
     */
    private function seedHouseAccounts(): void
    {
        $houses = House::all();

        foreach ($houses as $house) {
            HouseAccount::firstOrCreate(
                ['house_id' => $house->id],
                [
                    'current_balance' => rand(-5000, 10000), // Mix of credit and debit balances
                    'total_credit' => rand(0, 50000),
                    'total_debit' => rand(0, 40000),
                    'available_credit' => rand(0, 10000),
                    'credit_limit' => 10000,
                    'last_transaction_date' => now()->subDays(rand(1, 30)),
                    'last_payment_date' => now()->subDays(rand(1, 60)),
                    'last_invoice_date' => now()->subDays(rand(1, 30)),
                    'account_status' => 'active',
                    'notes' => 'Account created automatically during seeding',
                ]
            );
        }

        $this->command->info('House accounts seeded successfully!');
    }

    /**
     * Seed invoices.
     */
    private function seedInvoices(): void
    {
        $houses = House::all();

        foreach ($houses as $house) {
            $this->createInvoicesForHouse($house);
        }

        $this->command->info('Invoices seeded successfully!');
    }

    /**
     * Create invoices for a specific house.
     */
    private function createInvoicesForHouse($house)
    {
        // Get approved readings for this house, ordered by date
        $readings = MeterReading::where('house_id', $house->id)
            ->where('status', 'approved')
            ->orderBy('reading_date')
            ->get();

        if ($readings->count() < 2) {
            return; // Need at least 2 readings to create an invoice
        }

        // Get water rate for this estate
        $waterRate = WaterRate::where('estate_id', $house->estate_id)
            ->where('is_active', true)
            ->first();

        if (! $waterRate) {
            return;
        }

        // Create invoices for the last 6 months
        for ($i = 6; $i >= 1; $i--) {
            $periodStart = now()->subMonths($i)->startOfMonth();
            $periodEnd = now()->subMonths($i)->endOfMonth();

            // Find reading for this period
            $reading = $readings->where('reading_date', '>=', $periodStart)
                ->where('reading_date', '<=', $periodEnd)
                ->first();

            if (! $reading) {
                continue;
            }

            // Get previous reading
            $previousReading = $readings->where('reading_date', '<', $periodStart)->last();

            if (! $previousReading) {
                continue;
            }

            // Calculate invoice amounts using water rate calculation method
            $consumption = $reading->consumption;
            $amount = $this->calculateAmount($waterRate, $consumption);

            // Create invoice
            $invoice = Invoice::firstOrCreate(
                [
                    'house_id' => $house->id,
                    'billing_period_start' => $periodStart,
                    'billing_period_end' => $periodEnd,
                ],
                [
                    'water_rate_id' => $waterRate->id,
                    'meter_reading_id' => $reading->id,
                    'invoice_number' => $this->generateInvoiceNumber(),
                    'previous_reading' => $previousReading->current_reading,
                    'current_reading' => $reading->current_reading,
                    'consumption' => $consumption,
                    'rate_per_unit' => $waterRate->rate_per_unit,
                    'amount' => $amount['variable_amount'],
                    'fixed_charge' => $amount['fixed_charge'],
                    'tax_amount' => $amount['tax_amount'],
                    'discount_amount' => 0,
                    'adjustments' => 0,
                    'previous_balance' => rand(-1000, 2000),
                    'final_amount' => $amount['total_amount'] + rand(-1000, 2000),
                    'total_amount' => $amount['total_amount'], // Ensure total_amount is set
                    'currency' => 'NGN',
                    'status' => $this->getRandomInvoiceStatus(),
                    'approval_status' => 'approved',
                    'due_date' => $periodEnd->copy()->addDays(15),
                    'invoice_type' => $this->getRandomInvoiceType(),
                    'payment_terms' => 'net_15',
                    'notes' => $this->generateRandomInvoiceNotes(),
                ]
            );

            // Create line items if invoice was created
            if ($invoice->wasRecentlyCreated) {
                // Water consumption line item
                InvoiceLineItem::create([
                    'invoice_id' => $invoice->id,
                    'item_type' => 'water_consumption',
                    'description' => "Water consumption for {$periodStart->format('M Y')}",
                    'quantity' => $consumption,
                    'unit_price' => $waterRate->rate_per_unit,
                    'amount' => $amount['variable_amount'],
                    'tax_rate' => 0,
                    'tax_amount' => 0,
                    'total_amount' => $amount['variable_amount'],
                    'notes' => 'Standard water consumption charges',
                ]);

                // Fixed charge line item
                InvoiceLineItem::create([
                    'invoice_id' => $invoice->id,
                    'item_type' => 'fixed_charge',
                    'description' => "Monthly fixed charge for {$periodStart->format('M Y')}",
                    'quantity' => 1,
                    'unit_price' => $waterRate->fixed_charge,
                    'amount' => $waterRate->fixed_charge,
                    'tax_rate' => 0,
                    'tax_amount' => 0,
                    'total_amount' => $waterRate->fixed_charge,
                    'notes' => 'Monthly service fixed charge',
                ]);

                // Add payment if status is paid
                if ($invoice->status === 'paid') {
                    $paymentDate = $invoice->due_date->copy()->addDays(rand(-5, 10));
                    $invoice->update([
                        'paid_at' => $paymentDate,
                        'payment_reference' => $this->generatePaymentReference(),
                    ]);
                }
            }
        }
    }

    /**
     * Seed WhatsApp messages (sample data).
     */
    private function seedWhatsAppMessages(): void
    {
        $houses = House::all();
        $messages = [];

        foreach ($houses->take(10) as $house) { // Limit to 10 houses for sample data
            $contact = $house->contacts()->where('whatsapp_notifications', true)->first();

            if (! $contact) {
                continue;
            }

            // Create 1-3 messages per contact
            $messageCount = rand(1, 3);

            for ($i = 0; $i < $messageCount; $i++) {
                $messageType = rand(0, 1) === 0 ? 'invoice' : 'reminder';
                $status = rand(0, 1) === 0 ? 'sent' : 'delivered';

                $messages[] = [
                    'house_id' => $house->id,
                    'contact_id' => $contact->id,
                    'message_type' => $messageType,
                    'phone_number' => $contact->whatsapp_number,
                    'message_content' => $this->generateWhatsAppMessage($messageType),
                    'status' => $status,
                    'message_id' => 'wamid.'.uniqid(),
                    'sent_at' => now()->subDays(rand(1, 30)),
                    'delivered_at' => $status === 'delivered' ? now()->subDays(rand(0, 29)) : null,
                    'read_at' => $status === 'delivered' && rand(0, 1) === 0 ? now()->subDays(rand(0, 28)) : null,
                    'error_message' => null,
                    'retry_count' => 0,
                    'created_at' => now()->subDays(rand(1, 30)),
                    'updated_at' => now()->subDays(rand(0, 29)),
                ];
            }
        }

        if (! empty($messages)) {
            WhatsAppMessage::insert($messages);
        }

        $this->command->info('WhatsApp messages seeded successfully!');
    }

    // Helper methods

    private function generateHouseNumber($estate, $index)
    {
        $prefix = strtoupper(substr($estate->name, 0, 3));
        $estateCode = $estate->code;

        return $estateCode.'-'.$prefix.'-'.str_pad($index, 3, '0', STR_PAD_LEFT);
    }

    private function getBuildingName($index)
    {
        $buildings = ['Block A', 'Block B', 'Block C', 'Tower 1', 'Tower 2', 'Garden Villa'];

        return $buildings[($index - 1) % count($buildings)];
    }

    private function getBlockName($index)
    {
        $blocks = ['A', 'B', 'C', 'D', 'E', 'F'];

        return $blocks[($index - 1) % count($blocks)];
    }

    private function getFloorNumber($index)
    {
        return (($index - 1) % 5) + 1; // Floors 1-5
    }

    private function getHouseType($index)
    {
        $types = ['apartment', 'duplex', 'bungalow', 'terraced', 'penthouse'];

        return $types[($index - 1) % count($types)];
    }

    private function getBedroomCount($index)
    {
        $bedrooms = [1, 2, 3, 3, 4, 4, 5]; // More 3-4 bedroom houses

        return $bedrooms[($index - 1) % count($bedrooms)];
    }

    private function generateRandomNotes()
    {
        $notes = [
            'Regular monthly reading',
            'Meter accessed without issues',
            'Customer present during reading',
            'Meter functioning normally',
            'Reading taken as scheduled',
            'No irregularities observed',
            'Meter seal intact',
            'Clear access to meter',
            'Customer confirmed reading',
            'Standard reading procedure followed',
        ];

        return $notes[array_rand($notes)];
    }

    private function calculateAmount($waterRate, $consumption)
    {
        $tierRates = json_decode($waterRate->tier_rates, true);
        $variableAmount = 0;
        $remainingConsumption = $consumption;

        foreach ($tierRates as $tier) {
            if ($remainingConsumption <= 0) {
                break;
            }

            $tierConsumption = min($remainingConsumption, $tier['max_units'] - $tier['min_units'] + 1);
            $variableAmount += $tierConsumption * $tier['rate'];
            $remainingConsumption -= $tierConsumption;
        }

        $fixedCharge = $waterRate->fixed_charge;
        $taxAmount = 0; // No tax for now
        $totalAmount = $variableAmount + $fixedCharge + $taxAmount;

        return [
            'variable_amount' => $variableAmount,
            'fixed_charge' => $fixedCharge,
            'tax_amount' => $taxAmount,
            'total_amount' => $totalAmount,
        ];
    }

    private function generateInvoiceNumber()
    {
        $date = now()->format('Ymd');
        $sequence = Invoice::whereDate('created_at', now())->count() + 1;

        return 'INV-'.$date.'-'.str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }

    private function getRandomInvoiceStatus()
    {
        $statuses = ['draft', 'sent', 'paid', 'overdue'];
        $weights = [5, 25, 60, 10]; // Higher chance of being paid

        return $this->weightedRandomChoice($statuses, $weights);
    }

    private function weightedRandomChoice($items, $weights)
    {
        $totalWeight = array_sum($weights);
        $random = rand(1, $totalWeight);

        foreach ($items as $index => $item) {
            $random -= $weights[$index];
            if ($random <= 0) {
                return $item;
            }
        }

        return $items[array_rand($items)];
    }

    private function generateRandomInvoiceNotes()
    {
        $notes = [
            'Regular monthly billing',
            'Meter reading taken on schedule',
            'No irregularities detected',
            'Customer account in good standing',
            'Billing as per consumption',
            'Standard water charges',
            'Customer requested electronic billing',
            'Payment received on time',
            null, // No notes
            null, // No notes
        ];

        return $notes[array_rand($notes)];
    }

    private function generatePaymentReference()
    {
        return 'PAY-'.date('Ym').'-'.str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
    }

    private function generateWhatsAppMessage($type)
    {
        if ($type === 'invoice') {
            return 'Dear customer, your water invoice for this month is ready. Please check your email or login to the portal to view and pay. Thank you.';
        } else {
            return 'Reminder: Your water bill payment is due soon. Please ensure timely payment to avoid late fees. Thank you.';
        }
    }

    private function getRandomInvoiceType()
    {
        $types = ['regular', 'bill', 'adjustment', 'final'];
        $weights = [70, 20, 5, 5]; // Higher chance of regular and bill

        return $this->weightedRandomChoice($types, $weights);
    }
}
