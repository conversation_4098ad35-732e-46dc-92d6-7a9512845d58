<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     *
     * This seeder now uses a consolidated approach:
     * 1. CoreSeeder - Creates foundational data (estates, users, roles, permissions, settings)
     * 2. TestDataSeeder - Creates realistic test data for development and testing
     *
     * The old individual seeders are kept for backward compatibility but are no longer called by default.
     */
    public function run(): void
    {
        $this->command->info('Starting database seeding...');

        // Seed core system data first
        $this->call([
            CoreSeeder::class,
        ]);

        // Seed test data (skip in production environment)
        if ($this->shouldSeedTestData()) {
            $this->call([
                TestDataSeeder::class,
            ]);
        }

        $this->command->info('Database seeding completed successfully!');
    }

    /**
     * Determine if test data should be seeded.
     */
    private function shouldSeedTestData(): bool
    {
        // Don't seed test data in production
        if (app()->environment('production')) {
            return false;
        }

        // Check if we should seed test data based on command options
        if ($this->command->option('class')) {
            // If running specific seeder class, let it run
            return true;
        }

        // Default to seeding test data in local and testing environments
        return app()->environment(['local', 'testing']);
    }
}
