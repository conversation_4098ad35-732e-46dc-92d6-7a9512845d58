<?php

namespace Database\Seeders;

use App\Models\Estate;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;

class CoreSeeder extends Seeder
{
    /**
     * Run the core database seeds.
     * This seeder creates the foundational data required for the application to function.
     */
    public function run(): void
    {
        $this->command->info('Starting CoreSeeder...');

        // Seed estates first (required for user assignments)
        $this->seedEstates();

        // Seed Spatie roles and permissions using the dedicated PermissionSeeder
        $this->call(PermissionSeeder::class);

        // Seed core users with role assignments
        $this->seedCoreUsers();

        // Seed system settings
        $this->seedSystemSettings();

        $this->command->info('CoreSeeder completed successfully!');
    }

    /**
     * Seed the core estates for the system.
     */
    private function seedEstates(): void
    {
        $estates = [
            [
                'name' => 'Sunset Gardens Estate',
                'code' => 'SGE-001',
                'address' => '123 Garden Street',
                'city' => 'Lagos',
                'state' => 'Lagos State',
                'postal_code' => '100001',
                'country' => 'Nigeria',
                'contact_person' => 'Estate Manager',
                'contact_phone' => '+234-************',
                'contact_email' => '<EMAIL>',
                'total_houses' => 0,
                'occupied_houses' => 0,
                'is_active' => true,
                'settings' => json_encode([
                    'billing_cycle' => 'monthly',
                    'reading_day' => 25,
                    'due_days' => 15,
                    'late_fee_rate' => 5.0,
                ]),
            ],
            [
                'name' => 'Green Valley Estate',
                'code' => 'GVE-002',
                'address' => '456 Valley Road',
                'city' => 'Abuja',
                'state' => 'FCT',
                'postal_code' => '900001',
                'country' => 'Nigeria',
                'contact_person' => 'Estate Manager',
                'contact_phone' => '+234-************',
                'contact_email' => '<EMAIL>',
                'total_houses' => 0,
                'occupied_houses' => 0,
                'is_active' => true,
                'settings' => json_encode([
                    'billing_cycle' => 'monthly',
                    'reading_day' => 20,
                    'due_days' => 15,
                    'late_fee_rate' => 5.0,
                ]),
            ],
        ];

        foreach ($estates as $estateData) {
            Estate::firstOrCreate(
                ['code' => $estateData['code']],
                $estateData
            );
        }

        $this->command->info('Core estates seeded successfully!');
    }

    /**
     * Seed core users with role assignments.
     */
    private function seedCoreUsers(): void
    {
        // Get estates (should be created by seedEstates)
        $estate1 = Estate::where('code', 'SGE-001')->first();
        $estate2 = Estate::where('code', 'GVE-002')->first();

        if (! $estate1 || ! $estate2) {
            $this->command->error('Required estates not found. Please ensure estates are seeded first.');

            return;
        }

        // Create or update admin user
        $admin = User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'System Administrator',
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
            ]
        );
        $admin->assignRole('admin');

        // Create or update manager user
        $manager = User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'John Manager',
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
            ]
        );
        $manager->assignRole('manager');

        // Create or update caretaker users
        $caretaker1 = User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Alice Caretaker',
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
            ]
        );
        $caretaker1->assignRole('caretaker');

        $caretaker2 = User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Bob Caretaker',
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
            ]
        );
        $caretaker2->assignRole('caretaker');

        // Create or update reviewer users
        $reviewer1 = User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Carol Reviewer',
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
            ]
        );
        $reviewer1->assignRole('reviewer');

        $reviewer2 = User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'David Reviewer',
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
            ]
        );
        $reviewer2->assignRole('reviewer');

        // Create or update resident user
        $resident = User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Test Resident',
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
            ]
        );
        $resident->assignRole('resident');

        // Assign users to estates (sync to avoid duplicates)
        $admin->assignedEstates()->sync([
            $estate1->id => ['role_type' => 'manager', 'assigned_from' => now()],
            $estate2->id => ['role_type' => 'manager', 'assigned_from' => now()],
        ]);

        $manager->assignedEstates()->sync([
            $estate1->id => ['role_type' => 'manager', 'assigned_from' => now()],
            $estate2->id => ['role_type' => 'manager', 'assigned_from' => now()],
        ]);

        $caretaker1->assignedEstates()->sync([$estate1->id => ['role_type' => 'caretaker', 'assigned_from' => now()]]);
        $caretaker2->assignedEstates()->sync([$estate2->id => ['role_type' => 'caretaker', 'assigned_from' => now()]]);
        $reviewer1->assignedEstates()->sync([$estate1->id => ['role_type' => 'reviewer', 'assigned_from' => now()]]);
        $reviewer2->assignedEstates()->sync([$estate2->id => ['role_type' => 'reviewer', 'assigned_from' => now()]]);

        // Assign resident to first estate for testing
        $resident->assignedEstates()->sync([$estate1->id => ['role_type' => 'manager', 'assigned_from' => now()]]);

        $this->command->info('Core users seeded successfully!');
        $this->command->table(
            ['Email', 'Password', 'Role', 'Estates'],
            [
                [$admin->email, 'password', 'Admin', 'All Estates'],
                [$manager->email, 'password', 'Manager', 'All Estates'],
                [$caretaker1->email, 'password', 'Caretaker', $estate1->name],
                [$caretaker2->email, 'password', 'Caretaker', $estate2->name],
                [$reviewer1->email, 'password', 'Reviewer', $estate1->name],
                [$reviewer2->email, 'password', 'Reviewer', $estate2->name],
                [$resident->email, 'password', 'Resident', $estate1->name],
            ]
        );
    }

    /**
     * Seed basic system settings.
     */
    private function seedSystemSettings(): void
    {
        $settings = [
            [
                'key' => 'app_name',
                'group' => 'general',
                'value' => 'Water Management System',
                'type' => 'string',
                'display_name' => 'Application Name',
                'description' => 'The name of the application',
                'is_public' => true,
                'is_required' => true,
            ],
            [
                'key' => 'default_currency',
                'group' => 'billing',
                'value' => 'NGN',
                'type' => 'string',
                'display_name' => 'Default Currency',
                'description' => 'Default currency for billing',
                'is_public' => true,
                'is_required' => true,
            ],
            [
                'key' => 'billing_cycle',
                'group' => 'billing',
                'value' => 'monthly',
                'type' => 'string',
                'display_name' => 'Billing Cycle',
                'description' => 'Default billing cycle',
                'is_public' => false,
                'is_required' => true,
                'options' => json_encode(['monthly', 'quarterly', 'annually']),
            ],
            [
                'key' => 'invoice_due_days',
                'group' => 'billing',
                'value' => '15',
                'type' => 'integer',
                'display_name' => 'Invoice Due Days',
                'description' => 'Number of days until invoice is due',
                'is_public' => false,
                'is_required' => true,
            ],
            [
                'key' => 'late_fee_rate',
                'group' => 'billing',
                'value' => '5.0',
                'type' => 'decimal',
                'display_name' => 'Late Fee Rate (%)',
                'description' => 'Late fee percentage rate',
                'is_public' => false,
                'is_required' => true,
            ],
            [
                'key' => 'whatsapp_enabled',
                'group' => 'notifications',
                'value' => 'false',
                'type' => 'boolean',
                'display_name' => 'WhatsApp Enabled',
                'description' => 'Enable WhatsApp notifications',
                'is_public' => false,
                'is_required' => false,
            ],
            [
                'key' => 'email_notifications_enabled',
                'group' => 'notifications',
                'value' => 'true',
                'type' => 'boolean',
                'display_name' => 'Email Notifications Enabled',
                'description' => 'Enable email notifications',
                'is_public' => false,
                'is_required' => false,
            ],
        ];

        foreach ($settings as $setting) {
            DB::table('system_settings')->updateOrInsert(
                ['key' => $setting['key']],
                array_merge($setting, [
                    'created_by' => 1, // Admin user
                    'updated_by' => 1, // Admin user
                    'created_at' => now(),
                    'updated_at' => now(),
                ])
            );
        }

        $this->command->info('System settings seeded successfully!');
    }
}
