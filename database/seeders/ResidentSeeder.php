<?php

namespace Database\Seeders;

use App\Models\Contact;
use App\Models\Estate;
use App\Models\House;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class ResidentSeeder extends Seeder
{
    public function run(): void
    {
        // Get all estates and houses
        $estates = Estate::all();
        $houses = House::all();

        // Create resident users for each estate with different scenarios
        $residents = [];

        // Estate 1: Sunset Gardens Estate - Multiple residents
        $estate1 = $estates->first();
        $estate1Houses = $houses->where('estate_id', $estate1->id)->take(4);

        $residents[] = $this->createResidentWithHouse(
            '<PERSON>',
            '<EMAIL>',
            $estate1Houses->first(),
            'owner',
            'Primary resident of House A1'
        );

        $residents[] = $this->createResidentWithHouse(
            'Sarah Johnson',
            '<EMAIL>',
            $estate1Houses->skip(1)->first(),
            'tenant',
            'Tenant of House A2'
        );

        $residents[] = $this->createResidentWithHouse(
            '<PERSON>',
            '<EMAIL>',
            $estate1Houses->skip(2)->first(),
            'owner',
            'Primary resident of House A3'
        );

        // Estate 2: Green Valley Estate - Family with multiple houses
        $estate2 = $estates->skip(1)->first();
        $estate2Houses = $houses->where('estate_id', $estate2->id)->take(3);

        $familyUser = $this->createResidentWithHouse(
            'David Wilson',
            '<EMAIL>',
            $estate2Houses->first(),
            'owner',
            'Family owns multiple properties'
        );

        // Link the same user to multiple houses (family scenario)
        $this->linkUserToHouse($familyUser, $estate2Houses->skip(1)->first(), 'owner');
        $this->linkUserToHouse($familyUser, $estate2Houses->skip(2)->first(), 'owner');

        $residents[] = $familyUser;

        $residents[] = $this->createResidentWithHouse(
            'Emma Davis',
            '<EMAIL>',
            $estate2Houses->skip(1)->first(),
            'tenant',
            'Tenant of House B2'
        );

        // Estate 3: Ocean View Estate - Single resident
        $estate3 = $estates->skip(2)->first();
        $estate3House = $houses->where('estate_id', $estate3->id)->first();

        $residents[] = $this->createResidentWithHouse(
            'Robert Taylor',
            '<EMAIL>',
            $estate3House,
            'owner',
            'Single property owner'
        );

        // Estate 4: Mountain Ridge Estate - Multiple contacts per house
        $estate4 = $estates->skip(3)->first();
        $estate4House = $houses->where('estate_id', $estate4->id)->first();

        $mainResident = $this->createResidentWithHouse(
            'Lisa Anderson',
            '<EMAIL>',
            $estate4House,
            'owner',
            'Primary resident with family members'
        );

        // Add additional contacts for the same house (family members)
        $this->createAdditionalContact(
            'James Anderson',
            '<EMAIL>',
            $estate4House,
            'family_member',
            false,
            $mainResident
        );

        $this->createAdditionalContact(
            'Sophie Anderson',
            '<EMAIL>',
            $estate4House,
            'family_member',
            false,
            $mainResident
        );

        $residents[] = $mainResident;

        // Estate 5: City Center Estate - Commercial and residential mix
        $estate5 = $estates->skip(4)->first();
        $estate5Houses = $houses->where('estate_id', $estate5->id)->take(2);

        $residents[] = $this->createResidentWithHouse(
            'William Martinez',
            '<EMAIL>',
            $estate5Houses->first(),
            'owner',
            'City center apartment owner'
        );

        $residents[] = $this->createResidentWithHouse(
            'Jennifer Garcia',
            '<EMAIL>',
            $estate5Houses->skip(1)->first(),
            'tenant',
            'City center tenant'
        );

        // Create some residents without email verification for testing
        $unverifiedResident = $this->createResidentWithHouse(
            'Thomas Lee',
            '<EMAIL>',
            $houses->where('estate_id', $estates->skip(5)->first()->id)->first(),
            'tenant',
            'Unverified resident account'
        );

        $unverifiedResident->email_verified_at = null;
        $unverifiedResident->save();
        $residents[] = $unverifiedResident;

        // Create a resident with multiple house types for comprehensive testing
        $multiHouseResident = $this->createResidentWithHouse(
            'Patricia White',
            '<EMAIL>',
            $houses->where('estate_id', $estates->skip(6)->first()->id)->first(),
            'owner',
            'Multi-property investor'
        );

        // Link to additional houses in different estates
        $additionalHouses = $houses->whereIn('estate_id', [
            $estates->skip(7)->first()->id,
            $estates->skip(1)->first()->id, // Already has Green Valley
        ])->take(2);

        foreach ($additionalHouses as $house) {
            $this->linkUserToHouse($multiHouseResident, $house, 'owner');
        }

        $residents[] = $multiHouseResident;

        // Display seeded residents information
        $this->command->info('Resident users seeded successfully!');
        $this->command->table(
            ['Name', 'Email', 'Password', 'Role', 'Houses', 'Contact Type'],
            collect($residents)->map(function ($resident) {
                $houseCount = $resident->contacts()->count();
                $primaryContact = $resident->contacts()->where('is_primary', true)->first();
                $contactType = $primaryContact ? $primaryContact->type : 'N/A';

                return [
                    $resident->name,
                    $resident->email,
                    'password',
                    'Resident',
                    $houseCount.' house(s)',
                    ucfirst($contactType),
                ];
            })
        );

        $this->command->comment('Use these credentials to test the resident portal:');
        $this->command->comment('Email: <EMAIL>, Password: password');
        $this->command->comment('Email: <EMAIL>, Password: password (multiple houses)');
        $this->command->comment('Email: <EMAIL>, Password: password (family contacts)');
        $this->command->comment('Email: <EMAIL>, Password: password (unverified)');
    }

    /**
     * Create a resident user with a contact linked to a house.
     */
    private function createResidentWithHouse(string $name, string $email, House $house, string $contactType, string $notes): User
    {
        // Create or update the user
        $user = User::updateOrCreate(
            ['email' => $email],
            [
                'name' => $name,
                'password' => Hash::make('password'),
                'role' => 'resident',
                'email_verified_at' => now(),
            ]
        );

        // Create primary contact for this user and house
        Contact::updateOrCreate(
            [
                'user_id' => $user->id,
                'house_id' => $house->id,
                'is_primary' => true,
            ],
            [
                'name' => $name,
                'email' => $email,
                'phone' => $this->generatePhoneNumber(),
                'whatsapp_number' => $this->generatePhoneNumber(),
                'type' => $contactType,
                'is_active' => true,
                'receive_invoices' => true,
                'receive_notifications' => true,
                'notes' => $notes,
            ]
        );

        return $user;
    }

    /**
     * Link an existing user to an additional house.
     */
    private function linkUserToHouse(User $user, House $house, string $contactType): void
    {
        Contact::updateOrCreate(
            [
                'user_id' => $user->id,
                'house_id' => $house->id,
                'is_primary' => false, // Additional houses are not primary
            ],
            [
                'name' => $user->name,
                'email' => $user->email,
                'phone' => $this->generatePhoneNumber(),
                'whatsapp_number' => $this->generatePhoneNumber(),
                'type' => $contactType,
                'is_active' => true,
                'receive_invoices' => true,
                'receive_notifications' => true,
                'notes' => 'Additional property for '.$user->name,
            ]
        );
    }

    /**
     * Create an additional contact for a house (family member scenario).
     */
    private function createAdditionalContact(string $name, string $email, House $house, string $contactType, bool $isPrimary, ?User $linkedUser = null): Contact
    {
        return Contact::updateOrCreate(
            [
                'email' => $email,
                'house_id' => $house->id,
            ],
            [
                'user_id' => $linkedUser?->id,
                'name' => $name,
                'phone' => $this->generatePhoneNumber(),
                'whatsapp_number' => $this->generatePhoneNumber(),
                'type' => $contactType,
                'is_primary' => $isPrimary,
                'is_active' => true,
                'receive_invoices' => $isPrimary, // Only primary receives invoices
                'receive_notifications' => true,
                'notes' => $isPrimary ? 'Primary contact' : 'Family member contact',
            ]
        );
    }

    /**
     * Generate a realistic phone number.
     */
    private function generatePhoneNumber(): string
    {
        $prefixes = ['071', '072', '079', '070', '074', '011', '020'];
        $prefix = $prefixes[array_rand($prefixes)];
        $suffix = str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);

        return $prefix.$suffix;
    }
}
