<?php

namespace Database\Seeders;

use App\Models\Estate;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UserRoleSeeder extends Seeder
{
    public function run(): void
    {
        // Get estates (should be created by EstateSeeder)
        $estate1 = Estate::where('code', 'SGE-001')->first();
        $estate2 = Estate::where('code', 'GVE-002')->first();

        if (! $estate1 || ! $estate2) {
            $this->command->error('Required estates not found. Please ensure EstateSeeder runs first.');

            return;
        }

        // Create or update admin user
        $admin = User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'System Administrator',
                'password' => Hash::make('password'),
            ]
        );
        $admin->assignRole('admin');

        // Create or update manager user
        $manager = User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'John Manager',
                'password' => Hash::make('password'),
            ]
        );
        $manager->assignRole('manager');

        // Create or update caretaker users
        $caretaker1 = User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Alice Caretaker',
                'password' => Hash::make('password'),
            ]
        );
        $caretaker1->assignRole('caretaker');

        $caretaker2 = User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Bob Caretaker',
                'password' => Hash::make('password'),
            ]
        );
        $caretaker2->assignRole('caretaker');

        // Create or update reviewer users
        $reviewer1 = User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Carol Reviewer',
                'password' => Hash::make('password'),
            ]
        );
        $reviewer1->assignRole('reviewer');

        $reviewer2 = User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'David Reviewer',
                'password' => Hash::make('password'),
            ]
        );
        $reviewer2->assignRole('reviewer');

        // Create or update simple resident user
        $resident = User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Test Resident',
                'password' => Hash::make('password'),
            ]
        );
        $resident->assignRole('resident');

        // Assign users to estates (sync to avoid duplicates)
        $admin->assignedEstates()->sync([
            $estate1->id => ['assigned_by' => $admin->id],
            $estate2->id => ['assigned_by' => $admin->id],
        ]);
        $caretaker1->assignedEstates()->sync([$estate1->id => ['assigned_by' => $manager->id]]);
        $caretaker2->assignedEstates()->sync([$estate2->id => ['assigned_by' => $manager->id]]);
        $reviewer1->assignedEstates()->sync([$estate1->id => ['assigned_by' => $manager->id]]);
        $reviewer2->assignedEstates()->sync([$estate2->id => ['assigned_by' => $manager->id]]);
        $manager->assignedEstates()->sync([
            $estate1->id => ['assigned_by' => $manager->id],
            $estate2->id => ['assigned_by' => $manager->id],
        ]);

        // Assign resident to first estate for testing
        $resident->assignedEstates()->sync([$estate1->id => ['assigned_by' => $admin->id]]);

        $this->command->info('Users and estates seeded successfully!');
        $this->command->table(
            ['Email', 'Password', 'Role', 'Estates'],
            [
                [$admin->email, 'password', 'Admin', 'All Estates'],
                [$manager->email, 'password', 'Manager', 'All Estates'],
                [$caretaker1->email, 'password', 'Caretaker', $estate1->name],
                [$caretaker2->email, 'password', 'Caretaker', $estate2->name],
                [$reviewer1->email, 'password', 'Reviewer', $estate1->name],
                [$reviewer2->email, 'password', 'Reviewer', $estate2->name],
                [$resident->email, 'password', 'Resident', $estate1->name],
            ]
        );
    }
}
