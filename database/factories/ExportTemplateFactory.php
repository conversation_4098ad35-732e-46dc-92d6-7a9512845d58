<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\ExportTemplate;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends FactoryExportTemplate>
 */
class ExportTemplateFactory extends Factory
{
    protected $model = ExportTemplate::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'name' => $this->faker->sentence(3),
            'entity_type' => $this->faker->randomElement(['estates', 'houses', 'contacts', 'meter_readings', 'invoices']),
            'format' => $this->faker->randomElement(['xlsx', 'csv', 'pdf']),
            'columns' => ['ID', 'Name', 'Created At', 'Status'],
            'filters' => [],
            'is_public' => $this->faker->boolean(),
            'is_scheduled' => $this->faker->boolean(),
            'schedule_frequency' => $this->faker->optional()->randomElement(['daily', 'weekly', 'monthly']),
            'last_run_at' => $this->faker->optional()->dateTime(),
            'next_run_at' => $this->faker->optional()->dateTime(),
        ];
    }

    /**
     * Indicate that the template is public.
     */
    public function public(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_public' => true,
        ]);
    }

    /**
     * Indicate that the template is private.
     */
    public function private(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_public' => false,
        ]);
    }

    /**
     * Indicate that the template is scheduled.
     */
    public function scheduled(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_scheduled' => true,
            'schedule_frequency' => $this->faker->randomElement(['daily', 'weekly', 'monthly']),
        ]);
    }

    /**
     * Indicate that the template is not scheduled.
     */
    public function notScheduled(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_scheduled' => false,
            'schedule_frequency' => null,
        ]);
    }

    /**
     * Indicate that the template is for a specific entity type.
     */
    public function forEntityType(string $entityType): static
    {
        return $this->state(fn (array $attributes) => [
            'entity_type' => $entityType,
        ]);
    }

    /**
     * Indicate that the template is for a specific format.
     */
    public function forFormat(string $format): static
    {
        return $this->state(fn (array $attributes) => [
            'format' => $format,
        ]);
    }
}
