<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\ExportJob;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends FactoryExportJob>
 */
class ExportJobFactory extends Factory
{
    protected $model = ExportJob::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'entity_type' => $this->faker->randomElement(['estates', 'houses', 'contacts', 'meter_readings', 'invoices']),
            'format' => $this->faker->randomElement(['xlsx', 'csv', 'pdf']),
            'status' => $this->faker->randomElement(['pending', 'processing', 'completed', 'failed']),
            'file_name' => $this->faker->word().'.'.$this->faker->randomElement(['xlsx', 'csv', 'pdf']),
            'file_path' => 'exports/'.$this->faker->word().'.'.$this->faker->randomElement(['xlsx', 'csv', 'pdf']),
            'filters' => [],
            'total_records' => $this->faker->numberBetween(0, 1000),
            'error' => $this->faker->optional()->sentence(),
            'started_at' => $this->faker->optional()->dateTime(),
            'completed_at' => $this->faker->optional()->dateTime(),
        ];
    }

    /**
     * Indicate that the export job is pending.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'pending',
            'started_at' => null,
            'completed_at' => null,
        ]);
    }

    /**
     * Indicate that the export job is processing.
     */
    public function processing(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'processing',
            'started_at' => now(),
            'completed_at' => null,
        ]);
    }

    /**
     * Indicate that the export job is completed.
     */
    public function completed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'completed',
            'started_at' => now(),
            'completed_at' => now(),
            'error' => null,
        ]);
    }

    /**
     * Indicate that the export job has failed.
     */
    public function failed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'failed',
            'started_at' => now(),
            'completed_at' => now(),
            'error' => $this->faker->sentence(),
        ]);
    }

    /**
     * Indicate that the export job is for a specific entity type.
     */
    public function forEntityType(string $entityType): static
    {
        return $this->state(fn (array $attributes) => [
            'entity_type' => $entityType,
        ]);
    }

    /**
     * Indicate that the export job is for a specific format.
     */
    public function forFormat(string $format): static
    {
        return $this->state(fn (array $attributes) => [
            'format' => $format,
        ]);
    }
}
