<?php

namespace Database\Factories;

use App\Models\AccountTransaction;
use App\Models\HouseAccount;
use App\Models\Invoice;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\AccountTransaction>
 */
class AccountTransactionFactory extends Factory
{
    protected $model = AccountTransaction::class;

    public function definition(): array
    {
        return [
            'house_account_id' => HouseAccount::factory(),
            'transaction_type' => $this->faker->randomElement(['invoice', 'payment', 'adjustment', 'credit_note']),
            'reference_type' => $this->faker->randomElement(['Invoice', 'Payment', 'Adjustment']),
            'reference_id' => Invoice::factory(),
            'amount' => $this->faker->randomFloat(2, 10, 5000),
            'balance_before' => $this->faker->randomFloat(2, 0, 10000),
            'balance_after' => $this->faker->randomFloat(2, 0, 10000),
            'description' => $this->faker->sentence(),
            'user_id' => User::factory(),
            'transaction_reference' => 'TXN-'.$this->faker->unique()->numerify('######'),
        ];
    }

    public function invoice(): static
    {
        return $this->state(fn (array $attributes) => [
            'transaction_type' => 'invoice',
            'reference_type' => 'Invoice',
            'description' => 'Invoice #'.$this->faker->numerify('INV-####'),
        ]);
    }

    public function payment(): static
    {
        return $this->state(fn (array $attributes) => [
            'transaction_type' => 'payment',
            'reference_type' => 'Payment',
            'description' => 'Payment for invoice #'.$this->faker->numerify('INV-####'),
        ]);
    }

    public function adjustment(): static
    {
        return $this->state(fn (array $attributes) => [
            'transaction_type' => 'adjustment',
            'reference_type' => 'Adjustment',
            'description' => 'Adjustment: '.$this->faker->randomElement(['Discount', 'Penalty', 'Correction']),
        ]);
    }

    public function creditNote(): static
    {
        return $this->state(fn (array $attributes) => [
            'transaction_type' => 'credit_note',
            'reference_type' => 'CreditNote',
            'description' => 'Credit Note #'.$this->faker->numerify('CN-####'),
        ]);
    }

    public function forHouseAccount(HouseAccount $account): static
    {
        return $this->state(fn (array $attributes) => [
            'house_account_id' => $account->id,
        ]);
    }

    public function byUser(User $user): static
    {
        return $this->state(fn (array $attributes) => [
            'user_id' => $user->id,
        ]);
    }

    public function withAmount(float $amount): static
    {
        return $this->state(fn (array $attributes) => [
            'amount' => $amount,
        ]);
    }
}
