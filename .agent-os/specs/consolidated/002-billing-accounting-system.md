# Consolidated Spec: Billing & Accounting System

> **Consolidated From**: 4 individual specs
> - 2025-07-25-invoice-generation-service
> - 2025-08-04-comprehensive-billing-system
> - 2025-07-31-water-rate-management-spec
> - 2025-08-04-performance-optimization

> **Created**: 2025-08-08 (Consolidated)
> **Status**: Ready for Implementation

## Overview

Comprehensive billing and accounting system for water utility operations, covering invoice generation, payment processing, account management, financial reporting, and rate management. This consolidated spec provides a unified approach to managing the complete financial lifecycle from meter reading to payment reconciliation.

## Core Features

### 1. Invoice Generation & Management
- **Automated Billing**: Scheduled and on-demand invoice generation for all properties
- **Tiered Pricing Support**: Complex water rate structures with consumption-based tiers
- **PDF Generation**: Professional, branded invoice templates with QR codes and barcodes
- **Invoice Lifecycle**: Complete workflow from draft through payment to archival
- **Bulk Operations**: Mass invoice generation, approval, and distribution

### 2. House Account System
- **Double-Entry Accounting**: Proper accounting principles with debit/credit tracking
- **Running Balance Management**: Real-time balance calculation and forward carry
- **Transaction History**: Complete audit trail of all financial transactions
- **Account Status Management**: Active, suspended, and closed account states
- **Multi-Currency Support**: Handle different currencies with exchange rate management

### 3. Payment Processing & Reconciliation
- **Multiple Payment Methods**: Bank transfer, mobile money, cash, check processing
- **Payment Application**: Automatic payment application to outstanding invoices
- **Reconciliation System**: Match payments with invoices and handle discrepancies
- **Aging Management**: Track overdue invoices and aging buckets
- **Credit & Adjustment Management**: Handle credits, refunds, and invoice adjustments

### 4. Financial Reporting & Analytics
- **Aging Reports**: Comprehensive aging analysis with visual bucketing
- **Revenue Analytics**: Revenue trends, forecasts, and performance metrics
- **Customer Statements**: Detailed account statements with transaction history
- **Financial Dashboards**: Real-time financial performance monitoring
- **Export Capabilities**: Excel, PDF, and CSV export with professional formatting

### 5. Water Rate Management
- **Flexible Rate Structures**: Flat, tiered, seasonal, and time-of-use pricing
- **Rate Configuration**: Easy-to-use rate setup and management interface
- **Historical Rates**: Maintain rate history for accurate billing of past periods
- **Rate Analytics**: Impact analysis and revenue projection tools
- **Estate-Specific Rates**: Custom rates per estate with override capabilities

## Technical Architecture

### Database Schema (Consolidated)

#### Billing & Accounting Tables
```sql
-- Water rates with flexible pricing structures
water_rates (
    id, estate_id, name, rate_type, rate_per_unit, minimum_charge, minimum_units,
    fixed_charge, currency, billing_unit, tax_rate, includes_tax,
    effective_from, effective_to, tier_rates, seasonal_rates,
    is_active, description, created_at, updated_at
)

-- Invoices with comprehensive billing information
invoices (
    id, invoice_number, house_id, meter_reading_id, water_rate_id,
    billing_period_start, billing_period_end,
    previous_reading, current_reading, consumption, rate_per_unit,
    amount, fixed_charge, tax_amount, discount_amount, adjustments,
    previous_balance, final_amount, currency,
    status, approval_status, approved_by, approved_at,
    due_date, sent_at, paid_at, payment_reference,
    invoice_type, parent_invoice_id, payment_terms, billing_address,
    customer_reference, pdf_path, public_link, notes,
    created_at, updated_at
)

-- House accounts with double-entry accounting
house_accounts (
    id, house_id, current_balance, total_credit, total_debit,
    available_credit, credit_limit,
    last_transaction_date, last_payment_date, last_invoice_date,
    account_status, notes, created_at, updated_at
)

-- Account transactions with comprehensive tracking
account_transactions (
    id, house_account_id, transaction_type, reference_type, reference_id,
    amount, currency, exchange_rate, balance_before, balance_after, direction,
    transaction_reference, batch_reference, payment_method, payment_reference,
    receipt_number, description, payment_details, reconciliation_data,
    status, processed_at, reconciled_at, reconciled_by,
    user_id, created_at, updated_at
)

-- Invoice line items for detailed billing
invoice_line_items (
    id, invoice_id, item_type, description, quantity, unit_price,
    amount, tax_rate, tax_amount, total_amount, notes,
    created_at, updated_at
)

-- Invoice payments with application tracking
invoice_payments (
    id, invoice_id, account_transaction_id, amount, remaining_balance,
    payment_status, payment_method, payment_reference, receipt_number,
    payment_date, notes, created_at, updated_at
)

-- Invoice adjustments with approval workflow
invoice_adjustments (
    id, invoice_id, user_id, adjustment_type, reason, amount, percentage,
    status, approved_by, approved_at, approval_notes, notes,
    created_at, updated_at
)
```

### Service Layer Architecture

#### 1. Invoice Generation Service
```php
class InvoiceGenerationService
{
    public function generateForEstate(Estate $estate, Carbon $period): InvoiceBatch
    public function generateForHouse(House $house, Carbon $period): Invoice
    public function calculateBill(House $house, $consumption, WaterRate $rate): BillingCalculation
    public function applyTieredPricing($consumption, WaterRate $rate): array
    public function generateInvoicePdf(Invoice $invoice): string
    public function sendInvoice(Invoice $invoice): void
    public function bulkGenerateInvoices(array $houses, Carbon $period): BulkGenerationResult
}
```

#### 2. Billing Calculation Service
```php
class BillingCalculationService
{
    public function calculateConsumptionCharges($consumption, WaterRate $rate): array
    public function calculateBaseCharges(House $house, WaterRate $rate): float
    public function calculateTaxes(float $subtotal, array $taxRates): array
    public function calculateLateFees(Invoice $invoice): float
    public function applyProRatedCharges(Invoice $invoice, Carbon $rateChangeDate): Invoice
    public function calculateFinalAmount(Invoice $invoice): float
    public function validateBillingCalculations(Invoice $invoice): ValidationResult
}
```

#### 3. Account Management Service
```php
class AccountManagementService
{
    public function createHouseAccount(House $house): HouseAccount
    public function processTransaction(HouseAccount $account, array $transactionData): AccountTransaction
    public function applyPayment(Invoice $invoice, array $paymentData): PaymentResult
    public function calculateAccountBalance(HouseAccount $account): float
    public function getAccountStatement(HouseAccount $account, $period): AccountStatement
    public function reconcileTransactions(array $transactions): ReconciliationResult
}
```

#### 4. Financial Reporting Service
```php
class FinancialReportingService
{
    public function generateAgingReport(Estate $estate, $asOfDate): AgingReport
    public function generateRevenueReport(Estate $estate, $period): RevenueReport
    public function generateCustomerStatement(House $house, $period): CustomerStatement
    public function getFinancialDashboard(Estate $estate): FinancialDashboard
    public function exportReport(Report $report, string $format): ExportResult
    public function scheduleReportGeneration(array $scheduleConfig): ScheduledReport
}
```

#### 5. Water Rate Management Service
```php
class WaterRateManagementService
{
    public function createWaterRate(Estate $estate, array $rateData): WaterRate
    public function updateWaterRate(WaterRate $rate, array $data): WaterRate
    public function calculateRateImpact(WaterRate $rate, $projectionPeriod): RateImpact
    public function validateRateStructure(array $rateData): ValidationResult
    public function getActiveRatesForEstate(Estate $estate, $date): Collection
    public function archiveRate(WaterRate $rate): void
}
```

### Livewire Components (Consolidated)

#### Billing Management Components
```php
// Invoice Management
InvoiceManager - Main invoice CRUD interface
InvoiceGenerator - Bulk invoice generation wizard
InvoiceApproval - Invoice approval workflow dashboard
InvoiceViewer - Detailed invoice display with PDF preview
InvoiceAdjustment - Invoice adjustment and credit management

// Account Management
AccountManager - House account management interface
AccountStatement - Account statement viewer and exporter
TransactionManager - Transaction history and reconciliation
PaymentProcessor - Payment entry and application interface
AgingManager - Aging analysis and collection management

// Rate Management
RateManager - Water rate configuration interface
RateCalculator - Rate impact analysis tool
RateHistory - Historical rate viewer and comparison
RateAnalytics - Rate performance and revenue analysis

// Financial Reporting
FinancialDashboard - Real-time financial metrics dashboard
AgingReport - Interactive aging report with bucketing
RevenueReport - Revenue analysis and forecasting
CustomerStatement - Customer statement generation and delivery
ReportExporter - Multi-format report export interface
```

## Implementation Strategy

### Phase 1: Core Billing Infrastructure
1. **Database Migration**: Implement billing and accounting tables
2. **Model Development**: Create Invoice, HouseAccount, AccountTransaction models
3. **Service Layer**: Implement core billing calculation and account management
4. **Basic Invoice Generation**: Simple invoice creation and PDF generation

### Phase 2: Advanced Billing Features
1. **Tiered Pricing**: Complex rate structure implementation
2. **Invoice Workflow**: Complete invoice lifecycle management
3. **Payment Processing**: Payment application and reconciliation
4. **Account Management**: Double-entry accounting system

### Phase 3: Financial Reporting
1. **Aging Reports**: Comprehensive aging analysis and bucketing
2. **Revenue Analytics**: Revenue tracking and forecasting
3. **Customer Statements**: Detailed account statements
4. **Export Capabilities**: Multi-format report generation

### Phase 4: Rate Management
1. **Rate Configuration**: Flexible rate structure management
2. **Rate Analytics**: Impact analysis and performance tracking
3. **Historical Rates**: Rate history and archival system
4. **Estate-Specific Rates**: Custom rate configuration per estate

### Phase 5: Performance & Optimization
1. **Bulk Processing**: Optimized bulk invoice generation
2. **Caching Strategy**: Performance optimization for financial data
3. **Database Optimization**: Indexing and query optimization
4. **Background Processing**: Queue-based heavy operations

## Security & Compliance

### Financial Data Protection
- **Transaction Security**: Secure processing of all financial transactions
- **Audit Trail**: Complete audit trail for all financial operations
- **Data Encryption**: Encryption of sensitive financial data
- **Access Control**: Role-based access to financial functions

### Compliance Requirements
- **Tax Compliance**: Accurate tax calculation and reporting
- **Audit Requirements**: Maintain audit-ready financial records
- **Regulatory Reporting**: Generate required regulatory reports
- **Data Retention**: Compliant financial data retention policies

### Financial Controls
- **Double-Entry Accounting**: Ensure accounting integrity
- **Reconciliation Processes**: Regular reconciliation of accounts
- **Approval Workflows**: Multi-level approval for financial operations
- **Segregation of Duties**: Proper separation of financial responsibilities

## Testing Strategy

### Unit Tests
- **Billing Calculations**: Test all rate structures and calculations
- **Account Transactions**: Test double-entry accounting principles
- **Invoice Generation**: Test PDF generation and invoice workflows
- **Financial Reports**: Test report accuracy and data integrity

### Integration Tests
- **Billing Workflows**: End-to-end billing process testing
- **Payment Processing**: Complete payment application and reconciliation
- **Financial Reporting**: Report generation and export testing
- **Rate Management**: Rate configuration and impact analysis testing

### Performance Tests
- **Bulk Invoice Generation**: Performance with large datasets
- **Financial Reporting**: Report generation performance
- **Concurrent Processing**: Multiple users processing financial data
- **Database Performance**: Query optimization and indexing effectiveness

## Success Metrics

### Functional Metrics
- **Invoice Generation**: Support for 10,000+ invoices per hour
- **Payment Processing**: 99.9% accuracy in payment application
- **Account Reconciliation**: 95% auto-reconciliation rate
- **Report Generation**: Sub-second report generation for standard reports

### Financial Metrics
- **Billing Accuracy**: 99.9% accuracy in billing calculations
- **Revenue Recognition**: Proper revenue recognition and reporting
- **Aging Management**: 30% reduction in overdue invoices
- **Cost Efficiency**: 50% reduction in billing processing costs

### User Experience Metrics
- **Manager Efficiency**: 70% reduction in time for billing tasks
- **Reporting Speed**: 80% faster report generation
- **Data Access**: Real-time financial data availability
- **Error Reduction**: 90% reduction in billing errors

## Integration Points

### Meter Reading Integration
- **Consumption Data**: Automatic consumption calculation from meter readings
- **Reading Validation**: Ensure valid readings before billing
- **Historical Data**: Access to historical consumption for rate analysis

### Property Management Integration
- **House Information**: House and estate data for billing
- **Contact Information**: Customer data for invoice delivery
- **Account Setup**: Automatic account creation for new houses

### Communication Integration
- **Invoice Delivery**: Automated invoice delivery via preferred channels
- **Payment Reminders**: Automated dunning and payment reminders
- **Customer Communication**: Integrated messaging for billing inquiries

## Future Enhancements

### Advanced Features
- **AI-Powered Analytics**: Predictive analytics for revenue forecasting
- **Machine Learning**: Anomaly detection in billing patterns
- **Blockchain**: Immutable financial records and audit trails
- **Real-Time Processing**: Real-time payment processing and reconciliation

### Expansion Capabilities
- **Multi-Utility Support**: Expand to electricity, gas, and other utilities
- **International Expansion**: Multi-currency and multi-language support
- **Mobile Payments**: Advanced mobile payment integration
- **API Ecosystem**: Open API for third-party integrations

This consolidated specification provides a comprehensive foundation for billing and accounting operations within the water management system, ensuring financial accuracy, regulatory compliance, and excellent user experience across all financial operations.