# Consolidated Spec: Core Property Management System

> **Consolidated From**: 6 individual specs
> - 2025-07-24-userrole-access-fix
> - 2025-07-25-house-contact-management  
> - 2025-07-25-inactive-contact-prevention
> - 2025-07-27-finish-contact-management
> - 2025-07-27-add-crud-views-for-userflow
> - 2025-07-31-resident-portal-spec

> **Created**: 2025-08-08 (Consolidated)
> **Status**: Ready for Implementation

## Overview

Comprehensive property management system for water utility operations, covering estate management, house registry, contact management, resident portal, and role-based access control. This consolidated spec provides a unified approach to managing properties, residents, and their relationships within the water management ecosystem.

## Core Features

### 1. Estate Management System
- **Multi-Estate Support**: Manage multiple residential estates with centralized oversight
- **Estate Configuration**: Detailed estate settings, contact information, and operational parameters
- **Geographic Management**: GPS coordinates, service areas, and zone-based management
- **Estate Analytics**: Occupancy rates, property statistics, and performance metrics

### 2. House Registry & Management
- **Comprehensive House Data**: Detailed property information including building details, meter specifications, and utility connections
- **Smart Meter Integration**: Support for analog, digital, and IoT-enabled water meters
- **Property Classification**: Residential, commercial, and mixed-use property types
- **House Lifecycle Management**: From registration through occupancy changes and deactivation

### 3. Contact & Resident Management
- **Multi-Contact Support**: Owners, tenants, caretakers, and emergency contacts per property
- **Contact Lifecycle**: From initial registration through relationship changes and archival
- **Communication Preferences**: Multi-channel communication setup (email, SMS, WhatsApp)
- **Resident Self-Service**: Portal for personal information management and service requests

### 4. Role-Based Access Control
- **Five-Tier Role System**: Admin, Manager, Reviewer, Caretaker, Resident with distinct responsibilities
- **Estate-Based Access**: Users restricted to assigned estates and properties
- **Hierarchical Management**: Manager → Reviewer → Caretaker reporting structure
- **Permission Granularity**: Fine-grained control over all system operations

### 5. Resident Portal Integration
- **Self-Service Dashboard**: Personal overview of bills, consumption, and account status
- **Bill Viewing & Payment**: Access to invoices, payment history, and online payment options
- **Consumption Analytics**: Personal water usage trends and conservation insights
- **Communication Center**: Direct messaging with management and inquiry tracking

## Technical Architecture

### Database Schema (Consolidated)

#### Core Tables
```sql
-- Estates table with enhanced contact and management fields
estates (
    id, name, code, address, city, state, postal_code, country,
    contact_person, contact_phone, contact_email, emergency_contact, emergency_phone,
    manager_name, manager_phone, manager_email, website, social_media,
    total_houses, occupied_houses, settings, is_active, 
    created_at, updated_at, deleted_at
)

-- Houses table with comprehensive property and meter information
houses (
    id, estate_id, house_number, unit_number, building_name, block, floor,
    type, billing_cycle, property_id, utility_account_number,
    meter_number, meter_type, meter_brand, meter_model, meter_capacity,
    meter_installation_date, initial_reading, bedrooms, size_sqft, monthly_rent,
    occupancy_date, gps_coordinates, amenities, utilities, notes, is_active,
    created_at, updated_at, deleted_at
)

-- Contacts table with detailed personal and communication data
contacts (
    id, house_id, user_id, name, first_name, last_name, middle_name,
    email, alternative_email, phone, alternative_phone, whatsapp_number,
    id_number, date_of_birth, occupation, company, postal_address,
    emergency_contact_name, emergency_contact_phone,
    type, is_primary, receive_invoices, receive_notifications,
    preferred_language, email_notifications, sms_notifications, whatsapp_notifications,
    invoice_delivery_method, communication_preferences, is_active,
    created_at, updated_at, deleted_at
)

-- House-Contact relationship management
house_contacts (
    id, house_id, contact_id, relationship_type, start_date, end_date,
    is_current, notes, created_at, updated_at
)

-- User estate assignments for access control
user_estate_assignments (
    id, user_id, estate_id, role_type, assigned_from, assigned_to,
    is_active, notes, created_at, updated_at
)
```

### Service Layer Architecture

#### 1. Estate Management Service
```php
class EstateManagementService
{
    public function createEstate(array $data): Estate
    public function updateEstate(Estate $estate, array $data): Estate
    public function assignManager(Estate $estate, User $manager): void
    public function getEstateAnalytics(Estate $estate): EstateAnalytics
    public function manageEstateSettings(Estate $estate, array $settings): void
}
```

#### 2. House Registry Service
```php
class HouseRegistryService  
{
    public function registerHouse(Estate $estate, array $data): House
    public function updateHouseDetails(House $house, array $data): House
    public function assignMeter(House $house, array $meterData): void
    public function updateOccupancy(House $house, array $occupancyData): void
    public function getHouseAnalytics(House $house): HouseAnalytics
}
```

#### 3. Contact Management Service
```php
class ContactManagementService
{
    public function createContact(House $house, array $data): Contact
    public function updateContact(Contact $contact, array $data): Contact
    public function assignContactToHouse(Contact $contact, House $house): void
    public function manageContactPreferences(Contact $contact, array $preferences): void
    public function handleContactTypeChange(Contact $contact, string $newType): void
}
```

#### 4. Resident Portal Service
```php
class ResidentPortalService
{
    public function authenticateResident(array $credentials): AuthResult
    public function getResidentDashboard(User $resident): ResidentDashboard
    public function getResidentBills(User $resident, array $filters): Collection
    public function getConsumptionAnalytics(User $resident, $period): ConsumptionAnalytics
    public function submitInquiry(User $resident, array $inquiryData): Inquiry
}
```

#### 5. Permission Validation Service
```php
class PermissionValidationService
{
    public function validateEstateAccess(User $user, Estate $estate): bool
    public function validateHouseAccess(User $user, House $house): bool
    public function validateContactAccess(User $user, Contact $contact): bool
    public function getUserEstates(User $user): Collection
    public function getUserPermissions(User $user): array
}
```

### Livewire Components (Consolidated)

#### Property Management Components
```php
// Estate Management
EstateManager - Main estate CRUD interface
EstateSelector - Estate selection dropdown
EstateAnalytics - Estate performance dashboard
EstateSettings - Estate configuration management

// House Registry  
HouseRegistry - Main house management interface
HouseForm - Create/edit house form
HouseSearch - Advanced house search and filtering
HouseImport - CSV house import functionality
HouseDetails - Comprehensive house information display

// Contact Management
ContactManager - Main contact interface
ContactForm - Add/edit contact form
ContactImport - CSV contact import
ContactMerge - Duplicate contact resolution
ContactPreferences - Communication preference management

// Resident Portal
ResidentDashboard - Resident self-service dashboard
ResidentBills - Bill viewing and payment
ResidentConsumption - Usage analytics and trends
ResidentInquiries - Inquiry submission and tracking
ResidentProfile - Personal information management
```

## Implementation Strategy

### Phase 1: Core Property Infrastructure
1. **Database Migration**: Implement consolidated property management tables
2. **Model Development**: Create Estate, House, Contact models with relationships
3. **Service Layer**: Implement core property management services
4. **Basic CRUD**: Create fundamental create/read/update/delete operations

### Phase 2: Contact & Relationship Management
1. **Contact System**: Implement comprehensive contact management
2. **House-Contact Relationships**: Many-to-many relationship management
3. **Communication Preferences**: Multi-channel preference system
4. **Contact Lifecycle**: From creation through archival and restoration

### Phase 3: Access Control & Security
1. **Role Implementation**: Five-tier role system with proper permissions
2. **Estate Assignments**: User-estate assignment system
3. **Permission Validation**: Comprehensive access control validation
4. **Security Auditing**: Complete audit trail for all property data access

### Phase 4: Resident Portal
1. **Authentication**: Separate resident authentication flow
2. **Self-Service Interface**: Resident dashboard and personal data access
3. **Bill & Usage**: Personal billing and consumption information
4. **Communication**: Inquiry system and messaging with management

### Phase 5: Advanced Features
1. **Analytics & Reporting**: Property performance and occupancy analytics
2. **Import/Export**: Bulk data management capabilities
3. **Integration**: Meter reading and billing system integration
4. **Mobile Optimization**: Mobile-responsive interfaces for field operations

## Security & Compliance

### Data Protection
- **Personal Information**: Secure storage and processing of resident data
- **Contact Privacy**: Granular control over contact information sharing
- **Access Logging**: Comprehensive audit trail for all data access
- **Data Retention**: Compliant data retention and archival policies

### Access Control
- **Role Separation**: Strict enforcement of role-based boundaries
- **Estate Isolation**: Users can only access assigned estates
- **Data Minimization**: Collect only necessary property and contact data
- **Permission Validation**: Real-time validation of all access requests

### Privacy Compliance
- **Consent Management**: Manage consent for communications and data usage
- **Right to Access**: Residents can access their personal data
- **Right to Correction**: Allow correction of personal information
- **Data Portability**: Export personal data in standard formats

## Testing Strategy

### Unit Tests
- **Model Relationships**: Test all property model associations
- **Service Logic**: Business logic for property management operations
- **Permission Validation**: Access control validation scenarios
- **Data Validation**: Input validation and data integrity checks

### Integration Tests
- **Property Workflows**: End-to-end property management processes
- **Contact Management**: Complete contact lifecycle testing
- **Access Control**: Role-based access across all property operations
- **Resident Portal**: Resident self-service functionality testing

### Performance Tests
- **Large Estate Management**: Performance with many properties
- **Contact Search**: Search performance with large contact databases
- **Permission Validation**: Access control performance under load
- **Concurrent Access**: Multiple users managing properties simultaneously

## Success Metrics

### Functional Metrics
- **Estate Management**: Support for 1000+ estates with sub-second response times
- **House Registry**: Manage 100,000+ houses with efficient search and filtering
- **Contact System**: Support multiple contacts per house with preference management
- **Access Control**: Zero unauthorized access incidents with comprehensive audit logging

### User Experience Metrics
- **Manager Efficiency**: 50% reduction in time for estate management tasks
- **Data Entry Accuracy**: 95% reduction in data entry errors through validation
- **Resident Satisfaction**: 90%+ satisfaction with self-service portal
- **Search Performance**: Sub-second search results across all property data

### System Performance Metrics
- **Database Performance**: <100ms response times for property queries
- **Permission Validation**: <10ms access control validation
- **Concurrent Users**: Support 100+ concurrent property managers
- **Data Integrity**: 100% data consistency with proper constraint validation

## Integration Points

### Meter Reading Integration
- **House Availability**: Houses automatically available for meter reading entry
- **Contact Pre-population**: Contact information available in reading forms
- **Meter Information**: Meter details pre-filled for accurate data entry

### Billing System Integration
- **Primary Contact Selection**: Automatic selection for invoice delivery
- **Property Information**: House and estate data available for billing
- **Communication Preferences**: Delivery preferences applied to invoice distribution

### Communication Integration
- **WhatsApp Integration**: Contact numbers validated for WhatsApp messaging
- **Email Notifications**: Preference-based email communication
- **SMS Notifications**: Critical alerts via SMS based on preferences

## Future Enhancements

### Advanced Features
- **IoT Integration**: Direct smart meter connectivity for real-time data
- **Mobile Field App**: Dedicated mobile application for property managers
- **AI-Powered Analytics**: Predictive analytics for property management
- **Integration Platform**: API ecosystem for third-party integrations

### Expansion Capabilities
- **Multi-Tenant Support**: Serve multiple water utility companies
- **Geographic Expansion**: Support for different countries and regions
- **Service Diversification**: Expand to other utility services
- **Advanced Analytics**: Machine learning for consumption patterns and optimization

This consolidated specification provides a comprehensive foundation for property management within the water management system, ensuring scalability, security, and excellent user experience across all user roles.